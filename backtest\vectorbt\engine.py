#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
VectorBT回测引擎实现

基于VectorBT库实现的高性能向量化回测引擎。
"""

from typing import Dict, Any, Union, Optional, List, Tuple, Callable
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import vectorbt as vbt
import logging

from ..base import BacktestEngine, Strategy, BacktestResults
from ..config import DEFAULT_BACKTEST_PARAMS, DEFAULT_VECTORBT_PARAMS, merge_parameters
from .results import VectorBTResults

logger = logging.getLogger(__name__)


class VectorBTEngine(BacktestEngine):
    """
    VectorBT回测引擎
    
    使用VectorBT库实现的回测引擎，支持高性能向量化计算。
    """
    
    def __init__(self, data: pd.DataFrame, **kwargs):
        """
        初始化VectorBT回测引擎
        
        Parameters
        ----------
        data : pd.DataFrame
            市场数据
        **kwargs : dict
            引擎参数
        """
        super().__init__(data, **kwargs)
        
        # 保存引擎参数
        self.engine_params = kwargs
        self.vectorbt_params = kwargs.pop('vectorbt_params', {})
        
        # 处理特殊的VectorBT参数
        self.freq = kwargs.pop('freq', None)
        
        # 设置默认参数
        self.params = {
            'initial_capital': kwargs.pop('initial_capital', 10000),
            'commission_rate': kwargs.pop('commission_rate', 0.001),
            'slippage': kwargs.pop('slippage', 0),
            'leverage': kwargs.pop('leverage', 1.0),
            'direction': kwargs.pop('direction', 'both'),  # long, short, both
            'cash_sharing': kwargs.pop('cash_sharing', False),
            'benchmark': kwargs.pop('benchmark', None),
            'risk_free_rate': kwargs.pop('risk_free_rate', 0.0),
        }
        
        # 保存其他所有参数
        self.params.update(kwargs)
        
        # 验证数据
        self._validate_data(data)
        
        # 映射列名
        self.data = self._map_columns(data)
        
        # 初始化结果
        self.portfolio = None
        self.results = None
    
    def _validate_data(self, data: pd.DataFrame) -> None:
        """
        验证输入数据是否符合要求
        
        Parameters
        ----------
        data : pd.DataFrame
            要验证的数据
            
        Raises
        ------
        ValueError
            如果数据格式不正确
        """
        required_columns = ['open', 'high', 'low', 'close']
        
        # 检查索引是否为日期时间
        if not isinstance(data.index, pd.DatetimeIndex):
            raise ValueError("数据索引必须是DatetimeIndex类型")
        
        # 检查必要的列是否存在（不区分大小写）
        data_cols_lower = [col.lower() for col in data.columns]
        for col in required_columns:
            if col not in data_cols_lower:
                raise ValueError(f"数据缺少必要的列: {col}")
    
    def _map_columns(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        将输入数据的列名映射为标准小写格式
        
        Parameters
        ----------
        data : pd.DataFrame
            原始数据
            
        Returns
        -------
        pd.DataFrame
            列名标准化后的数据
        """
        # 创建列名映射
        col_mapping = {}
        standard_cols = ['open', 'high', 'low', 'close', 'volume']
        
        for col in data.columns:
            col_lower = col.lower()
            if col_lower in standard_cols:
                col_mapping[col] = col_lower
        
        # 重命名列
        renamed_data = data.rename(columns=col_mapping)
        return renamed_data
    
    def run(self, strategy: Strategy, **kwargs) -> BacktestResults:
        """
        运行回测
        
        Parameters
        ----------
        strategy : Strategy
            策略实例
        **kwargs : dict
            额外参数，会覆盖初始化时的参数
            
        Returns
        -------
        BacktestResults
            回测结果
        """
        # 更新参数
        run_params = self.params.copy()
        run_params.update(kwargs)
        
        # 获取策略信号
        signals = strategy.generate_signals(self.data)
        
        # 处理频率参数
        freq = kwargs.get('freq', self.freq)
        if freq is None and hasattr(self.data.index, 'freq') and self.data.index.freq is not None:
            freq = self.data.index.freq
        elif freq is None:
            # 尝试推断频率
            freq_inferred = pd.infer_freq(self.data.index)
            if freq_inferred is not None:
                freq = freq_inferred
            else:
                # 默认使用日频率
                freq = 'D'
                
        # 确保数据是合适的VectorBT格式
        price_data = self.data['close'].copy()
        if price_data.index.freq is None:
            price_data.index.freq = freq
        
        # 使用日志记录而不是打印到控制台
        logger.debug(f"使用频率: {freq}")
        
        # ✅ 修复信号数组对齐问题
        print(f"数据长度: {len(self.data)}, 信号长度: {len(signals['entries'])}")
        
        # 确保信号与数据索引完全对齐
        aligned_entries = signals['entries'].reindex(self.data.index, fill_value=False)
        aligned_exits = signals['exits'].reindex(self.data.index, fill_value=False)
        
        print(f"对齐后 - 数据长度: {len(self.data)}, 入场信号长度: {len(aligned_entries)}, 出场信号长度: {len(aligned_exits)}")
        
        # 运行VectorBT回测
        # 创建portfolio
        try:
            self.portfolio = vbt.Portfolio.from_signals(
                price_data,
                aligned_entries,
                aligned_exits,
                price=self.data['close'],
                init_cash=run_params['initial_capital'],
                fees=run_params['commission_rate'],
                slippage=run_params['slippage'],
                direction=run_params['direction'],
                freq=freq,
                cash_sharing=run_params['cash_sharing']
            )
        except Exception as e:
            print(f"VectorBT Portfolio创建失败: {e}")
            # 尝试使用其他方法
            print("尝试使用替代方法创建Portfolio...")
            try:
                entries = aligned_entries.astype(float)
                exits = aligned_exits.astype(float)
                direction = 1.0 if run_params['direction'] in ['both', 'long'] else -1.0
                
                # 创建持仓大小序列
                if hasattr(entries, 'index'):
                    size = pd.Series(direction, index=entries.index)
                    # 在信号点设置持仓大小
                    entry_points = entries.astype(bool)
                    exit_points = exits.astype(bool)
                    trading_points = entry_points | exit_points
                    size = size.loc[trading_points]
                    # 使用vectorbt的方法前移一个位置
                    size = size.shift(1).fillna(0)
                else:
                    # 如果没有索引，创建简单数组
                    size = np.ones(len(price_data)) * direction
                
                # 使用from_holding方法
                self.portfolio = vbt.Portfolio.from_holding(
                    price_data,
                    size=size,
                    init_cash=run_params['initial_capital'],
                    fees=run_params['commission_rate'],
                    slippage=run_params['slippage'],
                    freq=freq,
                    cash_sharing=run_params['cash_sharing']
                )
            except Exception as e2:
                print(f"替代方法也失败了: {e2}")
                # 创建一个空的Portfolio
                print("创建空Portfolio...")
                # 如果所有尝试都失败，返回一个空的结果
                metrics = {
                    'total_return': 0.0,
                    'max_drawdown': 0.0,
                    'win_rate': 0.0,
                    'sharpe_ratio': 0.0
                }
                
                # 创建回测结果
                empty_series = pd.Series(run_params['initial_capital'], index=self.data.index)
                zero_series = pd.Series(0, index=self.data.index)
                empty_df = pd.DataFrame(index=self.data.index)
                
                return VectorBTResults(
                    equity=empty_series,
                    returns=zero_series,
                    positions=empty_df,
                    trades=pd.DataFrame(),
                    metrics=metrics,
                    strategy=str(strategy),
                    params=run_params
                )
        
        # 计算指标
        metrics = self._calculate_metrics(self.portfolio)
        
        # 创建回测结果
        returns = self.portfolio.returns() if hasattr(self.portfolio, 'returns') and callable(self.portfolio.returns) else pd.Series(0, index=self.data.index)
        
        results = VectorBTResults(
            equity=self.portfolio.value if not callable(self.portfolio.value) else self.portfolio.value(),
            returns=returns,
            positions=self._get_positions(),
            trades=self._get_trades(),
            metrics=metrics,
            strategy=str(strategy),
            params=run_params,
            portfolio=self.portfolio
        )
        
        return results
    
    def _calculate_metrics(self, portfolio: vbt.Portfolio) -> Dict[str, Any]:
        """
        计算回测指标
        
        Parameters
        ----------
        portfolio : vbt.Portfolio
            VectorBT Portfolio对象
            
        Returns
        -------
        Dict[str, Any]
            指标字典
        """
        # 检查是否有交易记录
        try:
            trades_count = portfolio.trades.count()
            has_trades = (not hasattr(trades_count, 'empty') or not trades_count.empty) and (
                isinstance(trades_count, (int, np.integer)) and trades_count > 0 or 
                hasattr(trades_count, 'sum') and trades_count.sum() > 0
            )
        except Exception as e:
            print(f"检查交易记录时出错: {e}")
            has_trades = False
        
        # 安全地调用方法，处理所有异常
        def safe_call(func, default=0):
            """安全调用方法，处理所有异常"""
            try:
                result = func()
                
                # 如果结果是Series或DataFrame，获取第一个值
                if isinstance(result, (pd.Series, pd.DataFrame)):
                    if len(result) > 0:
                        return result.iloc[0]
                    else:
                        return default
                
                return result
            except Exception as e:
                print(f"调用 {func.__name__ if hasattr(func, '__name__') else func} 时出错: {e}")
                return default
        
        # 初始化指标字典
        metrics = {}
        
        # 计算总收益率
        try:
            metrics['total_return'] = safe_call(portfolio.total_return)
        except Exception as e:
            print(f"计算总收益率时出错: {e}")
            # 尝试手动计算
            try:
                equity = portfolio.value if not callable(portfolio.value) else portfolio.value()
                initial_value = equity.iloc[0]
                final_value = equity.iloc[-1]
                metrics['total_return'] = (final_value / initial_value) - 1
            except Exception as e2:
                print(f"手动计算总收益率时出错: {e2}")
                metrics['total_return'] = 0.0
        
        # 计算最大回撤
        try:
            metrics['max_drawdown'] = safe_call(portfolio.max_drawdown)
        except Exception as e:
            print(f"计算最大回撤时出错: {e}")
            # 尝试手动计算
            try:
                equity = portfolio.value if not callable(portfolio.value) else portfolio.value()
                # 计算累计最大值
                running_max = equity.cummax()
                # 计算相对于累计最大值的回撤
                drawdown = (equity - running_max) / running_max
                metrics['max_drawdown'] = abs(drawdown.min())
            except Exception as e2:
                print(f"手动计算最大回撤时出错: {e2}")
                metrics['max_drawdown'] = 0.0
        
        # 计算胜率
        if has_trades:
            try:
                metrics['win_rate'] = safe_call(portfolio.trades.win_rate)
            except Exception as e:
                print(f"计算胜率时出错: {e}")
                # 尝试手动计算
                try:
                    wins = safe_call(lambda: portfolio.trades.winning.count())
                    total = safe_call(lambda: portfolio.trades.count())
                    if total > 0:
                        metrics['win_rate'] = wins / total
                    else:
                        metrics['win_rate'] = 0.0
                except Exception as e2:
                    print(f"手动计算胜率时出错: {e2}")
                    metrics['win_rate'] = 0.0
        else:
            metrics['win_rate'] = 0.0
        
        # 计算年化收益率
        try:
            metrics['annual_return'] = safe_call(portfolio.annualized_return)
        except Exception as e:
            print(f"计算年化收益率时出错: {e}")
            # 尝试手动计算
            try:
                returns = portfolio.returns()
                days_count = len(returns)
                metrics['annual_return'] = (1 + metrics['total_return']) ** (252 / days_count) - 1
            except Exception as e2:
                print(f"手动计算年化收益率时出错: {e2}")
                metrics['annual_return'] = 0.0
        
        # 计算波动率
        try:
            # 在新版本的vectorbt中，ann_volatility可能不存在或有不同的名称
            # 尝试使用正确的方法或直接计算
            if hasattr(portfolio, 'ann_volatility'):
                metrics['volatility'] = safe_call(portfolio.ann_volatility)
            elif hasattr(portfolio, 'annual_volatility'):
                metrics['volatility'] = safe_call(portfolio.annual_volatility)
            else:
                # 手动计算年化波动率
                returns = portfolio.returns()
                metrics['volatility'] = returns.std() * np.sqrt(252)
        except Exception as e:
            print(f"计算波动率时出错: {e}")
            # 尝试手动计算
            try:
                returns = portfolio.returns()
                metrics['volatility'] = returns.std() * np.sqrt(252)
            except Exception as e2:
                print(f"手动计算波动率时出错: {e2}")
                metrics['volatility'] = 0.0
        
        # 计算夏普比率
        try:
            metrics['sharpe_ratio'] = safe_call(portfolio.sharpe_ratio)
        except Exception as e:
            print(f"计算夏普比率时出错: {e}")
            # 尝试手动计算
            try:
                returns = portfolio.returns()
                if returns.std() > 0:
                    metrics['sharpe_ratio'] = returns.mean() / returns.std() * np.sqrt(252)
                else:
                    metrics['sharpe_ratio'] = 0.0
            except Exception as e2:
                print(f"手动计算夏普比率时出错: {e2}")
                metrics['sharpe_ratio'] = 0.0
        
        # 如果有交易记录，计算交易相关指标
        if has_trades:
            try:
                metrics['total_trades'] = safe_call(portfolio.trades.count)
            except Exception as e:
                metrics['total_trades'] = 0
                
            try:
                metrics['avg_trade_pnl'] = safe_call(lambda: portfolio.trades.pnl.mean())
            except Exception as e:
                metrics['avg_trade_pnl'] = 0.0
                
            try:
                metrics['max_winner'] = safe_call(lambda: portfolio.trades.pnl.max())
            except Exception as e:
                metrics['max_winner'] = 0.0
                
            try:
                metrics['max_loser'] = safe_call(lambda: portfolio.trades.pnl.min())
            except Exception as e:
                metrics['max_loser'] = 0.0
                
            try:
                metrics['loss_rate'] = 1 - metrics['win_rate']
            except Exception as e:
                metrics['loss_rate'] = 1.0
        else:
            # 如果没有交易记录，设置默认值
            metrics['total_trades'] = 0
            metrics['avg_trade_pnl'] = 0.0
            metrics['max_winner'] = 0.0
            metrics['max_loser'] = 0.0
            metrics['loss_rate'] = 1.0
        
        # 计算最大回撤持续时间
        try:
            # 在新版本的vectorbt中，max_dd_duration可能不存在或有不同的名称
            # 尝试使用正确的方法或直接计算
            if hasattr(portfolio, 'max_dd_duration'):
                metrics['max_drawdown_len'] = safe_call(portfolio.max_dd_duration)
            elif hasattr(portfolio, 'drawdown_duration'):
                # 如果存在drawdown_duration方法，使用它计算最大回撤持续时间
                dd_duration = safe_call(portfolio.drawdown_duration)
                if isinstance(dd_duration, pd.Series):
                    metrics['max_drawdown_len'] = dd_duration.max()
                else:
                    metrics['max_drawdown_len'] = dd_duration
            else:
                # 手动计算最大回撤持续时间
                equity = portfolio.value if not callable(portfolio.value) else portfolio.value()
                # 计算累计最大值
                running_max = equity.cummax()
                # 计算相对于累计最大值的回撤
                drawdowns = (equity - running_max) / running_max
                # 创建回撤状态序列：True表示在回撤中
                in_drawdown = drawdowns < 0
                # 使用连续状态计算回撤持续时间
                duration = 0
                max_duration = 0
                for is_dd in in_drawdown:
                    if is_dd:
                        duration += 1
                    else:
                        max_duration = max(max_duration, duration)
                        duration = 0
                # 最后检查一次
                max_duration = max(max_duration, duration)
                metrics['max_drawdown_len'] = max_duration
        except Exception as e:
            print(f"计算最大回撤持续时间时出错: {e}")
            metrics['max_drawdown_len'] = 0
        
        # 确保所有指标都是Python原生类型
        for key, value in metrics.items():
            if isinstance(value, (pd.Series, pd.DataFrame, np.ndarray)):
                if len(value) > 0:
                    metrics[key] = float(value.iloc[0] if hasattr(value, 'iloc') else value[0])
                else:
                    metrics[key] = 0.0
            elif isinstance(value, np.number):
                metrics[key] = float(value)
        
        return metrics
    
    def _get_positions(self) -> pd.DataFrame:
        """
        获取持仓数据
        
        Returns
        -------
        pd.DataFrame
            持仓数据
        """
        if self.portfolio is None:
            return pd.DataFrame()
        
        # 从VectorBT获取持仓信息
        positions = pd.DataFrame({
            'size': self.portfolio.positions.size,
            'value': self.portfolio.positions.values
        })
        
        return positions
    
    def _get_trades(self) -> pd.DataFrame:
        """
        获取交易记录
        
        Returns
        -------
        pd.DataFrame
            交易记录
        """
        if self.portfolio is None:
            return pd.DataFrame()
        
        # 检查是否有交易记录
        try:
            trades_count = self.portfolio.trades.count()
            has_trades = (not hasattr(trades_count, 'empty') or not trades_count.empty) and (
                isinstance(trades_count, (int, np.integer)) and trades_count > 0 or 
                hasattr(trades_count, 'sum') and trades_count.sum() > 0
            )
        except:
            has_trades = False
            
        if not has_trades:
            return pd.DataFrame()
        
        # 从VectorBT获取交易记录
        trades = self.portfolio.trades.records_readable
        
        return trades
    
    def plot(self, **kwargs) -> None:
        """
        可视化回测结果
        
        Parameters
        ----------
        **kwargs : dict
            可视化参数
        """
        if self.portfolio is None:
            raise RuntimeError("请先运行回测再进行可视化")
        
        # 合并默认可视化参数和用户参数
        plot_params = {}
        plot_params.update(self.vectorbt_params)
        plot_params.update(kwargs)
        
        # 确定绘图类型
        plot_type = plot_params.get('plot_type', 'basic')
        
        if plot_type == 'basic':
            # 基础图表: 净值曲线和交易点
            self._plot_basic(**plot_params)
        elif plot_type == 'detailed':
            # 详细图表: 包括更多性能指标
            self._plot_detailed(**plot_params)
        elif plot_type == 'full':
            # 完整图表: 使用VectorBT的内置绘图功能
            self._plot_full(**plot_params)
        else:
            raise ValueError(f"不支持的绘图类型: {plot_type}")
    
    def _plot_basic(self, **kwargs) -> None:
        """
        绘制基础回测结果图表
        
        Parameters
        ----------
        **kwargs : dict
            可视化参数
        """
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # 绘制净值曲线
        equity = self.portfolio.value
        # 检查equity是否为函数，如果是则调用它
        if callable(equity):
            equity = equity()
            
        equity.plot(ax=ax, label='Portfolio Value')
        
        # 添加买卖点标记
        try:
            trades_count = self.portfolio.trades.count()
            has_trades = (not hasattr(trades_count, 'empty') or not trades_count.empty) and (
                isinstance(trades_count, (int, np.integer)) and trades_count > 0 or 
                hasattr(trades_count, 'sum') and trades_count.sum() > 0
            )
        except:
            has_trades = False
            
        if has_trades and hasattr(self.portfolio.trades, 'records_readable'):
            try:
                entries = self.portfolio.trades.records_readable.loc[
                    self.portfolio.trades.records_readable['Side'] == 'Buy', 'Entry Time'
                ]
                exits = self.portfolio.trades.records_readable.loc[
                    self.portfolio.trades.records_readable['Side'] == 'Buy', 'Exit Time'
                ]
                
                # 获取对应时间点的净值
                entry_values = equity.loc[entries]
                exit_values = equity.loc[exits]
                
                # 添加标记
                ax.scatter(entries, entry_values, color='green', marker='^', label='Entry', zorder=5)
                ax.scatter(exits, exit_values, color='red', marker='v', label='Exit', zorder=5)
            except:
                pass  # 如果出现错误，跳过绘制交易点
        
        # 设置图表属性
        ax.set_title('Portfolio Performance')
        ax.set_ylabel('Portfolio Value')
        ax.grid(True)
        ax.legend()
        
        plt.tight_layout()
        
        # 如果指定了保存路径，保存图表
        if 'savefig' in kwargs:
            plt.savefig(kwargs['savefig'])
        
        # 除非明确指定不显示，否则显示图表
        if kwargs.get('show', True):
            plt.show()
    
    def _plot_detailed(self, **kwargs) -> None:
        """
        绘制详细回测结果图表
        
        Parameters
        ----------
        **kwargs : dict
            可视化参数
        """
        if self.portfolio is None:
            raise RuntimeError("请先运行回测再进行可视化")
        
        # 创建四个子图
        fig, axs = plt.subplots(3, 1, figsize=(14, 12), gridspec_kw={'height_ratios': [2, 1, 1]})
        
        # 1. 净值曲线和交易点
        equity = self.portfolio.value
        # 检查equity是否为函数，如果是则调用它
        if callable(equity):
            equity = equity()
            
        equity.plot(ax=axs[0], label='Portfolio Value')
        
        if self.portfolio.trades.count().sum() > 0:
            entries = self.portfolio.trades.records_readable.loc[
                self.portfolio.trades.records_readable['Side'] == 'Buy', 'Entry Time'
            ]
            exits = self.portfolio.trades.records_readable.loc[
                self.portfolio.trades.records_readable['Side'] == 'Buy', 'Exit Time'
            ]
            
            # 获取对应时间点的净值
            entry_values = equity.loc[entries]
            exit_values = equity.loc[exits]
            
            # 添加标记
            axs[0].scatter(entries, entry_values, color='green', marker='^', label='Entry', zorder=5)
            axs[0].scatter(exits, exit_values, color='red', marker='v', label='Exit', zorder=5)
        
        axs[0].set_title('Portfolio Performance')
        axs[0].set_ylabel('Portfolio Value')
        axs[0].grid(True)
        axs[0].legend()
        
        # 2. 回撤图
        drawdowns = self.portfolio.drawdown
        if callable(drawdowns):
            drawdowns = drawdowns()
            
        drawdowns.plot(ax=axs[1], color='red', alpha=0.5, label='Drawdowns')
        axs[1].set_title('Portfolio Drawdowns')
        axs[1].set_ylabel('Drawdown %')
        axs[1].grid(True)
        axs[1].legend()
        
        # 3. 月度收益热图
        returns = self.portfolio.returns()
        if returns.shape[0] > 30:  # 确保有足够的数据
            # 转换为月度收益
            monthly_returns = returns.resample('M').apply(
                lambda x: (1 + x).prod() - 1
            )
            
            # 创建月度收益表格
            monthly_table = pd.DataFrame({
                'Returns': monthly_returns
            })
            monthly_table['Year'] = monthly_table.index.year
            monthly_table['Month'] = monthly_table.index.month
            
            # 透视表
            heatmap_data = monthly_table.pivot_table(
                values='Returns', 
                index='Year', 
                columns='Month'
            )
            
            # 绘制热图
            im = axs[2].imshow(
                heatmap_data.values, 
                cmap='RdYlGn',
                aspect='auto',
                vmin=-0.1,
                vmax=0.1
            )
            
            # 设置坐标轴标签
            axs[2].set_xticks(np.arange(12))
            axs[2].set_xticklabels(['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'])
            axs[2].set_yticks(np.arange(len(heatmap_data.index)))
            axs[2].set_yticklabels(heatmap_data.index)
            
            # 添加颜色条
            cbar = plt.colorbar(im, ax=axs[2])
            cbar.set_label('Monthly Return')
            
            # 添加文本标签
            for i in range(len(heatmap_data.index)):
                for j in range(12):
                    if j < heatmap_data.values.shape[1] and not np.isnan(heatmap_data.values[i, j]):
                        text = f"{heatmap_data.values[i, j]:.1%}"
                        axs[2].text(j, i, text, ha='center', va='center', color='black')
            
            axs[2].set_title('Monthly Returns')
        else:
            axs[2].text(0.5, 0.5, 'Insufficient data for monthly returns', 
                       horizontalalignment='center', verticalalignment='center')
        
        plt.tight_layout()
        
        # 如果指定了保存路径，保存图表
        if 'savefig' in kwargs:
            plt.savefig(kwargs['savefig'])
        
        # 除非明确指定不显示，否则显示图表
        if kwargs.get('show', True):
            plt.show()
        
        # 显示主要性能指标
        self._display_metrics()
    
    def _plot_full(self, **kwargs) -> None:
        """
        使用VectorBT原生绘图功能
        
        Parameters
        ----------
        **kwargs : dict
            可视化参数
        """
        if self.portfolio is None:
            raise RuntimeError("请先运行回测再进行可视化")
        
        # 使用VectorBT的内置绘图功能
        fig = self.portfolio.plot(**kwargs)
        
        # 如果指定了保存路径，保存图表
        if 'savefig' in kwargs:
            plt.savefig(kwargs['savefig'])
        
        # 除非明确指定不显示，否则显示图表
        if kwargs.get('show', True):
            plt.show()
    
    def _display_metrics(self) -> None:
        """
        显示回测性能指标
        """
        if self.results is None:
            return
        
        # 获取指标
        metrics = self.results.metrics
        
        # 创建性能指标表格
        metrics_df = pd.DataFrame({
            'Metric': list(metrics.keys()),
            'Value': list(metrics.values())
        })
        
        # 自定义格式化函数
        def format_value(x):
            if isinstance(x, (float, np.float64)):
                if abs(x) < 0.01:
                    return f"{x:.6f}"
                else:
                    return f"{x:.4f}"
            return str(x)
        
        # 格式化值
        metrics_df['Value'] = metrics_df['Value'].apply(format_value)
        
        # 打印性能指标
        print("\n===== 回测性能指标 =====")
        print(metrics_df.to_string(index=False))
        print("========================\n") 