#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自定义指标构建器模块

提供用于构建自定义技术指标的构建器类和相关工具。
使用构建器模式和链式API，使创建复杂指标变得简单直观。
"""

from typing import Dict, Any, List, Optional, Union, Tuple, Callable
import pandas as pd
import numpy as np
from copy import deepcopy

from ..base import Indicator


class CustomIndicator(Indicator):
    """
    自定义指标基类
    
    用于创建自定义指标的基类，支持组件化组合，可以包含多个子指标或操作。
    """
    
    def __init__(self, name: str, **kwargs):
        """
        初始化自定义指标
        
        Parameters
        ----------
        name : str
            自定义指标名称
        **kwargs : dict
            其他参数
        """
        super().__init__(name, "custom", **kwargs)
        self.components = []
        self.calculation_func = None
        self.special_signals = {}
        
    def add_component(self, component: Indicator) -> 'CustomIndicator':
        """
        添加指标组件
        
        Parameters
        ----------
        component : Indicator
            要添加的指标组件
            
        Returns
        -------
        CustomIndicator
            返回自身，支持链式调用
        """
        self.components.append(component)
        return self
    
    def set_calculation_function(self, func: Callable) -> 'CustomIndicator':
        """
        设置计算函数
        
        Parameters
        ----------
        func : Callable
            用于计算指标的函数，接受DataFrame和组件结果作为参数
            
        Returns
        -------
        CustomIndicator
            返回自身，支持链式调用
        """
        self.calculation_func = func
        return self
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算自定义指标值
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据
            
        Returns
        -------
        pd.DataFrame
            包含计算结果的DataFrame
        """
        result = data.copy()
        component_results = []
        
        # 计算所有组件
        for component in self.components:
            comp_result = component.calculate(data)
            component_results.append(comp_result)
        
        # 使用自定义计算函数
        if self.calculation_func is not None:
            result = self.calculation_func(result, component_results)
        else:
            # 默认合并所有组件结果
            for comp_result in component_results:
                for col in comp_result.columns:
                    if col not in result.columns:
                        result[col] = comp_result[col]
        
        self._result = result
        return result


class IndicatorBuilder:
    """
    指标构建器
    
    使用构建器模式和链式API创建自定义指标。
    """
    
    def __init__(self):
        """初始化构建器"""
        self.steps = []
        self.name = "CustomIndicator"
        self.params = {}
    
    def set_name(self, name: str) -> 'IndicatorBuilder':
        """
        设置指标名称
        
        Parameters
        ----------
        name : str
            指标名称
            
        Returns
        -------
        IndicatorBuilder
            返回自身，支持链式调用
        """
        self.name = name
        return self
    
    def set_params(self, **params) -> 'IndicatorBuilder':
        """
        设置指标参数
        
        Parameters
        ----------
        **params : dict
            指标参数
            
        Returns
        -------
        IndicatorBuilder
            返回自身，支持链式调用
        """
        self.params = params
        return self
    
    def add(self, indicator: Indicator) -> 'IndicatorBuilder':
        """
        添加指标
        
        Parameters
        ----------
        indicator : Indicator
            要添加的指标
            
        Returns
        -------
        IndicatorBuilder
            返回自身，支持链式调用
        """
        self.steps.append(('add', indicator))
        return self
    
    def apply(self, func: Callable, **params) -> 'IndicatorBuilder':
        """
        应用自定义函数
        
        Parameters
        ----------
        func : Callable
            要应用的函数
        **params : dict
            函数参数
            
        Returns
        -------
        IndicatorBuilder
            返回自身，支持链式调用
        """
        self.steps.append(('apply', (func, params)))
        return self
    
    def transform(self, transform_type: str, **params) -> 'IndicatorBuilder':
        """
        应用数据转换
        
        Parameters
        ----------
        transform_type : str
            转换类型，如'shift', 'rolling', 'diff', 'pct_change'等
        **params : dict
            转换参数
            
        Returns
        -------
        IndicatorBuilder
            返回自身，支持链式调用
        """
        self.steps.append(('transform', (transform_type, params)))
        return self
    
    def combine(self, operation: str, other: Union[Indicator, float, int], **params) -> 'IndicatorBuilder':
        """
        指标组合操作
        
        Parameters
        ----------
        operation : str
            操作类型，如'add', 'subtract', 'multiply', 'divide'等
        other : Indicator或数值
            另一个操作数
        **params : dict
            操作参数
            
        Returns
        -------
        IndicatorBuilder
            返回自身，支持链式调用
        """
        self.steps.append(('combine', (operation, other, params)))
        return self
    
    def generate_signal(self, signal_type: str, **params) -> 'IndicatorBuilder':
        """
        添加信号生成
        
        Parameters
        ----------
        signal_type : str
            信号类型，如'cross', 'threshold', 'pattern'等
        **params : dict
            信号参数
            
        Returns
        -------
        IndicatorBuilder
            返回自身，支持链式调用
        """
        self.steps.append(('signal', (signal_type, params)))
        return self
    
    def build(self) -> CustomIndicator:
        """
        构建自定义指标
        
        根据之前添加的构建步骤，创建一个自定义指标实例。
        
        如果有多个apply步骤，它们将按顺序应用，并且每个函数都会接收
        前一个函数的结果以及所有组件的计算结果列表。
        这样可以确保不会丢失任何计算的列，特别是当使用多个指标组件时。
        
        Returns
        -------
        CustomIndicator
            构建的自定义指标
        """
        # 创建自定义指标实例
        indicator = CustomIndicator(self.name, **self.params)
        
        # 收集所有apply函数，以便能够按顺序应用它们
        apply_funcs = []
        
        # 处理各个构建步骤
        for step_type, step_data in self.steps:
            if step_type == 'add':
                indicator.add_component(step_data)
            elif step_type == 'apply':
                func, params = step_data
                # 收集应用函数而不是立即设置，这允许多个函数按顺序应用
                apply_funcs.append((func, params))
            elif step_type == 'signal':
                # 处理信号生成器
                # 信号生成已迁移到FreqTrade原生系统
# from .signals import create_signal_generator
                if isinstance(step_data, tuple) and len(step_data) == 2:
                    signal_type, params = step_data
                    signal_gen = create_signal_generator(signal_type, **params)
                else:
                    signal_gen = step_data
                
                # 添加信号生成逻辑到计算函数
                old_func = indicator.calculation_func
                
                def signal_wrapper(data, comp_results, sg=signal_gen, of=old_func):
                    if of is not None:
                        result = of(data, comp_results)
                    else:
                        result = data.copy()
                        for comp_result in comp_results:
                            for col in comp_result.columns:
                                if col not in result.columns:
                                    result[col] = comp_result[col]
                    
                    # 应用信号生成器
                    return sg.generate(result)
                
                indicator.set_calculation_function(signal_wrapper)
            elif step_type == 'signal_special':
                # 处理特殊信号生成器（如布林带信号）
                special_type, signal_gen = step_data
                
                # 添加特殊信号生成逻辑到计算函数
                old_func = indicator.calculation_func
                
                # 这里不应用信号生成器，因为它需要在process_bb_signal函数中特殊处理
                # 但我们需要确保它被保存在计算函数中
                indicator.special_signals = {special_type: signal_gen}
            # 其他步骤将在operations, transforms和signals模块中实现
        
        # 如果有应用函数，创建一个组合函数来按顺序应用它们
        if apply_funcs:
            def combined_calculation(data, comp_results):
                # 首先确保所有组件结果的列都被复制到结果中
                result = data.copy()
                for comp_result in comp_results:
                    for col in comp_result.columns:
                        if col not in result.columns:
                            result[col] = comp_result[col]
                
                # 然后按顺序应用所有函数
                for func, params in apply_funcs:
                    result = func(result, comp_results, **params)
                
                return result
            
            indicator.set_calculation_function(combined_calculation)
        
        return indicator


def create_custom_indicator(name: str = "CustomIndicator", **kwargs) -> IndicatorBuilder:
    """
    创建自定义指标构建器
    
    Parameters
    ----------
    name : str, optional
        指标名称，默认为"CustomIndicator"
    **kwargs : dict
        指标参数
        
    Returns
    -------
    IndicatorBuilder
        指标构建器
    """
    return IndicatorBuilder().set_name(name).set_params(**kwargs) 