#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
VectorBT Walk Forward Analysis

提供VectorBT策略的Walk Forward Analysis功能。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Any, Union, Optional, List, Tuple, Callable
import logging
import time
from datetime import datetime

from ...base import Strategy, BacktestResults
from .. import engine
from .optimizer import VectorBTOptimizer

logger = logging.getLogger(__name__)

class VectorBTWalkForward:
    """
    VectorBT Walk Forward Analysis实现
    
    提供滚动窗口的策略参数优化和测试功能。
    """
    
    def __init__(self, engine_instance: engine.VectorBTEngine, strategy_class, data: pd.DataFrame,
                 train_size: float = 0.6, test_size: float = 0.4, n_windows: int = 5,
                 metric: str = 'sharpe_ratio', maximize: bool = True, **engine_kwargs):
        """
        初始化Walk Forward Analysis
        
        Parameters
        ----------
        engine_instance : engine.VectorBTEngine
            VectorBT引擎实例
        strategy_class : type
            策略类（非实例）
        data : pd.DataFrame
            回测数据
        train_size : float, optional
            训练集比例，默认为0.6
        test_size : float, optional
            测试集比例，默认为0.4
        n_windows : int, optional
            窗口数量，默认为5
        metric : str, optional
            优化目标指标，默认为'sharpe_ratio'
        maximize : bool, optional
            是否最大化指标，默认为True
        **engine_kwargs : dict
            引擎参数
        """
        self.engine = engine_instance
        self.strategy_class = strategy_class
        self.data = data
        self.train_size = train_size
        self.test_size = test_size
        self.n_windows = n_windows
        self.metric = metric
        self.maximize = maximize
        self.engine_kwargs = engine_kwargs
        self.results = None
        
    def run(self, param_grid: Dict[str, List], n_jobs: int = 1, progress_callback: Callable = None) -> pd.DataFrame:
        """
        运行Walk Forward Analysis
        
        Parameters
        ----------
        param_grid : dict
            参数网格，格式为 {'param_name': [value1, value2, ...]}
        n_jobs : int, optional
            并行任务数，默认为1
        progress_callback : callable, optional
            进度回调函数，接收(当前窗口, 总窗口数)参数
            
        Returns
        -------
        pd.DataFrame
            测试窗口结果
        """
        # 计算每个窗口的大小
        full_size = len(self.data)
        window_size = int(full_size / self.n_windows)
        
        # 存储窗口结果
        window_results = []
        
        for i in range(self.n_windows):
            logger.info(f"Processing window {i+1}/{self.n_windows}")
            
            # 回调进度
            if progress_callback is not None:
                progress_callback(i, self.n_windows)
            
            # 计算窗口边界
            start_idx = i * window_size
            train_end_idx = start_idx + int(window_size * self.train_size)
            test_end_idx = min(train_end_idx + int(window_size * self.test_size), full_size)
            
            # 获取训练和测试数据
            train_data = self.data.iloc[start_idx:train_end_idx].copy()
            test_data = self.data.iloc[train_end_idx:test_end_idx].copy()
            
            # 创建优化器并优化训练数据
            optimizer = VectorBTOptimizer(
                engine_instance=self.engine, 
                strategy_class=self.strategy_class, 
                data=train_data,
                metric=self.metric,
                maximize=self.maximize,
                **self.engine_kwargs
            )
            
            # 运行网格搜索
            optimizer.grid_search(param_grid, n_jobs=n_jobs)
            
            # 获取最佳参数
            best_params = optimizer.get_best_params()
            
            # 在测试数据上评估
            test_engine = engine.VectorBTEngine(test_data, **self.engine_kwargs)
            best_strategy = self.strategy_class(**best_params)
            test_results = test_engine.run(best_strategy)
            
            # 记录窗口结果
            window_result = {
                'window': i+1,
                'train_start': self.data.index[start_idx],
                'train_end': self.data.index[train_end_idx-1],
                'test_start': self.data.index[train_end_idx],
                'test_end': self.data.index[test_end_idx-1] if test_end_idx < len(self.data) else self.data.index[-1],
                'best_params': best_params,
                **test_results.metrics
            }
            
            window_results.append(window_result)
        
        # 完成所有窗口后的最终进度回调
        if progress_callback is not None:
            progress_callback(self.n_windows, self.n_windows)
            
        # 转换为DataFrame
        results_df = pd.DataFrame(window_results)
        self.results = results_df
        
        return results_df
    
    def plot_results(self):
        """绘制Walk Forward Analysis结果"""
        if self.results is None:
            raise ValueError("Please run analysis first")
        
        # 检查数据有效性
        if len(self.results) == 0:
            print("No usable Walk Forward Analysis results for plotting")
            return
            
        # 检查是否所有指标都是相同值或无穷大/零
        sharpe_valid = not (self.results['sharpe_ratio'].isnull().all() or 
                          (self.results['sharpe_ratio'] == float('inf')).all() or 
                          (self.results['sharpe_ratio'] == 0).all() or 
                          self.results['sharpe_ratio'].std() == 0)
        
        return_valid = not (self.results['total_return'].isnull().all() or 
                          (self.results['total_return'] == 0).all() or 
                          self.results['total_return'].std() == 0)
        
        drawdown_valid = not (self.results['max_drawdown'].isnull().all() or 
                            (self.results['max_drawdown'] == 0).all() or 
                            self.results['max_drawdown'].std() == 0)
        
        if not sharpe_valid and not return_valid and not drawdown_valid:
            print("All test window performance metrics are invalid or identical, cannot plot meaningful result charts")
            return
            
        # 创建图表
        fig, axes = plt.subplots(3, 1, figsize=(12, 12))
        
        # 绘制每个窗口的性能指标
        window_nums = self.results['window']
        
        # 绘制夏普比率
        axes[0].plot(window_nums, self.results['sharpe_ratio'], 'o-', label='Sharpe Ratio')
        axes[0].set_title('Sharpe Ratio by Window')
        axes[0].set_xlabel('Window')
        axes[0].set_ylabel('Sharpe Ratio')
        axes[0].grid(True)
        
        # 绘制收益率
        axes[1].plot(window_nums, self.results['total_return'], 'o-', label='Total Return')
        axes[1].set_title('Return by Window')
        axes[1].set_xlabel('Window')
        axes[1].set_ylabel('Return')
        axes[1].grid(True)
        
        # 绘制最大回撤
        axes[2].plot(window_nums, self.results['max_drawdown'], 'o-', label='Max Drawdown')
        axes[2].set_title('Max Drawdown by Window')
        axes[2].set_xlabel('Window')
        axes[2].set_ylabel('Drawdown')
        axes[2].grid(True)
        
        plt.tight_layout()
        plt.show()
        
        # 参数稳定性分析
        param_keys = list(self.results['best_params'].iloc[0].keys())
        
        # 创建参数变化图
        fig, axes = plt.subplots(len(param_keys), 1, figsize=(12, 3*len(param_keys)))
        
        if len(param_keys) == 1:
            # 如果只有一个参数，axes不是数组
            param = param_keys[0]
            param_values = [window_params[param] for window_params in self.results['best_params']]
            axes.plot(window_nums, param_values, 'o-', label=param)
            axes.set_title(f'{param} by Window')
            axes.set_xlabel('Window')
            axes.set_ylabel('Value')
            axes.grid(True)
        else:
            # 多个参数
            for i, param in enumerate(param_keys):
                param_values = [window_params[param] for window_params in self.results['best_params']]
                axes[i].plot(window_nums, param_values, 'o-', label=param)
                axes[i].set_title(f'{param} by Window')
                axes[i].set_xlabel('Window')
                axes[i].set_ylabel('Value')
                axes[i].grid(True)
            
        plt.tight_layout()
        plt.show()
        
        # 绘制训练与测试期间
        plt.figure(figsize=(12, 6))
        
        # 为每个窗口创建训练和测试区间
        for i, row in self.results.iterrows():
            # 训练区间
            train_start = row['train_start']
            train_end = row['train_end']
            plt.axvspan(train_start, train_end, alpha=0.2, color='blue', label='Training' if i == 0 else "")
            
            # 测试区间
            test_start = row['test_start']
            test_end = row['test_end']
            plt.axvspan(test_start, test_end, alpha=0.2, color='green', label='Testing' if i == 0 else "")
            
            # 添加窗口边界
            plt.axvline(x=train_start, color='gray', linestyle='--', alpha=0.5)
            
        # 获取完整回测期间的价格数据
        if 'close' in self.data.columns:
            plt.plot(self.data.index, self.data['close'], color='black', label='Price')
            
        plt.title('Walk Forward Analysis: Training and Testing Periods')
        plt.xlabel('Date')
        plt.ylabel('Price')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def get_robust_params(self) -> Dict[str, Any]:
        """
        获取稳健的参数组合
        
        根据测试窗口的表现，计算最稳健的参数组合。
        
        Returns
        -------
        dict
            稳健的参数组合
        """
        if self.results is None:
            raise ValueError("Please run analysis first")
            
        # 提取每个窗口的最佳参数
        param_sets = self.results['best_params'].tolist()
        
        # 获取所有参数名
        all_params = {}
        for param_set in param_sets:
            for key, value in param_set.items():
                if key not in all_params:
                    all_params[key] = []
                all_params[key].append(value)
        
        # 计算每个参数的众数或平均值
        robust_params = {}
        for param, values in all_params.items():
            # 对于数值型参数，取平均值
            if all(isinstance(x, (int, float)) for x in values):
                if all(isinstance(x, int) for x in values):
                    # 整数参数取四舍五入的平均值
                    robust_params[param] = int(round(np.mean(values)))
                else:
                    # 浮点数参数取平均值
                    robust_params[param] = np.mean(values)
            else:
                # 对于非数值型参数，取众数
                from scipy import stats
                try:
                    mode_result = stats.mode(values)
                    # SciPy 1.10.0+ 返回ModeResult对象
                    if hasattr(mode_result, 'mode') and hasattr(mode_result.mode, '__len__'):
                        robust_params[param] = mode_result.mode[0]
                    # 较新版本可能返回值而不是数组
                    elif hasattr(mode_result, 'mode'):
                        robust_params[param] = mode_result.mode
                    # 回退方案
                    else:
                        # 手动计算众数
                        from collections import Counter
                        counts = Counter(values)
                        robust_params[param] = counts.most_common(1)[0][0]
                except Exception as e:
                    # 如果众数计算失败，使用第一个值
                    robust_params[param] = values[0]
                
        return robust_params
    
    def analyze_parameter_stability(self):
        """
        分析参数稳定性
        
        计算每个参数在不同窗口间的变化程度。
        
        Returns
        -------
        pd.DataFrame
            参数稳定性分析结果
        """
        if self.results is None:
            raise ValueError("Please run analysis first")
            
        # 提取每个窗口的最佳参数
        param_sets = self.results['best_params'].tolist()
        
        # 获取所有参数名
        all_params = {}
        for param_set in param_sets:
            for key, value in param_set.items():
                if key not in all_params:
                    all_params[key] = []
                all_params[key].append(value)
        
        # 计算每个参数的统计指标
        stability_stats = {}
        for param, values in all_params.items():
            if all(isinstance(x, (int, float)) for x in values):
                # 数值型参数
                values_array = np.array(values)
                stability_stats[param] = {
                    'mean': np.mean(values_array),
                    'std': np.std(values_array),
                    'cv': np.std(values_array) / np.mean(values_array) if np.mean(values_array) != 0 else np.nan,
                    'min': np.min(values_array),
                    'max': np.max(values_array),
                    'range': np.max(values_array) - np.min(values_array),
                    'range_pct': (np.max(values_array) - np.min(values_array)) / np.mean(values_array) if np.mean(values_array) != 0 else np.nan
                }
            else:
                # 非数值型参数
                from collections import Counter
                counts = Counter(values)
                most_common = counts.most_common()
                most_common_count = most_common[0][1] if most_common else 0
                stability_stats[param] = {
                    'unique_values': len(counts),
                    'most_common': most_common[0][0] if most_common else None,
                    'most_common_count': most_common_count,
                    'most_common_pct': most_common_count / len(values) if values else 0,
                }
        
        # 转换为DataFrame
        stability_df = pd.DataFrame(stability_stats).T
        stability_df.index.name = 'parameter'
        
        # 按照变异系数(CV)或唯一值比例排序
        if 'cv' in stability_df.columns:
            stability_df = stability_df.sort_values(by='cv', ascending=True)
        elif 'unique_values' in stability_df.columns:
            stability_df = stability_df.sort_values(by='most_common_pct', ascending=False)
        
        return stability_df
    
    def monte_carlo_forecast(self, robust_params: Optional[Dict[str, Any]] = None, 
                             n_simulations: int = 30, forecast_periods: int = 100):
        """
        基于Walk Forward结果进行蒙特卡洛预测
        
        使用历史窗口的性能分布，模拟未来可能的表现范围。
        
        Parameters
        ----------
        robust_params : dict, optional
            稳健参数，默认为None表示自动计算
        n_simulations : int, optional
            模拟次数，默认为30
        forecast_periods : int, optional
            预测期数，默认为100
            
        Returns
        -------
        pd.DataFrame
            模拟结果
        """
        if self.results is None:
            raise ValueError("Please run analysis first")
            
        # 如果没有提供稳健参数，则计算
        if robust_params is None:
            robust_params = self.get_robust_params()
            
        # 获取历史窗口的每日收益率
        window_daily_returns = []
        
        for i, row in self.results.iterrows():
            # 在测试数据上评估
            test_start = row['test_start']
            test_end = row['test_end']
            test_data = self.data[(self.data.index >= test_start) & (self.data.index <= test_end)].copy()
            
            if len(test_data) > 0:
                # 运行回测
                test_engine = engine.VectorBTEngine(test_data, **self.engine_kwargs)
                test_strategy = self.strategy_class(**row['best_params'])
                test_results = test_engine.run(test_strategy)
                
                # 获取每日回报
                if hasattr(test_results, 'returns') and test_results.returns is not None:
                    daily_returns = test_results.returns
                    window_daily_returns.append(daily_returns)
        
        # 如果没有收集到每日回报，则使用简化的方法
        if not window_daily_returns:
            logger.warning("No daily returns available, using simplified Monte Carlo simulation")
            
            # 使用测试窗口的总回报率估计每日回报
            daily_return_estimates = []
            for i, row in self.results.iterrows():
                # 计算测试窗口天数
                test_days = (row['test_end'] - row['test_start']).days
                if test_days > 0:
                    # 估计每日回报率
                    daily_ret = (1 + row['total_return']) ** (1 / test_days) - 1
                    daily_return_estimates.append(daily_ret)
            
            if not daily_return_estimates:
                raise ValueError("Cannot estimate daily returns for simulation")
                
            # 计算每日回报的均值和标准差
            mu = np.mean(daily_return_estimates)
            sigma = np.std(daily_return_estimates)
            
            # 进行蒙特卡洛模拟
            simulations = []
            for i in range(n_simulations):
                # 生成正态分布的随机收益率
                random_returns = np.random.normal(mu, sigma, forecast_periods)
                # 计算累积收益
                cumulative_returns = np.cumprod(1 + random_returns) - 1
                simulations.append(cumulative_returns)
                
            # 转换为DataFrame
            sim_df = pd.DataFrame(simulations).T
            
            # 绘制模拟结果
            self._plot_monte_carlo_results(sim_df)
            
            return sim_df
            
        else:
            # 合并所有窗口的每日回报
            all_returns = pd.concat(window_daily_returns)
            
            # 计算每日回报的均值和标准差
            mu = all_returns.mean()
            sigma = all_returns.std()
            
            # 进行蒙特卡洛模拟
            simulations = []
            for i in range(n_simulations):
                # 生成正态分布的随机收益率
                random_returns = np.random.normal(mu, sigma, forecast_periods)
                # 计算累积收益
                cumulative_returns = np.cumprod(1 + random_returns) - 1
                simulations.append(cumulative_returns)
                
            # 转换为DataFrame
            sim_df = pd.DataFrame(simulations).T
            
            # 绘制模拟结果
            self._plot_monte_carlo_results(sim_df)
            
            return sim_df
    
    def _plot_monte_carlo_results(self, sim_df: pd.DataFrame):
        """
        绘制蒙特卡洛模拟结果
        
        Parameters
        ----------
        sim_df : pd.DataFrame
            模拟结果
        """
        plt.figure(figsize=(12, 6))
        
        # 绘制所有模拟路径
        for i in range(sim_df.shape[1]):
            plt.plot(sim_df.index, sim_df[i], 'b-', alpha=0.1)
            
        # 计算分位数
        median = sim_df.median(axis=1)
        q25 = sim_df.quantile(0.25, axis=1)
        q75 = sim_df.quantile(0.75, axis=1)
        q05 = sim_df.quantile(0.05, axis=1)
        q95 = sim_df.quantile(0.95, axis=1)
        
        # 绘制分位数
        plt.plot(sim_df.index, median, 'r-', linewidth=2, label='Median')
        plt.plot(sim_df.index, q25, 'g--', linewidth=1.5, label='25% Quantile')
        plt.plot(sim_df.index, q75, 'g--', linewidth=1.5, label='75% Quantile')
        plt.plot(sim_df.index, q05, 'k--', linewidth=1, label='5% Quantile')
        plt.plot(sim_df.index, q95, 'k--', linewidth=1, label='95% Quantile')
        
        # 填充置信区间
        plt.fill_between(sim_df.index, q25, q75, alpha=0.2, color='g', label='50% Confidence Interval')
        plt.fill_between(sim_df.index, q05, q95, alpha=0.1, color='g', label='90% Confidence Interval')
        
        # 添加标题和标签
        plt.title('Monte Carlo Simulation - Future Cumulative Returns Forecast')
        plt.xlabel('Forecast Period')
        plt.ylabel('Cumulative Return')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
        # 绘制直方图
        plt.figure(figsize=(10, 6))
        
        # 提取最终收益分布
        final_returns = sim_df.iloc[-1]
        
        # 检查数据是否有效
        if final_returns.std() == 0 or final_returns.isnull().all():
            print("Cannot plot final returns histogram: all values are identical or invalid")
            return
        
        # 绘制直方图
        n, bins, patches = plt.hist(final_returns, bins=30, alpha=0.7, color='skyblue', density=True)
        
        # 添加密度曲线 - 如果数据允许
        try:
            from scipy import stats
            # 检查数据是否适合KDE
            if len(final_returns.unique()) > 3:  # 至少需要几个不同的值
                kde = stats.gaussian_kde(final_returns)
                x = np.linspace(final_returns.min(), final_returns.max(), 100)
                plt.plot(x, kde(x), 'r-', linewidth=2)
        except Exception as e:
            print(f"Cannot generate KDE curve: {e}")
        
        # 添加分位数线
        plt.axvline(x=final_returns.median(), color='r', linestyle='-', linewidth=2, label=f'Median: {final_returns.median():.2%}')
        plt.axvline(x=final_returns.quantile(0.25), color='g', linestyle='--', linewidth=1.5, label=f'25% Quantile: {final_returns.quantile(0.25):.2%}')
        plt.axvline(x=final_returns.quantile(0.75), color='g', linestyle='--', linewidth=1.5, label=f'75% Quantile: {final_returns.quantile(0.75):.2%}')
        plt.axvline(x=final_returns.quantile(0.05), color='k', linestyle='--', linewidth=1, label=f'5% Quantile: {final_returns.quantile(0.05):.2%}')
        plt.axvline(x=final_returns.quantile(0.95), color='k', linestyle='--', linewidth=1, label=f'95% Quantile: {final_returns.quantile(0.95):.2%}')
        
        # 添加标题和标签
        plt.title('Final Cumulative Return Distribution')
        plt.xlabel('Cumulative Return')
        plt.ylabel('Frequency Density')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show() 