#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
综合分析示例脚本

展示如何使用量化交易系统的回测分析和报告生成功能。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from backtest.base import BacktestResults
from backtest.analysis.metrics import calculate_metrics
from backtest.analysis.visualization import (
    plot_performance,
    plot_returns_distribution,
    plot_drawdown_periods,
    plot_trade_analysis
)
from backtest.analysis.reporting import generate_report
from backtest.analysis.robustness import (
    monte_carlo_simulation,
    plot_monte_carlo_simulation,
    bootstrap_analysis,
    plot_bootstrap_distributions
)
from backtest.analysis.multi_period import (
    analyze_by_period,
    analyze_calendar_effects,
    plot_period_performance
)
from backtest.analysis.comparison import (
    compare_strategies,
    plot_strategy_comparison
)


def create_sample_backtest_results(name, start_date='2018-01-01', end_date='2022-12-31', 
                                 performance_factor=1.0, volatility_factor=1.0, seed=None):
    """创建示例策略回测结果"""
    # 设置随机种子
    if seed is not None:
        np.random.seed(seed)
    
    # 创建日期范围
    dates = pd.date_range(start=start_date, end=end_date, freq='B')
    n = len(dates)
    
    # 创建模拟净值曲线
    # 基础趋势
    trend = np.linspace(0, 0.5 * performance_factor, n)
    
    # 随机波动
    noise = 0.001 * volatility_factor * np.random.randn(n).cumsum()
    
    # 组合成收益率
    returns = pd.Series(0.0005 * performance_factor + 
                       0.002 * volatility_factor * np.random.randn(n) + 
                       np.diff(np.concatenate([[0], trend + noise])), 
                       index=dates)
    
    # 创建净值曲线
    equity = (1 + returns).cumprod()
    
    # 创建回撤序列
    drawdowns = equity / equity.cummax() - 1
    
    # 创建模拟交易记录
    trades = pd.DataFrame({
        'Entry Time': pd.date_range(start=start_date, end=end_date, freq='W')[:-1],
        'Exit Time': pd.date_range(start=start_date, end=end_date, freq='W')[1:],
        'Entry Price': np.random.uniform(90, 110, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'Exit Price': np.random.uniform(90, 110, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'Size': np.random.randint(1, 10, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'PnL': np.random.uniform(-5, 5, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'Return': np.random.uniform(-0.05, 0.05, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1)
    })
    
    # 创建模拟持仓记录
    positions = pd.DataFrame(index=dates)
    positions['position'] = np.random.randint(-2, 3, len(dates))
    
    # 创建回测结果对象
    class SampleBacktestResults(BacktestResults):
        @property
        def name(self):
            return name
        
        @property
        def trades(self):
            return trades
        
        @property
        def positions(self):
            return positions
        
        def get_returns(self):
            return returns
        
        def get_drawdowns(self):
            return drawdowns
        
        def equity(self):
            return equity
        
        @property
        def metrics(self):
            return {}
    
    return SampleBacktestResults()


def main():
    """主函数"""
    # 设置输出目录
    output_dir = os.path.join(os.path.dirname(__file__), 'output')
    os.makedirs(output_dir, exist_ok=True)
    
    print("1. 创建示例回测结果...")
    # 创建主策略回测结果
    strategy = create_sample_backtest_results("示例策略", seed=42)
    
    # 创建基准指数
    benchmark = create_sample_backtest_results("基准指数", 
                                             performance_factor=0.7, 
                                             volatility_factor=1.1, 
                                             seed=43)
    benchmark_returns = benchmark.get_returns()
    
    print("\n2. 计算性能指标...")
    # 计算性能指标
    metrics = calculate_metrics(strategy, risk_free_rate=0.01, benchmark=benchmark_returns)
    
    # 打印主要指标
    print("\n主要性能指标:")
    print(f"总回报率: {metrics['total_return']:.2%}")
    print(f"年化收益率: {metrics['annualized_return']:.2%}")
    print(f"夏普比率: {metrics['daily_sharpe']:.2f}")
    print(f"索提诺比率: {metrics['daily_sortino']:.2f}")
    print(f"最大回撤: {metrics['max_drawdown']:.2%}")
    print(f"卡玛比率: {metrics['calmar_ratio']:.2f}")
    
    if 'win_rate' in metrics:
        print(f"胜率: {metrics['win_rate']:.2%}")
        print(f"盈亏比: {metrics['profit_factor']:.2f}")
    
    print("\n3. 绘制性能图表...")
    # 绘制性能概览图
    fig1 = plot_performance(strategy, benchmark=benchmark_returns)
    fig1.savefig(os.path.join(output_dir, 'performance_overview.png'))
    
    # 绘制收益分布图
    fig2, ax2 = plt.subplots(figsize=(10, 6))
    plot_returns_distribution(strategy, benchmark=benchmark_returns, ax=ax2)
    fig2.savefig(os.path.join(output_dir, 'returns_distribution.png'))
    
    # 绘制回撤期间分析图
    fig3, ax3 = plt.subplots(figsize=(10, 6))
    plot_drawdown_periods(strategy, ax=ax3)
    fig3.savefig(os.path.join(output_dir, 'drawdown_periods.png'))
    
    # 绘制交易分析图
    fig4 = plot_trade_analysis(strategy)
    fig4.savefig(os.path.join(output_dir, 'trade_analysis.png'))
    
    print("\n4. 进行稳健性分析...")
    # 进行蒙特卡洛模拟
    returns = strategy.get_returns()
    mc_simulations = monte_carlo_simulation(returns, n_simulations=500, seed=42)
    
    # 绘制蒙特卡洛模拟结果
    fig5, ax5 = plt.subplots(figsize=(10, 6))
    plot_monte_carlo_simulation(mc_simulations, original_equity=strategy.equity(), ax=ax5)
    fig5.savefig(os.path.join(output_dir, 'monte_carlo_simulation.png'))
    
    # 进行Bootstrap分析
    bootstrap_results = bootstrap_analysis(returns, n_bootstraps=200, 
                                         metrics=['total_return', 'sharpe_ratio', 'max_drawdown'])
    
    # 绘制Bootstrap分布图
    fig6 = plot_bootstrap_distributions(bootstrap_results)
    fig6.savefig(os.path.join(output_dir, 'bootstrap_distributions.png'))
    
    print("\n5. 进行多周期分析...")
    # 按年度分析
    yearly_analysis = analyze_by_period(strategy, period_type='year')
    print("\n年度表现:")
    print(yearly_analysis[['total_return', 'sharpe_ratio', 'max_drawdown']])
    
    # 分析日历效应
    calendar_effects = analyze_calendar_effects(strategy)
    print("\n日历效应:")
    print(calendar_effects['DayOfWeek'][['mean', 'win_rate']])
    
    # 绘制期间表现对比图
    fig7 = plot_period_performance(strategy, period_type='year')
    fig7.savefig(os.path.join(output_dir, 'period_performance.png'))
    
    print("\n6. 进行策略比较...")
    # 创建额外的策略进行比较
    strategy2 = create_sample_backtest_results("策略2", 
                                             performance_factor=1.2, 
                                             volatility_factor=1.3, 
                                             seed=44)
    
    strategy3 = create_sample_backtest_results("策略3", 
                                             performance_factor=0.8, 
                                             volatility_factor=0.7, 
                                             seed=45)
    
    # 比较多个策略
    strategies = {
        "策略1": strategy,
        "策略2": strategy2,
        "策略3": strategy3
    }
    
    # 执行策略比较
    comparison_results = compare_strategies(strategies, benchmark_returns)
    
    # 打印比较结果
    print("\n策略比较结果:")
    print(comparison_results[['total_return', 'annualized_return', 'daily_sharpe', 'max_drawdown']])
    
    # 绘制策略比较图
    figures = plot_strategy_comparison(strategies)
    
    # 保存每个图表
    for name, fig in figures.items():
        fig.savefig(os.path.join(output_dir, f'strategy_comparison_{name}.png'))
    
    print("\n7. 生成报告...")
    # 生成HTML报告
    html_report_path = os.path.join(output_dir, 'analysis_report.html')
    generate_report(strategy, benchmark=benchmark_returns, 
                   output_format='html', output_path=html_report_path, 
                   strategy_name='综合分析示例')
    print(f"HTML报告已保存至: {html_report_path}")
    
    # 尝试生成PDF报告
    try:
        pdf_report_path = os.path.join(output_dir, 'analysis_report.pdf')
        generate_report(strategy, benchmark=benchmark_returns, 
                       output_format='pdf', output_path=pdf_report_path, 
                       strategy_name='综合分析示例')
        print(f"PDF报告已保存至: {pdf_report_path}")
    except Exception as e:
        print(f"生成PDF报告失败: {e}")
    
    # 生成文本报告
    text_report_path = os.path.join(output_dir, 'analysis_report.txt')
    generate_report(strategy, benchmark=benchmark_returns, 
                   output_format='text', output_path=text_report_path, 
                   strategy_name='综合分析示例')
    print(f"文本报告已保存至: {text_report_path}")
    
    print("\n分析完成，结果已保存至:", output_dir)


if __name__ == "__main__":
    main() 