#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化结果比较工具

比较Backtrader和VectorBT引擎的参数优化和Walk Forward Analysis结果。
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import matplotlib.pyplot as plt
import logging
import sys
import time

# 添加项目根目录到路径，确保正确的绝对路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

# 导入相关模块
from backtest.vectorbt.engine import VectorBTEngine
from backtest.vectorbt.optimization import VectorBTOptimizer, VectorBTWalkForward
from backtest.backtrader.core import BacktraderEngine
from backtest.backtrader.optimization import ParameterOptimizer, WalkForwardAnalysis
from backtest.strategies.templates import MovingAverageCrossover
from data.structures import OHLCV
from data.sources.utils import download_crypto_data

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data(n_days=200):
    """创建测试数据或下载真实数据"""
    try:
        # 尝试下载比特币数据
        logger.info("尝试下载比特币历史数据...")
        data = download_crypto_data("BTC/USDT", "1d", 
                                    start_time=(datetime.now() - timedelta(days=n_days)),
                                    end_time=datetime.now())
        if data is not None and len(data) > 0:
            logger.info(f"成功下载数据，共 {len(data)} 条记录")
            return data
    except Exception as e:
        logger.warning(f"无法下载数据: {e}")
    
    # 如果下载失败，则生成模拟数据
    logger.info("生成模拟数据...")
    # 生成日期序列
    end_date = datetime.now()
    start_date = end_date - timedelta(days=n_days)
    dates = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # 生成价格数据
    np.random.seed(42)
    price = 100
    prices = [price]
    
    for i in range(1, len(dates)):
        change_percent = np.random.normal(0.0005, 0.01)
        price = price * (1 + change_percent)
        prices.append(price)
    
    # 基于收盘价生成OHLCV数据
    closes = np.array(prices)
    highs = closes * (1 + np.random.uniform(0, 0.015, len(dates)))
    lows = closes * (1 - np.random.uniform(0, 0.015, len(dates)))
    opens = lows + np.random.uniform(0, 1, len(dates)) * (highs - lows)
    volumes = np.random.uniform(1000, 10000, len(dates)) * closes
    
    # 创建DataFrame
    df = pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    }, index=dates)
    
    return df

def compare_grid_search(data: pd.DataFrame, param_grid: dict, n_jobs: int = 1):
    """
    比较两个引擎的网格搜索性能和结果
    
    Parameters
    ----------
    data : pd.DataFrame
        回测数据
    param_grid : dict
        参数网格
    n_jobs : int, optional
        并行任务数，默认为1
        
    Returns
    -------
    dict
        比较结果
    """
    logger.info("开始比较网格搜索性能和结果...")
    
    # 创建回测引擎
    vbt_engine = VectorBTEngine(data, initial_capital=100000)
    bt_engine = BacktraderEngine(data, initial_cash=100000)
    
    # 创建参数优化器
    vbt_optimizer = VectorBTOptimizer(
        engine_instance=vbt_engine,
        strategy_class=MovingAverageCrossover,
        data=data,
        metric='sharpe_ratio',
        maximize=True,
        initial_capital=100000,
        commission_rate=0.001
    )
    
    bt_optimizer = ParameterOptimizer(
        engine=bt_engine,
        strategy_class=MovingAverageCrossover,
        data=data,
        metric='sharpe_ratio',
        maximize=True,
        initial_cash=100000,
        commission=0.001
    )
    
    # 运行VectorBT网格搜索并计时
    logger.info("运行VectorBT网格搜索...")
    vbt_start_time = time.time()
    vbt_results = vbt_optimizer.grid_search(param_grid, n_jobs=n_jobs)
    vbt_duration = time.time() - vbt_start_time
    
    # 运行Backtrader网格搜索并计时
    logger.info("运行Backtrader网格搜索...")
    bt_start_time = time.time()
    bt_results = bt_optimizer.grid_search(param_grid, n_jobs=n_jobs)
    bt_duration = time.time() - bt_start_time
    
    # 获取最佳参数
    vbt_best_params = vbt_optimizer.get_best_params()
    bt_best_params = bt_optimizer.get_best_params()
    
    # 输出比较结果
    print("\n网格搜索性能比较:")
    print(f"VectorBT 用时: {vbt_duration:.2f} 秒")
    print(f"Backtrader 用时: {bt_duration:.2f} 秒")
    print(f"速度比: {bt_duration / vbt_duration:.2f}x (VectorBT更快)")
    
    print("\n最佳参数比较:")
    print(f"VectorBT 最佳参数: {vbt_best_params}")
    print(f"Backtrader 最佳参数: {bt_best_params}")
    
    # 参数一致性检查
    param_match = True
    for param in vbt_best_params:
        if param in bt_best_params and vbt_best_params[param] != bt_best_params[param]:
            param_match = False
            break
    
    print(f"参数一致性: {'一致' if param_match else '不一致'}")
    
    # 获取VectorBT最佳结果的性能指标
    vbt_best_row = vbt_results.iloc[0]
    vbt_metrics = {
        'sharpe_ratio': vbt_best_row['sharpe_ratio'],
        'total_return': vbt_best_row['total_return'],
        'max_drawdown': vbt_best_row['max_drawdown'],
        'win_rate': vbt_best_row['win_rate'] if 'win_rate' in vbt_best_row else None
    }
    
    # 获取Backtrader最佳结果的性能指标
    bt_best_row = bt_results.iloc[0]
    bt_metrics = {
        'sharpe_ratio': bt_best_row['sharpe_ratio'],
        'total_return': bt_best_row['total_return'],
        'max_drawdown': bt_best_row['max_drawdown'],
        'win_rate': bt_best_row['win_rate'] if 'win_rate' in bt_best_row else None
    }
    
    print("\n性能指标比较:")
    print(f"指标\t\tVectorBT\t\tBacktrader")
    print(f"夏普比率\t{vbt_metrics['sharpe_ratio']:.2f}\t\t{bt_metrics['sharpe_ratio']:.2f}")
    print(f"总回报\t\t{vbt_metrics['total_return']:.2%}\t\t{bt_metrics['total_return']:.2%}")
    print(f"最大回撤\t{vbt_metrics['max_drawdown']:.2%}\t\t{bt_metrics['max_drawdown']:.2%}")
    if vbt_metrics['win_rate'] is not None and bt_metrics['win_rate'] is not None:
        print(f"胜率\t\t{vbt_metrics['win_rate']:.2%}\t\t{bt_metrics['win_rate']:.2%}")
    
    # 比较结果相关性
    comparison_results = {
        'vbt_duration': vbt_duration,
        'bt_duration': bt_duration,
        'vbt_best_params': vbt_best_params,
        'bt_best_params': bt_best_params,
        'param_match': param_match,
        'vbt_metrics': vbt_metrics,
        'bt_metrics': bt_metrics,
        'vbt_results': vbt_results,
        'bt_results': bt_results
    }
    
    # 可视化比较结果
    plot_optimization_comparison(comparison_results)
    
    return comparison_results

def compare_walk_forward(data: pd.DataFrame, param_grid: dict, n_windows: int = 5, n_jobs: int = 1):
    """
    比较两个引擎的Walk Forward Analysis性能和结果
    
    Parameters
    ----------
    data : pd.DataFrame
        回测数据
    param_grid : dict
        参数网格
    n_windows : int, optional
        窗口数量，默认为5
    n_jobs : int, optional
        并行任务数，默认为1
        
    Returns
    -------
    dict
        比较结果
    """
    logger.info("开始比较Walk Forward Analysis性能和结果...")
    
    # 创建回测引擎
    vbt_engine = VectorBTEngine(data, initial_capital=100000)
    bt_engine = BacktraderEngine(data, initial_cash=100000)
    
    # 创建WFA
    vbt_wfa = VectorBTWalkForward(
        engine_instance=vbt_engine,
        strategy_class=MovingAverageCrossover,
        data=data,
        train_size=0.7,
        test_size=0.3,
        n_windows=n_windows,
        metric='sharpe_ratio',
        maximize=True,
        initial_capital=100000,
        commission_rate=0.001
    )
    
    bt_wfa = WalkForwardAnalysis(
        engine=bt_engine,
        strategy_class=MovingAverageCrossover,
        data=data,
        train_size=0.7,
        test_size=0.3,
        n_windows=n_windows,
        metric='sharpe_ratio',
        maximize=True,
        initial_cash=100000,
        commission=0.001
    )
    
    # 运行VectorBT WFA并计时
    logger.info("运行VectorBT Walk Forward Analysis...")
    vbt_start_time = time.time()
    vbt_results = vbt_wfa.run(param_grid, n_jobs=n_jobs)
    vbt_duration = time.time() - vbt_start_time
    
    # 运行Backtrader WFA并计时
    logger.info("运行Backtrader Walk Forward Analysis...")
    bt_start_time = time.time()
    bt_results = bt_wfa.run(param_grid, n_jobs=n_jobs)
    bt_duration = time.time() - bt_start_time
    
    # 获取稳健参数
    vbt_robust_params = vbt_wfa.get_robust_params()
    bt_robust_params = bt_wfa.get_robust_params()
    
    # 输出比较结果
    print("\nWalk Forward Analysis性能比较:")
    print(f"VectorBT 用时: {vbt_duration:.2f} 秒")
    print(f"Backtrader 用时: {bt_duration:.2f} 秒")
    print(f"速度比: {bt_duration / vbt_duration:.2f}x (VectorBT更快)")
    
    print("\n稳健参数比较:")
    print(f"VectorBT 稳健参数: {vbt_robust_params}")
    print(f"Backtrader 稳健参数: {bt_robust_params}")
    
    # 参数一致性检查
    param_match = True
    for param in vbt_robust_params:
        if param in bt_robust_params:
            # 对于数值型参数，检查差异是否在10%以内
            if isinstance(vbt_robust_params[param], (int, float)) and isinstance(bt_robust_params[param], (int, float)):
                if max(vbt_robust_params[param], bt_robust_params[param]) != 0:  # 避免除以零
                    diff_pct = abs(vbt_robust_params[param] - bt_robust_params[param]) / max(vbt_robust_params[param], bt_robust_params[param])
                    if diff_pct > 0.1:  # 差异超过10%
                        param_match = False
                        break
            # 对于非数值型参数，检查是否完全相同
            elif vbt_robust_params[param] != bt_robust_params[param]:
                param_match = False
                break
    
    print(f"参数稳定性一致性: {'一致' if param_match else '不一致'}")
    
    # 计算每个窗口的平均性能
    vbt_avg_metrics = {
        'sharpe_ratio': vbt_results['sharpe_ratio'].mean(),
        'total_return': vbt_results['total_return'].mean(),
        'max_drawdown': vbt_results['max_drawdown'].mean()
    }
    
    bt_avg_metrics = {
        'sharpe_ratio': bt_results['sharpe_ratio'].mean(),
        'total_return': bt_results['total_return'].mean(),
        'max_drawdown': bt_results['max_drawdown'].mean()
    }
    
    print("\n平均性能指标比较:")
    print(f"指标\t\tVectorBT\t\tBacktrader")
    print(f"夏普比率\t{vbt_avg_metrics['sharpe_ratio']:.2f}\t\t{bt_avg_metrics['sharpe_ratio']:.2f}")
    print(f"总回报\t\t{vbt_avg_metrics['total_return']:.2%}\t\t{bt_avg_metrics['total_return']:.2%}")
    print(f"最大回撤\t{vbt_avg_metrics['max_drawdown']:.2%}\t\t{bt_avg_metrics['max_drawdown']:.2%}")
    
    # 比较结果
    comparison_results = {
        'vbt_duration': vbt_duration,
        'bt_duration': bt_duration,
        'vbt_robust_params': vbt_robust_params,
        'bt_robust_params': bt_robust_params,
        'param_match': param_match,
        'vbt_avg_metrics': vbt_avg_metrics,
        'bt_avg_metrics': bt_avg_metrics,
        'vbt_results': vbt_results,
        'bt_results': bt_results
    }
    
    # 可视化比较结果
    plot_wfa_comparison(comparison_results)
    
    return comparison_results

def plot_optimization_comparison(comparison_results: dict):
    """
    可视化优化比较结果
    
    Parameters
    ----------
    comparison_results : dict
        比较结果
    """
    # 性能比较
    plt.figure(figsize=(12, 6))
    
    # 耗时比较
    plt.subplot(1, 2, 1)
    engines = ['VectorBT', 'Backtrader']
    durations = [comparison_results['vbt_duration'], comparison_results['bt_duration']]
    plt.bar(engines, durations, color=['skyblue', 'orange'])
    plt.ylabel('Time (seconds)')
    plt.title('Optimization Duration Comparison')
    
    for i, v in enumerate(durations):
        plt.text(i, v + 0.1, f"{v:.2f}s", ha='center')
    
    # 性能指标比较
    plt.subplot(1, 2, 2)
    metrics = ['sharpe_ratio', 'total_return', 'max_drawdown']
    vbt_values = [comparison_results['vbt_metrics'][m] for m in metrics]
    bt_values = [comparison_results['bt_metrics'][m] for m in metrics]
    
    x = np.arange(len(metrics))
    width = 0.35
    
    plt.bar(x - width/2, vbt_values, width, label='VectorBT', color='skyblue')
    plt.bar(x + width/2, bt_values, width, label='Backtrader', color='orange')
    
    plt.ylabel('Value')
    plt.title('Performance Metrics Comparison')
    plt.xticks(x, metrics)
    plt.legend()
    
    plt.tight_layout()
    plt.show()
    
    # 参数比较
    vbt_params = comparison_results['vbt_best_params']
    bt_params = comparison_results['bt_best_params']
    
    # 找出共同参数
    common_params = set(vbt_params.keys()) & set(bt_params.keys())
    
    if common_params:
        plt.figure(figsize=(12, 6))
        
        for i, param in enumerate(common_params):
            plt.subplot(1, len(common_params), i + 1)
            
            values = [vbt_params[param], bt_params[param]]
            plt.bar(['VectorBT', 'Backtrader'], values, color=['skyblue', 'orange'])
            plt.title(f'{param}')
            
            # 如果值差异很大，使用对数刻度
            if max(values) / (min(values) + 1e-10) > 10:
                plt.yscale('log')
            
            for j, v in enumerate(values):
                plt.text(j, v, f"{v}", ha='center', va='bottom')
        
        plt.tight_layout()
        plt.show()

def plot_wfa_comparison(comparison_results: dict):
    """
    可视化Walk Forward Analysis比较结果
    
    Parameters
    ----------
    comparison_results : dict
        比较结果
    """
    # 性能比较
    plt.figure(figsize=(12, 6))
    
    # 耗时比较
    plt.subplot(1, 2, 1)
    engines = ['VectorBT', 'Backtrader']
    durations = [comparison_results['vbt_duration'], comparison_results['bt_duration']]
    plt.bar(engines, durations, color=['skyblue', 'orange'])
    plt.ylabel('Time (seconds)')
    plt.title('WFA Duration Comparison')
    
    for i, v in enumerate(durations):
        plt.text(i, v + 0.1, f"{v:.2f}s", ha='center')
    
    # 平均性能指标比较
    plt.subplot(1, 2, 2)
    metrics = ['sharpe_ratio', 'total_return', 'max_drawdown']
    vbt_values = [comparison_results['vbt_avg_metrics'][m] for m in metrics]
    bt_values = [comparison_results['bt_avg_metrics'][m] for m in metrics]
    
    x = np.arange(len(metrics))
    width = 0.35
    
    plt.bar(x - width/2, vbt_values, width, label='VectorBT', color='skyblue')
    plt.bar(x + width/2, bt_values, width, label='Backtrader', color='orange')
    
    plt.ylabel('Value')
    plt.title('Average Performance Metrics Comparison')
    plt.xticks(x, metrics)
    plt.legend()
    
    plt.tight_layout()
    plt.show()
    
    # 稳健参数比较
    vbt_params = comparison_results['vbt_robust_params']
    bt_params = comparison_results['bt_robust_params']
    
    # 找出共同参数
    common_params = set(vbt_params.keys()) & set(bt_params.keys())
    
    if common_params:
        plt.figure(figsize=(12, 6))
        
        for i, param in enumerate(common_params):
            plt.subplot(1, len(common_params), i + 1)
            
            values = [vbt_params[param], bt_params[param]]
            plt.bar(['VectorBT', 'Backtrader'], values, color=['skyblue', 'orange'])
            plt.title(f'{param}')
            
            # 如果值差异很大，使用对数刻度
            if max(values) / (min(values) + 1e-10) > 10:
                plt.yscale('log')
            
            for j, v in enumerate(values):
                plt.text(j, v, f"{v}", ha='center', va='bottom')
        
        plt.tight_layout()
        plt.show()
    
    # 窗口性能比较
    vbt_results = comparison_results['vbt_results']
    bt_results = comparison_results['bt_results']
    
    plt.figure(figsize=(15, 12))
    
    # 夏普比率比较
    plt.subplot(3, 1, 1)
    plt.plot(vbt_results['window'], vbt_results['sharpe_ratio'], 'o-', label='VectorBT', color='skyblue')
    plt.plot(bt_results['window'], bt_results['sharpe_ratio'], 's-', label='Backtrader', color='orange')
    plt.title('Sharpe Ratio by Window')
    plt.xlabel('Window')
    plt.ylabel('Sharpe Ratio')
    plt.grid(True)
    plt.legend()
    
    # 收益率比较
    plt.subplot(3, 1, 2)
    plt.plot(vbt_results['window'], vbt_results['total_return'], 'o-', label='VectorBT', color='skyblue')
    plt.plot(bt_results['window'], bt_results['total_return'], 's-', label='Backtrader', color='orange')
    plt.title('Total Return by Window')
    plt.xlabel('Window')
    plt.ylabel('Return')
    plt.grid(True)
    plt.legend()
    
    # 最大回撤比较
    plt.subplot(3, 1, 3)
    plt.plot(vbt_results['window'], vbt_results['max_drawdown'], 'o-', label='VectorBT', color='skyblue')
    plt.plot(bt_results['window'], bt_results['max_drawdown'], 's-', label='Backtrader', color='orange')
    plt.title('Max Drawdown by Window')
    plt.xlabel('Window')
    plt.ylabel('Drawdown')
    plt.grid(True)
    plt.legend()
    
    plt.tight_layout()
    plt.show()

def main():
    """主函数"""
    print("回测引擎优化比较工具")
    print("-------------------")
    print("1. 网格搜索比较")
    print("2. Walk Forward Analysis比较")
    print("3. 两者都比较")
    
    choice = input("请选择要比较的优化方法 (1/2/3): ")
    
    # 获取数据
    data = create_test_data(n_days=365)
    
    # 定义参数网格
    param_grid = {
        'short_window': [5, 10, 15, 20],
        'long_window': [30, 40, 50, 60],
        'position_size': [0.25, 0.5, 0.75]
    }
    
    if choice == '1':
        compare_grid_search(data, param_grid)
    elif choice == '2':
        compare_walk_forward(data, param_grid, n_windows=3)
    elif choice == '3':
        print("\n运行网格搜索比较...")
        grid_comparison = compare_grid_search(data, param_grid)
        
        print("\n运行Walk Forward Analysis比较...")
        wfa_comparison = compare_walk_forward(data, param_grid, n_windows=3)
    else:
        print("无效选择，退出")

if __name__ == "__main__":
    main() 