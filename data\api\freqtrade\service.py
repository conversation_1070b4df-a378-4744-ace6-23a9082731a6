"""
Freqtrade服务管理器

🔥 根本性重构：简化为FreqtradeClient的轻量级封装
移除所有复杂的异步处理和队列机制
"""

import logging
from typing import Dict, Any, Optional, List
from .client import FreqtradeClient, FreqtradeError
from .models import TradeSignal, TradeResult, BotStatus

logger = logging.getLogger(__name__)


class FreqtradeService:
    """
    Freqtrade服务管理器 - 🔥 简化版本
    
    提供FreqtradeClient的轻量级封装
    """
    
    def __init__(
        self,
        server_url: str,
        username: str,
        password: str
    ):
        """
        初始化Freqtrade服务
        
        Parameters
        ----------
        server_url : str
            Freqtrade API服务器URL
        username : str
            API用户名
        password : str
            API密码
        """
        self.client = FreqtradeClient(server_url, username, password)
    
    def ping(self) -> bool:
        """测试连接"""
        return self.client.ping()
    
    def execute_trade(self, signal: TradeSignal) -> TradeResult:
        """
        执行交易信号
        
        Parameters
        ----------
        signal : TradeSignal
            交易信号
            
        Returns
        -------
        TradeResult
            交易执行结果
        """
        try:
            # 根据信号类型选择操作
            if signal.side in ['long', 'short']:
                return self.client.force_enter_trade(signal)
            else:
                # 对于exit信号，需要先获取tradeid
                trades = self.client.get_open_trades()
                matching_trades = [t for t in trades if t.get('pair') == signal.pair]
                
                if not matching_trades:
                    return TradeResult(success=False, error=f"未找到{signal.pair}的开放交易")
                
                tradeid = matching_trades[0].get('tradeid')
                return self.client.force_exit_trade(tradeid, signal.amount)
                
        except Exception as e:
            logger.error(f"执行交易失败: {e}")
            return TradeResult(success=False, error=str(e))
    
    def get_open_trades(self) -> List[Dict[str, Any]]:
        """获取开放交易列表"""
        try:
            return self.client.get_open_trades()
        except Exception as e:
            logger.error(f"获取开放交易失败: {e}")
            return []
    
    def get_bot_status(self) -> BotStatus:
        """获取机器人状态"""
        try:
            return self.client.get_bot_status()
        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            return BotStatus(status='error', running_since=None, trade_count=0, performance=[])
    
    def get_balance(self) -> Dict[str, Any]:
        """获取账户余额"""
        try:
            return self.client.get_balance()
        except Exception as e:
            logger.error(f"获取余额失败: {e}")
            return {}
    
    def start_bot(self) -> bool:
        """启动机器人"""
        return self.client.start_bot()
    
    def stop_bot(self) -> bool:
        """停止机器人"""
        return self.client.stop_bot()
    
    def pause_bot(self) -> bool:
        """暂停机器人"""
        return self.client.pause_bot() 