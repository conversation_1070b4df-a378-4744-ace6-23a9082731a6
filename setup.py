"""
项目安装配置

用于安装和配置量化交易系统
"""

from setuptools import setup, find_packages

setup(
    name="ari-quantification",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "pandas>=1.3.0",
        "numpy>=1.20.0",
        "matplotlib>=3.4.0",
        "seaborn>=0.11.0",
        "plotly>=5.0.0",
        "TA-Lib>=0.4.0",
        "ccxt>=2.0.0",
        "tabulate>=0.8.0",
    ],
    extras_require={
        "dev": [
            "pytest>=6.0.0",
            "black>=21.0.0",
            "flake8>=3.9.0",
            "mypy>=0.800",
        ],
        "docs": [
            "sphinx>=4.0.0",
            "sphinx-rtd-theme>=0.5.0",
        ],
    },
    author="AriQuantification",
    author_email="<EMAIL>",
    description="个人量化交易系统",
    keywords="quantitative,trading,cryptocurrency,backtesting",
    python_requires=">=3.8",
) 