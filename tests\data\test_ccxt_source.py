"""
CCXT数据源测试

测试CCXT数据源的功能，包括连接、获取数据等。
"""

import os
import sys
import unittest
from unittest import mock
import pandas as pd
from datetime import datetime, timedelta

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from data.sources.ccxt_source import CCXTDataSource, CCXTConfig
from data.structures import OHLCVColumns


class TestCCXTDataSource(unittest.TestCase):
    """测试CCXT数据源"""
    
    def setUp(self):
        """测试准备"""
        # 使用mock避免真实API调用
        self.exchange_id = 'binance'
        self.patcher = mock.patch('ccxt.binance')
        self.mock_exchange_class = self.patcher.start()
        
        # 设置mock交易所的属性和方法
        self.mock_exchange = mock.MagicMock()
        self.mock_exchange_class.return_value = self.mock_exchange
        
        # 模拟交易所的属性
        self.mock_exchange.timeframes = {'1m': '1m', '5m': '5m', '1h': '1h', '1d': '1d'}
        self.mock_exchange.symbols = ['BTC/USDT', 'ETH/USDT', 'XRP/USDT']
        self.mock_exchange.has = {'fetchOHLCV': True}
        self.mock_exchange.load_markets.return_value = None
        
        # 创建数据源
        self.config = CCXTConfig(exchange_id=self.exchange_id)
        self.data_source = CCXTDataSource(self.config)
    
    def tearDown(self):
        """测试清理"""
        self.patcher.stop()
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.data_source.exchange_id, self.exchange_id)
        self.assertIsNotNone(self.data_source.exchange)
        self.mock_exchange.load_markets.assert_called_once()
    
    def test_get_symbols(self):
        """测试获取交易对"""
        symbols = self.data_source.get_symbols()
        self.assertEqual(symbols, ['BTC/USDT', 'ETH/USDT', 'XRP/USDT'])
    
    def test_get_timeframes(self):
        """测试获取时间周期"""
        timeframes = self.data_source.get_timeframes()
        self.assertEqual(set(timeframes), {'1m', '5m', '1h', '1d'})
    
    def test_validate_symbol(self):
        """测试验证交易对"""
        self.assertTrue(self.data_source.validate_symbol('BTC/USDT'))
        self.assertFalse(self.data_source.validate_symbol('INVALID/PAIR'))
    
    def test_validate_timeframe(self):
        """测试验证时间周期"""
        self.assertTrue(self.data_source.validate_timeframe('1h'))
        self.assertFalse(self.data_source.validate_timeframe('2s'))
    
    def test_fetch_data(self):
        """测试获取数据"""
        # 模拟OHLCV数据
        mock_candles = [
            [1620000000000, 50000.0, 51000.0, 49000.0, 50500.0, 10.5],  # [timestamp, open, high, low, close, volume]
            [1620003600000, 50500.0, 52000.0, 50000.0, 51500.0, 15.2],
            [1620007200000, 51500.0, 52500.0, 51000.0, 52000.0, 8.3],
        ]
        self.mock_exchange.fetch_ohlcv.return_value = mock_candles
        
        # 设置测试参数
        symbol = 'BTC/USDT'
        timeframe = '1h'
        start_time = datetime(2021, 5, 3, 0, 0, 0)
        end_time = datetime(2021, 5, 3, 3, 0, 0)
        
        # 获取数据
        df = self.data_source.fetch_data(symbol, timeframe, start_time, end_time)
        
        # 验证结果
        self.assertIsInstance(df, pd.DataFrame)
        self.assertEqual(len(df), 3)
        self.assertEqual(list(df.columns), [OHLCVColumns.OPEN, OHLCVColumns.HIGH, 
                                         OHLCVColumns.LOW, OHLCVColumns.CLOSE, 
                                         OHLCVColumns.VOLUME])
        
        # 验证调用参数
        self.mock_exchange.fetch_ohlcv.assert_called_with(
            symbol=symbol,
            timeframe=timeframe,
            since=int(start_time.timestamp() * 1000),
            limit=mock.ANY
        )
    
    def test_get_exchange_info(self):
        """测试获取交易所信息"""
        self.mock_exchange.name = 'Binance'
        self.mock_exchange.rateLimit = 1000
        
        info = self.data_source.get_exchange_info()
        
        self.assertEqual(info['id'], self.exchange_id)
        self.assertEqual(info['name'], 'Binance')
        self.assertEqual(info['symbols_count'], 3)
        self.assertEqual(set(info['timeframes']), {'1m', '5m', '1h', '1d'})
        self.assertEqual(info['has'], {'fetchOHLCV': True})
        self.assertEqual(info['rate_limit'], 1000)


if __name__ == '__main__':
    unittest.main() 