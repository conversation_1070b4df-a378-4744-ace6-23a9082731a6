"""
数据API模块

提供统一的数据访问接口，包括市场数据获取、数据操作和数据管理功能。
"""

# 导入子API模块
from data.api.market_data import MarketDataAPI
from data.api.operations import DataOperationsAPI
from data.api.management import DataManagementAPI

# 从主API文件导入DataAPI类
import importlib.util
import sys
import os
import logging
from datetime import datetime
from typing import Union, Optional, Dict, Any

import pandas as pd
import numpy as np

# 导入数据源工具函数
from data.sources.utils import download_crypto_data

# 设置日志记录器
logger = logging.getLogger(__name__)

# 动态导入data/api.py模块
api_path = os.path.join(os.path.dirname(__file__), "..", "api.py")
spec = importlib.util.spec_from_file_location("data_api", api_path)

# 确保spec不为None
if spec is not None and spec.loader is not None:
    data_api_module = importlib.util.module_from_spec(spec)
    sys.modules["data_api"] = data_api_module
    spec.loader.exec_module(data_api_module)
    
    # 从模块中获取DataAPI类
    DataAPI = data_api_module.DataAPI
else:
    logger.error(f"无法导入DataAPI类，文件路径: {api_path}")
    # 创建一个空的占位符类，避免导入错误
    class DataAPI:
        def __init__(self, *args, **kwargs):
            raise NotImplementedError("DataAPI类无法正确加载，请检查项目结构")

def create_data_api_instance() -> DataAPI:
    """
    创建一个配置好的DataAPI实例
    
    Returns
    -------
    DataAPI
        配置好的数据API实例
    """
    try:
        from data.storage.optimized_storage import OptimizedStorage
        from data.sources.ccxt_source import CCXTDataSource, CCXTConfig
        
        # 创建存储目录
        storage_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../data/storage/data"))
        os.makedirs(storage_dir, exist_ok=True)
        
        # 创建存储实例
        storage = OptimizedStorage(storage_dir)
        
        # 创建API实例
        api = DataAPI(storage)
        
        # 配置CCXT数据源
        config = CCXTConfig(
            exchange_id='binance',
            timeout=30000,
            enable_rate_limit=True
        )
        ccxt_source = CCXTDataSource(config)
        
        # 注册数据源
        api.register_data_source('binance', ccxt_source, is_default=True)
        
        return api
    except Exception as e:
        logger.error(f"创建DataAPI实例失败: {e}")
        raise

def download_data(
    exchange: str,
    symbol: str,
    timeframe: str,
    start_date: Union[str, datetime],
    end_date: Union[str, datetime],
    use_cache: bool = True,
    update_cache: bool = True,
    data_api_instance: Optional[Any] = None
) -> pd.DataFrame:
    """
    从交易所下载历史市场数据 - 简化版
    
    该函数是download_crypto_data的封装，提供更简单的接口
    
    Parameters
    ----------
    exchange : str
        交易所名称，例如'binance'
    symbol : str
        交易对，例如'BTC/USDT'或'BTCUSDT'
    timeframe : str
        时间周期，例如'1h', '4h', '1d'
    start_date : Union[str, datetime]
        开始日期，格式为'YYYY-MM-DD'或datetime对象
    end_date : Union[str, datetime]
        结束日期，格式为'YYYY-MM-DD'或datetime对象
    use_cache : bool, optional
        是否使用缓存数据 (仅兼容参数，实际未使用)
    update_cache : bool, optional
        是否更新缓存数据 (仅兼容参数，实际未使用)
    data_api_instance : Optional[Any], optional
        可选的DataAPI实例 (仅兼容参数，实际未使用)
        
    Returns
    -------
    pd.DataFrame
        OHLCV市场数据
    """
    try:
        # 转换日期格式
        if isinstance(start_date, str):
            start_time = datetime.strptime(start_date, '%Y-%m-%d')
        else:
            start_time = start_date
            
        if isinstance(end_date, str):
            end_time = datetime.strptime(end_date, '%Y-%m-%d')
        else:
            end_time = end_date
        
        # 确保交易对格式正确 - 如果使用的是Binance，需要确保符号格式正确
        if exchange.lower() == 'binance' and '/' not in symbol:
            # 如果是BTCUSDT格式，转换为BTC/USDT格式
            if symbol.endswith('USDT'):
                formatted_symbol = f"{symbol[:-4]}/USDT"
            else:
                # 尝试添加斜杠 - 这是一个简单的启发式方法，可能需要更复杂的逻辑
                formatted_symbol = f"{symbol[:3]}/{symbol[3:]}"
                logger.warning(f"尝试将 {symbol} 转换为 {formatted_symbol}，但这可能不正确")
        else:
            formatted_symbol = symbol
        
        logger.info(f"从{exchange}获取{formatted_symbol} {timeframe}数据，时间范围: {start_time} 到 {end_time}")
        
        # 直接调用download_crypto_data函数
        data = download_crypto_data(
            symbol=formatted_symbol,
            timeframe=timeframe,
            start_time=start_time,
            end_time=end_time,
            exchange=exchange
        )
        
        if data.empty:
            logger.warning(f"未获取到数据，将使用示例数据")
            return _get_sample_data(formatted_symbol, start_time, end_time)
        
        logger.info(f"获取到{len(data)}条{formatted_symbol} {timeframe}数据")
        return data
        
    except Exception as e:
        logger.error(f"下载数据时出错: {e}")
        # 返回示例数据作为后备方案
        return _get_sample_data(symbol, start_time, end_time) if 'start_time' in locals() else pd.DataFrame()

def _get_sample_data(symbol: str, start_time: datetime, end_time: datetime) -> pd.DataFrame:
    """
    生成示例市场数据（仅用于测试或当无法获取真实数据时）
    
    Parameters
    ----------
    symbol : str
        交易对
    start_time : datetime
        开始时间
    end_time : datetime
        结束时间
        
    Returns
    -------
    pd.DataFrame
        示例OHLCV数据
    """
    # 创建日期范围（每4小时一个数据点）
    date_range = pd.date_range(start=start_time, end=end_time, freq='4h')
    
    # 生成随机价格数据
    import numpy as np
    n = len(date_range)
    
    # 基础价格值
    base_price = 10000 if 'BTC' in symbol else 1000
    
    # 创建随机走势
    np.random.seed(42)  # 固定随机种子，确保可重复性
    returns = np.random.normal(0, 0.02, n).cumsum()
    multiplier = np.exp(returns)
    
    # 生成价格数据
    close_prices = base_price * multiplier
    high_prices = close_prices * np.random.uniform(1.01, 1.05, n)
    low_prices = close_prices * np.random.uniform(0.95, 0.99, n)
    open_prices = close_prices * np.random.uniform(0.98, 1.02, n)
    volumes = np.random.uniform(1000, 5000, n)
    
    # 创建DataFrame
    df = pd.DataFrame({
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': close_prices,
        'volume': volumes
    }, index=date_range)
    
    logger.info(f"生成了示例数据: {symbol}, {len(df)}行")
    return df

# 导出所有API类和函数
__all__ = [
    'MarketDataAPI',
    'DataOperationsAPI',
    'DataManagementAPI',
    'DataAPI',
    'download_data',
    'create_data_api_instance',
]

"""
注意：DataAPI类定义在data/api.py文件中，请直接从那里导入：
from data.api import DataAPI
""" 