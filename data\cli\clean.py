"""
数据清洗命令行接口

提供数据清洗和预处理的命令行工具。
"""

import argparse
import logging
import sys
import os
import json
import pandas as pd
from typing import Dict, List, Optional, Any

from data.processing.cleaner import fill_missing_values, handle_outliers, remove_duplicates, align_timestamps
from data.processing.transformer import normalize_data, standardize_data, extract_time_features, calculate_returns
from data.processing.pipeline import ProcessingPipeline, CleaningStep, TransformStep, FeatureEngineeringStep
from data.storage.optimized_storage import OptimizedStorage


logger = logging.getLogger(__name__)


def clean_command(args: Optional[argparse.Namespace] = None):
    """
    执行数据清洗命令
    
    Args:
        args: 命令行参数
    """
    if args is None:
        # 如果没有提供参数，则解析命令行
        parser = argparse.ArgumentParser(description='数据清洗工具')
        _add_clean_args(parser)
        args = parser.parse_args(sys.argv[2:])  # 跳过第一个参数（脚本名）和第二个参数（子命令）
    
    # 检查是否提供了输入文件
    if not args.input:
        logger.error("未指定输入文件")
        return
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input):
        logger.error(f"输入文件不存在: {args.input}")
        return
    
    # 加载数据
    logger.info(f"正在加载数据: {args.input}")
    try:
        # 统一使用pandas的功能加载数据
        df = pd.read_csv(args.input, index_col=0, parse_dates=True, compression='infer')
    except Exception as e:
        logger.error(f"加载数据失败: {str(e)}")
        return
    
    # 记录原始数据信息
    rows_before = len(df)
    columns_before = df.columns.tolist()
    logger.info(f"原始数据: {rows_before} 行, {len(columns_before)} 列")
    
    # 处理数据
    if args.config:
        # 从配置文件加载流水线
        logger.info(f"从配置文件加载处理流水线: {args.config}")
        try:
            pipeline = ProcessingPipeline.load_from_file(args.config)
            df_processed = pipeline.process(df)
            logger.info(f"使用流水线 '{pipeline.name}' 处理数据完成")
        except Exception as e:
            logger.error(f"加载或应用流水线失败: {str(e)}")
            return
    else:
        # 创建新的处理流水线
        pipeline = ProcessingPipeline(name=args.name or "命令行创建的流水线")
        
        # 添加清洗步骤
        if args.fill_missing or args.handle_outliers or args.remove_duplicates or args.align_timestamps:
            # 处理duplicate_keep参数，将字符串'false'转换为布尔值False
            duplicate_keep_value = args.duplicate_keep
            if duplicate_keep_value == 'false':
                duplicate_keep_value = False
                
            cleaning_step = CleaningStep(
                fill_missing=args.fill_missing,
                fill_columns=args.fill_columns.split(",") if args.fill_columns else None,
                outlier_method=args.outlier_method,
                outlier_threshold=args.outlier_threshold,
                outlier_action=args.outlier_action,
                outlier_columns=args.outlier_columns.split(",") if args.outlier_columns else None,
                remove_duplicates=args.remove_duplicates,
                duplicate_keep=duplicate_keep_value or "last",
                align_timestamps=args.align_timestamps,
                align_freq=args.align_freq,
                align_method=args.align_method or "nearest"
            )
            pipeline.add_step(cleaning_step)
            logger.info("添加数据清洗步骤")
        
        # 添加转换步骤
        if args.normalize or args.standardize or args.extract_time or args.calculate_returns:
            transform_step = TransformStep(
                normalize=args.normalize,
                norm_method=args.norm_method or "minmax",
                norm_columns=args.norm_columns.split(",") if args.norm_columns else None,
                standardize=args.standardize,
                std_columns=args.std_columns.split(",") if args.std_columns else None,
                extract_time=args.extract_time,
                calc_returns=args.calculate_returns,
                return_method=args.return_method or "pct_change",
                return_periods=args.return_periods,
                return_column=args.return_column
            )
            pipeline.add_step(transform_step)
            logger.info("添加数据转换步骤")
        
        # 添加特征工程步骤
        if args.calc_volatility or args.calc_ma or args.calc_rsi or args.calc_macd:
            feature_step = FeatureEngineeringStep(
                calc_volatility=args.calc_volatility,
                vol_window=args.vol_window,
                vol_column=args.vol_column,
                vol_annualize=args.vol_annualize,
                vol_scaling=args.vol_scaling,
                calc_ma=args.calc_ma,
                ma_column=args.ma_column,
                ma_windows=[int(w) for w in args.ma_windows.split(",")] if args.ma_windows else [5, 10, 20, 50, 200],
                calc_rsi=args.calc_rsi,
                rsi_column=args.rsi_column,
                rsi_window=args.rsi_window,
                calc_macd=args.calc_macd,
                macd_column=args.macd_column,
                macd_fast=args.macd_fast,
                macd_slow=args.macd_slow,
                macd_signal=args.macd_signal
            )
            pipeline.add_step(feature_step)
            logger.info("添加特征工程步骤")
        
        # 处理数据
        logger.info("开始处理数据...")
        df_processed = pipeline.process(df)
        logger.info("数据处理完成")
        
        # 保存流水线配置
        if args.save_config:
            pipeline.save_to_file(args.save_config)
            logger.info(f"流水线配置已保存至: {args.save_config}")
    
    # 打印处理结果信息
    rows_after = len(df_processed)
    columns_after = df_processed.columns.tolist()
    new_columns = [col for col in columns_after if col not in columns_before]
    
    logger.info(f"处理后数据: {rows_after} 行, {len(columns_after)} 列")
    logger.info(f"行数变化: {rows_after - rows_before}")
    logger.info(f"新增列数: {len(new_columns)}")
    
    if new_columns and args.verbose:
        logger.info(f"新增列: {', '.join(new_columns)}")
    
    # 保存处理后的数据
    if args.output:
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(args.output), exist_ok=True)
            
            # 根据是否压缩选择保存方式
            compression = 'gzip' if args.compress or args.output.endswith('.gz') else None
            out_path = args.output
            if compression == 'gzip' and not out_path.endswith('.gz'):
                out_path = f"{out_path}.gz"
            
            # 保存数据
            df_processed.to_csv(out_path, compression=compression)
            logger.info(f"数据已保存到: {out_path}, {len(df_processed)}行")
            
        except Exception as e:
            logger.error(f"保存数据失败: {args.output}, 错误: {str(e)}")
    else:
        logger.warning("未指定输出文件，处理结果未保存")


def _add_clean_args(parser):
    """
    添加数据清洗命令的参数
    
    Args:
        parser: 参数解析器
    """
    # 输入输出参数
    parser.add_argument('-i', '--input', required=True, help='输入数据文件路径')
    parser.add_argument('-o', '--output', help='输出数据文件路径')
    parser.add_argument('-c', '--config', help='配置文件路径，用于加载预定义的处理流水线')
    parser.add_argument('--save-config', help='保存处理流水线配置的文件路径')
    parser.add_argument('--name', help='处理流水线名称')
    parser.add_argument('--compress', action='store_true', help='是否压缩输出文件')
    
    # 清洗参数
    parser.add_argument('--fill-missing', choices=['ffill', 'bfill', 'interpolate', 'zero', 'mean'], help='缺失值填充方法')
    parser.add_argument('--fill-columns', help='要填充的列，逗号分隔')
    parser.add_argument('--handle-outliers', action='store_true', help='是否处理异常值')
    parser.add_argument('--outlier-method', choices=['zscore', 'iqr'], help='异常值检测方法')
    parser.add_argument('--outlier-threshold', type=float, default=3.0, help='异常值阈值')
    parser.add_argument('--outlier-action', choices=['clip', 'remove', 'replace'], help='异常值处理方法')
    parser.add_argument('--outlier-columns', help='要处理异常值的列，逗号分隔')
    parser.add_argument('--remove-duplicates', action='store_true', help='是否移除重复行')
    parser.add_argument('--duplicate-keep', choices=['first', 'last', 'false'], help='保留哪个重复项')
    parser.add_argument('--align-timestamps', action='store_true', help='是否对齐时间戳')
    parser.add_argument('--align-freq', help='时间对齐频率，如5min, 1h, 1d')
    parser.add_argument('--align-method', choices=['nearest', 'forward', 'backward'], help='时间对齐方法')
    
    # 转换参数
    parser.add_argument('--normalize', action='store_true', help='是否归一化数据')
    parser.add_argument('--norm-method', choices=['minmax', 'decimal'], help='归一化方法')
    parser.add_argument('--norm-columns', help='要归一化的列，逗号分隔')
    parser.add_argument('--standardize', action='store_true', help='是否标准化数据')
    parser.add_argument('--std-columns', help='要标准化的列，逗号分隔')
    parser.add_argument('--extract-time', action='store_true', help='是否提取时间特征')
    parser.add_argument('--calculate-returns', action='store_true', help='是否计算收益率')
    parser.add_argument('--return-method', choices=['pct_change', 'log_return'], help='收益率计算方法')
    parser.add_argument('--return-periods', type=int, default=1, help='收益率计算周期')
    parser.add_argument('--return-column', help='用于计算收益率的列')
    
    # 特征工程参数
    parser.add_argument('--calc-volatility', action='store_true', help='是否计算波动率')
    parser.add_argument('--vol-window', type=int, default=20, help='波动率窗口大小')
    parser.add_argument('--vol-column', help='用于计算波动率的列')
    parser.add_argument('--vol-annualize', action='store_true', help='是否年化波动率')
    parser.add_argument('--vol-scaling', type=float, default=252.0, help='年化因子')
    parser.add_argument('--calc-ma', action='store_true', help='是否计算移动平均')
    parser.add_argument('--ma-column', help='用于计算移动平均的列')
    parser.add_argument('--ma-windows', default='5,10,20,50,200', help='移动平均窗口大小，逗号分隔')
    parser.add_argument('--calc-rsi', action='store_true', help='是否计算RSI')
    parser.add_argument('--rsi-column', help='用于计算RSI的列')
    parser.add_argument('--rsi-window', type=int, default=14, help='RSI窗口大小')
    parser.add_argument('--calc-macd', action='store_true', help='是否计算MACD')
    parser.add_argument('--macd-column', help='用于计算MACD的列')
    parser.add_argument('--macd-fast', type=int, default=12, help='MACD快线周期')
    parser.add_argument('--macd-slow', type=int, default=26, help='MACD慢线周期')
    parser.add_argument('--macd-signal', type=int, default=9, help='MACD信号线周期')
    
    # 通用参数
    parser.add_argument('-v', '--verbose', action='store_true', help='输出详细日志')


if __name__ == '__main__':
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    # 执行命令
    clean_command() 