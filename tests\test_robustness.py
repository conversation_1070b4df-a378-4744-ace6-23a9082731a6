#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
稳健性分析模块的单元测试

测试蒙特卡洛模拟、Bootstrap分析和参数敏感性分析功能。
"""

import unittest
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from backtest.base import BacktestResults
from backtest.analysis.robustness import (
    monte_carlo_simulation,
    analyze_monte_carlo_results,
    bootstrap_analysis,
    sensitivity_analysis,
    maximum_adverse_excursion
)


class TestRobustnessAnalysis(unittest.TestCase):
    """稳健性分析模块的单元测试"""

    def setUp(self):
        """设置测试环境"""
        # 创建示例收益率序列
        np.random.seed(42)
        dates = pd.date_range(start='2020-01-01', end='2022-12-31', freq='B')
        self.returns = pd.Series(0.001 + 0.01 * np.random.randn(len(dates)), index=dates)
        
        # 创建示例净值曲线
        self.equity = (1 + self.returns).cumprod()
        
        # 创建示例回撤序列
        self.drawdowns = self.equity / self.equity.cummax() - 1
        
        # 创建示例交易记录
        self.trades = pd.DataFrame({
            'Entry Time': pd.date_range(start='2020-01-01', end='2022-12-31', freq='W')[:-1],
            'Exit Time': pd.date_range(start='2020-01-01', end='2022-12-31', freq='W')[1:],
            'Entry Price': np.random.uniform(90, 110, len(pd.date_range(start='2020-01-01', end='2022-12-31', freq='W')) - 1),
            'Exit Price': np.random.uniform(90, 110, len(pd.date_range(start='2020-01-01', end='2022-12-31', freq='W')) - 1),
            'PnL': np.random.uniform(-5, 5, len(pd.date_range(start='2020-01-01', end='2022-12-31', freq='W')) - 1),
            'Return': np.random.uniform(-0.05, 0.05, len(pd.date_range(start='2020-01-01', end='2022-12-31', freq='W')) - 1),
            'Max Adverse Excursion': np.random.uniform(-0.1, 0, len(pd.date_range(start='2020-01-01', end='2022-12-31', freq='W')) - 1)
        })
        
        # 创建示例回测结果对象
        class SampleBacktestResults(BacktestResults):
            def __init__(self, returns, equity, drawdowns, trades):
                self._returns = returns
                self._equity = equity
                self._drawdowns = drawdowns
                self._trades = trades
            
            @property
            def trades(self):
                return self._trades
            
            @property
            def positions(self):
                return pd.DataFrame(index=self._returns.index)
            
            def get_returns(self):
                return self._returns
            
            def get_drawdowns(self):
                return self._drawdowns
            
            def equity(self):
                return self._equity
            
            @property
            def metrics(self):
                return {}
        
        self.results = SampleBacktestResults(self.returns, self.equity, self.drawdowns, self.trades)
    
    def test_monte_carlo_simulation(self):
        """测试蒙特卡洛模拟功能"""
        # 执行蒙特卡洛模拟
        n_simulations = 100
        simulations = monte_carlo_simulation(self.returns, n_simulations=n_simulations, seed=42)
        
        # 验证模拟结果的形状
        self.assertEqual(simulations.shape[1], n_simulations)
        
        # 验证模拟结果的长度
        self.assertEqual(len(simulations), len(self.returns))
        
        # 验证所有模拟路径的初始值都是1
        for i in range(n_simulations):
            self.assertAlmostEqual(simulations.iloc[0, i], 1.0)
    
    def test_analyze_monte_carlo_results(self):
        """测试蒙特卡洛结果分析功能"""
        # 创建一个简单的模拟结果
        n_periods = 252
        n_simulations = 100
        
        # 创建一个确定性的模拟结果DataFrame
        np.random.seed(42)
        simulations = pd.DataFrame(
            np.random.normal(0.001, 0.01, (n_periods, n_simulations)).cumsum(axis=0),
            columns=range(n_simulations)
        )
        simulations = (1 + simulations).cumprod()
        
        # 分析模拟结果
        stats = analyze_monte_carlo_results(simulations)
        
        # 验证返回的统计指标
        self.assertIn('mean_return', stats)
        self.assertIn('median_return', stats)
        self.assertIn('std_dev', stats)
        self.assertIn('min_return', stats)
        self.assertIn('max_return', stats)
        self.assertIn('probability_positive', stats)
        self.assertIn('percentile_5', stats)
        self.assertIn('percentile_95', stats)
        
        # 验证统计值的合理性
        self.assertTrue(stats['min_return'] <= stats['percentile_5'])
        self.assertTrue(stats['percentile_5'] <= stats['median_return'])
        self.assertTrue(stats['median_return'] <= stats['percentile_95'])
        self.assertTrue(stats['percentile_95'] <= stats['max_return'])
    
    def test_bootstrap_analysis(self):
        """测试Bootstrap分析功能"""
        # 执行Bootstrap分析
        n_bootstraps = 50
        # 使用已知可以计算的指标
        metrics = ['total_return', 'volatility']
        
        bootstrap_results = bootstrap_analysis(
            self.returns, 
            n_bootstraps=n_bootstraps, 
            metrics=metrics
        )
        
        # 验证返回的指标
        for metric in metrics:
            self.assertIn(metric, bootstrap_results)
            
            # 验证每个指标的统计值
            self.assertIn('mean', bootstrap_results[metric])
            self.assertIn('median', bootstrap_results[metric])
            self.assertIn('std', bootstrap_results[metric])
            self.assertIn('percentile_5', bootstrap_results[metric])
            self.assertIn('percentile_95', bootstrap_results[metric])
            
            # 验证统计值的存在性而不强制要求顺序关系
            # 对于小样本的bootstrap，百分位数和中位数的关系可能不稳定
            self.assertFalse(pd.isna(bootstrap_results[metric]['percentile_5']))
            self.assertFalse(pd.isna(bootstrap_results[metric]['median']))
            self.assertFalse(pd.isna(bootstrap_results[metric]['percentile_95']))
            
            # 验证95%百分位数大于等于5%百分位数
            self.assertTrue(bootstrap_results[metric]['percentile_95'] >= bootstrap_results[metric]['percentile_5'])
    
    def test_sensitivity_analysis(self):
        """测试参数敏感性分析功能"""
        # 创建一个简单的策略函数
        def sample_strategy(param):
            # 参数影响 - 假设是移动平均线周期
            performance_factor = -0.1 * (param / 50) + 0.2  # 较小的值更好
            
            # 创建模拟回测结果
            returns = pd.Series(0.0005 * performance_factor + 0.001 * np.random.randn(len(self.returns)), 
                               index=self.returns.index)
            equity = (1 + returns).cumprod()
            
            # 创建简化的回测结果对象
            class SimpleBacktestResults(BacktestResults):
                def get_returns(self):
                    return returns
                
                def equity(self):
                    return equity
                
                @property
                def metrics(self):
                    return {
                        'total_return': equity.iloc[-1] / equity.iloc[0] - 1,
                        'sharpe_ratio': returns.mean() / returns.std() * np.sqrt(252)
                    }
                
                @property
                def trades(self):
                    return pd.DataFrame()
                
                @property
                def positions(self):
                    return pd.DataFrame()
                
                def get_drawdowns(self):
                    return equity / equity.cummax() - 1
            
            return SimpleBacktestResults()
        
        # 执行参数敏感性分析
        param_values = [10, 20, 30, 40, 50]
        metrics = ['total_return', 'sharpe_ratio']
        
        sensitivity_results = sensitivity_analysis(
            sample_strategy,
            'MA Period',
            param_values,
            metrics=metrics
        )
        
        # 验证返回的DataFrame
        self.assertIsInstance(sensitivity_results, pd.DataFrame)
        
        # 验证DataFrame的列
        for metric in metrics:
            self.assertIn(metric, sensitivity_results.columns)
        
        # 验证DataFrame的索引
        for param in param_values:
            self.assertIn(param, sensitivity_results.index)
        
        # 验证结果的合理性 - 较小的参数值应该有更好的表现
        self.assertTrue(sensitivity_results.loc[10, 'total_return'] > 
                       sensitivity_results.loc[50, 'total_return'])
    
    def test_maximum_adverse_excursion(self):
        """测试最大不利偏移分析功能"""
        # 执行最大不利偏移分析
        mae_data = maximum_adverse_excursion(self.results)
        
        # 验证返回的DataFrame
        self.assertIsInstance(mae_data, pd.DataFrame)
        
        # 验证DataFrame的列
        self.assertIn('MAE', mae_data.columns)
        self.assertIn('Final Return', mae_data.columns)
        
        # 验证DataFrame的行数
        self.assertEqual(len(mae_data), len(self.trades))


if __name__ == '__main__':
    unittest.main() 