"""
数据模块工具函数

提供各种辅助功能，包括数据转换、时间处理、格式验证等。
"""

import os
import re
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional, Union, Any, Literal

import pandas as pd
import numpy as np

from data.structures import TimeFrame, OHLCVColumns


def parse_timeframe(timeframe: str) -> Tuple[int, str]:
    """
    解析时间周期字符串，返回数值和单位
    
    Args:
        timeframe: 时间周期字符串，如"1m", "4h", "1d"
        
    Returns:
        包含数值和单位的元组，如(1, "m"), (4, "h"), (1, "d")
        
    Raises:
        ValueError: 如果时间周期格式无效
    """
    # 正则表达式匹配数字和单位
    match = re.match(r"^(\d+)([mhdwM])$", timeframe)
    if not match:
        raise ValueError(f"无效的时间周期格式: {timeframe}")
    
    value = int(match.group(1))
    unit = match.group(2)
    
    return value, unit


def timeframe_to_seconds(timeframe: str) -> int:
    """
    将时间周期转换为秒数
    
    Args:
        timeframe: 时间周期字符串，如"1m", "4h", "1d"
        
    Returns:
        表示时间周期的秒数
        
    Raises:
        ValueError: 如果时间周期格式无效或不支持
    """
    value, unit = parse_timeframe(timeframe)
    
    # 转换为秒
    if unit == "m":
        return value * 60
    elif unit == "h":
        return value * 60 * 60
    elif unit == "d":
        return value * 60 * 60 * 24
    elif unit == "w":
        return value * 60 * 60 * 24 * 7
    elif unit == "M":
        # 月份转换为秒是近似的，这里以30天计算
        return value * 60 * 60 * 24 * 30
    else:
        raise ValueError(f"不支持的时间单位: {unit}")


def timeframe_to_timedelta(timeframe: str) -> timedelta:
    """
    将时间周期转换为timedelta对象
    
    Args:
        timeframe: 时间周期字符串，如"1m", "4h", "1d"
        
    Returns:
        表示时间周期的timedelta对象
    """
    seconds = timeframe_to_seconds(timeframe)
    return timedelta(seconds=seconds)


def resample_ohlcv(df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
    """
    将OHLCV数据重采样为指定的时间周期
    
    Args:
        df: 包含OHLCV数据的DataFrame
        timeframe: 目标时间周期，如"1h", "4h", "1d"
        
    Returns:
        重采样后的DataFrame
    """
    # 确保DataFrame索引为时间类型
    if not isinstance(df.index, pd.DatetimeIndex):
        raise ValueError("DataFrame必须以时间为索引")
    
    # 解析时间周期
    value, unit = parse_timeframe(timeframe)
    pandas_rule = f"{value}{unit}"
    
    # 替换月份单位'M'为'MS'(月初)，因为pandas使用不同的表示
    if unit == "M":
        pandas_rule = f"{value}MS"
    
    # 重采样聚合规则
    agg_dict = {
        OHLCVColumns.OPEN: 'first',
        OHLCVColumns.HIGH: 'max',
        OHLCVColumns.LOW: 'min',
        OHLCVColumns.CLOSE: 'last',
        OHLCVColumns.VOLUME: 'sum'
    }
    
    # 添加可选的扩展列
    if OHLCVColumns.VWAP in df.columns:
        # VWAP需要根据成交量加权计算
        df['volume_price'] = df[OHLCVColumns.CLOSE] * df[OHLCVColumns.VOLUME]
        agg_dict['volume_price'] = 'sum'
    
    if OHLCVColumns.TRADES in df.columns:
        agg_dict[OHLCVColumns.TRADES] = 'sum'
    
    if OHLCVColumns.BID in df.columns:
        agg_dict[OHLCVColumns.BID] = 'last'
    
    if OHLCVColumns.ASK in df.columns:
        agg_dict[OHLCVColumns.ASK] = 'last'
    
    # 执行重采样
    resampled = df.resample(pandas_rule).agg(agg_dict)  # type: ignore
    
    # 计算VWAP
    if OHLCVColumns.VWAP in df.columns:
        resampled[OHLCVColumns.VWAP] = resampled['volume_price'] / resampled[OHLCVColumns.VOLUME]
        resampled.drop('volume_price', axis=1, inplace=True)
    
    # 删除没有成交的行
    resampled = resampled.dropna(subset=[OHLCVColumns.OPEN])
    
    return resampled


def merge_dataframes(dfs: List[pd.DataFrame], how: Literal['outer', 'inner'] = 'outer') -> pd.DataFrame:
    """
    合并多个OHLCV DataFrame
    
    Args:
        dfs: 要合并的DataFrame列表
        how: 合并方式，'outer' 保留所有时间点，'inner' 只保留共同的时间点
        
    Returns:
        合并后的DataFrame
    """
    if not dfs:
        return pd.DataFrame()
    
    # 过滤掉空的DataFrame
    valid_dfs = [df for df in dfs if not df.empty]
    
    if not valid_dfs:
        return pd.DataFrame()
    
    # 如果只有一个有效的DataFrame，直接返回
    if len(valid_dfs) == 1:
        return valid_dfs[0].copy()
    
    # 合并所有DataFrame
    result = pd.concat(valid_dfs)
    
    # 按索引排序
    result = result.sort_index()
    
    # 删除重复的行，保留最后一个
    result = result[~result.index.duplicated(keep='last')]
    
    # 如果需要inner join，手动处理
    if how == 'inner' and len(valid_dfs) > 1:
        # 获取所有DataFrame索引的交集
        common_index = set(valid_dfs[0].index)
        for df in valid_dfs[1:]:
            common_index = common_index.intersection(set(df.index))
        
        # 只保留共同的索引
        result = result.loc[sorted(common_index)]
    
    return result


def get_timeframe_file_path(base_dir: str, symbol: str, timeframe: str) -> str:
    """
    获取时间周期数据文件路径
    
    Args:
        base_dir: 基础目录
        symbol: 交易对或资产代码
        timeframe: 时间周期
        
    Returns:
        数据文件的完整路径
    """
    # 处理交易对名称，将'/'替换为'_'以避免路径问题
    safe_symbol = symbol.replace('/', '_')
    
    # 创建目录（如果不存在）
    symbol_dir = os.path.join(base_dir, safe_symbol)
    os.makedirs(symbol_dir, exist_ok=True)
    
    # 构建文件路径
    file_path = os.path.join(symbol_dir, f"{timeframe}.csv")
    
    return file_path  