#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Backtrader分析器模块

提供Backtrader回测结果分析和性能评估功能。
"""

import backtrader as bt
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Any, Union, Optional, List, Tuple
import os

class PerformanceAnalyzer:
    """
    回测性能分析器
    
    分析和展示Backtrader回测结果的性能指标。
    """
    
    def __init__(self, cerebro: bt.Cerebro, strat=None):
        """
        初始化性能分析器
        
        Parameters
        ----------
        cerebro : bt.Cerebro
            Backtrader的cerebro实例
        strat : bt.Strategy, optional
            已执行的策略实例，默认为None
        """
        self.cerebro = cerebro
        self.strat = strat
        
    def get_metrics(self) -> Dict[str, Any]:
        """
        获取回测性能指标
        
        Returns
        -------
        dict
            包含性能指标的字典
        """
        if self.strat is None:
            raise ValueError("没有可用的策略实例，请先运行回测")
            
        metrics = {}
        
        # 获取各种分析结果
        sharpe = self.strat.analyzers.sharpe.get_analysis()
        drawdown = self.strat.analyzers.drawdown.get_analysis()
        returns = self.strat.analyzers.returns.get_analysis()
        trades = self.strat.analyzers.trades.get_analysis()
        
        # 提取并汇总关键指标
        metrics['sharpe_ratio'] = sharpe.get('sharperatio', 0.0)
        metrics['max_drawdown_pct'] = drawdown.get('max', {}).get('drawdown', 0.0) * 100
        metrics['max_drawdown_len'] = drawdown.get('max', {}).get('len', 0)
        metrics['total_return_pct'] = returns.get('rtot', 0.0) * 100
        metrics['annual_return_pct'] = returns.get('rnorm', 0.0) * 100
        metrics['volatility'] = returns.get('rstd', 0.0) * 100
        
        # 交易相关指标
        total_trades = trades.get('total', {}).get('total', 0)
        won_trades = trades.get('won', {}).get('total', 0)
        lost_trades = trades.get('lost', {}).get('total', 0)
        
        metrics['total_trades'] = total_trades
        metrics['win_rate_pct'] = (won_trades / total_trades * 100) if total_trades > 0 else 0.0
        metrics['loss_rate_pct'] = (lost_trades / total_trades * 100) if total_trades > 0 else 0.0
        metrics['avg_trade_pnl'] = trades.get('pnl', {}).get('average', 0.0)
        metrics['max_winner'] = trades.get('won', {}).get('pnlmax', 0.0)
        metrics['max_loser'] = trades.get('lost', {}).get('pnlmin', 0.0)
        
        return metrics
        
    def get_trade_stats(self) -> pd.DataFrame:
        """
        获取交易统计数据
        
        Returns
        -------
        pd.DataFrame
            包含交易统计的DataFrame
        """
        if self.strat is None:
            raise ValueError("没有可用的策略实例，请先运行回测")
            
        # 获取交易分析结果
        trade_analysis = self.strat.analyzers.trades.get_analysis()
        
        # 创建统计数据字典
        stats = {
            '总交易次数': trade_analysis.get('total', {}).get('total', 0),
            '盈利交易': trade_analysis.get('won', {}).get('total', 0),
            '亏损交易': trade_analysis.get('lost', {}).get('total', 0),
            '平均持仓时间(天)': trade_analysis.get('len', {}).get('average', 0),
            '平均盈利': trade_analysis.get('won', {}).get('pnl', {}).get('average', 0),
            '平均亏损': trade_analysis.get('lost', {}).get('pnl', {}).get('average', 0),
            '最大盈利': trade_analysis.get('won', {}).get('pnl', {}).get('max', 0),
            '最大亏损': trade_analysis.get('lost', {}).get('pnl', {}).get('min', 0),
            '盈亏比': abs(trade_analysis.get('won', {}).get('pnl', {}).get('average', 0) / 
                         trade_analysis.get('lost', {}).get('pnl', {}).get('average', 1))
        }
        
        # 转换为DataFrame
        return pd.DataFrame(list(stats.items()), columns=['指标', '数值'])
        
    def plot_performance(self, figsize=(15, 10), save_path=None):
        """
        绘制性能报告图表
        
        Parameters
        ----------
        figsize : tuple, optional
            图表尺寸，默认为(15, 10)
        save_path : str, optional
            保存图表的路径，默认为None表示不保存
        """
        if self.strat is None:
            raise ValueError("没有可用的策略实例，请先运行回测")
            
        # 获取性能指标
        metrics = self.get_metrics()
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=figsize)
        fig.suptitle('回测性能报告', fontsize=16)
        
        # 绘制净值曲线
        portfolio_value = self.strat.observers.broker.lines.value
        axes[0, 0].plot(portfolio_value, label='账户净值')
        axes[0, 0].set_title('账户净值曲线')
        axes[0, 0].set_ylabel('净值')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 绘制回撤图
        drawdown = self.strat.analyzers.drawdown.get_analysis()
        max_dd = drawdown.get('max', {}).get('drawdown', 0.0) * 100
        axes[0, 1].fill_between(range(len(portfolio_value)), 
                               0, 
                               self.strat.analyzers.drawdown.lines.drawdown * 100,
                               alpha=0.3, color='red', label=f'回撤 (最大: {max_dd:.2f}%)')
        axes[0, 1].set_title('回撤图')
        axes[0, 1].set_ylabel('回撤百分比 (%)')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # 绘制交易次数统计
        trade_stats = self.get_trade_stats()
        win_rate = metrics['win_rate_pct']
        loss_rate = metrics['loss_rate_pct']
        axes[1, 0].bar(['盈利交易', '亏损交易'], [win_rate, loss_rate], color=['green', 'red'])
        axes[1, 0].set_title('交易胜率统计')
        axes[1, 0].set_ylabel('百分比 (%)')
        for i, v in enumerate([win_rate, loss_rate]):
            axes[1, 0].text(i, v + 1, f'{v:.1f}%', ha='center')
        axes[1, 0].grid(True)
        
        # 绘制关键指标
        key_metrics = {
            '总收益': f"{metrics['total_return_pct']:.2f}%",
            '年化收益': f"{metrics['annual_return_pct']:.2f}%",
            '夏普比率': f"{metrics['sharpe_ratio']:.2f}",
            '最大回撤': f"{metrics['max_drawdown_pct']:.2f}%",
            '交易次数': f"{metrics['total_trades']}",
            '胜率': f"{metrics['win_rate_pct']:.2f}%"
        }
        
        axes[1, 1].axis('off')
        y_pos = 0.9
        for key, value in key_metrics.items():
            axes[1, 1].text(0.1, y_pos, f"{key}:", fontsize=12, fontweight='bold')
            axes[1, 1].text(0.5, y_pos, value, fontsize=12)
            y_pos -= 0.15
            
        plt.tight_layout()
        
        # 保存图表
        if save_path is not None:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            
        plt.show()
        
    def generate_report(self, output_dir=None) -> Dict[str, Any]:
        """
        生成完整的回测报告
        
        Parameters
        ----------
        output_dir : str, optional
            报告输出目录，默认为None表示不输出文件
            
        Returns
        -------
        dict
            包含报告数据的字典
        """
        # 获取性能指标
        metrics = self.get_metrics()
        trade_stats = self.get_trade_stats()
        
        # 创建报告字典
        report = {
            'metrics': metrics,
            'trade_stats': trade_stats,
        }
        
        # 如果指定了输出目录，则保存报告和图表
        if output_dir is not None:
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存图表
            plot_path = os.path.join(output_dir, 'performance_chart.png')
            self.plot_performance(save_path=plot_path)
            
            # 保存指标CSV
            metrics_df = pd.DataFrame({k: [v] for k, v in metrics.items()})
            metrics_path = os.path.join(output_dir, 'metrics.csv')
            metrics_df.to_csv(metrics_path, index=False)
            
            # 保存交易统计CSV
            trade_stats_path = os.path.join(output_dir, 'trade_stats.csv')
            trade_stats.to_csv(trade_stats_path, index=False)
            
            # 添加输出路径到报告
            report['output_files'] = {
                'plot': plot_path,
                'metrics': metrics_path,
                'trade_stats': trade_stats_path
            }
        
        return report


class CustomAnalyzer(bt.Analyzer):
    """
    自定义Backtrader分析器
    
    可以扩展此类来创建自定义的分析器。
    """
    
    params = (
        ('param1', None),
    )
    
    def __init__(self):
        """初始化分析器"""
        self.results = {}
        
    def start(self):
        """策略开始时调用"""
        pass
        
    def next(self):
        """每个bar时调用"""
        pass
        
    def stop(self):
        """策略结束时调用"""
        pass
        
    def get_analysis(self):
        """获取分析结果"""
        return self.results
