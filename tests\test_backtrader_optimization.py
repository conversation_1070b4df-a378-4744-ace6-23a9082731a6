#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Backtrader优化模块测试

测试Backtrader优化功能，包括网格搜索和Walk Forward Analysis。
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import sys
import tempfile
import matplotlib
matplotlib.use('Agg')  # 设置非交互式后端，避免测试时显示图表

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))

# 导入测试目标模块
from backtest.backtrader.core import BacktraderEngine
from backtest.backtrader.optimization import ParameterOptimizer, WalkForwardAnalysis
from backtest.strategies.templates import MovingAverageCrossover
from backtest.base import Strategy, BacktestResults

def create_test_data(n_days=200):
    """创建测试用的价格数据"""
    # 生成日期序列
    end_date = datetime.now()
    start_date = end_date - timedelta(days=n_days)
    dates = pd.date_range(start=start_date, end=end_date, freq='B')
    
    # 生成价格数据
    np.random.seed(42)
    price = 100
    prices = [price]
    
    for i in range(1, len(dates)):
        change_percent = np.random.normal(0.0005, 0.01)
        price = price * (1 + change_percent)
        prices.append(price)
    
    # 基于收盘价生成OHLCV数据
    closes = np.array(prices)
    highs = closes * (1 + np.random.uniform(0, 0.015, len(dates)))
    lows = closes * (1 - np.random.uniform(0, 0.015, len(dates)))
    opens = lows + np.random.uniform(0, 1, len(dates)) * (highs - lows)
    volumes = np.random.uniform(1000, 10000, len(dates)) * closes
    
    # 创建DataFrame
    df = pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    }, index=dates)
    
    return df


class MockStrategy(Strategy):
    """模拟策略，用于测试"""
    
    def __init__(self, window_size=20, threshold=0.01, **params):
        params.update({
            'window_size': window_size,
            'threshold': threshold
        })
        super().__init__(**params)
        self.window_size = window_size
        self.threshold = threshold
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        signals = pd.DataFrame(index=data.index)
        signals['ma'] = data['close'].rolling(window=self.window_size).mean()
        signals['entries'] = (data['close'] > signals['ma'] * (1 + self.threshold))
        signals['exits'] = (data['close'] < signals['ma'] * (1 - self.threshold))
        return signals


class TestParameterOptimizer(unittest.TestCase):
    """测试参数优化器"""
    
    def setUp(self):
        """准备测试环境"""
        self.data = create_test_data()
        self.engine = BacktraderEngine(self.data, initial_cash=100000)
        self.strategy_class = MovingAverageCrossover  # 使用真实策略类
    
    def test_optimizer_initialization(self):
        """测试优化器初始化"""
        optimizer = ParameterOptimizer(
            engine=self.engine,
            strategy_class=self.strategy_class,
            data=self.data,
            metric='sharpe_ratio',
            maximize=True
        )
        
        self.assertIsNotNone(optimizer)
        self.assertEqual(optimizer.metric, 'sharpe_ratio')
        self.assertTrue(optimizer.maximize)
    
    def test_grid_search(self):
        """测试网格搜索功能"""
        optimizer = ParameterOptimizer(
            engine=self.engine,
            strategy_class=self.strategy_class,
            data=self.data,
            metric='sharpe_ratio',
            maximize=True
        )
        
        # 定义参数网格 - 使用较小范围以加快测试
        param_grid = {
            'short_window': [10, 20],
            'long_window': [30, 40]
        }
        
        # 运行网格搜索
        results = optimizer.grid_search(param_grid, n_jobs=1)
        
        # 验证结果
        self.assertIsInstance(results, pd.DataFrame)
        self.assertEqual(len(results), 4)  # 2x2=4种组合
        self.assertIn('short_window', results.columns)
        self.assertIn('long_window', results.columns)
        self.assertIn('sharpe_ratio', results.columns)
    
    def test_get_best_params(self):
        """测试获取最佳参数功能"""
        optimizer = ParameterOptimizer(
            engine=self.engine,
            strategy_class=self.strategy_class,
            data=self.data,
            metric='sharpe_ratio',
            maximize=True
        )
        
        # 定义参数网格
        param_grid = {
            'short_window': [10, 20],
            'long_window': [30, 40]
        }
        
        # 运行网格搜索
        optimizer.grid_search(param_grid, n_jobs=1)
        
        # 获取最佳参数
        best_params = optimizer.get_best_params()
        
        # 验证结果
        self.assertIsInstance(best_params, dict)
        self.assertIn('short_window', best_params)
        self.assertIn('long_window', best_params)
    
    def test_plot_optimization_results(self):
        """测试绘图功能"""
        optimizer = ParameterOptimizer(
            engine=self.engine,
            strategy_class=self.strategy_class,
            data=self.data,
            metric='sharpe_ratio',
            maximize=True
        )
        
        # 定义参数网格
        param_grid = {
            'short_window': [10, 20],
            'long_window': [30, 40]
        }
        
        # 运行网格搜索
        optimizer.grid_search(param_grid, n_jobs=1)
        
        # 测试绘图功能(不显示图像)
        try:
            # 使用非交互式后端
            with tempfile.TemporaryDirectory() as tmpdirname:
                temp_file = os.path.join(tmpdirname, 'plot.png')
                optimizer.plot_optimization_results()
                # 如果没有抛出异常，则测试通过
                self.assertTrue(True)
        except Exception as e:
            self.fail(f"绘图方法抛出异常: {e}")


class TestWalkForwardAnalysis(unittest.TestCase):
    """测试Walk Forward Analysis"""
    
    def setUp(self):
        """准备测试环境"""
        self.data = create_test_data(n_days=400)  # 使用更多数据用于Walk Forward测试
        self.engine = BacktraderEngine(self.data, initial_cash=100000)
        self.strategy_class = MovingAverageCrossover  # 使用真实策略类
    
    def test_wfa_initialization(self):
        """测试WFA初始化"""
        wfa = WalkForwardAnalysis(
            engine=self.engine,
            strategy_class=self.strategy_class,
            data=self.data,
            train_size=0.6,
            test_size=0.4,
            n_windows=3
        )
        
        self.assertIsNotNone(wfa)
        self.assertEqual(wfa.train_size, 0.6)
        self.assertEqual(wfa.test_size, 0.4)
        self.assertEqual(wfa.n_windows, 3)
    
    def test_wfa_run(self):
        """测试WFA运行功能"""
        wfa = WalkForwardAnalysis(
            engine=self.engine,
            strategy_class=self.strategy_class,
            data=self.data,
            train_size=0.6,
            test_size=0.4,
            n_windows=2  # 使用较少窗口加快测试
        )
        
        # 定义参数网格
        param_grid = {
            'short_window': [10, 20],
            'long_window': [30, 40]
        }
        
        # 运行WFA
        results = wfa.run(param_grid, n_jobs=1)
        
        # 验证结果
        self.assertIsInstance(results, pd.DataFrame)
        self.assertEqual(len(results), 2)  # 2个窗口
        self.assertIn('window', results.columns)
        self.assertIn('best_params', results.columns)
        self.assertIn('sharpe_ratio', results.columns)
    
    def test_get_robust_params(self):
        """测试获取稳健参数功能"""
        wfa = WalkForwardAnalysis(
            engine=self.engine,
            strategy_class=self.strategy_class,
            data=self.data,
            train_size=0.6,
            test_size=0.4,
            n_windows=2
        )
        
        # 定义参数网格
        param_grid = {
            'short_window': [10, 20],
            'long_window': [30, 40]
        }
        
        # 运行WFA
        wfa.run(param_grid, n_jobs=1)
        
        # 获取稳健参数
        robust_params = wfa.get_robust_params()
        
        # 验证结果
        self.assertIsInstance(robust_params, dict)
        self.assertIn('short_window', robust_params)
        self.assertIn('long_window', robust_params)
    
    def test_plot_results(self):
        """测试绘图功能"""
        wfa = WalkForwardAnalysis(
            engine=self.engine,
            strategy_class=self.strategy_class,
            data=self.data,
            train_size=0.6,
            test_size=0.4,
            n_windows=2
        )
        
        # 定义参数网格
        param_grid = {
            'short_window': [10, 20],
            'long_window': [30, 40]
        }
        
        # 运行WFA
        wfa.run(param_grid, n_jobs=1)
        
        # 测试绘图功能(不显示图像)
        try:
            # 使用非交互式后端
            with tempfile.TemporaryDirectory() as tmpdirname:
                temp_file = os.path.join(tmpdirname, 'plot.png')
                wfa.plot_results()
                # 如果没有抛出异常，则测试通过
                self.assertTrue(True)
        except Exception as e:
            self.fail(f"绘图方法抛出异常: {e}")


if __name__ == '__main__':
    unittest.main()