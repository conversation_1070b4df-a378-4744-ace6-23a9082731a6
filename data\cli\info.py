"""
数据信息命令

提供查询已下载数据信息的命令行工具。
"""

import os
import sys
import argparse
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

from tabulate import tabulate

from data.storage.optimized_storage import OptimizedStorage


def info_command():
    """
    数据信息命令入口点
    """
    parser = argparse.ArgumentParser(description='查询已下载的数据信息')
    
    # 基本参数
    parser.add_argument('--symbol', '-s', help='交易对，如BTC/USDT，不指定则显示所有')
    parser.add_argument('--timeframe', '-t', help='时间周期，如1m, 1h, 1d，不指定则显示所有')
    
    # 存储选项
    parser.add_argument('--data-dir', '-d', default='data', help='数据目录 (默认: ./data)')
    
    # 其他选项
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细信息')
    parser.add_argument('--format', '-f', choices=['table', 'csv', 'json'], default='table',
                      help='输出格式 (默认: table)')
    
    args = parser.parse_args()
    
    # 配置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(level=log_level, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger = logging.getLogger('data-info')
    
    try:
        # 初始化存储
        storage = OptimizedStorage(args.data_dir)
        
        # 如果指定了交易对和时间周期，显示详细信息
        if args.symbol and args.timeframe:
            info = get_single_data_info(storage, args.symbol, args.timeframe)
            if not info:
                logger.error(f"未找到数据: {args.symbol} {args.timeframe}")
                return 1
            
            # 显示详细信息
            if args.format == 'table':
                print_info_table([info], args.verbose)
            elif args.format == 'csv':
                print_info_csv([info], args.verbose)
            elif args.format == 'json':
                print_info_json([info], args.verbose)
            
        # 如果只指定了交易对，显示该交易对的所有时间周期
        elif args.symbol:
            timeframes = storage.get_timeframes(args.symbol)
            if not timeframes:
                logger.error(f"未找到交易对: {args.symbol}")
                return 1
            
            # 获取所有时间周期的信息
            info_list = []
            for tf in timeframes:
                info = get_single_data_info(storage, args.symbol, tf)
                if info:
                    info_list.append(info)
            
            # 显示信息
            if args.format == 'table':
                print_info_table(info_list, args.verbose)
            elif args.format == 'csv':
                print_info_csv(info_list, args.verbose)
            elif args.format == 'json':
                print_info_json(info_list, args.verbose)
            
        # 如果只指定了时间周期，显示所有交易对的该时间周期
        elif args.timeframe:
            symbols = storage.get_symbols()
            if not symbols:
                logger.error(f"未找到任何数据")
                return 1
            
            # 获取所有交易对的指定时间周期信息
            info_list = []
            for symbol in symbols:
                if storage.has_data(symbol, args.timeframe):
                    info = get_single_data_info(storage, symbol, args.timeframe)
                    if info:
                        info_list.append(info)
            
            if not info_list:
                logger.error(f"未找到时间周期为 {args.timeframe} 的数据")
                return 1
                
            # 显示信息
            if args.format == 'table':
                print_info_table(info_list, args.verbose)
            elif args.format == 'csv':
                print_info_csv(info_list, args.verbose)
            elif args.format == 'json':
                print_info_json(info_list, args.verbose)
            
        # 否则显示所有数据
        else:
            symbols = storage.get_symbols()
            if not symbols:
                logger.error(f"未找到任何数据")
                return 1
            
            # 获取所有数据的信息
            info_list = []
            for symbol in symbols:
                timeframes = storage.get_timeframes(symbol)
                for tf in timeframes:
                    info = get_single_data_info(storage, symbol, tf)
                    if info:
                        info_list.append(info)
            
            # 显示信息
            if args.format == 'table':
                print_info_table(info_list, args.verbose)
            elif args.format == 'csv':
                print_info_csv(info_list, args.verbose)
            elif args.format == 'json':
                print_info_json(info_list, args.verbose)
        
        return 0
    
    except Exception as e:
        logger.error(f"获取信息失败: {str(e)}", exc_info=args.verbose)
        return 1


def get_single_data_info(storage: OptimizedStorage, symbol: str, timeframe: str) -> Optional[Dict[str, Any]]:
    """
    获取单个数据的信息
    
    Args:
        storage: 数据存储实例
        symbol: 交易对
        timeframe: 时间周期
        
    Returns:
        数据信息字典，如果数据不存在则返回None
    """
    if not storage.has_data(symbol, timeframe):
        return None
    
    info = storage.get_data_info(symbol, timeframe)
    
    # 计算时间跨度
    if info['start_time'] and info['end_time']:
        duration = info['end_time'] - info['start_time']
        info['duration_days'] = duration.days + duration.seconds / 86400
    else:
        info['duration_days'] = 0
    
    return info


def print_info_table(info_list: List[Dict[str, Any]], verbose: bool = False):
    """
    以表格形式打印数据信息
    
    Args:
        info_list: 数据信息列表
        verbose: 是否显示详细信息
    """
    if not info_list:
        print("没有数据")
        return
    
    # 基本表头
    headers = ["交易对", "时间周期", "记录数", "开始时间", "结束时间", "天数"]
    
    # 详细表头
    if verbose:
        headers.extend(["文件大小", "压缩", "文件路径"])
    
    # 准备表格数据
    rows = []
    for info in info_list:
        # 基本行
        row = [
            info['symbol'],
            info['timeframe'],
            info['rows'],
            info['start_time'].strftime('%Y-%m-%d %H:%M') if info['start_time'] else 'N/A',
            info['end_time'].strftime('%Y-%m-%d %H:%M') if info['end_time'] else 'N/A',
            f"{info['duration_days']:.1f}" if info.get('duration_days') else 'N/A'
        ]
        
        # 详细行
        if verbose:
            file_size = info.get('file_size', 0)
            if file_size > 1024 * 1024:
                size_str = f"{file_size / (1024 * 1024):.2f} MB"
            elif file_size > 1024:
                size_str = f"{file_size / 1024:.2f} KB"
            else:
                size_str = f"{file_size} B"
                
            row.extend([
                size_str,
                "是" if info.get('compressed') else "否",
                info.get('file_path', 'N/A')
            ])
        
        rows.append(row)
    
    # 打印表格
    print(tabulate(rows, headers=headers, tablefmt="grid"))


def print_info_csv(info_list: List[Dict[str, Any]], verbose: bool = False):
    """
    以CSV格式打印数据信息
    
    Args:
        info_list: 数据信息列表
        verbose: 是否显示详细信息
    """
    if not info_list:
        print("没有数据")
        return
    
    # 基本表头
    headers = ["symbol", "timeframe", "rows", "start_time", "end_time", "duration_days"]
    
    # 详细表头
    if verbose:
        headers.extend(["file_size", "compressed", "file_path"])
    
    # 打印表头
    print(",".join(headers))
    
    # 打印数据
    for info in info_list:
        # 基本数据
        row = [
            info['symbol'],
            info['timeframe'],
            str(info['rows']),
            info['start_time'].strftime('%Y-%m-%d %H:%M') if info['start_time'] else 'N/A',
            info['end_time'].strftime('%Y-%m-%d %H:%M') if info['end_time'] else 'N/A',
            f"{info['duration_days']:.1f}" if info.get('duration_days') else 'N/A'
        ]
        
        # 详细数据
        if verbose:
            row.extend([
                str(info.get('file_size', 0)),
                "True" if info.get('compressed') else "False",
                info.get('file_path', 'N/A')
            ])
        
        print(",".join(row))


def print_info_json(info_list: List[Dict[str, Any]], verbose: bool = False):
    """
    以JSON格式打印数据信息
    
    Args:
        info_list: 数据信息列表
        verbose: 是否显示详细信息
    """
    import json
    
    if not info_list:
        print("[]")
        return
    
    # 准备JSON数据
    json_data = []
    for info in info_list:
        # 转换datetime为字符串
        data = {
            'symbol': info['symbol'],
            'timeframe': info['timeframe'],
            'rows': info['rows'],
            'start_time': info['start_time'].strftime('%Y-%m-%d %H:%M') if info['start_time'] else None,
            'end_time': info['end_time'].strftime('%Y-%m-%d %H:%M') if info['end_time'] else None,
            'duration_days': info.get('duration_days', 0)
        }
        
        # 详细信息
        if verbose:
            data.update({
                'file_size': info.get('file_size', 0),
                'compressed': info.get('compressed', False),
                'file_path': info.get('file_path', None)
            })
        
        json_data.append(data)
    
    # 打印JSON
    print(json.dumps(json_data, indent=2))


if __name__ == '__main__':
    sys.exit(info_command()) 