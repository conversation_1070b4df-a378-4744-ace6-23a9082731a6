"""
数据管理API

提供数据源、存储和元数据管理的高级接口。
"""

import logging
import os
from datetime import datetime
from typing import List, Dict, Optional, Any, Union, Set

import pandas as pd

from data.base import DataSource, DataStorage
from data.storage.optimized_storage import OptimizedStorage


class DataManagementAPI:
    """
    数据管理API
    
    提供数据源、存储和元数据管理的高级接口。
    支持数据源注册、存储配置、数据同步和元数据管理等功能。
    """
    
    def __init__(self, storage: DataStorage):
        """
        初始化数据管理API
        
        Args:
            storage: 数据存储对象
        """
        self.storage = storage
        self.data_sources: Dict[str, DataSource] = {}
        self.default_data_source: Optional[str] = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def register_data_source(self, name: str, data_source: DataSource, 
                           is_default: bool = False) -> None:
        """
        注册数据源
        
        Args:
            name: 数据源名称
            data_source: 数据源对象
            is_default: 是否设为默认数据源
        """
        self.data_sources[name] = data_source
        self.logger.info(f"注册数据源: {name}")
        
        if is_default or self.default_data_source is None:
            self.default_data_source = name
            self.logger.info(f"设置默认数据源: {name}")
    
    def unregister_data_source(self, name: str) -> bool:
        """
        注销数据源
        
        Args:
            name: 数据源名称
            
        Returns:
            是否成功注销
        """
        if name in self.data_sources:
            del self.data_sources[name]
            self.logger.info(f"注销数据源: {name}")
            
            # 如果注销的是默认数据源，重置默认数据源
            if self.default_data_source == name:
                self.default_data_source = next(iter(self.data_sources)) if self.data_sources else None
                if self.default_data_source:
                    self.logger.info(f"重置默认数据源: {self.default_data_source}")
                else:
                    self.logger.info("无默认数据源")
            
            return True
        else:
            self.logger.warning(f"未找到数据源: {name}")
            return False
    
    def set_default_data_source(self, name: str) -> bool:
        """
        设置默认数据源
        
        Args:
            name: 数据源名称
            
        Returns:
            是否成功设置
        """
        if name in self.data_sources:
            self.default_data_source = name
            self.logger.info(f"设置默认数据源: {name}")
            return True
        else:
            self.logger.warning(f"未找到数据源: {name}")
            return False
    
    def get_data_sources(self) -> List[str]:
        """
        获取所有注册的数据源名称
        
        Returns:
            数据源名称列表
        """
        return list(self.data_sources.keys())
    
    def get_default_data_source(self) -> Optional[str]:
        """
        获取默认数据源名称
        
        Returns:
            默认数据源名称
        """
        return self.default_data_source
    
    def get_data_info(self, symbol: Optional[str] = None, 
                     timeframe: Optional[str] = None) -> Dict[str, Any]:
        """
        获取数据信息
        
        Args:
            symbol: 交易对或资产代码 (可选)
            timeframe: 时间周期 (可选)
            
        Returns:
            数据信息字典
        """
        if symbol and timeframe:
            # 获取特定交易对和时间周期的数据信息
            if self.storage.has_data(symbol, timeframe):
                return self.storage.get_data_info(symbol, timeframe)
            else:
                return {}
        elif symbol:
            # 获取特定交易对的所有时间周期数据信息
            result = {}
            timeframes = self.get_available_timeframes(symbol)
            for tf in timeframes:
                result[tf] = self.storage.get_data_info(symbol, tf)
            return result
        else:
            # 获取所有数据信息
            result = {}
            symbols = self.get_available_symbols()
            for sym in symbols:
                result[sym] = {}
                timeframes = self.get_available_timeframes(sym)
                for tf in timeframes:
                    result[sym][tf] = self.storage.get_data_info(sym, tf)
            return result
    
    def get_available_symbols(self) -> List[str]:
        """
        获取存储中可用的交易对列表
        
        Returns:
            交易对代码列表
        """
        # 检查存储对象是否支持get_available_symbols方法
        if isinstance(self.storage, OptimizedStorage):
            return self.storage.get_available_symbols()
        else:
            self.logger.warning("存储对象不支持get_available_symbols方法")
            return []
    
    def get_available_timeframes(self, symbol: Optional[str] = None) -> List[str]:
        """
        获取存储中可用的时间周期列表
        
        Args:
            symbol: 交易对或资产代码 (可选)
            
        Returns:
            时间周期列表
        """
        # 检查存储对象是否支持get_available_timeframes方法
        if isinstance(self.storage, OptimizedStorage):
            return self.storage.get_available_timeframes(symbol)
        else:
            self.logger.warning("存储对象不支持get_available_timeframes方法")
            return []
    
    def delete_data(self, symbol: str, timeframe: str) -> bool:
        """
        删除数据
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            
        Returns:
            是否成功删除
        """
        # 检查存储对象是否支持delete_data方法
        if isinstance(self.storage, OptimizedStorage):
            return self.storage.delete_data(symbol, timeframe)
        else:
            self.logger.warning("存储对象不支持delete_data方法")
            return False
    
    def sync_data(self, symbol: str, timeframe: str, 
                start_time: Optional[datetime] = None,
                end_time: Optional[datetime] = None,
                data_source: Optional[str] = None,
                force_update: bool = False) -> bool:
        """
        同步数据
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            start_time: 开始时间 (可选)
            end_time: 结束时间 (可选)
            data_source: 数据源名称 (可选)
            force_update: 是否强制更新已有数据
            
        Returns:
            是否成功同步
        """
        # 处理数据源参数
        source_name = data_source or self.default_data_source
        if not source_name or source_name not in self.data_sources:
            self.logger.error(f"未指定有效的数据源: {source_name}")
            return False
        
        source = self.data_sources[source_name]
        
        # 确定同步的时间范围
        now = datetime.now()
        sync_end = end_time or now
        
        if not start_time:
            if self.storage.has_data(symbol, timeframe) and not force_update:
                # 如果已有数据且不强制更新，则从最后一条数据开始同步
                data_info = self.storage.get_data_info(symbol, timeframe)
                last_time = data_info.get("end_time")
                if last_time:
                    sync_start = last_time
                else:
                    # 如果无法获取最后时间，默认同步最近30天数据
                    sync_start = datetime(sync_end.year, sync_end.month, sync_end.day) - pd.Timedelta(days=30)
            else:
                # 默认同步最近30天数据
                sync_start = datetime(sync_end.year, sync_end.month, sync_end.day) - pd.Timedelta(days=30)
        else:
            sync_start = start_time
        
        try:
            # 获取数据
            self.logger.info(f"同步数据: {symbol} {timeframe} 从 {sync_start} 到 {sync_end}")
            data = source.fetch_data(symbol, timeframe, sync_start, sync_end)
            
            if data.empty:
                self.logger.warning(f"未获取到数据: {symbol} {timeframe}")
                return False
            
            # 保存数据
            if force_update or not self.storage.has_data(symbol, timeframe):
                # 强制更新或无现有数据，直接保存
                self.storage.save_data(data, symbol, timeframe)
            else:
                # 有现有数据，更新数据
                if isinstance(self.storage, OptimizedStorage):
                    self.storage.update_data(data, symbol, timeframe)
                else:
                    # 如果不支持update_data，则使用save_data
                    self.logger.warning(f"存储对象不支持update_data方法，使用save_data替代")
                    existing_data = self.storage.load_data(symbol, timeframe)
                    if not existing_data.empty:
                        # 合并现有数据和新数据
                        from data.utils import merge_dataframes
                        merged_data = merge_dataframes([existing_data, data])
                        self.storage.save_data(merged_data, symbol, timeframe)
                    else:
                        self.storage.save_data(data, symbol, timeframe)
            
            self.logger.info(f"同步完成: {symbol} {timeframe}, 共{len(data)}条数据")
            return True
        
        except Exception as e:
            self.logger.error(f"同步数据失败: {e}")
            return False
    
    def get_storage_info(self) -> Dict[str, Any]:
        """
        获取存储信息
        
        Returns:
            存储信息字典
        """
        result = {
            "type": type(self.storage).__name__,
            "symbols_count": 0,
            "total_records": 0,
            "storage_size": 0,
        }
        
        # 获取可用的交易对和时间周期
        symbols = self.get_available_symbols()
        result["symbols_count"] = len(symbols)
        
        # 统计总记录数
        total_records = 0
        for symbol in symbols:
            timeframes = self.get_available_timeframes(symbol)
            for timeframe in timeframes:
                info = self.storage.get_data_info(symbol, timeframe)
                records = info.get("rows", 0)
                total_records += records
        
        result["total_records"] = total_records
        
        # 如果是OptimizedStorage，获取更多信息
        if isinstance(self.storage, OptimizedStorage):
            try:
                storage_path = self.storage.get_storage_path()
                result["storage_path"] = storage_path
                
                # 计算存储大小
                storage_size = 0
                if os.path.exists(storage_path) and os.path.isdir(storage_path):
                    for dirpath, dirnames, filenames in os.walk(storage_path):
                        for filename in filenames:
                            filepath = os.path.join(dirpath, filename)
                            storage_size += os.path.getsize(filepath)
                
                result["storage_size"] = storage_size
                result["storage_size_human"] = self._format_size(storage_size)
            except Exception as e:
                self.logger.error(f"获取存储信息失败: {e}")
        
        return result
    
    def _format_size(self, size_bytes: int) -> str:
        """
        格式化文件大小
        
        Args:
            size_bytes: 字节大小
            
        Returns:
            格式化后的大小字符串
        """
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.2f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.2f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.2f} GB" 