#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
傅立叶变换指标模块

实现傅立叶变换指标，通过频谱分析识别时间序列中的周期性模式。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Union, List, Tuple

from ..base import Indicator
from ..utils import validate_data


class FourierTransform(Indicator):
    """
    傅立叶变换指标
    
    将时间序列分解为频率成分，帮助识别主要周期。
    傅立叶变换将时间域的数据转换为频率域，从而可以识别隐藏的周期性模式。
    """
    
    def __init__(
        self, 
        column: str = 'close', 
        detrend: bool = True,
        padding: bool = True,
        n_harmonics: int = 5,
        min_freq: float = 0.01,  # 最小频率，对应最大周期
        **kwargs
    ):
        """
        初始化傅立叶变换指标
        
        Parameters
        ----------
        column : str, optional
            用于计算的列名，默认为'close'
        detrend : bool, optional
            是否在计算前去除趋势，默认为True
        padding : bool, optional
            是否使用零填充以提高频率分辨率，默认为True
        n_harmonics : int, optional
            要提取的主要谐波数量，默认为5
        min_freq : float, optional
            最小频率阈值，用于过滤低频噪声，默认为0.01
        **kwargs : dict
            其他参数
        """
        if n_harmonics <= 0:
            raise ValueError("谐波数量必须大于0")
        
        if not (0 <= min_freq < 0.5):
            raise ValueError("最小频率必须在0到0.5之间")
        
        super().__init__(
            name="FourierTransform", 
            category="cycle", 
            column=column,
            detrend=detrend,
            padding=padding,
            n_harmonics=n_harmonics,
            min_freq=min_freq,
            **kwargs
        )
    
    def _detrend_series(self, series: pd.Series) -> pd.Series:
        """
        去除时间序列的趋势成分
        
        Parameters
        ----------
        series : pd.Series
            输入时间序列
            
        Returns
        -------
        pd.Series
            去趋势后的时间序列
        """
        # 简单的线性去趋势
        x = np.arange(len(series))
        # 处理可能的NaN值
        mask = ~np.isnan(series)
        if np.sum(mask) <= 1:  # 数据点不足
            return series
            
        # 线性回归去趋势
        try:
            slope, intercept = np.polyfit(x[mask], series[mask], 1)
            trend = slope * x + intercept
            return series - trend
        except Exception:
            # 如果去趋势失败，返回原始序列
            return series
    
    def _compute_fft(self, series: pd.Series, padding: bool = True) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        计算傅立叶变换
        
        Parameters
        ----------
        series : pd.Series
            输入时间序列
        padding : bool, optional
            是否使用零填充，默认为True
            
        Returns
        -------
        Tuple[np.ndarray, np.ndarray, np.ndarray]
            频率、频谱幅度和相位
        """
        # 处理缺失值
        clean_series = series.dropna().values
        n = len(clean_series)
        
        # 应用窗函数减少泄漏
        window = np.hanning(n)
        windowed_series = clean_series * window
        
        # 可选零填充，提高频率分辨率
        if padding:
            # 填充到下一个2的幂，提高FFT效率
            next_power_of_2 = 2 ** int(np.ceil(np.log2(n)))
            padded_series = np.zeros(next_power_of_2)
            padded_series[:n] = windowed_series
            n_fft = next_power_of_2
        else:
            padded_series = windowed_series
            n_fft = n
        
        # 计算FFT
        fft_result = np.fft.fft(padded_series)
        
        # 计算幅度谱(取绝对值并归一化)
        magnitude = np.abs(fft_result) / n
        
        # 由于对称性，只取前半部分
        magnitude = magnitude[:n_fft//2]
        # 0频率和奈奎斯特频率的幅度需要特殊处理（不乘以2）
        magnitude[1:-1] = 2 * magnitude[1:-1]  # 乘以2（因为我们只取了一半）
        
        # 计算相位谱
        phase = np.angle(fft_result)[:n_fft//2]
        
        # 计算频率轴
        freq = np.fft.fftfreq(n_fft)[:n_fft//2]
        
        return freq, magnitude, phase
    
    def _find_dominant_frequencies(
        self, 
        freq: np.ndarray, 
        magnitude: np.ndarray, 
        n_harmonics: int, 
        min_freq: float
    ) -> List[Tuple[float, float, float]]:
        """
        找出主要频率成分
        
        Parameters
        ----------
        freq : np.ndarray
            频率数组
        magnitude : np.ndarray
            幅度数组
        n_harmonics : int
            要提取的谐波数量
        min_freq : float
            最小频率阈值
            
        Returns
        -------
        List[Tuple[float, float, float]]
            主要频率、幅度和周期，按幅度排序
        """
        # 过滤最小频率
        valid_idx = (freq >= min_freq)
        valid_freq = freq[valid_idx]
        valid_magnitude = magnitude[valid_idx]
        
        if len(valid_freq) == 0:
            return []
        
        # 找出最强的n个谐波
        indices = np.argsort(valid_magnitude)[::-1]  # 按幅度降序
        n_harmonics = min(n_harmonics, len(indices))  # 确保不超过可用谐波数
        
        result = []
        for i in range(n_harmonics):
            idx = indices[i]
            f = valid_freq[idx]
            mag = valid_magnitude[idx]
            period = 1.0 / f if f > 0 else float('inf')  # 避免除以零
            result.append((f, mag, period))
        
        return result
    
    def _reconstruct_signal(
        self, 
        original_series: pd.Series, 
        harmonics: List[Tuple[float, float, float]]
    ) -> pd.Series:
        """
        使用主要谐波重建信号
        
        Parameters
        ----------
        original_series : pd.Series
            原始时间序列
        harmonics : List[Tuple[float, float, float]]
            主要谐波（频率、幅度、周期）
            
        Returns
        -------
        pd.Series
            重建的信号
        """
        n = len(original_series)
        time = np.arange(n)
        reconstructed = np.zeros(n)
        
        # 使用傅立叶级数重建信号
        for freq, mag, _ in harmonics:
            # 将频率转换为角频率（2π*f）
            omega = 2 * np.pi * freq
            # 添加余弦分量
            reconstructed += mag * np.cos(omega * time)
        
        # 如果去除了趋势，可以加回均值
        if self.params['detrend']:
            reconstructed += original_series.mean()
        
        return pd.Series(reconstructed, index=original_series.index)
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算傅立叶变换指标
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据，需包含用于计算的列
            
        Returns
        -------
        pd.DataFrame
            包含傅立叶变换结果的DataFrame
        """
        column = self.params['column']
        validate_data(data, [column])
        
        df = data.copy()
        detrend = self.params['detrend']
        padding = self.params['padding']
        n_harmonics = self.params['n_harmonics']
        min_freq = self.params['min_freq']
        
        # 获取数据序列
        series = df[column].copy()
        original_series = series.copy()
        
        # 可选地去除趋势
        if detrend:
            series = self._detrend_series(series)
        
        # 计算傅立叶变换
        freq, magnitude, phase = self._compute_fft(series, padding)
        
        # 找出主要频率成分
        harmonics = self._find_dominant_frequencies(freq, magnitude, n_harmonics, min_freq)
        
        # 存储结果
        result = df.copy()
        
        # 重建信号
        if harmonics:
            reconstructed = self._reconstruct_signal(original_series, harmonics)
            result['fourier_reconstructed'] = reconstructed
        
        # 存储额外信息用于可视化
        self._freq = freq
        self._magnitude = magnitude
        self._phase = phase
        self._harmonics = pd.DataFrame(harmonics, columns=['frequency', 'amplitude', 'period'])
        self._result = result
        
        return result
    
    def get_dominant_cycles(self) -> pd.DataFrame:
        """
        获取主要周期成分
        
        Returns
        -------
        pd.DataFrame
            包含频率、幅度和周期的DataFrame
        """
        if not hasattr(self, '_harmonics'):
            return pd.DataFrame(columns=['frequency', 'amplitude', 'period'])
        
        return self._harmonics
    
    def plot(self, ax=None, **kwargs):
        """
        绘制傅立叶变换结果
        
        Parameters
        ----------
        ax : matplotlib.axes.Axes, optional
            用于绘图的Axes对象
        **kwargs : dict
            传递给绘图函数的其他参数
            
        Returns
        -------
        List[matplotlib.axes.Axes]
            绘图结果
        """
        if not hasattr(self, '_freq') or not hasattr(self, '_magnitude'):
            raise ValueError("没有计算结果可供绘制，请先调用calculate方法")
        
        import matplotlib.pyplot as plt
        
        if ax is None:
            fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=kwargs.get('figsize', (12, 12)))
        else:
            # 如果提供了ax，假设它是一个3x1的子图
            if isinstance(ax, (list, tuple)) and len(ax) >= 3:
                ax1, ax2, ax3 = ax[0], ax[1], ax[2]
            else:
                fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=kwargs.get('figsize', (12, 12)))
        
        column = self.params['column']
        
        # 绘制原始数据和重建数据
        ax1.plot(self._result.index, self._result[column], label='原始数据', color='blue')
        
        if 'fourier_reconstructed' in self._result.columns:
            ax1.plot(self._result.index, self._result['fourier_reconstructed'], 
                   label='傅立叶重建', color='red', linestyle='--')
        
        ax1.set_title(f"{column.capitalize()}数据和傅立叶重建")
        ax1.grid(True)
        ax1.legend()
        
        # 绘制频谱
        ax2.plot(self._freq, self._magnitude, color='blue')
        ax2.set_title("频谱")
        ax2.set_xlabel('频率')
        ax2.set_ylabel('幅度')
        
        # 标记主要谐波
        if hasattr(self, '_harmonics') and not self._harmonics.empty:
            for freq, amplitude, _ in self._harmonics.values:
                ax2.plot(freq, amplitude, 'ro', ms=10)
                ax2.annotate(f'{freq:.4f}', (freq, amplitude), 
                          textcoords="offset points", 
                          xytext=(0, 10), 
                          ha='center')
        
        ax2.grid(True)
        
        # 绘制周期图 (1/频率)
        if hasattr(self, '_harmonics') and not self._harmonics.empty:
            # 按周期长度排序
            sorted_cycles = self._harmonics.sort_values('period')
            periods = sorted_cycles['period'].values
            amplitudes = sorted_cycles['amplitude'].values
            
            ax3.bar(range(len(periods)), amplitudes, tick_label=[f'{p:.1f}' for p in periods])
            ax3.set_title("主要周期及其幅度")
            ax3.set_xlabel('周期长度')
            ax3.set_ylabel('幅度')
            ax3.grid(True)
        else:
            ax3.text(0.5, 0.5, '没有找到显著周期', ha='center', va='center')
        
        plt.tight_layout()
        
        return [ax1, ax2, ax3] 