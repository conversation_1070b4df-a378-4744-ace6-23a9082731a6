"""
实时监控实现

提供交易信号监控、资金变化监控和价格波动监控等功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Union, Callable, Set
from datetime import datetime, timedelta
from risk.monitoring.base import MonitorBase, MonitoringEvent, EventSeverity


class TradingSignalMonitor(MonitorBase):
    """
    交易信号监控
    
    监控交易信号的质量和频率，检测异常模式。
    """
    
    def __init__(self, name: str = "trading_signal_monitor", description: str = "监控交易信号的异常", 
                enabled: bool = True, check_interval: int = 1, max_signals_per_interval: int = 10,
                min_signal_interval: int = 5, signal_history_size: int = 100):
        """
        初始化交易信号监控器
        
        Parameters
        ----------
        name : str, optional
            监控器名称，默认为"trading_signal_monitor"
        description : str, optional
            监控器描述，默认为"监控交易信号的异常"
        enabled : bool, optional
            监控器是否启用，默认为True
        check_interval : int, optional
            检查间隔（以秒为单位），默认为1
        max_signals_per_interval : int, optional
            每个间隔内最大信号数量，超过视为异常，默认为10
        min_signal_interval : int, optional
            最小信号间隔（以秒为单位），小于视为异常，默认为5
        signal_history_size : int, optional
            保存的信号历史数量，默认为100
        """
        super().__init__(name, description, enabled, check_interval)
        self.max_signals_per_interval = max_signals_per_interval
        self.min_signal_interval = min_signal_interval
        self.signal_history: List[Dict[str, Any]] = []
        self.signal_history_size = signal_history_size
        self.last_signal_time: Dict[str, datetime] = {}  # 按交易对记录最后信号时间
    
    def add_signal(self, signal: Dict[str, Any]) -> None:
        """
        添加信号记录
        
        Parameters
        ----------
        signal : Dict[str, Any]
            信号数据，需要包含以下键：
            - symbol: 交易对
            - action: 操作（买/卖）
            - price: 价格
            - timestamp: 时间戳
            - quantity: 数量
            - signal_type: 信号类型
        """
        # 为信号添加接收时间戳
        signal['received_at'] = datetime.now()
        
        # 添加到历史记录
        self.signal_history.append(signal)
        
        # 超过大小限制时，移除最旧的记录
        if len(self.signal_history) > self.signal_history_size:
            self.signal_history = self.signal_history[-self.signal_history_size:]
    
    def check(self, context: Dict[str, Any] = None) -> List[MonitoringEvent]:
        """
        检查交易信号异常
        
        Parameters
        ----------
        context : Dict[str, Any], optional
            检查上下文，可包含最新信号数据，默认为None
        
        Returns
        -------
        List[MonitoringEvent]
            检测到的异常事件列表
        """
        events = []
        
        # 如果上下文中包含新信号，添加到历史记录
        if context and 'signals' in context:
            signals = context['signals']
            if isinstance(signals, list):
                for signal in signals:
                    self.add_signal(signal)
            else:
                self.add_signal(signals)
        
        # 检查信号频率异常
        if self.signal_history:
            # 获取过去check_interval秒内的信号
            now = datetime.now()
            interval_start = now - timedelta(seconds=self.check_interval)
            recent_signals = [s for s in self.signal_history 
                              if s['received_at'] >= interval_start]
            
            # 检查信号数量是否过多
            if len(recent_signals) > self.max_signals_per_interval:
                events.append(MonitoringEvent(
                    event_type="excessive_signals",
                    severity=EventSeverity.WARNING,
                    message=f"检测到过多交易信号: {len(recent_signals)}个 (最大允许: {self.max_signals_per_interval})",
                    related_data={"signal_count": len(recent_signals), 
                                 "max_allowed": self.max_signals_per_interval}
                ))
            
            # 按交易对检查信号间隔是否过小
            signals_by_symbol = {}
            for signal in recent_signals:
                symbol = signal.get('symbol', 'unknown')
                if symbol not in signals_by_symbol:
                    signals_by_symbol[symbol] = []
                signals_by_symbol[symbol].append(signal)
            
            for symbol, symbol_signals in signals_by_symbol.items():
                if len(symbol_signals) >= 2:
                    # 检查最小信号间隔
                    sorted_signals = sorted(symbol_signals, key=lambda s: s['received_at'])
                    for i in range(1, len(sorted_signals)):
                        prev_time = sorted_signals[i-1]['received_at']
                        curr_time = sorted_signals[i]['received_at']
                        interval = (curr_time - prev_time).total_seconds()
                        
                        if interval < self.min_signal_interval:
                            events.append(MonitoringEvent(
                                event_type="rapid_signals",
                                severity=EventSeverity.WARNING,
                                message=f"交易对 {symbol} 的信号间隔过短: {interval:.2f}秒 (最小要求: {self.min_signal_interval}秒)",
                                related_data={"symbol": symbol, "interval": interval, 
                                             "min_required": self.min_signal_interval,
                                             "signal1": sorted_signals[i-1], 
                                             "signal2": sorted_signals[i]}
                            ))
            
            # 检查反向交易信号
            for symbol, symbol_signals in signals_by_symbol.items():
                buy_signals = [s for s in symbol_signals if s.get('action') == 'buy']
                sell_signals = [s for s in symbol_signals if s.get('action') == 'sell']
                
                if buy_signals and sell_signals:
                    # 获取最新的买入和卖出信号
                    latest_buy = max(buy_signals, key=lambda s: s['received_at'])
                    latest_sell = max(sell_signals, key=lambda s: s['received_at'])
                    
                    # 如果两个信号时间接近，可能是异常
                    time_diff = abs((latest_buy['received_at'] - latest_sell['received_at']).total_seconds())
                    if time_diff < self.min_signal_interval * 2:
                        events.append(MonitoringEvent(
                            event_type="conflicting_signals",
                            severity=EventSeverity.WARNING,
                            message=f"交易对 {symbol} 检测到冲突的买入/卖出信号，间隔仅 {time_diff:.2f}秒",
                            related_data={"symbol": symbol, "time_diff": time_diff,
                                         "buy_signal": latest_buy, "sell_signal": latest_sell}
                        ))
        
        return events


class CapitalChangeMonitor(MonitorBase):
    """
    资金变化监控
    
    监控账户资金的变化，检测异常波动。
    """
    
    def __init__(self, name: str = "capital_change_monitor", description: str = "监控资金变化", 
                enabled: bool = True, check_interval: int = 5, 
                max_drawdown_pct: float = 5.0, max_hourly_drawdown_pct: float = 10.0,
                max_daily_drawdown_pct: float = 20.0, capital_history_size: int = 1000):
        """
        初始化资金变化监控器
        
        Parameters
        ----------
        name : str, optional
            监控器名称，默认为"capital_change_monitor"
        description : str, optional
            监控器描述，默认为"监控资金变化"
        enabled : bool, optional
            监控器是否启用，默认为True
        check_interval : int, optional
            检查间隔（以秒为单位），默认为5
        max_drawdown_pct : float, optional
            最大允许回撤百分比（检查间隔内），默认为5.0
        max_hourly_drawdown_pct : float, optional
            最大小时回撤百分比，默认为10.0
        max_daily_drawdown_pct : float, optional
            最大日回撤百分比，默认为20.0
        capital_history_size : int, optional
            保存的资金历史记录数量，默认为1000
        """
        super().__init__(name, description, enabled, check_interval)
        self.max_drawdown_pct = max_drawdown_pct
        self.max_hourly_drawdown_pct = max_hourly_drawdown_pct
        self.max_daily_drawdown_pct = max_daily_drawdown_pct
        self.capital_history = []
        self.capital_history_size = capital_history_size
    
    def add_capital_record(self, equity: float, available: float = None, 
                          margin: float = None) -> None:
        """
        添加资金记录
        
        Parameters
        ----------
        equity : float
            净值
        available : float, optional
            可用资金，默认为None
        margin : float, optional
            已用保证金，默认为None
        """
        record = {
            'timestamp': datetime.now(),
            'equity': equity,
            'available': available,
            'margin': margin
        }
        
        self.capital_history.append(record)
        
        # 超过大小限制时，移除最旧的记录
        if len(self.capital_history) > self.capital_history_size:
            self.capital_history = self.capital_history[-self.capital_history_size:]
    
    def calculate_drawdown(self, records: List[Dict[str, Any]]) -> float:
        """
        计算回撤百分比
        
        Parameters
        ----------
        records : List[Dict[str, Any]]
            资金记录列表
        
        Returns
        -------
        float
            回撤百分比
        """
        if not records or len(records) < 2:
            return 0.0
        
        # 按时间排序
        sorted_records = sorted(records, key=lambda r: r['timestamp'])
        
        # 计算每个点的回撤
        max_equity = sorted_records[0]['equity']
        max_drawdown = 0.0
        
        for record in sorted_records:
            equity = record['equity']
            max_equity = max(max_equity, equity)
            drawdown = (max_equity - equity) / max_equity * 100 if max_equity > 0 else 0
            max_drawdown = max(max_drawdown, drawdown)
        
        return max_drawdown
    
    def check(self, context: Dict[str, Any] = None) -> List[MonitoringEvent]:
        """
        检查资金变化异常
        
        Parameters
        ----------
        context : Dict[str, Any], optional
            检查上下文，可包含最新资金数据，默认为None
        
        Returns
        -------
        List[MonitoringEvent]
            检测到的异常事件列表
        """
        events = []
        
        # 如果上下文中包含资金信息，添加到历史记录
        if context and 'equity' in context:
            self.add_capital_record(
                equity=context['equity'],
                available=context.get('available'),
                margin=context.get('margin')
            )
        
        # 检查资金变化异常
        if len(self.capital_history) >= 2:
            now = datetime.now()
            
            # 获取短期、小时和日内资金记录
            short_term_records = [r for r in self.capital_history 
                                 if r['timestamp'] >= now - timedelta(seconds=self.check_interval)]
            hourly_records = [r for r in self.capital_history 
                             if r['timestamp'] >= now - timedelta(hours=1)]
            daily_records = [r for r in self.capital_history 
                            if r['timestamp'] >= now - timedelta(days=1)]
            
            # 计算各时间段的回撤
            short_term_drawdown = self.calculate_drawdown(short_term_records)
            hourly_drawdown = self.calculate_drawdown(hourly_records)
            daily_drawdown = self.calculate_drawdown(daily_records)
            
            # 检查短期回撤
            if short_term_drawdown > self.max_drawdown_pct:
                events.append(MonitoringEvent(
                    event_type="capital_short_term_drawdown",
                    severity=EventSeverity.WARNING,
                    message=f"短期资金回撤过大: {short_term_drawdown:.2f}% (最大允许: {self.max_drawdown_pct}%)",
                    related_data={"drawdown": short_term_drawdown, 
                                 "max_allowed": self.max_drawdown_pct,
                                 "timeframe": f"{self.check_interval}秒"}
                ))
            
            # 检查小时回撤
            if hourly_drawdown > self.max_hourly_drawdown_pct:
                events.append(MonitoringEvent(
                    event_type="capital_hourly_drawdown",
                    severity=EventSeverity.ERROR,
                    message=f"小时资金回撤过大: {hourly_drawdown:.2f}% (最大允许: {self.max_hourly_drawdown_pct}%)",
                    related_data={"drawdown": hourly_drawdown, 
                                 "max_allowed": self.max_hourly_drawdown_pct,
                                 "timeframe": "1小时"}
                ))
            
            # 检查日内回撤
            if daily_drawdown > self.max_daily_drawdown_pct:
                events.append(MonitoringEvent(
                    event_type="capital_daily_drawdown",
                    severity=EventSeverity.CRITICAL,
                    message=f"日内资金回撤过大: {daily_drawdown:.2f}% (最大允许: {self.max_daily_drawdown_pct}%)",
                    related_data={"drawdown": daily_drawdown, 
                                 "max_allowed": self.max_daily_drawdown_pct,
                                 "timeframe": "1天"}
                ))
            
            # 检查资金急剧变化
            if len(short_term_records) >= 2:
                first_record = short_term_records[0]
                last_record = short_term_records[-1]
                
                # 根本性修复：只有当初始资金大于0时才计算变化率
                if first_record['equity'] > 0:
                    pct_change = (last_record['equity'] - first_record['equity']) / first_record['equity'] * 100
                    
                    # 如果变化率过大（正值或负值），生成事件
                    if abs(pct_change) > self.max_drawdown_pct:
                        severity = EventSeverity.INFO if pct_change > 0 else EventSeverity.WARNING
                        direction = "增加" if pct_change > 0 else "减少"
                        
                        events.append(MonitoringEvent(
                            event_type="rapid_capital_change",
                            severity=severity,
                            message=f"资金快速{direction}: {abs(pct_change):.2f}% 在 {self.check_interval}秒内",
                            related_data={"change_pct": pct_change, 
                                         "initial_equity": first_record['equity'],
                                         "current_equity": last_record['equity'],
                                         "timeframe": f"{self.check_interval}秒"}
                        ))
                # 当初始资金为0时，检查是否有资金注入
                elif last_record['equity'] > 0:
                    events.append(MonitoringEvent(
                        event_type="capital_initialization",
                        severity=EventSeverity.INFO,
                        message=f"检测到资金注入: ${last_record['equity']:,.2f}",
                        related_data={"initial_equity": first_record['equity'],
                                     "current_equity": last_record['equity'],
                                     "timeframe": f"{self.check_interval}秒"}
                    ))
        
        return events


class PriceVolatilityMonitor(MonitorBase):
    """
    价格波动监控
    
    监控资产价格的异常波动。
    """
    
    def __init__(self, name: str = "price_volatility_monitor", description: str = "监控价格异常波动", 
                enabled: bool = True, check_interval: int = 5, 
                max_short_term_change_pct: float = 3.0, 
                max_hourly_change_pct: float = 8.0,
                price_history_size: int = 1000):
        """
        初始化价格波动监控器
        
        Parameters
        ----------
        name : str, optional
            监控器名称，默认为"price_volatility_monitor"
        description : str, optional
            监控器描述，默认为"监控价格异常波动"
        enabled : bool, optional
            监控器是否启用，默认为True
        check_interval : int, optional
            检查间隔（以秒为单位），默认为5
        max_short_term_change_pct : float, optional
            最大短期价格变化百分比，默认为3.0
        max_hourly_change_pct : float, optional
            最大小时价格变化百分比，默认为8.0
        price_history_size : int, optional
            每个资产保存的价格历史记录数量，默认为1000
        """
        super().__init__(name, description, enabled, check_interval)
        self.max_short_term_change_pct = max_short_term_change_pct
        self.max_hourly_change_pct = max_hourly_change_pct
        self.price_history = {}  # Dict[str, List[Dict[str, Any]]] 按资产存储价格历史
        self.price_history_size = price_history_size
    
    def add_price_record(self, symbol: str, price: float, volume: float = None) -> None:
        """
        添加价格记录
        
        Parameters
        ----------
        symbol : str
            资产符号
        price : float
            价格
        volume : float, optional
            成交量，默认为None
        """
        record = {
            'timestamp': datetime.now(),
            'price': price,
            'volume': volume
        }
        
        if symbol not in self.price_history:
            self.price_history[symbol] = []
        
        self.price_history[symbol].append(record)
        
        # 超过大小限制时，移除最旧的记录
        if len(self.price_history[symbol]) > self.price_history_size:
            self.price_history[symbol] = self.price_history[symbol][-self.price_history_size:]
    
    def calculate_price_change(self, records: List[Dict[str, Any]]) -> float:
        """
        计算价格变化百分比
        
        Parameters
        ----------
        records : List[Dict[str, Any]]
            价格记录列表
        
        Returns
        -------
        float
            价格变化百分比
        """
        if not records or len(records) < 2:
            return 0.0
        
        # 按时间排序
        sorted_records = sorted(records, key=lambda r: r['timestamp'])
        
        # 计算价格变化百分比
        first_price = sorted_records[0]['price']
        last_price = sorted_records[-1]['price']
        
        if first_price <= 0:
            return 0.0
        
        return (last_price - first_price) / first_price * 100
    
    def check(self, context: Dict[str, Any] = None) -> List[MonitoringEvent]:
        """
        检查价格波动异常
        
        Parameters
        ----------
        context : Dict[str, Any], optional
            检查上下文，可包含最新价格数据，默认为None
        
        Returns
        -------
        List[MonitoringEvent]
            检测到的异常事件列表
        """
        events = []
        
        # 如果上下文中包含价格信息，添加到历史记录
        if context and 'prices' in context:
            prices = context['prices']
            for symbol, data in prices.items():
                if isinstance(data, dict):
                    self.add_price_record(
                        symbol=symbol,
                        price=data.get('price', 0),
                        volume=data.get('volume')
                    )
                else:
                    # 如果只是价格值
                    self.add_price_record(symbol=symbol, price=float(data))
        
        # 检查价格变化异常
        now = datetime.now()
        
        for symbol, history in self.price_history.items():
            if len(history) < 2:
                continue
            
            # 获取短期和小时价格记录
            short_term_records = [r for r in history 
                                 if r['timestamp'] >= now - timedelta(seconds=self.check_interval)]
            hourly_records = [r for r in history 
                             if r['timestamp'] >= now - timedelta(hours=1)]
            
            # 计算各时间段的价格变化
            short_term_change = self.calculate_price_change(short_term_records)
            hourly_change = self.calculate_price_change(hourly_records)
            
            # 检查短期价格变化
            if abs(short_term_change) > self.max_short_term_change_pct:
                direction = "上涨" if short_term_change > 0 else "下跌"
                severity = EventSeverity.INFO if short_term_change > 0 else EventSeverity.WARNING
                
                events.append(MonitoringEvent(
                    event_type="rapid_price_change",
                    severity=severity,
                    message=f"{symbol} 价格快速{direction}: {abs(short_term_change):.2f}% 在 {self.check_interval}秒内",
                    related_data={"symbol": symbol, "change_pct": short_term_change, 
                                 "max_allowed": self.max_short_term_change_pct,
                                 "timeframe": f"{self.check_interval}秒",
                                 "initial_price": short_term_records[0]['price'],
                                 "current_price": short_term_records[-1]['price']}
                ))
            
            # 检查小时价格变化
            if abs(hourly_change) > self.max_hourly_change_pct:
                direction = "上涨" if hourly_change > 0 else "下跌"
                severity = EventSeverity.INFO if hourly_change > 0 else EventSeverity.ERROR
                
                events.append(MonitoringEvent(
                    event_type="significant_price_change",
                    severity=severity,
                    message=f"{symbol} 价格大幅{direction}: {abs(hourly_change):.2f}% 在过去1小时内",
                    related_data={"symbol": symbol, "change_pct": hourly_change, 
                                 "max_allowed": self.max_hourly_change_pct,
                                 "timeframe": "1小时",
                                 "initial_price": hourly_records[0]['price'],
                                 "current_price": hourly_records[-1]['price']}
                ))
        
        return events 