#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化集成模块测试

测试参数优化与批量分析的集成功能。
"""

import unittest
import pandas as pd
import numpy as np
import os
import tempfile
import matplotlib.pyplot as plt
import shutil
from datetime import datetime, timedelta

# 导入需要测试的模块
from backtest.analysis.optimization_integration import OptimizationAnalyzer, run_optimization_and_analyze
from backtest.base import Strategy, BacktestResults


class MockStrategy(Strategy):
    """用于测试的模拟策略类"""

    def __init__(self, param1=10, param2=20):
        self.param1 = param1
        self.param2 = param2
        self.params = {'param1': param1, 'param2': param2}

    def generate_signals(self, data):
        # 简单地返回原始数据
        return data


def create_mock_optimization_results(params_grid, n_metrics=5, seed=42):
    """创建模拟优化结果DataFrame"""
    np.random.seed(seed)
    
    # 生成网格参数的所有组合
    import itertools
    param_combinations = list(itertools.product(*params_grid.values()))
    
    # 创建结果DataFrame
    results_data = []
    param_names = list(params_grid.keys())
    
    for i, param_values in enumerate(param_combinations):
        row = dict(zip(param_names, param_values))
        
        # 添加性能指标（模拟值）
        row['sharpe_ratio'] = np.random.uniform(0.5, 2.0)
        row['total_return'] = np.random.uniform(0.1, 0.5)
        row['max_drawdown'] = np.random.uniform(-0.3, -0.05)
        row['volatility'] = np.random.uniform(0.05, 0.2)
        row['win_rate'] = np.random.uniform(0.4, 0.7)
        
        results_data.append(row)
    
    results_df = pd.DataFrame(results_data)
    
    # 根据夏普比率排序
    results_df = results_df.sort_values('sharpe_ratio', ascending=False).reset_index(drop=True)
    
    return results_df


def create_mock_data(n_days=100, seed=42):
    """创建模拟市场数据"""
    np.random.seed(seed)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=n_days)
    dates = pd.date_range(start=start_date, end=end_date, freq='D')
    
    price = 100
    prices = [price]
    
    for i in range(1, len(dates)):
        change_percent = np.random.normal(0.0005, 0.01)
        price = price * (1 + change_percent)
        prices.append(price)
    
    # 创建OHLCV数据
    closes = np.array(prices)
    highs = closes * (1 + np.random.uniform(0, 0.01, len(dates)))
    lows = closes * (1 - np.random.uniform(0, 0.01, len(dates)))
    opens = lows + np.random.uniform(0, 1, len(dates)) * (highs - lows)
    volumes = np.random.uniform(100000, 1000000, len(dates))
    
    df = pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    }, index=dates)
    
    return df


class TestOptimizationAnalyzer(unittest.TestCase):
    """测试OptimizationAnalyzer类"""

    def setUp(self):
        """测试前准备工作"""
        # 创建模拟参数网格
        self.param_grid = {
            'param1': [5, 10, 15],
            'param2': [20, 30, 40]
        }
        
        # 创建模拟优化结果
        self.opt_results = create_mock_optimization_results(self.param_grid)
        
        # 创建优化分析器
        self.analyzer = OptimizationAnalyzer(self.opt_results, engine_type='backtrader')
        
        # 创建临时目录用于测试报告生成
        self.test_output_dir = tempfile.mkdtemp()
        
        # 创建模拟市场数据（用于报告生成测试）
        self.market_data = create_mock_data()

    def tearDown(self):
        """测试后清理工作"""
        # 移除临时目录
        shutil.rmtree(self.test_output_dir, ignore_errors=True)

    def test_initialization(self):
        """测试初始化"""
        # 验证优化结果正确存储
        pd.testing.assert_frame_equal(self.analyzer.optimization_results, self.opt_results)
        
        # 验证引擎类型正确存储
        self.assertEqual(self.analyzer.engine_type, 'backtrader')
        
        # 验证参数列正确识别
        self.assertEqual(set(self.analyzer.param_columns), {'param1', 'param2'})
        
        # 测试不支持的引擎类型
        with self.assertRaises(ValueError):
            OptimizationAnalyzer(self.opt_results, engine_type='unsupported')

    def test_get_top_params(self):
        """测试获取排名靠前的参数组合"""
        # 默认指标（夏普比率）
        top_results = self.analyzer.get_top_params(top_n=2)
        self.assertEqual(len(top_results), 2)
        self.assertEqual(top_results.iloc[0].name, 0)  # 第一行应该是索引0
        
        # 使用不同指标
        top_by_return = self.analyzer.get_top_params(metric='total_return', top_n=3)
        self.assertEqual(len(top_by_return), 3)
        
        # 测试升序排序
        top_by_drawdown = self.analyzer.get_top_params(
            metric='max_drawdown', 
            top_n=2, 
            ascending=True  # 对于回撤，越小越好
        )
        self.assertEqual(len(top_by_drawdown), 2)
        
        # 测试无效指标
        with self.assertRaises(ValueError):
            self.analyzer.get_top_params(metric='invalid_metric')

    def test_generate_sensitivity_report(self):
        """测试生成参数敏感性分析报告"""
        # HTML格式（返回内容）
        html_report = self.analyzer.generate_sensitivity_report(
            param_name='param1',
            metrics=['sharpe_ratio', 'total_return', 'max_drawdown']
        )
        self.assertIsInstance(html_report, str)
        self.assertIn('<!DOCTYPE html>', html_report)
        
        # PDF格式（保存到文件）
        pdf_path = os.path.join(self.test_output_dir, 'sensitivity.pdf')
        self.analyzer.generate_sensitivity_report(
            param_name='param1',
            output_path=pdf_path,
            output_format='pdf'
        )
        self.assertTrue(os.path.exists(pdf_path))
        
        # 测试无效参数名
        with self.assertRaises(ValueError):
            self.analyzer.generate_sensitivity_report(param_name='invalid_param')

    def test_generate_top_n_reports(self):
        """测试生成排名靠前参数的报告"""
        output_dir = os.path.join(self.test_output_dir, 'top_reports')
        
        # 创建一个带有mock_generate_report方法的子类
        class MockOptimizationAnalyzer(OptimizationAnalyzer):
            def generate_top_n_reports(self, **kwargs):
                # 创建输出目录
                os.makedirs(kwargs['output_dir'], exist_ok=True)
                # 创建模拟报告文件
                for i in range(1, kwargs['top_n'] + 1):
                    report_path = os.path.join(kwargs['output_dir'], f'params_rank_{i}.html')
                    with open(report_path, 'w') as f:
                        f.write(f"Mock report for rank {i}")
                return True
        
        # 使用模拟分析器
        mock_analyzer = MockOptimizationAnalyzer(self.opt_results, engine_type='backtrader')
        
        mock_analyzer.generate_top_n_reports(
            top_n=2,
            metric='sharpe_ratio',
            output_dir=output_dir,
            output_format='html',
            data=self.market_data  # 提供市场数据以生成更完整的报告
        )
        
        # 验证目录已创建
        self.assertTrue(os.path.exists(output_dir))
        
        # 验证报告文件已生成
        self.assertTrue(os.path.exists(os.path.join(output_dir, 'params_rank_1.html')))
        self.assertTrue(os.path.exists(os.path.join(output_dir, 'params_rank_2.html')))

    def test_export_results(self):
        """测试导出优化结果"""
        # 导出到Excel
        excel_path = os.path.join(self.test_output_dir, 'optimization_results.xlsx')
        self.analyzer.export_results_to_excel(excel_path)
        self.assertTrue(os.path.exists(excel_path))
        
        # 导出到CSV
        csv_path = os.path.join(self.test_output_dir, 'optimization_results.csv')
        self.analyzer.export_results_to_csv(csv_path)
        self.assertTrue(os.path.exists(csv_path))


class TestRunOptimizationAndAnalyze(unittest.TestCase):
    """测试run_optimization_and_analyze辅助函数"""

    def setUp(self):
        """测试前准备工作"""
        self.market_data = create_mock_data()
        self.param_grid = {
            'param1': [5, 10],
            'param2': [20, 30]
        }
        self.test_output_dir = tempfile.mkdtemp()

    def tearDown(self):
        """测试后清理工作"""
        shutil.rmtree(self.test_output_dir, ignore_errors=True)

    @unittest.skip("需要实际引擎运行，跳过自动测试")
    def test_run_optimization_and_analyze(self):
        """测试优化并分析辅助函数"""
        # 此测试需要实际运行优化器，可能需要较长时间
        # 因此标记为skip，可根据需要手动运行
        results = run_optimization_and_analyze(
            strategy_class=MockStrategy,
            data=self.market_data,
            param_grid=self.param_grid,
            engine_type='vectorbt',  # 使用更快的vectorbt引擎
            top_n=2,
            generate_reports=True,
            output_dir=self.test_output_dir
        )
        
        # 验证返回结果
        self.assertIn('optimization_results', results)
        self.assertIn('best_params', results)
        self.assertIn('analyzer', results)


if __name__ == '__main__':
    unittest.main() 