#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
波动性指标使用示例

演示如何使用波动性指标进行市场分析和交易决策。
"""

import os
import sys
# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec

# 尝试导入yfinance，如果不成功则不使用
try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    YFINANCE_AVAILABLE = False
    print("yfinance模块未安装，将使用模拟数据。如需获取实际数据，请安装yfinance: pip install yfinance")

from indicators.volatility import AverageTrueRange, StandardDeviation, HistoricalVolatility, BollingerWidth


def fetch_sample_data(ticker="AAPL", period="1y", interval="1d"):
    """
    获取示例数据
    
    Parameters
    ----------
    ticker : str, optional
        股票代码，默认为"AAPL"
    period : str, optional
        获取数据的时间范围，默认为"1y"
    interval : str, optional
        数据间隔，默认为"1d"
        
    Returns
    -------
    pd.DataFrame
        股票价格数据
    """
    # 创建模拟数据
    dates = pd.date_range(start='2023-01-01', periods=252, freq='D')
    np.random.seed(42)
    close = 100 + np.cumsum(np.random.normal(0, 1, len(dates)))
    high = close + np.random.uniform(0, 3, len(dates))
    low = close - np.random.uniform(0, 3, len(dates))
    open_prices = close.copy()
    np.random.shuffle(open_prices)
    volume = np.random.randint(1000, 10000, len(dates))
    
    data = pd.DataFrame({
        'open': open_prices,
        'high': high,
        'low': low,
        'close': close,
        'volume': volume
    }, index=dates)
    
    # 如果yfinance可用且用户希望尝试获取真实数据，则尝试获取
    if YFINANCE_AVAILABLE:
        try:
            print(f"尝试从Yahoo Finance获取{ticker}的数据...")
            real_data = yf.download(ticker, period=period, interval=interval, progress=False)
            if not real_data.empty:
                # 确保列名转换为小写
                column_map = {}
                for col in real_data.columns:
                    column_map[col] = col.lower()
                
                data = real_data.rename(columns=column_map)
                print("成功获取实际数据。")
            else:
                print("无法获取实际数据，使用模拟数据代替。")
        except Exception as e:
            print(f"获取实际数据失败: {e}，使用模拟数据代替。")
    
    # 调试信息：检查数据结构
    print(f"数据类型: {type(data)}")
    print(f"数据形状: {data.shape}")
    print(f"数据索引类型: {type(data.index)}")
    
    # 确保数据包含必需的列
    required_columns = ['open', 'high', 'low', 'close', 'volume']
    for col in required_columns:
        if col not in data.columns:
            print(f"警告: 数据中缺少'{col}'列，请检查数据来源。")
    
    # 确保小写列名，避免大小写问题
    data.columns = [col.lower() for col in data.columns]
            
    print(f"数据列: {list(data.columns)}")
    return data


def atr_example():
    """
    ATR指标使用示例
    """
    print("=== ATR平均真实波幅示例 ===")
    
    # 获取数据
    data = fetch_sample_data()
    
    # 创建ATR指标
    atr = AverageTrueRange(period=14)
    
    # 计算ATR
    result = atr.calculate(data)
    
    # 显示结果
    print(f"数据头部:\n{result[['close', 'tr', 'atr']].head()}")
    print(f"数据尾部:\n{result[['close', 'tr', 'atr']].tail()}")
    
    # 绘制图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True, gridspec_kw={'height_ratios': [3, 1]})
    
    # 绘制价格
    ax1.plot(data.index, data['close'], label='收盘价', color='black')
    ax1.set_title('价格图表')
    ax1.set_ylabel('价格')
    ax1.grid(True)
    ax1.legend()
    
    # 绘制ATR
    atr.plot(ax=ax2)
    
    plt.tight_layout()
    plt.show()
    
    print("ATR交易思路:")
    print("1. ATR可用于设置动态止损位，通常为当前价格减去1.5-2倍的ATR值")
    print("2. ATR可用于判断市场波动性，高ATR表示高波动性，低ATR表示低波动性")
    print("3. ATR突增可能意味着市场趋势变化或突发事件")
    print("4. 可用ATR设计头寸规模，波动性高时减小头寸，波动性低时增加头寸")
    

def standard_deviation_example():
    """
    标准差指标使用示例
    """
    print("\n=== 标准差指标示例 ===")
    
    # 获取数据
    data = fetch_sample_data()
    
    # 创建标准差指标
    sd = StandardDeviation(period=20)
    
    # 计算标准差
    result = sd.calculate(data)
    
    # 显示结果
    print(f"数据头部:\n{result[['close', 'sd', 'sd_pct']].head()}")
    print(f"数据尾部:\n{result[['close', 'sd', 'sd_pct']].tail()}")
    
    # 绘制图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True, gridspec_kw={'height_ratios': [3, 1]})
    
    # 绘制价格
    ax1.plot(data.index, data['close'], label='close', color='black')
    ax1.set_title('price chart')
    ax1.set_ylabel('Price')
    ax1.grid(True)
    ax1.legend()
    
    # 绘制标准差
    sd.plot(ax=ax2)
    
    plt.tight_layout()
    plt.show()
    
    print("标准差交易思路:")
    print("1. 标准差可用于识别价格波动的稳定性和不确定性")
    print("2. 高标准差表示价格波动大，可能表示趋势转变或市场不稳定")
    print("3. 低标准差表示价格波动小，可能表示盘整或趋势稳定")
    print("4. 可结合均线使用，判断价格偏离均线的程度")


def historical_volatility_example():
    """
    历史波动率指标使用示例
    """
    print("\n=== 历史波动率指标示例 ===")
    
    # 获取数据
    data = fetch_sample_data()
    
    # 创建历史波动率指标
    hv = HistoricalVolatility(period=20, trading_periods=252)
    
    # 计算历史波动率
    result = hv.calculate(data)
    
    # 显示结果
    print(f"数据头部:\n{result[['close', 'returns', 'hv', 'hv_pct']].head()}")
    print(f"数据尾部:\n{result[['close', 'returns', 'hv', 'hv_pct']].tail()}")
    
    # 绘制图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True, gridspec_kw={'height_ratios': [3, 1]})
    
    # 绘制价格
    ax1.plot(data.index, data['close'], label='close', color='black')
    ax1.set_title('price chart')
    ax1.set_ylabel('Price')
    ax1.grid(True)
    ax1.legend()
    
    # 绘制历史波动率
    hv.plot(ax=ax2)
    
    plt.tight_layout()
    plt.show()
    
    print("历史波动率交易思路:")
    print("1. 历史波动率常用于期权定价和风险管理")
    print("2. 低波动率后通常会出现高波动率，可用于预判市场变化")
    print("3. 波动率均值回归特性可用于构建波动率交易策略")
    print("4. 波动率突变可能预示着重要市场转折点")


def bollinger_width_example():
    """
    布林带宽度指标使用示例
    """
    print("\n=== 布林带宽度指标示例 ===")
    
    # 获取数据
    data = fetch_sample_data()
    
    # 创建布林带宽度指标
    bbw = BollingerWidth(period=20, std_dev=2.0)
    
    # 计算布林带宽度
    result = bbw.calculate(data)
    
    # 显示结果
    print(f"数据头部:\n{result[['close', 'bb_middle', 'bb_width', 'bb_width_pct']].head()}")
    print(f"数据尾部:\n{result[['close', 'bb_middle', 'bb_width', 'bb_width_pct']].tail()}")
    
    # 绘制图表
    bbw.plot()
    
    plt.tight_layout()
    plt.show()
    
    print("布林带宽度交易思路:")
    print("1. 带宽收窄通常预示着波动性减小和盘整，可能后续出现大幅波动")
    print("2. 带宽扩大表示波动性增加，通常在趋势中出现")
    print("3. 带宽周期性变化可用于判断市场从盘整到趋势的转变")
    print("4. 极低的带宽值常常是重要突破前的征兆")


def volatility_comparison():
    """
    波动性指标比较示例
    """
    print("\n=== 波动性指标比较 ===")
    
    # 获取数据
    data = fetch_sample_data()
    
    # 创建多个波动性指标
    atr = AverageTrueRange(period=14)
    sd = StandardDeviation(period=20)
    hv = HistoricalVolatility(period=20)
    bbw = BollingerWidth(period=20)
    
    # 计算指标
    data_atr = atr.calculate(data)
    data_sd = sd.calculate(data)
    data_hv = hv.calculate(data)
    data_bbw = bbw.calculate(data)
    
    # 创建比较图
    fig = plt.figure(figsize=(14, 10))
    gs = GridSpec(5, 1, figure=fig)
    
    # 价格子图
    ax_price = fig.add_subplot(gs[0, 0])
    ax_price.plot(data.index, data['close'], label='close', color='black')
    ax_price.set_title('price chart')
    ax_price.grid(True)
    ax_price.legend()
    
    # ATR子图
    ax_atr = fig.add_subplot(gs[1, 0], sharex=ax_price)
    ax_atr.plot(data_atr.index, data_atr['atr'], label='ATR', color='blue')
    ax_atr.set_title('ATR average true range')
    ax_atr.grid(True)
    ax_atr.legend()
    
    # 标准差子图
    ax_sd = fig.add_subplot(gs[2, 0], sharex=ax_price)
    ax_sd.plot(data_sd.index, data_sd['sd_pct'], label='SD %', color='purple')
    ax_sd.set_title('standard deviation percentage')
    ax_sd.grid(True)
    ax_sd.legend()
    
    # 历史波动率子图
    ax_hv = fig.add_subplot(gs[3, 0], sharex=ax_price)
    ax_hv.plot(data_hv.index, data_hv['hv_pct'], label='HV %', color='red')
    ax_hv.set_title('historical volatility percentage')
    ax_hv.grid(True)
    ax_hv.legend()
    
    # 布林带宽度子图
    ax_bbw = fig.add_subplot(gs[4, 0], sharex=ax_price)
    ax_bbw.plot(data_bbw.index, data_bbw['bb_width_pct'], label='BBW %', color='green')
    ax_bbw.set_title('bollinger band width percentage')
    ax_bbw.grid(True)
    ax_bbw.legend()
    
    plt.tight_layout()
    plt.show()
    
    print("波动性指标比较观察:")
    print("1. 不同波动性指标可能对市场变化有不同的敏感度")
    print("2. ATR关注价格变动的绝对值，而其他指标更关注相对变动")
    print("3. 历史波动率考虑了收益率的变化，对趋势转变更敏感")
    print("4. 布林带宽度结合了价格和波动性，可以发现价格压缩区域")
    print("5. 多个波动性指标一致确认，信号强度更大")


if __name__ == "__main__":
    # 运行示例
    atr_example()
    standard_deviation_example()
    historical_volatility_example()
    bollinger_width_example()
    volatility_comparison() 