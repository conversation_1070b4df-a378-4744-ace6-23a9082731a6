#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
技术指标模块

核心技术指标已迁移到freqtrade技术库，使用更专业的市场验证实现。
建议使用freqtrade_integration模块中的专业指标。
"""

from .base import Indicator, CompositeIndicator

# 导入子模块
from . import trend
from . import oscillators
from . import volume
from . import volatility
from . import cycles

# 导入freqtrade集成模块
try:
    from . import freqtrade_integration
    FREQTRADE_INTEGRATION_AVAILABLE = True
except ImportError:
    FREQTRADE_INTEGRATION_AVAILABLE = False

# 版本信息
__version__ = '0.1.0'

# 导出的内容
__all__ = [
    'Indicator',
    'CompositeIndicator',
    'trend',
    'oscillators',
    'volume',
    'volatility',
    'cycles',
]

# 如果freqtrade集成可用，添加到导出列表
if FREQTRADE_INTEGRATION_AVAILABLE:
    __all__.append('freqtrade_integration')

# 模块级常量
CATEGORIES = {
    'trend': '趋势类指标',
    'oscillator': '振荡器类指标',
    'volume': '量能类指标',
    'volatility': '波动性指标',
    'cycle': '周期类指标',
    'composite': '复合指标',
}

# 日志设置
import logging
logger = logging.getLogger(__name__) 