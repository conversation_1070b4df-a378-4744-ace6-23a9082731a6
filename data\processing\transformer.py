"""
数据转换模块

提供数据转换功能，包括标准化、归一化、时间特征提取和收益率计算等功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
from datetime import datetime, time

from data.structures import OHLCVColumns


def normalize_data(df: pd.DataFrame, method: str = 'minmax', columns: Optional[List[str]] = None) -> pd.DataFrame:
    """
    对数据进行归一化处理
    
    Args:
        df: 要处理的DataFrame
        method: 归一化方法，'minmax'(最小-最大归一化), 'decimal'(小数定标归一化)
        columns: 要处理的列，默认为None表示处理所有数值列
        
    Returns:
        归一化后的DataFrame
    """
    # 复制DataFrame以避免修改原始数据
    df_normalized = df.copy()
    
    # 如果未指定列，则处理所有数值列
    if columns is None:
        target_columns = df_normalized.select_dtypes(include=[np.number]).columns.tolist()
    else:
        # 确保指定的列存在于DataFrame中且为数值类型
        target_columns = [col for col in columns 
                         if col in df_normalized.columns and pd.api.types.is_numeric_dtype(df_normalized[col])]
        if not target_columns:
            return df_normalized  # 如果没有有效的列，则直接返回原始数据
    
    # 应用归一化方法
    for col in target_columns:
        if method == 'minmax':
            min_val = df_normalized[col].min()
            max_val = df_normalized[col].max()
            
            # 避免除以零
            if max_val == min_val:
                df_normalized[col] = 0.5  # 如果所有值相等，则将其归一化为0.5
            else:
                df_normalized[col] = (df_normalized[col] - min_val) / (max_val - min_val)
        
        elif method == 'decimal':
            # 找到最大绝对值
            max_abs = np.abs(df_normalized[col]).max()
            if max_abs > 0:
                # 计算所需的缩放因子
                scale = 10 ** np.ceil(np.log10(max_abs))
                df_normalized[col] = df_normalized[col] / scale
            else:
                df_normalized[col] = 0
        
        else:
            raise ValueError(f"不支持的归一化方法: {method}")
    
    return df_normalized


def standardize_data(df: pd.DataFrame, columns: Optional[List[str]] = None) -> pd.DataFrame:
    """
    对数据进行标准化处理(Z-score标准化)
    
    Args:
        df: 要处理的DataFrame
        columns: 要处理的列，默认为None表示处理所有数值列
        
    Returns:
        标准化后的DataFrame
    """
    # 复制DataFrame以避免修改原始数据
    df_standardized = df.copy()
    
    # 如果未指定列，则处理所有数值列
    if columns is None:
        target_columns = df_standardized.select_dtypes(include=[np.number]).columns.tolist()
    else:
        # 确保指定的列存在于DataFrame中且为数值类型
        target_columns = [col for col in columns 
                         if col in df_standardized.columns and pd.api.types.is_numeric_dtype(df_standardized[col])]
        if not target_columns:
            return df_standardized  # 如果没有有效的列，则直接返回原始数据
    
    # 应用Z-score标准化
    for col in target_columns:
        mean = df_standardized[col].mean()
        std = df_standardized[col].std()
        
        # 避免除以零
        if std > 0:
            df_standardized[col] = (df_standardized[col] - mean) / std
        else:
            df_standardized[col] = 0  # 如果标准差为0，则将所有值标准化为0
    
    return df_standardized


def extract_time_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    从时间戳索引中提取时间特征
    
    Args:
        df: 要处理的DataFrame，必须有时间戳索引
        
    Returns:
        添加了时间特征的DataFrame
    """
    # 确保索引是datetime类型
    if not isinstance(df.index, pd.DatetimeIndex):
        raise ValueError("DataFrame必须以时间为索引")
    
    # 复制DataFrame以避免修改原始数据
    df_with_features = df.copy()
    
    # 提取基本时间特征
    df_with_features['hour'] = df.index.hour
    df_with_features['day'] = df.index.day
    df_with_features['weekday'] = df.index.weekday  # 0:周一, 6:周日
    df_with_features['month'] = df.index.month
    df_with_features['quarter'] = df.index.quarter
    df_with_features['year'] = df.index.year
    
    # 是否为周末
    df_with_features['is_weekend'] = df.index.weekday >= 5
    
    # 交易时段特征（以纽约市场为例）
    ny_market_open = time(9, 30)  # 9:30 AM ET
    ny_market_close = time(16, 0)  # 4:00 PM ET
    
    # 检查时间是否在纽约市场交易时段内
    df_with_features['is_market_hours'] = (
        (df.index.time >= ny_market_open) & 
        (df.index.time < ny_market_close) & 
        (df.index.weekday < 5)  # 排除周末
    )
    
    # 月初/月末特征
    df_with_features['is_month_start'] = df.index.is_month_start
    df_with_features['is_month_end'] = df.index.is_month_end
    
    # 季度初/季度末特征
    df_with_features['is_quarter_start'] = df.index.is_quarter_start
    df_with_features['is_quarter_end'] = df.index.is_quarter_end
    
    # 年初/年末特征
    df_with_features['is_year_start'] = df.index.is_year_start
    df_with_features['is_year_end'] = df.index.is_year_end
    
    # 一天中的时间周期（正弦和余弦变换）
    seconds_in_day = 24 * 60 * 60
    timestamp = df.index.map(lambda x: x.hour * 3600 + x.minute * 60 + x.second)
    df_with_features['day_sin'] = np.sin(timestamp * (2 * np.pi / seconds_in_day))
    df_with_features['day_cos'] = np.cos(timestamp * (2 * np.pi / seconds_in_day))
    
    # 一年中的时间周期
    day_of_year = df.index.dayofyear
    days_in_year = 365.25  # 考虑闰年
    df_with_features['year_sin'] = np.sin(day_of_year * (2 * np.pi / days_in_year))
    df_with_features['year_cos'] = np.cos(day_of_year * (2 * np.pi / days_in_year))
    
    return df_with_features


def calculate_returns(df: pd.DataFrame, 
                     method: str = 'pct_change', 
                     periods: int = 1, 
                     column: str = OHLCVColumns.CLOSE) -> pd.DataFrame:
    """
    计算价格收益率
    
    Args:
        df: 要处理的DataFrame
        method: 收益率计算方法，'pct_change'(百分比变化) 或 'log_return'(对数收益率)
        periods: 计算周期，默认为1(相邻两个时间点)
        column: 用于计算收益率的列，默认为'close'
        
    Returns:
        添加了收益率列的DataFrame
    """
    # 确保指定的列存在
    if column not in df.columns:
        raise ValueError(f"列 {column} 不存在于DataFrame中")
    
    # 复制DataFrame以避免修改原始数据
    df_returns = df.copy()
    
    # 计算收益率
    if method == 'pct_change':
        # 计算百分比变化
        df_returns['return'] = df_returns[column].pct_change(periods=periods)
    elif method == 'log_return':
        # 计算对数收益率
        df_returns['return'] = np.log(df_returns[column] / df_returns[column].shift(periods))
    else:
        raise ValueError(f"不支持的收益率计算方法: {method}")
    
    # 添加更多的收益率特征
    if 'return' in df_returns.columns:
        # 累积收益率
        df_returns['cum_return'] = (1 + df_returns['return'].fillna(0)).cumprod() - 1
        
        # 不同周期的收益率
        if len(df_returns) > 1:
            # 日收益率(如果数据频率允许)
            df_returns['daily_return'] = df_returns[column].pct_change(freq='D')
            
            # 更长周期的收益率(如果数据量足够)
            if len(df_returns) > 5:
                df_returns['weekly_return'] = df_returns[column].pct_change(freq='W')
            
            if len(df_returns) > 30:
                df_returns['monthly_return'] = df_returns[column].pct_change(freq='ME')
    
    return df_returns 