#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
回测分析模块

提供回测结果分析、可视化和报告生成等功能。
"""

from .metrics import (
    calculate_metrics,
    calculate_ulcer_index,
    calculate_sterling_ratio,
    calculate_burke_ratio,
    calculate_kappa_ratio,
    calculate_tail_ratio,
    calculate_downside_deviation,
    calculate_max_consecutive_losses
)
from .visualization import (
    plot_performance,
    plot_equity_curve,
    plot_drawdowns,
    plot_monthly_returns_heatmap,
    plot_trades_distribution,
    plot_rolling_stats,
    plot_underwater,
    plot_returns_distribution,
    plot_drawdown_periods,
    plot_trade_analysis
)
from .reporting import (
    generate_report,
    export_metrics_to_json,
    export_trades_to_csv,
    generate_html_report,
    generate_text_report,
    generate_pdf_report
)
from .comparison import StrategyComparison, compare_strategies, compare_time_periods, compare_with_market_regimes
from .batch_processing import BatchProcessor, batch_analyze_results

# 导入新的稳健性分析模块
from .robustness import (
    monte_carlo_simulation,
    plot_monte_carlo_simulation,
    analyze_monte_carlo_results,
    maximum_adverse_excursion,
    plot_mae_analysis,
    bootstrap_analysis,
    plot_bootstrap_distributions,
    sensitivity_analysis,
    plot_sensitivity_analysis
)

# 导入新的多周期分析模块
from .multi_period import (
    analyze_by_periods,
    analyze_by_timeframes,
    analyze_by_calendar,
    analyze_rolling_windows,
    plot_calendar_heatmap,
    plot_rolling_windows,
    plot_comparison_by_periods
)

# 导入新的优化集成模块
from .optimization_integration import (
    OptimizationAnalyzer,
    run_optimization_and_analyze
)

__all__ = [
    # 基础指标计算
    'calculate_metrics',
    
    # 高级风险评估指标
    'calculate_ulcer_index',
    'calculate_sterling_ratio',
    'calculate_burke_ratio',
    'calculate_kappa_ratio',
    'calculate_tail_ratio',
    'calculate_downside_deviation',
    'calculate_max_consecutive_losses',
    
    # 基础可视化
    'plot_performance',
    'plot_equity_curve',
    'plot_drawdowns',
    'plot_monthly_returns_heatmap',
    'plot_trades_distribution',
    'plot_rolling_stats',
    'plot_underwater',
    
    # 高级可视化
    'plot_returns_distribution',
    'plot_drawdown_periods',
    'plot_trade_analysis',
    
    # 报告生成
    'generate_report',
    'generate_html_report',
    'generate_text_report',
    'generate_pdf_report',
    'export_metrics_to_json',
    'export_trades_to_csv',
    
    # 比较分析
    'StrategyComparison',
    'compare_strategies',
    'compare_time_periods',
    'compare_with_market_regimes',
    
    # 批量处理
    'BatchProcessor',
    'batch_analyze_results',
    
    # 稳健性分析
    'monte_carlo_simulation',
    'plot_monte_carlo_simulation',
    'analyze_monte_carlo_results',
    'maximum_adverse_excursion',
    'plot_mae_analysis',
    'bootstrap_analysis',
    'plot_bootstrap_distributions',
    'sensitivity_analysis',
    'plot_sensitivity_analysis',
    
    # 多周期分析
    'analyze_by_periods',
    'analyze_by_timeframes',
    'analyze_by_calendar',
    'analyze_rolling_windows',
    'plot_calendar_heatmap',
    'plot_rolling_windows',
    'plot_comparison_by_periods',
    
    # 优化集成
    'OptimizationAnalyzer',
    'run_optimization_and_analyze'
] 