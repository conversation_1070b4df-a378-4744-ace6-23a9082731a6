"""
仓位规模计算器

实现各种仓位规模计算策略，包括固定金额、固定百分比、凯利准则等。
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List, Union
import math
from risk.money_management.base import PositionSizer


class FixedAmountSizer(PositionSizer):
    """
    固定金额仓位规模计算器
    
    每次交易使用固定金额作为仓位规模。
    """
    
    def __init__(self, name: str = "FixedAmount", description: str = "", amount: float = 1000.0):
        """
        初始化固定金额仓位规模计算器
        
        Parameters
        ----------
        name : str, optional
            计算器名称，默认为"FixedAmount"
        description : str, optional
            计算器描述，默认为空字符串
        amount : float, optional
            每次交易的固定金额，默认为1000.0
        """
        super().__init__(name, description, amount=amount)
    
    def validate_params(self) -> None:
        """
        验证参数有效性
        
        Raises
        ------
        ValueError
            如果金额不是正数
        """
        if self.params.get("amount", 0) <= 0:
            raise ValueError("交易金额必须为正数")
    
    def calculate_position_size(self, 
                               context: Dict[str, Any], 
                               data: pd.DataFrame = None, 
                               risk_amount: float = None) -> float:
        """
        计算仓位规模
        
        Parameters
        ----------
        context : Dict[str, Any]
            交易上下文，包含当前资金、持仓等信息
        data : pd.DataFrame, optional
            相关市场数据，默认为None
        risk_amount : float, optional
            愿意承担的风险金额，默认为None
        
        Returns
        -------
        float
            固定金额作为仓位规模
        """
        capital = context.get("capital", 0)
        amount = self.params.get("amount")
        
        # 如果可用资金不足，则使用可用资金
        return min(amount, capital)


class FixedPercentSizer(PositionSizer):
    """
    固定百分比仓位规模计算器
    
    每次交易使用固定百分比的资金作为仓位规模。
    """
    
    def __init__(self, name: str = "FixedPercent", description: str = "", percent: float = 2.0):
        """
        初始化固定百分比仓位规模计算器
        
        Parameters
        ----------
        name : str, optional
            计算器名称，默认为"FixedPercent"
        description : str, optional
            计算器描述，默认为空字符串
        percent : float, optional
            每次交易的资金百分比，1.0表示1%，默认为2.0
        """
        super().__init__(name, description, percent=percent)
    
    def validate_params(self) -> None:
        """
        验证参数有效性
        
        Raises
        ------
        ValueError
            如果百分比不是正数或超过100
        """
        percent = self.params.get("percent", 0)
        if percent <= 0:
            raise ValueError("百分比必须为正数")
        if percent > 100:
            raise ValueError("百分比不能超过100")
    
    def calculate_position_size(self, 
                               context: Dict[str, Any], 
                               data: pd.DataFrame = None, 
                               risk_amount: float = None) -> float:
        """
        计算仓位规模
        
        Parameters
        ----------
        context : Dict[str, Any]
            交易上下文，包含当前资金、持仓等信息
        data : pd.DataFrame, optional
            相关市场数据，默认为None
        risk_amount : float, optional
            愿意承担的风险金额，默认为None
        
        Returns
        -------
        float
            按固定百分比计算的仓位规模（金额）
        """
        capital = context.get("capital", 0)
        percent = self.params.get("percent")
        
        return capital * (percent / 100)


class KellyCriterionSizer(PositionSizer):
    """
    凯利准则仓位规模计算器
    
    使用凯利准则公式计算最优仓位规模，公式为 f* = (bp - q) / b
    其中 b 是赔率，p 是获胜概率，q 是失败概率（q = 1 - p）。
    """
    
    def __init__(self, 
                name: str = "Kelly", 
                description: str = "", 
                win_rate: float = None, 
                win_loss_ratio: float = None,
                fraction: float = 1.0,
                lookback_periods: int = 20):
        """
        初始化凯利准则仓位规模计算器
        
        Parameters
        ----------
        name : str, optional
            计算器名称，默认为"Kelly"
        description : str, optional
            计算器描述，默认为空字符串
        win_rate : float, optional
            获胜概率，若为None则根据历史数据计算
        win_loss_ratio : float, optional
            盈亏比，即平均盈利/平均亏损，若为None则根据历史数据计算
        fraction : float, optional
            凯利比例系数，用于保守估计，范围为(0, 1]，默认为1.0（完全凯利）
        lookback_periods : int, optional
            计算历史胜率和盈亏比时的回溯期数，默认为20
        """
        super().__init__(
            name, 
            description, 
            win_rate=win_rate, 
            win_loss_ratio=win_loss_ratio,
            fraction=fraction,
            lookback_periods=lookback_periods
        )
    
    def validate_params(self) -> None:
        """
        验证参数有效性
        
        Raises
        ------
        ValueError
            如果参数无效
        """
        win_rate = self.params.get("win_rate")
        if win_rate is not None and (win_rate <= 0 or win_rate >= 1):
            raise ValueError("获胜概率必须在(0, 1)范围内")
            
        win_loss_ratio = self.params.get("win_loss_ratio")
        if win_loss_ratio is not None and win_loss_ratio <= 0:
            raise ValueError("盈亏比必须为正数")
        
        fraction = self.params.get("fraction")
        if fraction <= 0 or fraction > 1:
            raise ValueError("凯利比例系数必须在(0, 1]范围内")
            
        lookback_periods = self.params.get("lookback_periods")
        if lookback_periods is not None and lookback_periods <= 0:
            raise ValueError("回溯期数必须为正整数")
    
    def _calculate_win_rate_and_ratio(self, trades: List[Dict[str, Any]]) -> tuple:
        """
        根据历史交易记录计算胜率和盈亏比
        
        Parameters
        ----------
        trades : List[Dict[str, Any]]
            历史交易记录
        
        Returns
        -------
        tuple
            (胜率, 盈亏比)
        """
        if not trades:
            return 0.5, 1.0  # 默认值
        
        wins = [t for t in trades if t.get("pnl", 0) > 0]
        losses = [t for t in trades if t.get("pnl", 0) < 0]
        
        win_rate = len(wins) / len(trades) if trades else 0.5
        
        avg_win = sum(t.get("pnl", 0) for t in wins) / len(wins) if wins else 1.0
        avg_loss = sum(abs(t.get("pnl", 0)) for t in losses) / len(losses) if losses else 1.0
        
        win_loss_ratio = avg_win / avg_loss if avg_loss != 0 else 1.0
        
        return win_rate, win_loss_ratio
    
    def calculate_position_size(self, 
                               context: Dict[str, Any], 
                               data: pd.DataFrame = None, 
                               risk_amount: float = None) -> float:
        """
        计算仓位规模
        
        Parameters
        ----------
        context : Dict[str, Any]
            交易上下文，包含当前资金、持仓和历史交易等信息
        data : pd.DataFrame, optional
            相关市场数据，默认为None
        risk_amount : float, optional
            愿意承担的风险金额，默认为None
        
        Returns
        -------
        float
            按凯利准则计算的仓位规模（金额）
        """
        capital = context.get("capital", 0)
        
        # 获取胜率和盈亏比，优先使用参数设置的值，否则根据历史数据计算
        win_rate = self.params.get("win_rate")
        win_loss_ratio = self.params.get("win_loss_ratio")
        
        if win_rate is None or win_loss_ratio is None:
            # 如果未指定胜率或盈亏比，则根据历史交易计算
            trades = context.get("trades", [])
            lookback_periods = self.params.get("lookback_periods")
            recent_trades = trades[-lookback_periods:] if len(trades) > lookback_periods else trades
            
            calculated_win_rate, calculated_ratio = self._calculate_win_rate_and_ratio(recent_trades)
            
            win_rate = win_rate or calculated_win_rate
            win_loss_ratio = win_loss_ratio or calculated_ratio
        
        # 计算凯利比例 f* = (bp - q) / b，其中 b = win_loss_ratio, p = win_rate, q = 1 - win_rate
        b = win_loss_ratio
        p = win_rate
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b
        
        # 应用调整系数并确保结果非负
        kelly_fraction = max(0, kelly_fraction * self.params.get("fraction", 1.0))
        
        # 限制最大仓位比例为50%
        kelly_fraction = min(kelly_fraction, 0.5)
        
        return capital * kelly_fraction


class OptimalFSizer(PositionSizer):
    """
    最优F值仓位规模计算器
    
    基于Ralph Vince的最优F值理论计算仓位规模，通过蒙特卡洛模拟或历史数据计算。
    """
    
    def __init__(self, 
                name: str = "OptimalF", 
                description: str = "", 
                f_value: float = None,
                risk_multiple: float = 0.5,
                lookback_periods: int = 50):
        """
        初始化最优F值仓位规模计算器
        
        Parameters
        ----------
        name : str, optional
            计算器名称，默认为"OptimalF"
        description : str, optional
            计算器描述，默认为空字符串
        f_value : float, optional
            最优F值，若为None则根据历史数据计算
        risk_multiple : float, optional
            风险倍数，用于调整最优F值，范围为(0, 1]，默认为0.5
        lookback_periods : int, optional
            计算历史最优F值时的回溯期数，默认为50
        """
        super().__init__(
            name, 
            description, 
            f_value=f_value, 
            risk_multiple=risk_multiple,
            lookback_periods=lookback_periods
        )
    
    def validate_params(self) -> None:
        """
        验证参数有效性
        
        Raises
        ------
        ValueError
            如果参数无效
        """
        f_value = self.params.get("f_value")
        if f_value is not None and (f_value <= 0 or f_value > 1):
            raise ValueError("F值必须在(0, 1]范围内")
            
        risk_multiple = self.params.get("risk_multiple")
        if risk_multiple <= 0 or risk_multiple > 1:
            raise ValueError("风险倍数必须在(0, 1]范围内")
            
        lookback_periods = self.params.get("lookback_periods")
        if lookback_periods is not None and lookback_periods <= 0:
            raise ValueError("回溯期数必须为正整数")
    
    def _calculate_optimal_f(self, trades: List[Dict[str, Any]]) -> float:
        """
        根据历史交易记录计算最优F值
        
        使用简化的方法计算最优F值，实际应用中可能需要更复杂的优化算法。
        
        Parameters
        ----------
        trades : List[Dict[str, Any]]
            历史交易记录
        
        Returns
        -------
        float
            估计的最优F值
        """
        if not trades:
            return 0.02  # 默认保守值
        
        # 计算R倍数（每笔交易的盈亏相对于初始风险的倍数）
        r_multiples = []
        for trade in trades:
            pnl = trade.get("pnl", 0)
            initial_risk = trade.get("initial_risk")
            
            # 如果没有初始风险数据，使用估算值
            if initial_risk is None or initial_risk == 0:
                price = trade.get("entry_price", 1.0)
                stop_loss = trade.get("stop_loss_price")
                
                if stop_loss is not None and price != stop_loss:
                    initial_risk = abs(price - stop_loss)
                else:
                    # 假设风险是入场价格的1%
                    initial_risk = price * 0.01
            
            r_multiple = pnl / initial_risk if initial_risk else 0
            r_multiples.append(r_multiple)
        
        if not r_multiples:
            return 0.02
        
        # 使用简化的计算方法（在实际应用中应使用更复杂的优化方法）
        wins = [r for r in r_multiples if r > 0]
        losses = [r for r in r_multiples if r < 0]
        
        win_rate = len(wins) / len(r_multiples)
        avg_win = sum(wins) / len(wins) if wins else 1.0
        avg_loss = sum(abs(l) for l in losses) / len(losses) if losses else 1.0
        
        # 使用简化的凯利公式作为近似
        f = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
        
        # 限制范围并应用保守因子
        return max(0.01, min(f, 0.5))
    
    def calculate_position_size(self, 
                               context: Dict[str, Any], 
                               data: pd.DataFrame = None, 
                               risk_amount: float = None) -> float:
        """
        计算仓位规模
        
        Parameters
        ----------
        context : Dict[str, Any]
            交易上下文，包含当前资金、持仓和历史交易等信息
        data : pd.DataFrame, optional
            相关市场数据，默认为None
        risk_amount : float, optional
            愿意承担的风险金额，默认为None
        
        Returns
        -------
        float
            按最优F值计算的仓位规模（金额）
        """
        capital = context.get("capital", 0)
        
        # 获取F值，优先使用参数设置的值，否则根据历史数据计算
        f_value = self.params.get("f_value")
        
        if f_value is None:
            # 如果未指定F值，则根据历史交易计算
            trades = context.get("trades", [])
            lookback_periods = self.params.get("lookback_periods")
            recent_trades = trades[-lookback_periods:] if len(trades) > lookback_periods else trades
            
            f_value = self._calculate_optimal_f(recent_trades)
        
        # 应用风险倍数调整
        risk_multiple = self.params.get("risk_multiple", 0.5)
        adjusted_f = f_value * risk_multiple
        
        # 计算仓位规模
        position_size = capital * adjusted_f
        
        return position_size


class VolatilityAdjustedSizer(PositionSizer):
    """
    波动率调整仓位规模计算器
    
    根据市场波动率调整仓位规模，波动率越大，仓位越小。
    """
    
    def __init__(self, 
                name: str = "VolatilityAdjusted", 
                description: str = "", 
                target_risk_pct: float = 1.0,
                volatility_lookback: int = 20,
                volatility_max_pct: float = 5.0):
        """
        初始化波动率调整仓位规模计算器
        
        Parameters
        ----------
        name : str, optional
            计算器名称，默认为"VolatilityAdjusted"
        description : str, optional
            计算器描述，默认为空字符串
        target_risk_pct : float, optional
            目标风险百分比，默认为1.0（1%）
        volatility_lookback : int, optional
            计算波动率的回溯周期数，默认为20
        volatility_max_pct : float, optional
            最大可接受波动率百分比，超过此值仓位为0，默认为5.0（5%）
        """
        super().__init__(
            name, 
            description, 
            target_risk_pct=target_risk_pct,
            volatility_lookback=volatility_lookback,
            volatility_max_pct=volatility_max_pct
        )
    
    def validate_params(self) -> None:
        """
        验证参数有效性
        
        Raises
        ------
        ValueError
            如果参数无效
        """
        target_risk = self.params.get("target_risk_pct")
        if target_risk <= 0:
            raise ValueError("目标风险百分比必须为正数")
            
        lookback = self.params.get("volatility_lookback")
        if lookback <= 0:
            raise ValueError("波动率回溯周期必须为正整数")
            
        max_vol = self.params.get("volatility_max_pct")
        if max_vol <= 0:
            raise ValueError("最大可接受波动率必须为正数")
    
    def _calculate_volatility(self, data: pd.DataFrame) -> float:
        """
        计算波动率
        
        Parameters
        ----------
        data : pd.DataFrame
            市场数据，需要包含收盘价
        
        Returns
        -------
        float
            波动率（百分比）
        """
        if data is None or len(data) == 0:
            return float('inf')  # 如果没有数据，返回无穷大表示极高风险
        
        lookback = self.params.get("volatility_lookback")
        
        # 确保数据足够
        if len(data) < lookback:
            lookback = len(data)
        
        # 计算日收益率
        if 'close' in data.columns:
            returns = data['close'].pct_change().dropna()
            
            # 计算波动率（标准差）
            volatility = returns.std() * 100  # 转换为百分比
            
            return volatility
        
        return float('inf')  # 如果没有收盘价数据，返回无穷大
    
    def calculate_position_size(self, 
                               context: Dict[str, Any], 
                               data: pd.DataFrame = None, 
                               risk_amount: float = None) -> float:
        """
        计算仓位规模
        
        Parameters
        ----------
        context : Dict[str, Any]
            交易上下文，包含当前资金、持仓和历史交易等信息
        data : pd.DataFrame, optional
            相关市场数据，需要包含收盘价，默认为None
        risk_amount : float, optional
            愿意承担的风险金额，默认为None
        
        Returns
        -------
        float
            按波动率调整的仓位规模（金额）
        """
        capital = context.get("capital", 0)
        target_risk_pct = self.params.get("target_risk_pct")
        max_vol_pct = self.params.get("volatility_max_pct")
        
        # 计算当前波动率
        volatility = self._calculate_volatility(data)
        
        # 如果波动率过高，不交易
        if volatility >= max_vol_pct:
            return 0
        
        # 根据波动率调整仓位规模
        # 目标风险 / 当前波动率 = 仓位比例
        position_ratio = target_risk_pct / volatility if volatility > 0 else 0
        
        # 限制最大仓位比例为50%
        position_ratio = min(position_ratio, 0.5)
        
        return capital * position_ratio


class MaxDrawdownSizer(PositionSizer):
    """
    最大回撤调整仓位规模计算器
    
    根据历史最大回撤调整仓位规模，回撤越大，仓位越小。
    """
    
    def __init__(self, 
                name: str = "MaxDrawdown", 
                description: str = "", 
                max_allowed_dd_pct: float = 10.0,
                base_position_pct: float = 10.0,
                lookback_periods: int = 50):
        """
        初始化最大回撤调整仓位规模计算器
        
        Parameters
        ----------
        name : str, optional
            计算器名称，默认为"MaxDrawdown"
        description : str, optional
            计算器描述，默认为空字符串
        max_allowed_dd_pct : float, optional
            最大可接受回撤百分比，默认为10.0（10%）
        base_position_pct : float, optional
            基础仓位百分比，默认为10.0（10%）
        lookback_periods : int, optional
            计算最大回撤的回溯期数，默认为50
        """
        super().__init__(
            name, 
            description, 
            max_allowed_dd_pct=max_allowed_dd_pct,
            base_position_pct=base_position_pct,
            lookback_periods=lookback_periods
        )
    
    def validate_params(self) -> None:
        """
        验证参数有效性
        
        Raises
        ------
        ValueError
            如果参数无效
        """
        max_dd = self.params.get("max_allowed_dd_pct")
        if max_dd <= 0:
            raise ValueError("最大可接受回撤百分比必须为正数")
            
        base_pos = self.params.get("base_position_pct")
        if base_pos <= 0 or base_pos > 100:
            raise ValueError("基础仓位百分比必须在(0, 100]范围内")
            
        lookback = self.params.get("lookback_periods")
        if lookback <= 0:
            raise ValueError("回溯期数必须为正整数")
    
    def _calculate_max_drawdown(self, equity_curve: List[float]) -> float:
        """
        计算最大回撤
        
        Parameters
        ----------
        equity_curve : List[float]
            资金曲线
        
        Returns
        -------
        float
            最大回撤（百分比）
        """
        if not equity_curve:
            return 0
        
        peak = equity_curve[0]
        max_dd = 0
        
        for value in equity_curve:
            if value > peak:
                peak = value
            
            dd = (peak - value) / peak * 100 if peak > 0 else 0
            max_dd = max(max_dd, dd)
        
        return max_dd
    
    def calculate_position_size(self, 
                               context: Dict[str, Any], 
                               data: pd.DataFrame = None, 
                               risk_amount: float = None) -> float:
        """
        计算仓位规模
        
        Parameters
        ----------
        context : Dict[str, Any]
            交易上下文，包含当前资金、持仓和历史交易等信息
        data : pd.DataFrame, optional
            相关市场数据，默认为None
        risk_amount : float, optional
            愿意承担的风险金额，默认为None
        
        Returns
        -------
        float
            按最大回撤调整的仓位规模（金额）
        """
        capital = context.get("capital", 0)
        max_allowed_dd = self.params.get("max_allowed_dd_pct")
        base_position = self.params.get("base_position_pct") / 100
        
        # 获取资金曲线
        equity_curve = context.get("equity_curve", [])
        lookback = self.params.get("lookback_periods")
        
        # 取最近N个点
        recent_equity = equity_curve[-lookback:] if len(equity_curve) > lookback else equity_curve
        
        # 计算最大回撤
        max_dd = self._calculate_max_drawdown(recent_equity)
        
        # 根据回撤调整仓位规模
        # 如果回撤超过最大可接受值，不交易
        if max_dd >= max_allowed_dd:
            position_ratio = 0
        else:
            # 回撤越接近最大可接受值，仓位越小
            dd_ratio = 1 - (max_dd / max_allowed_dd)
            position_ratio = base_position * dd_ratio
        
        return capital * position_ratio 