#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
指标可视化示例

展示如何使用IndicatorVisualizer类来可视化各种技术指标。
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 确保能够导入父级目录中的模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from indicators.utils.visualization import IndicatorVisualizer
from indicators.trend.moving_averages import SMA, EMA
from indicators.oscillators.rsi import RSI
from indicators.volatility.bollinger_bands import BollingerBands
from indicators.volume.obv import OBV


def generate_sample_data(n_samples: int = 200):
    """
    生成样本数据
    
    Parameters
    ----------
    n_samples : int, optional
        样本数量, 默认为200
        
    Returns
    -------
    pd.DataFrame
        包含OHLCV数据的DataFrame
    """
    # 创建日期索引
    end_date = datetime.now()
    start_date = end_date - timedelta(days=n_samples)
    date_range = pd.date_range(start_date, end_date, periods=n_samples)
    
    # 生成收盘价
    np.random.seed(42)
    close = 100 + np.cumsum(np.random.normal(0, 1, n_samples))
    
    # 生成OHLCV数据
    data = pd.DataFrame(index=date_range)
    data['close'] = close
    data['open'] = data['close'].shift(1) * (1 + np.random.normal(0, 0.01, n_samples))
    data['high'] = np.maximum(data['open'], data['close']) * (1 + np.abs(np.random.normal(0, 0.005, n_samples)))
    data['low'] = np.minimum(data['open'], data['close']) * (1 - np.abs(np.random.normal(0, 0.005, n_samples)))
    data['volume'] = np.random.lognormal(10, 1, n_samples)
    
    # 填充第一行
    data.iloc[0, data.columns.get_indexer(['open'])] = close[0] * 0.99
    
    return data


def basic_visualization_example():
    """基础可视化示例"""
    # 生成样本数据
    data = generate_sample_data()
    
    # 创建可视化器
    visualizer = IndicatorVisualizer()
    
    # 设置数据
    visualizer.set_data(data)
    
    # 创建网格布局
    visualizer.add_grid_layout(height_ratios=[3, 1])
    
    # 添加价格子图
    visualizer.add_subplot_with_grid("price", 0)
    
    # 添加成交量子图
    visualizer.add_subplot_with_grid("volume", 1, sharex="price")
    
    # 绘制蜡烛图
    visualizer.plot_candle()
    
    # 绘制成交量
    visualizer.plot_volume()
    
    # 设置标题
    visualizer.set_title("Basic Price and Volume Chart")
    
    # 显示图表
    visualizer.show()


def moving_averages_example():
    """移动平均线示例"""
    # 生成样本数据
    data = generate_sample_data()
    
    # 计算SMA
    sma20 = SMA(window=20)
    sma50 = SMA(window=50)
    
    # 计算EMA
    ema20 = EMA(window=20)
    
    # 创建可视化器
    visualizer = IndicatorVisualizer(theme="tradingview")
    
    # 设置数据
    visualizer.set_data(data)
    
    # 创建网格布局
    visualizer.add_grid_layout(height_ratios=[3, 1])
    
    # 添加价格子图
    visualizer.add_subplot_with_grid("price", 0)
    
    # 添加成交量子图
    visualizer.add_subplot_with_grid("volume", 1, sharex="price")
    
    # 绘制蜡烛图
    visualizer.plot_candle()
    
    # 绘制移动平均线
    visualizer.plot_indicator(sma20, ax_name="price")
    visualizer.plot_indicator(sma50, ax_name="price")
    visualizer.plot_indicator(ema20, ax_name="price")
    
    # 绘制成交量
    visualizer.plot_volume()
    
    # 设置标题
    visualizer.set_title("Price and Moving Averages")
    
    # 显示图表
    visualizer.show()


def multiple_indicators_example():
    """多指标示例"""
    # 生成样本数据
    data = generate_sample_data()
    
    # 计算指标
    sma20 = SMA(window=20)
    bb = BollingerBands(window=20)
    rsi = RSI(window=14)
    obv = OBV()
    
    # 创建可视化器
    visualizer = IndicatorVisualizer(theme="dark")
    
    # 设置数据
    visualizer.set_data(data)
    
    # 创建网格布局
    visualizer.add_grid_layout(height_ratios=[3, 1, 1, 1])
    
    # 添加价格子图
    visualizer.add_subplot_with_grid("price", 0)
    
    # 添加RSI子图
    visualizer.add_subplot_with_grid("rsi", 1, sharex="price")
    
    # 添加OBV子图
    visualizer.add_subplot_with_grid("obv", 2, sharex="price")
    
    # 添加成交量子图
    visualizer.add_subplot_with_grid("volume", 3, sharex="price")
    
    # 绘制蜡烛图
    visualizer.plot_candle()
    
    # 绘制布林带
    visualizer.plot_indicator(bb, ax_name="price")
    
    # 绘制SMA
    visualizer.plot_indicator(sma20, ax_name="price")
    
    # 绘制RSI
    visualizer.plot_indicator(rsi, ax_name="rsi")
    
    # 绘制OBV
    visualizer.plot_indicator(obv, ax_name="obv")
    
    # 绘制成交量
    visualizer.plot_volume()
    
    # 设置标题
    visualizer.set_title("Multiple Indicators Technical Analysis Chart")
    
    # 显示图表
    visualizer.show()


def signals_example():
    """交易信号示例"""
    # 生成样本数据
    data = generate_sample_data()
    
    # 计算指标
    sma20 = SMA(window=20)
    sma50 = SMA(window=50)
    
    # 计算指标值
    result = sma20.calculate(data)
    result = sma50.calculate(result)
    
    # 生成交叉信号
    signals = pd.DataFrame(index=data.index)
    signals['buy_signal'] = 0.0
    signals['sell_signal'] = 0.0
    
    # 创建金叉/死叉信号 - 避免使用fillna导致的警告
    sma20_above = result['SMA_20'] > result['SMA_50']
    # 首先处理前一个值的缺失情况
    prev_above = sma20_above.shift(1)
    prev_above.iloc[0] = False  # 第一个值设为False而不是NaN
    
    # 现在计算交叉
    cross_above = sma20_above & ~prev_above
    cross_below = ~sma20_above & prev_above
    
    signals.loc[cross_above, 'buy_signal'] = 1.0
    signals.loc[cross_below, 'sell_signal'] = 1.0
    
    # 创建可视化器
    visualizer = IndicatorVisualizer()
    
    # 设置数据
    visualizer.set_data(result)
    
    # 创建网格布局
    visualizer.add_grid_layout(height_ratios=[3, 1])
    
    # 添加价格子图
    visualizer.add_subplot_with_grid("price", 0)
    
    # 添加成交量子图
    visualizer.add_subplot_with_grid("volume", 1, sharex="price")
    
    # 绘制蜡烛图
    visualizer.plot_candle()
    
    # 绘制移动平均线
    visualizer.plot_line("SMA_20", ax_name="price", label="SMA(20)")
    visualizer.plot_line("SMA_50", ax_name="price", label="SMA(50)")
    
    # 绘制信号
    visualizer.plot_signals(signals)
    
    # 绘制成交量
    visualizer.plot_volume()
    
    # 设置标题
    visualizer.set_title("Moving Average Crossover Signals ")
    
    # 显示图表
    visualizer.show()


def theme_comparison_example():
    """主题比较示例"""
    # 生成样本数据
    data = generate_sample_data(100)
    
    # 计算指标
    sma20 = SMA(window=20)
    bb = BollingerBands(window=20)
    
    # 计算指标值
    result = sma20.calculate(data)
    result = bb.calculate(result)
    
    # 创建三个图形，每个使用不同的主题
    themes = ["default", "dark", "tradingview"]
    
    # 直接使用单独的图形显示不同主题
    fig, axes = plt.subplots(3, 1, figsize=(15, 12))
    
    for i, theme in enumerate(themes):
        # 创建可视化器
        visualizer = IndicatorVisualizer(theme=theme)
        
        # 设置数据
        visualizer.set_data(result)
        
        # 直接使用matplotlib提供的子图
        ax = axes[i]
        
        # 清除当前子图内容
        ax.clear()
        
        # 设置标题
        ax.set_title(f"主题: {theme}")
        
        # 应用主题样式到当前子图
        ax.set_facecolor(visualizer._theme_config["axes.facecolor"])
        ax.tick_params(axis='both', colors=visualizer._theme_config["xtick.color"])
        ax.spines['bottom'].set_color(visualizer._theme_config["axes.edgecolor"])
        ax.spines['top'].set_color(visualizer._theme_config["axes.edgecolor"])
        ax.spines['left'].set_color(visualizer._theme_config["axes.edgecolor"])
        ax.spines['right'].set_color(visualizer._theme_config["axes.edgecolor"])
        ax.yaxis.label.set_color(visualizer._theme_config["axes.labelcolor"])
        ax.xaxis.label.set_color(visualizer._theme_config["axes.labelcolor"])
        
        if visualizer._theme_config["axes.grid"]:
            ax.grid(True, color=visualizer._theme_config["grid.color"], 
                   linestyle=visualizer._theme_config["grid.linestyle"])
        
        # 绘制蜡烛图
        if 'open' in result.columns and 'high' in result.columns and 'low' in result.columns and 'close' in result.columns:
            up = result['close'] >= result['open']
            down = result['close'] < result['open']
            
            # 上涨蜡烛
            if up.any():
                ax.bar(result.index[up], result['high'][up] - result['low'][up], 
                       bottom=result['low'][up], width=0.6, 
                       color=visualizer._theme_config["price.up.color"], alpha=0.3)
                ax.bar(result.index[up], result['close'][up] - result['open'][up], 
                       bottom=result['open'][up], width=0.6, 
                       color=visualizer._theme_config["price.up.color"])
            
            # 下跌蜡烛
            if down.any():
                ax.bar(result.index[down], result['high'][down] - result['low'][down], 
                       bottom=result['low'][down], width=0.6, 
                       color=visualizer._theme_config["price.down.color"], alpha=0.3)
                ax.bar(result.index[down], result['close'][down] - result['open'][down], 
                       bottom=result['open'][down], width=0.6, 
                       color=visualizer._theme_config["price.down.color"])
        
        # 绘制SMA
        ax.plot(result.index, result['SMA_20'], label='SMA(20)', linewidth=1.5)
        
        # 绘制布林带
        ax.plot(result.index, result['BB_upper'], label='BB Upper', linestyle='--', linewidth=1.5)
        ax.plot(result.index, result['BB_middle'], label='BB Middle', linestyle='-', linewidth=1.5)
        ax.plot(result.index, result['BB_lower'], label='BB Lower', linestyle='--', linewidth=1.5)
        
        # 添加图例
        ax.legend()
    
    fig.tight_layout()
    plt.show()


if __name__ == "__main__":
    print("指标可视化示例")
    print("请选择要运行的示例:")
    print("1. 基础可视化示例")
    print("2. 移动平均线示例")
    print("3. 多指标示例")
    print("4. 交易信号示例")
    print("5. 主题比较示例")
    print("6. 运行所有示例")
    
    choice = input("请输入选择 (1-6): ")
    
    if choice == '1' or choice == '6':
        print("运行基础可视化示例...")
        basic_visualization_example()
        
    if choice == '2' or choice == '6':
        print("运行移动平均线示例...")
        moving_averages_example()
        
    if choice == '3' or choice == '6':
        print("运行多指标示例...")
        multiple_indicators_example()
        
    if choice == '4' or choice == '6':
        print("运行交易信号示例...")
        signals_example()
        
    if choice == '5' or choice == '6':
        print("运行主题比较示例...")
        theme_comparison_example() 