"""
数据API测试

测试数据API的功能和接口。
"""

import os
import sys
import unittest
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

import pandas as pd
import numpy as np

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
sys.path.insert(0, project_root)

# 导入必要的类
from data.base import DataSource, DataStorage
from data.storage.optimized_storage import OptimizedStorage
from data.api import DataAPI  # 直接从data.api导入DataAPI类


class MockDataSource(DataSource):
    """模拟数据源"""
    
    def __init__(self):
        self.fetch_data_mock = MagicMock()
        self.get_symbols_mock = MagicMock()
        self.get_timeframes_mock = MagicMock()
        
        # 设置默认返回值
        self.get_symbols_mock.return_value = ['BTC/USDT', 'ETH/USDT']
        self.get_timeframes_mock.return_value = ['1m', '5m', '1h', '1d']
        
        # 创建模拟数据
        self._create_mock_data()
    
    def _create_mock_data(self):
        """创建模拟数据"""
        # 创建日期范围
        end_time = datetime.now()
        start_time = end_time - timedelta(days=10)
        dates = pd.date_range(start=start_time, end=end_time, freq='1D')
        
        # 创建OHLCV数据 - 使用固定值而不是随机值避免类型问题
        data = pd.DataFrame({
            'open': [10000 + i for i in range(len(dates))],
            'high': [10500 + i for i in range(len(dates))],
            'low': [9500 + i for i in range(len(dates))],
            'close': [10100 + i for i in range(len(dates))],
            'volume': [1000 + i * 10 for i in range(len(dates))]
        }, index=dates)
        
        self.mock_data = data
        self.fetch_data_mock.return_value = self.mock_data
    
    def fetch_data(self, symbol, timeframe, start_time, end_time):
        """获取市场数据"""
        return self.fetch_data_mock(symbol, timeframe, start_time, end_time)
    
    def get_symbols(self):
        """获取支持的交易对列表"""
        return self.get_symbols_mock()
    
    def get_timeframes(self):
        """获取支持的时间周期列表"""
        return self.get_timeframes_mock()


class TestDataAPI(unittest.TestCase):
    """测试数据API"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时存储目录
        self.temp_dir = os.path.join(os.path.dirname(__file__), 'temp_data')
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # 创建存储对象
        self.storage = OptimizedStorage(self.temp_dir)
        
        # 创建模拟数据源
        self.mock_source = MockDataSource()
        
        # 创建API对象
        self.api = DataAPI(self.storage)
        
        # 注册数据源
        self.api.register_data_source('mock', self.mock_source, is_default=True)
    
    def tearDown(self):
        """测试后清理"""
        # 清理临时文件
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_get_data(self):
        """测试获取数据"""
        symbol = 'BTC/USDT'
        timeframe = '1d'
        end_time = datetime.now()
        start_time = end_time - timedelta(days=5)
        
        # 获取数据
        data = self.api.get_data(symbol, timeframe, start_time, end_time)
        
        # 验证数据
        self.assertFalse(data.empty)
        self.assertEqual(len(data), len(self.mock_source.mock_data))
        self.assertTrue('open' in data.columns)
        self.assertTrue('high' in data.columns)
        self.assertTrue('low' in data.columns)
        self.assertTrue('close' in data.columns)
        self.assertTrue('volume' in data.columns)
        
        # 验证调用
        self.mock_source.fetch_data_mock.assert_called_once()
    
    def test_clean_data(self):
        """测试清洗数据"""
        # 创建测试数据
        dates = pd.date_range(start='2023-01-01', periods=5, freq='1D')
        data = pd.DataFrame({
            'open': [100, 101, np.nan, 103, 104],
            'high': [105, 106, np.nan, 108, 109],
            'low': [95, 96, np.nan, 98, 99],
            'close': [102, 103, np.nan, 105, 106],
            'volume': [1000, 1100, np.nan, 1300, 1400]
        }, index=dates)
        
        # 清洗数据 - 禁用时间戳对齐功能，避免频率转换错误
        cleaned_data = self.api.clean_data(data, fill_method='ffill', align_timestamps_flag=False)
        
        # 验证数据
        self.assertFalse(cleaned_data.empty)
        self.assertEqual(len(cleaned_data), len(data))
        self.assertFalse(cleaned_data.isna().any().any())  # 没有缺失值
    
    def test_transform_data(self):
        """测试转换数据"""
        # 创建测试数据
        dates = pd.date_range(start='2023-01-01', periods=5, freq='1D')
        data = pd.DataFrame({
            'open': [100, 101, 102, 103, 104],
            'high': [105, 106, 107, 108, 109],
            'low': [95, 96, 97, 98, 99],
            'close': [102, 103, 104, 105, 106],
            'volume': [1000, 1100, 1200, 1300, 1400]
        }, index=dates)
        
        # 转换数据
        transformed_data = self.api.transform_data(
            data, 
            calculate_returns_flag=True,
            returns_type='pct_change'
        )
        
        # 验证数据
        self.assertFalse(transformed_data.empty)
        self.assertEqual(len(transformed_data), len(data))
        self.assertTrue('close_return' in transformed_data.columns)
    
    def test_add_technical_indicators(self):
        """测试添加技术指标"""
        # 创建测试数据
        dates = pd.date_range(start='2023-01-01', periods=30, freq='1D')
        data = pd.DataFrame({
            'open': [100 + i for i in range(len(dates))],
            'high': [105 + i for i in range(len(dates))],
            'low': [95 + i for i in range(len(dates))],
            'close': [102 + i for i in range(len(dates))],
            'volume': [1000 + i * 10 for i in range(len(dates))]
        }, index=dates)
        
        # 添加技术指标
        data_with_indicators = self.api.add_technical_indicators(
            data,
            add_ma=True,
            ma_periods=[5, 10],
            add_rsi=True,
            rsi_period=14
        )
        
        # 验证数据
        self.assertFalse(data_with_indicators.empty)
        self.assertEqual(len(data_with_indicators), len(data))
        self.assertTrue('sma_5' in data_with_indicators.columns)
        self.assertTrue('sma_10' in data_with_indicators.columns)
        self.assertTrue('rsi' in data_with_indicators.columns)
    
    def test_prepare_strategy_data(self):
        """测试准备策略数据"""
        symbol = 'BTC/USDT'
        timeframe = '1d'
        end_time = datetime.now()
        start_time = end_time - timedelta(days=30)
        
        # 给mock_source.fetch_data_mock设置不同的return_value，使其返回固定值
        # 创建测试用的OHLCV数据 
        dates = pd.date_range(start=start_time, end=end_time, freq='1D')
        mock_data = pd.DataFrame({
            'open': [100 + i for i in range(len(dates))],
            'high': [105 + i for i in range(len(dates))],
            'low': [95 + i for i in range(len(dates))],
            'close': [102 + i for i in range(len(dates))],
            'volume': [1000 + i * 10 for i in range(len(dates))]
        }, index=dates)
        self.mock_source.fetch_data_mock.return_value = mock_data
        
        # 使用猴子补丁临时替换prepare_strategy_data方法以避免时间戳错误
        original_clean_data = self.api.clean_data
        
        def patched_clean_data(data, **kwargs):
            # 替换成不使用时间戳对齐的版本
            return original_clean_data(data, align_timestamps_flag=False, **kwargs)
        
        # 应用猴子补丁
        self.api.clean_data = patched_clean_data
        
        try:
            # 准备策略数据
            strategy_data = self.api.prepare_strategy_data(
                symbol,
                timeframe,
                start_time,
                end_time,
                indicators={'ma': {'ma_periods': [5, 10]}, 'rsi': {'rsi_period': 14}},
                clean_data=True,
                transform_data=True,
                transform_params={'calculate_returns_flag': True}
            )
            
            # 验证数据
            self.assertFalse(strategy_data.empty)
            self.assertTrue('sma_5' in strategy_data.columns)
            self.assertTrue('sma_10' in strategy_data.columns)
            self.assertTrue('rsi' in strategy_data.columns)
            self.assertTrue('close_return' in strategy_data.columns)
        finally:
            # 恢复原始方法
            self.api.clean_data = original_clean_data


if __name__ == '__main__':
    unittest.main() 