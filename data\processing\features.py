"""
特征工程模块

提供技术指标计算功能，生成用于量化分析和交易的特征。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any, Callable

from data.structures import OHLCVColumns


def calculate_volatility(df: pd.DataFrame, 
                        window: int = 20, 
                        column: str = OHLCVColumns.CLOSE, 
                        annualize: bool = True,
                        scaling: float = 252.0) -> pd.DataFrame:
    """
    计算价格波动率
    
    Args:
        df: 包含价格数据的DataFrame
        window: 滚动窗口大小，默认为20个周期
        column: 用于计算波动率的列，默认为收盘价
        annualize: 是否年化波动率
        scaling: 年化因子，日数据通常为252，小时数据为252*24，分钟数据需要相应调整
        
    Returns:
        添加了波动率指标的DataFrame
    """
    # 确保指定的列存在
    if column not in df.columns:
        raise ValueError(f"列 {column} 不存在于DataFrame中")
    
    # 复制DataFrame以避免修改原始数据
    df_features = df.copy()
    
    # 计算收益率
    returns = df_features[column].pct_change().dropna()
    
    # 计算滚动波动率（标准差）
    df_features['volatility'] = returns.rolling(window=window).std()
    
    # 年化波动率
    if annualize:
        df_features['volatility'] = df_features['volatility'] * np.sqrt(scaling)
    
    # 计算GARCH(1,1)波动率估计
    # 注意：这里使用的是一个简化的GARCH(1,1)实现
    if len(returns) > 30:  # 确保有足够的数据
        # 初始化波动率序列
        h = np.zeros(len(returns))
        
        # 设置GARCH参数，这些可以通过更复杂的拟合来确定
        omega = 0.00001
        alpha = 0.1
        beta = 0.85
        
        # 初始波动率
        h[0] = returns.iloc[0]**2
        
        # 计算GARCH波动率
        for t in range(1, len(returns)):
            h[t] = omega + alpha * returns.iloc[t-1]**2 + beta * h[t-1]
        
        # 添加到DataFrame
        garch_vol = pd.Series(np.sqrt(h), index=returns.index)
        if annualize:
            garch_vol = garch_vol * np.sqrt(scaling)
        df_features['garch_volatility'] = garch_vol
    
    # 添加高低价差波动率指标
    if all(col in df_features.columns for col in [OHLCVColumns.HIGH, OHLCVColumns.LOW]):
        # 真实波动率指标
        high_low_range = df_features[OHLCVColumns.HIGH] / df_features[OHLCVColumns.LOW] - 1
        df_features['high_low_volatility'] = high_low_range.rolling(window=window).mean()
        
        if annualize:
            df_features['high_low_volatility'] = df_features['high_low_volatility'] * np.sqrt(scaling)
    
    return df_features


def calculate_moving_averages(df: pd.DataFrame, 
                             column: str = OHLCVColumns.CLOSE, 
                             windows: List[int] = [5, 10, 20, 50, 200]) -> pd.DataFrame:
    """
    计算移动平均线
    
    Args:
        df: 包含价格数据的DataFrame
        column: 用于计算移动平均的列，默认为收盘价
        windows: 移动平均周期列表
        
    Returns:
        添加了移动平均指标的DataFrame
    """
    # 确保指定的列存在
    if column not in df.columns:
        raise ValueError(f"列 {column} 不存在于DataFrame中")
    
    # 复制DataFrame以避免修改原始数据
    df_features = df.copy()
    
    # 计算简单移动平均
    for window in windows:
        df_features[f'sma_{window}'] = df_features[column].rolling(window=window).mean()
        
        # 计算指数移动平均
        df_features[f'ema_{window}'] = df_features[column].ewm(span=window, adjust=False).mean()
    
    # 计算移动平均线交叉信号
    if len(windows) >= 2:
        # 对窗口进行排序
        sorted_windows = sorted(windows)
        
        # 计算快速EMA和慢速EMA之间的交叉信号
        fast_ema = df_features[f'ema_{sorted_windows[0]}']
        slow_ema = df_features[f'ema_{sorted_windows[-1]}']
        
        # 交叉信号：快线高于慢线为1，否则为0
        df_features['ema_cross'] = (fast_ema > slow_ema).astype(int)
        
        # 计算交叉点
        df_features['ema_cross_signal'] = df_features['ema_cross'].diff().fillna(0).astype(int)
    
    # 计算价格与长期移动平均线的关系
    if len(windows) > 0:
        long_term_ma = df_features[f'sma_{max(windows)}']
        df_features['price_to_ma'] = df_features[column] / long_term_ma - 1
    
    return df_features


def calculate_rsi(df: pd.DataFrame, 
                 column: str = OHLCVColumns.CLOSE, 
                 window: int = 14) -> pd.DataFrame:
    """
    计算相对强弱指标(RSI)
    
    Args:
        df: 包含价格数据的DataFrame
        column: 用于计算RSI的列，默认为收盘价
        window: 计算周期，默认为14
        
    Returns:
        添加了RSI指标的DataFrame
    """
    # 确保指定的列存在
    if column not in df.columns:
        raise ValueError(f"列 {column} 不存在于DataFrame中")
    
    # 复制DataFrame以避免修改原始数据
    df_features = df.copy()
    
    # 计算价格变化
    delta = df_features[column].diff()
    
    # 创建包含涨跌幅的DataFrame
    changes = pd.DataFrame(index=delta.index)
    changes['gain'] = delta.clip(lower=0)  # 只保留正值，负值变为0
    changes['loss'] = -delta.clip(upper=0)  # 只保留负值，正值变为0，并转为正数
    
    # 计算平均上涨和下跌
    avg_gain = changes['gain'].rolling(window=window).mean()
    avg_loss = changes['loss'].rolling(window=window).mean()
    
    # 计算相对强度，处理可能的除零情况
    eps = np.finfo(float).eps  # 一个非常小的数，避免除零
    rs = avg_gain / avg_loss.replace(0, eps)
    
    # 计算RSI
    df_features['rsi'] = 100 - (100 / (1 + rs))
    
    # 添加RSI超买超卖信号
    df_features['rsi_overbought'] = (df_features['rsi'] > 70).astype(int)
    df_features['rsi_oversold'] = (df_features['rsi'] < 30).astype(int)
    
    return df_features


def calculate_macd(df: pd.DataFrame, 
                  column: str = OHLCVColumns.CLOSE, 
                  fast_period: int = 12, 
                  slow_period: int = 26, 
                  signal_period: int = 9) -> pd.DataFrame:
    """
    计算移动平均线收敛/发散指标(MACD)
    
    Args:
        df: 包含价格数据的DataFrame
        column: 用于计算MACD的列，默认为收盘价
        fast_period: 快速EMA周期，默认为12
        slow_period: 慢速EMA周期，默认为26
        signal_period: 信号线EMA周期，默认为9
        
    Returns:
        添加了MACD指标的DataFrame
    """
    # 确保指定的列存在
    if column not in df.columns:
        raise ValueError(f"列 {column} 不存在于DataFrame中")
    
    # 复制DataFrame以避免修改原始数据
    df_features = df.copy()
    
    # 计算快速EMA和慢速EMA
    fast_ema = df_features[column].ewm(span=fast_period, adjust=False).mean()
    slow_ema = df_features[column].ewm(span=slow_period, adjust=False).mean()
    
    # 计算MACD线
    df_features['macd'] = fast_ema - slow_ema
    
    # 计算信号线
    df_features['macd_signal'] = df_features['macd'].ewm(span=signal_period, adjust=False).mean()
    
    # 计算MACD柱状图
    df_features['macd_histogram'] = df_features['macd'] - df_features['macd_signal']
    
    # 计算MACD交叉信号
    df_features['macd_cross'] = (df_features['macd'] > df_features['macd_signal']).astype(int)
    df_features['macd_cross_signal'] = df_features['macd_cross'].diff().fillna(0).astype(int)
    
    return df_features 

def calculate_bollinger_bands(df: pd.DataFrame, 
                             column: str = OHLCVColumns.CLOSE, 
                             window: int = 20, 
                             num_std: float = 2.0) -> pd.DataFrame:
    """
    计算布林带指标
    
    Args:
        df: 包含价格数据的DataFrame
        column: 用于计算布林带的列，默认为收盘价
        window: 移动平均窗口大小，默认为20
        num_std: 标准差倍数，默认为2.0
        
    Returns:
        添加了布林带指标的DataFrame
    """
    # 确保指定的列存在
    if column not in df.columns:
        raise ValueError(f"列 {column} 不存在于DataFrame中")
    
    # 复制DataFrame以避免修改原始数据
    df_features = df.copy()
    
    # 计算移动平均线
    df_features[f'bbands_middle_{column}'] = df_features[column].rolling(window=window).mean()
    
    # 计算移动标准差
    rolling_std = df_features[column].rolling(window=window).std()
    
    # 计算上轨和下轨
    df_features[f'bbands_upper_{column}'] = df_features[f'bbands_middle_{column}'] + (rolling_std * num_std)
    df_features[f'bbands_lower_{column}'] = df_features[f'bbands_middle_{column}'] - (rolling_std * num_std)
    
    # 计算带宽（Bandwidth）
    df_features['bbands_bandwidth'] = (df_features[f'bbands_upper_{column}'] - df_features[f'bbands_lower_{column}']) / df_features[f'bbands_middle_{column}']
    
    # 计算百分比B（%B）- 价格在带区间中的相对位置
    df_features['bbands_percent_b'] = (df_features[column] - df_features[f'bbands_lower_{column}']) / (df_features[f'bbands_upper_{column}'] - df_features[f'bbands_lower_{column}'])
    
    # 添加超买超卖信号
    df_features['bbands_overbought'] = (df_features[column] > df_features[f'bbands_upper_{column}']).astype(int)
    df_features['bbands_oversold'] = (df_features[column] < df_features[f'bbands_lower_{column}']).astype(int)
    
    return df_features 