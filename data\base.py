"""
数据模块基础类和接口定义

包含了DataSource和DataStorage抽象基类，定义了数据获取和存储的标准接口。
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Optional, Dict, Any, Union

import pandas as pd


class DataSource(ABC):
    """
    数据源抽象基类
    
    定义了从数据源获取数据的标准接口。所有具体的数据源实现类都应该继承此类。
    """
    
    @abstractmethod
    def fetch_data(self, symbol: str, timeframe: str, 
                  start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """
        获取指定时间范围的市场数据
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期，如"1m", "1h", "1d"等
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        pass
    
    @abstractmethod
    def get_symbols(self) -> List[str]:
        """
        获取支持的交易对列表
        
        Returns:
            交易对代码列表
        """
        pass
    
    @abstractmethod
    def get_timeframes(self) -> List[str]:
        """
        获取支持的时间周期列表
        
        Returns:
            时间周期列表
        """
        pass
    
    def validate_symbol(self, symbol: str) -> bool:
        """
        验证交易对是否有效
        
        Args:
            symbol: 交易对代码
            
        Returns:
            如果交易对有效返回True，否则返回False
        """
        return symbol in self.get_symbols()
    
    def validate_timeframe(self, timeframe: str) -> bool:
        """
        验证时间周期是否有效
        
        Args:
            timeframe: 时间周期
            
        Returns:
            如果时间周期有效返回True，否则返回False
        """
        return timeframe in self.get_timeframes()


class DataStorage(ABC):
    """
    数据存储抽象基类
    
    定义了数据存储的标准接口。所有具体的存储实现类都应该继承此类。
    """
    
    @abstractmethod
    def save_data(self, data: pd.DataFrame, symbol: str, 
                 timeframe: str) -> None:
        """
        保存市场数据
        
        Args:
            data: 包含OHLCV数据的DataFrame
            symbol: 交易对或资产代码
            timeframe: 时间周期
        """
        pass
    
    @abstractmethod
    def load_data(self, symbol: str, timeframe: str, 
                 start_time: Optional[datetime] = None, 
                 end_time: Optional[datetime] = None) -> pd.DataFrame:
        """
        加载市场数据
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            start_time: 开始时间 (可选)
            end_time: 结束时间 (可选)
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        pass
    
    @abstractmethod
    def has_data(self, symbol: str, timeframe: str) -> bool:
        """
        检查是否有指定的市场数据
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            
        Returns:
            如果存在数据返回True，否则返回False
        """
        pass
    
    @abstractmethod
    def get_symbols(self) -> List[str]:
        """
        获取存储中的所有交易对
        
        Returns:
            交易对代码列表
        """
        pass
    
    @abstractmethod
    def get_timeframes(self, symbol: Optional[str] = None) -> List[str]:
        """
        获取存储中的所有时间周期
        
        Args:
            symbol: 交易对或资产代码 (可选)
            
        Returns:
            时间周期列表
        """
        pass
    
    @abstractmethod
    def get_data_info(self, symbol: str, timeframe: str) -> Dict[str, Any]:
        """
        获取数据信息
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            
        Returns:
            包含数据信息的字典，如开始时间、结束时间、数据点数量等
        """
        pass 