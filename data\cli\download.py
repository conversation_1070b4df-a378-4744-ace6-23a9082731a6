"""
数据下载命令

提供从加密货币交易所下载历史数据的命令行工具。
"""

import os
import sys
import argparse
import logging
from datetime import datetime, timedelta

import pandas as pd

from data.sources.ccxt_source import CCXTDataSource, CCXTConfig
from data.storage.optimized_storage import OptimizedStorage


def download_command():
    """
    数据下载命令入口点
    """
    parser = argparse.ArgumentParser(description='从交易所下载历史数据')
    
    # 基本参数
    parser.add_argument('symbol', help='交易对，如BTC/USDT')
    parser.add_argument('timeframe', help='时间周期，如1m, 1h, 1d')
    parser.add_argument('--exchange', '-e', default='binance', help='交易所ID (默认: binance)')
    
    # 时间范围
    parser.add_argument('--start', '-s', help='开始时间，格式为YYYY-MM-DD或YYYY-MM-DD HH:MM:SS')
    parser.add_argument('--end', '-n', help='结束时间，格式为YYYY-MM-DD或YYYY-MM-DD HH:MM:SS')
    parser.add_argument('--days', '-d', type=int, help='要下载的天数 (从当前时间往前计算)')
    
    # API凭证
    parser.add_argument('--key', '-k', help='API Key')
    parser.add_argument('--secret', help='API Secret')
    
    # 存储选项
    parser.add_argument('--output', '-o', default='data', help='输出目录 (默认: ./data)')
    parser.add_argument('--no-compression', action='store_true', help='禁用数据压缩')
    
    # 其他选项
    parser.add_argument('--verbose', '-v', action='store_true', help='启用详细日志')
    parser.add_argument('--proxy', '-p', help='HTTP代理')
    
    args = parser.parse_args()
    
    # 配置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(level=log_level, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger = logging.getLogger('data-download')
    
    # 解析时间范围
    start_time, end_time = parse_time_range(args.start, args.end, args.days)
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 创建CCXT配置
    config = CCXTConfig(
        exchange_id=args.exchange,
        api_key=args.key,
        secret=args.secret,
        enable_rate_limit=True,
        proxy=args.proxy
    )
    
    try:
        # 初始化数据源
        logger.info(f"连接到交易所: {args.exchange}")
        source = CCXTDataSource(config)
        
        # 初始化存储
        storage = OptimizedStorage(args.output, compression=not args.no_compression)
        
        # 下载数据
        logger.info(f"下载 {args.symbol} {args.timeframe} 数据，从 {start_time} 到 {end_time}")
        data = source.get_data(args.symbol, args.timeframe, start_time, end_time)
        
        if data.empty:
            logger.error("未获取到任何数据")
            return 1
        
        # 保存数据
        logger.info(f"获取了 {len(data)} 条数据")
        storage.update_data(data, args.symbol, args.timeframe)
        logger.info(f"数据已保存到 {args.output}")
        
        # 显示数据信息
        info = storage.get_data_info(args.symbol, args.timeframe)
        logger.info(f"数据概要: {info['rows']} 条记录，从 {info['start_time']} 到 {info['end_time']}")
        
        return 0
    
    except Exception as e:
        logger.error(f"下载失败: {str(e)}", exc_info=args.verbose)
        return 1


def parse_time_range(start_str, end_str, days):
    """
    解析时间范围参数
    
    Args:
        start_str: 开始时间字符串
        end_str: 结束时间字符串
        days: 要下载的天数
        
    Returns:
        (start_time, end_time) 元组
    """
    # 默认结束时间为当前时间
    end_time = datetime.now()
    
    # 解析结束时间
    if end_str:
        try:
            if ' ' in end_str:
                # 尝试解析日期时间格式
                end_time = datetime.strptime(end_str, '%Y-%m-%d %H:%M:%S')
            else:
                # 尝试解析日期格式
                end_time = datetime.strptime(end_str, '%Y-%m-%d')
        except ValueError:
            print(f"错误：结束时间格式无效，应为 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS，收到: {end_str}")
            sys.exit(1)
    
    # 解析开始时间
    if start_str:
        try:
            if ' ' in start_str:
                # 尝试解析日期时间格式
                start_time = datetime.strptime(start_str, '%Y-%m-%d %H:%M:%S')
            else:
                # 尝试解析日期格式
                start_time = datetime.strptime(start_str, '%Y-%m-%d')
        except ValueError:
            print(f"错误：开始时间格式无效，应为 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS，收到: {start_str}")
            sys.exit(1)
    elif days:
        # 使用天数计算开始时间
        start_time = end_time - timedelta(days=days)
    else:
        # 默认下载30天数据
        start_time = end_time - timedelta(days=30)
    
    return start_time, end_time


if __name__ == '__main__':
    sys.exit(download_command()) 