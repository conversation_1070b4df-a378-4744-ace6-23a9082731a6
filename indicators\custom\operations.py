#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
指标操作模块

实现指标之间的算术运算、比较操作和组合功能。
提供加、减、乘、除、大于、小于等操作，使指标之间能够进行灵活组合。
"""

from typing import Dict, Any, List, Optional, Union, Tuple, Callable
import pandas as pd
import numpy as np
from abc import ABC, abstractmethod

from ..base import Indicator
from .builder import CustomIndicator


class Operation(ABC):
    """
    操作基类
    
    定义指标操作的接口，所有具体操作都继承自该类。
    """
    
    def __init__(self, name: str, left: Union[Indicator, str, float], right: Union[Indicator, str, float]):
        """
        初始化操作
        
        Parameters
        ----------
        name : str
            操作名称
        left : Indicator, str或数值
            左操作数，可以是指标对象、列名或常数
        right : Indicator, str或数值
            右操作数，可以是指标对象、列名或常数
        """
        self.name = name
        self.left = left
        self.right = right
    
    @abstractmethod
    def apply(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        应用操作
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据
            
        Returns
        -------
        pd.DataFrame
            操作结果
        """
        pass
    
    def _get_operand_value(self, data: pd.DataFrame, operand: Union[Indicator, str, float]) -> Union[pd.Series, float]:
        """
        获取操作数的值
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据
        operand : Indicator, str或数值
            操作数
            
        Returns
        -------
        pd.Series或数值
            操作数的值
        """
        if isinstance(operand, Indicator):
            # 如果是指标，计算指标值并使用第一个非OHLCV列
            result = operand.calculate(data)
            non_ohlcv_cols = [c for c in result.columns 
                             if c not in ['open', 'high', 'low', 'close', 'volume']]
            if non_ohlcv_cols:
                return result[non_ohlcv_cols[0]]
            else:
                raise ValueError(f"指标 {operand.name} 没有计算结果列")
        elif isinstance(operand, str):
            # 如果是字符串，尝试从数据中获取该列
            if operand in data.columns:
                return data[operand]
            else:
                raise ValueError(f"列 {operand} 在数据中不存在")
        else:
            # 否则假定是常数
            return operand


class Add(Operation):
    """加法操作"""
    
    def __init__(self, left: Union[Indicator, str, float], right: Union[Indicator, str, float]):
        """
        初始化加法操作
        
        Parameters
        ----------
        left : Indicator, str或数值
            左操作数
        right : Indicator, str或数值
            右操作数
        """
        super().__init__("Add", left, right)
    
    def apply(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        应用加法操作
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据
            
        Returns
        -------
        pd.DataFrame
            操作结果
        """
        result = data.copy()
        left_value = self._get_operand_value(result, self.left)
        right_value = self._get_operand_value(result, self.right)
        
        # 生成结果列名
        if isinstance(self.left, Indicator) and isinstance(self.right, Indicator):
            col_name = f"{self.left.name}_plus_{self.right.name}"
        elif isinstance(self.left, Indicator):
            col_name = f"{self.left.name}_plus_{self.right}"
        elif isinstance(self.right, Indicator):
            col_name = f"{self.left}_plus_{self.right.name}"
        else:
            col_name = f"{self.left}_plus_{self.right}"
        
        result[col_name] = left_value + right_value
        return result


class Subtract(Operation):
    """减法操作"""
    
    def __init__(self, left: Union[Indicator, str, float], right: Union[Indicator, str, float]):
        """
        初始化减法操作
        
        Parameters
        ----------
        left : Indicator, str或数值
            左操作数
        right : Indicator, str或数值
            右操作数
        """
        super().__init__("Subtract", left, right)
    
    def apply(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        应用减法操作
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据
            
        Returns
        -------
        pd.DataFrame
            操作结果
        """
        result = data.copy()
        left_value = self._get_operand_value(result, self.left)
        right_value = self._get_operand_value(result, self.right)
        
        # 生成结果列名
        if isinstance(self.left, Indicator) and isinstance(self.right, Indicator):
            col_name = f"{self.left.name}_minus_{self.right.name}"
        elif isinstance(self.left, Indicator):
            col_name = f"{self.left.name}_minus_{self.right}"
        elif isinstance(self.right, Indicator):
            col_name = f"{self.left}_minus_{self.right.name}"
        else:
            col_name = f"{self.left}_minus_{self.right}"
        
        result[col_name] = left_value - right_value
        return result


class Multiply(Operation):
    """乘法操作"""
    
    def __init__(self, left: Union[Indicator, str, float], right: Union[Indicator, str, float]):
        """
        初始化乘法操作
        
        Parameters
        ----------
        left : Indicator, str或数值
            左操作数
        right : Indicator, str或数值
            右操作数
        """
        super().__init__("Multiply", left, right)
    
    def apply(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        应用乘法操作
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据
            
        Returns
        -------
        pd.DataFrame
            操作结果
        """
        result = data.copy()
        left_value = self._get_operand_value(result, self.left)
        right_value = self._get_operand_value(result, self.right)
        
        # 生成结果列名
        if isinstance(self.left, Indicator) and isinstance(self.right, Indicator):
            col_name = f"{self.left.name}_times_{self.right.name}"
        elif isinstance(self.left, Indicator):
            col_name = f"{self.left.name}_times_{self.right}"
        elif isinstance(self.right, Indicator):
            col_name = f"{self.left}_times_{self.right.name}"
        else:
            col_name = f"{self.left}_times_{self.right}"
        
        result[col_name] = left_value * right_value
        return result


class Divide(Operation):
    """除法操作"""
    
    def __init__(self, left: Union[Indicator, str, float], right: Union[Indicator, str, float], 
                 fill_value: float = np.nan):
        """
        初始化除法操作
        
        Parameters
        ----------
        left : Indicator, str或数值
            左操作数
        right : Indicator, str或数值
            右操作数
        fill_value : float, optional
            除数为零时的填充值，默认为NaN
        """
        super().__init__("Divide", left, right)
        self.fill_value = fill_value
    
    def apply(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        应用除法操作
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据
            
        Returns
        -------
        pd.DataFrame
            操作结果
        """
        result = data.copy()
        left_value = self._get_operand_value(result, self.left)
        right_value = self._get_operand_value(result, self.right)
        
        # 生成结果列名
        if isinstance(self.left, Indicator) and isinstance(self.right, Indicator):
            col_name = f"{self.left.name}_div_{self.right.name}"
        elif isinstance(self.left, Indicator):
            col_name = f"{self.left.name}_div_{self.right}"
        elif isinstance(self.right, Indicator):
            col_name = f"{self.left}_div_{self.right.name}"
        else:
            col_name = f"{self.left}_div_{self.right}"
        
        # 安全除法，避免除零错误
        result[col_name] = np.divide(left_value, right_value, 
                                     out=np.full_like(left_value, self.fill_value, dtype=float),
                                     where=(right_value != 0))
        return result


class Compare(Operation):
    """比较操作"""
    
    def __init__(self, left: Union[Indicator, str, float], right: Union[Indicator, str, float],
                operator: str = 'gt'):
        """
        初始化比较操作
        
        Parameters
        ----------
        left : Indicator, str或数值
            左操作数
        right : Indicator, str或数值
            右操作数
        operator : str, optional
            比较运算符，可选值：'gt'(>), 'lt'(<), 'ge'(>=), 'le'(<=), 'eq'(==), 'ne'(!=)
            默认为'gt'
        """
        super().__init__("Compare", left, right)
        self.operator = operator
        
        # 验证操作符
        valid_operators = ['gt', 'lt', 'ge', 'le', 'eq', 'ne']
        if operator not in valid_operators:
            raise ValueError(f"无效的比较运算符: {operator}, 有效值: {valid_operators}")
    
    def apply(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        应用比较操作
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据
            
        Returns
        -------
        pd.DataFrame
            操作结果
        """
        result = data.copy()
        left_value = self._get_operand_value(result, self.left)
        right_value = self._get_operand_value(result, self.right)
        
        # 生成结果列名
        op_map = {'gt': 'gt', 'lt': 'lt', 'ge': 'ge', 'le': 'le', 'eq': 'eq', 'ne': 'ne'}
        op_symbol = op_map[self.operator]
        
        if isinstance(self.left, Indicator) and isinstance(self.right, Indicator):
            col_name = f"{self.left.name}_{op_symbol}_{self.right.name}"
        elif isinstance(self.left, Indicator):
            col_name = f"{self.left.name}_{op_symbol}_{self.right}"
        elif isinstance(self.right, Indicator):
            col_name = f"{self.left}_{op_symbol}_{self.right.name}"
        else:
            col_name = f"{self.left}_{op_symbol}_{self.right}"
        
        # 应用比较操作
        if self.operator == 'gt':
            result[col_name] = left_value > right_value
        elif self.operator == 'lt':
            result[col_name] = left_value < right_value
        elif self.operator == 'ge':
            result[col_name] = left_value >= right_value
        elif self.operator == 'le':
            result[col_name] = left_value <= right_value
        elif self.operator == 'eq':
            result[col_name] = left_value == right_value
        elif self.operator == 'ne':
            result[col_name] = left_value != right_value
        
        # 转换布尔结果为0/1，方便后续使用
        result[col_name] = result[col_name].astype(int)
        
        return result


def create_operation(operation_type: str, left: Union[Indicator, str, float], 
                    right: Union[Indicator, str, float], **params) -> Operation:
    """
    创建指标操作
    
    Parameters
    ----------
    operation_type : str
        操作类型，可选值：'add', 'subtract', 'multiply', 'divide', 'compare'
    left : Indicator, str或数值
        左操作数
    right : Indicator, str或数值
        右操作数
    **params : dict
        操作参数
        
    Returns
    -------
    Operation
        创建的操作对象
    """
    if operation_type == 'add':
        return Add(left, right)
    elif operation_type == 'subtract':
        return Subtract(left, right)
    elif operation_type == 'multiply':
        return Multiply(left, right)
    elif operation_type == 'divide':
        return Divide(left, right, fill_value=params.get('fill_value', np.nan))
    elif operation_type == 'compare':
        return Compare(left, right, operator=params.get('operator', 'gt'))
    else:
        raise ValueError(f"未知的操作类型: {operation_type}") 