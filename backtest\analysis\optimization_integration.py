#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
参数优化与批量分析集成模块

提供参数优化结果的批量分析和报告生成功能。
"""

from typing import Dict, Any, Union, Optional, List, Tuple, Callable, Type
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import logging
import time
from pathlib import Path
import json
import pickle
import warnings
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing

from ..base import Strategy, BacktestResults
from ..backtrader.optimization import ParameterOptimizer as BtParameterOptimizer
from ..vectorbt.optimization.optimizer import VectorBTOptimizer
from . import batch_processing
from .batch_processing import BatchProcessor
from .reporting import generate_report


logger = logging.getLogger(__name__)


class OptimizationAnalyzer:
    """
    参数优化分析器
    
    将参数优化结果与批量分析和报告生成功能集成。
    """
    
    def __init__(self, optimization_results: pd.DataFrame, engine_type: str = 'backtrader'):
        """
        初始化参数优化分析器
        
        Parameters
        ----------
        optimization_results : pd.DataFrame
            参数优化结果DataFrame，由参数优化器生成
        engine_type : str, optional
            使用的回测引擎类型，可选值为'backtrader'或'vectorbt'，默认为'backtrader'
        """
        self.optimization_results = optimization_results
        self.engine_type = engine_type.lower()
        self._validate_results()
        
        if self.engine_type not in ['backtrader', 'vectorbt']:
            raise ValueError(f"不支持的引擎类型: {engine_type}，必须为'backtrader'或'vectorbt'")
            
        self.param_columns = self._identify_param_columns()
        
    def _validate_results(self) -> None:
        """
        验证优化结果的格式
        
        Raises
        ------
        ValueError
            如果优化结果格式不正确
        """
        if not isinstance(self.optimization_results, pd.DataFrame):
            raise ValueError("优化结果必须是pandas DataFrame格式")
            
        if len(self.optimization_results) == 0:
            raise ValueError("优化结果不能为空")
            
    def _identify_param_columns(self) -> List[str]:
        """
        识别参数列
        
        根据优化结果的列名称识别参数列。
        
        Returns
        -------
        List[str]
            参数列名的列表
        """
        # 排除已知的非参数列
        metric_columns = [
            'total_return', 'annualized_return', 'sharpe_ratio', 'sortino_ratio',
            'calmar_ratio', 'max_drawdown', 'volatility', 'win_rate', 'profit_factor',
            'expectancy', 'avg_win', 'avg_loss', 'avg_holding_period', 'max_drawdown_duration',
            'daily_sharpe', 'daily_sortino', 'ulcer_index', 'sterling_ratio', 'burke_ratio',
            'kappa_ratio', 'tail_ratio', 'skew', 'kurtosis'
        ]
        
        # 识别参数列（非指标列）
        param_columns = []
        for col in self.optimization_results.columns:
            if col not in metric_columns:
                param_columns.append(col)
                
        return param_columns
                
    def get_top_params(self, 
                     metric: str = 'sharpe_ratio', 
                     top_n: int = 5, 
                     ascending: bool = False) -> pd.DataFrame:
        """
        获取排名靠前的参数组合
        
        Parameters
        ----------
        metric : str, optional
            用于排序的指标，默认为'sharpe_ratio'
        top_n : int, optional
            返回的参数组合数量，默认为5
        ascending : bool, optional
            排序顺序，默认为False（降序）
            
        Returns
        -------
        pd.DataFrame
            排名靠前的参数组合和对应的性能指标
        """
        if metric not in self.optimization_results.columns:
            raise ValueError(f"指标'{metric}'不在优化结果中")
            
        # 按指标排序
        sorted_results = self.optimization_results.sort_values(
            by=metric, 
            ascending=ascending
        )
        
        # 获取前N个结果
        top_results = sorted_results.head(top_n)
        
        return top_results
        
    def generate_sensitivity_report(self, 
                                  param_name: str,
                                  metrics: Optional[List[str]] = None,
                                  output_path: Optional[str] = None,
                                  output_format: str = 'html',
                                  **kwargs) -> Union[str, None]:
        """
        生成参数敏感性分析报告
        
        Parameters
        ----------
        param_name : str
            要分析的参数名称
        metrics : List[str], optional
            要分析的指标列表，默认为None（使用默认指标）
        output_path : str, optional
            输出路径，默认为None（返回报告内容）
        output_format : str, optional
            输出格式，可选值为'html'、'pdf'或'text'，默认为'html'
        **kwargs : dict
            其他报告参数
            
        Returns
        -------
        str or None
            如果output_path为None，则返回报告内容，否则返回None
        """
        if param_name not in self.param_columns:
            raise ValueError(f"参数'{param_name}'不在优化结果的参数列中")
            
        # 默认指标 - 根据优化结果中的实际可用指标来选择
        default_metrics = ['sharpe_ratio', 'total_return', 'max_drawdown']
        # 添加其他可能存在的指标
        for extra_metric in ['calmar_ratio', 'volatility', 'win_rate', 'annual_return']:
            if extra_metric in self.optimization_results.columns:
                default_metrics.append(extra_metric)
        
        # 如果提供了指标列表，筛选出优化结果中存在的指标
        if metrics:
            metrics_to_analyze = [m for m in metrics if m in self.optimization_results.columns]
            if not metrics_to_analyze:  # 如果筛选后为空，使用默认指标
                metrics_to_analyze = default_metrics
        else:
            metrics_to_analyze = default_metrics
        
        # 确保至少有一个指标可用
        if not metrics_to_analyze:
            raise ValueError("优化结果中没有可用的性能指标，请检查结果数据")
        
        # 创建报告内容
        if output_format == 'html':
            report_content = self._generate_html_sensitivity_report(
                param_name=param_name,
                metrics=metrics_to_analyze,
                **kwargs
            )
        elif output_format == 'pdf':
            if output_path is None:
                raise ValueError("生成PDF报告时必须提供output_path参数")
            self._generate_pdf_sensitivity_report(
                param_name=param_name,
                metrics=metrics_to_analyze,
                output_path=output_path,
                **kwargs
            )
            return None
        else:  # text
            report_content = self._generate_text_sensitivity_report(
                param_name=param_name,
                metrics=metrics_to_analyze,
                **kwargs
            )
            
        # 如果提供了输出路径，保存到文件
        if output_path is not None:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path) or '.', exist_ok=True)
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            return None
        else:
            return report_content
            
    def _generate_html_sensitivity_report(self,
                                       param_name: str,
                                       metrics: List[str],
                                       **kwargs) -> str:
        """
        生成HTML格式的参数敏感性分析报告
        
        Parameters
        ----------
        param_name : str
            要分析的参数名称
        metrics : List[str]
            要分析的指标列表
        **kwargs : dict
            其他报告参数
            
        Returns
        -------
        str
            HTML报告内容
        """
        import matplotlib
        matplotlib.use('Agg')
        import io
        import base64
        import seaborn as sns
        
        # 生成参数敏感性图表
        figs = []
        for metric in metrics:
            # 创建图表
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # 分组计算平均指标值
            grouped = self.optimization_results.groupby(param_name)[metric].mean()
            
            # 绘制折线图
            ax.plot(grouped.index, grouped.values, 'o-', linewidth=2, markersize=8)
            
            # 设置标题和标签
            ax.set_title(f"Parameter Sensitivity: {metric} vs {param_name}", fontsize=14)
            ax.set_xlabel(param_name, fontsize=12)
            ax.set_ylabel(metric, fontsize=12)
            
            # 添加网格线
            ax.grid(True, linestyle='--', alpha=0.7)
            
            # 美化图表
            sns.despine()
            fig.tight_layout()
            
            # 保存图表
            figs.append(fig)
            
        # 将图表转换为base64编码
        def fig_to_base64(fig):
            buf = io.BytesIO()
            fig.savefig(buf, format='png', dpi=100)
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)
            return img_str
            
        img_tags = [f'<img src="data:image/png;base64,{fig_to_base64(fig)}" />' for fig in figs]
        
        # 创建数据表格
        # 按参数值分组，计算每个指标的平均值
        try:
            # 使用更安全的方法生成分组数据表格
            # 先按参数值分组
            group_data = {}
            for metric in metrics:
                try:
                    grouped_series = self.optimization_results.groupby(param_name)[metric].mean()
                    for idx, val in grouped_series.items():
                        if idx not in group_data:
                            group_data[idx] = {}
                        group_data[idx][metric] = val
                except:
                    pass  # 忽略处理失败的指标
            
            # 将字典转换为DataFrame
            if group_data:
                # 创建带有参数列的DataFrame
                grouped_df = pd.DataFrame.from_dict(group_data, orient='index').reset_index()
                grouped_df.rename(columns={'index': param_name}, inplace=True)
                table_html = grouped_df.to_html(
                    classes='dataframe',
                    index=False,
                    float_format=lambda x: f"{x:.4f}" if isinstance(x, (int, float)) else str(x)
                )
            else:
                table_html = "<p>无法生成敏感性分析表格数据。</p>"
        except Exception as e:
            table_html = f"<p>生成敏感性分析表格时出错: {str(e)}</p>"
        
        # 创建HTML报告
        html_template = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parameter Sensitivity Analysis: {param_name}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }}
        .header {{
            background-color: #2c3e50;
            color: #fff;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 5px;
        }}
        .header h1 {{
            margin: 0;
            font-size: 24px;
        }}
        .header p {{
            margin: 5px 0 0 0;
            opacity: 0.8;
        }}
        .section {{
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }}
        .section h2 {{
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }}
        .chart-container {{
            margin: 20px 0;
            text-align: center;
        }}
        table.dataframe {{
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }}
        table.dataframe th, table.dataframe td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }}
        table.dataframe tr:nth-child(even) {{
            background-color: #f2f2f2;
        }}
        table.dataframe th {{
            padding-top: 12px;
            padding-bottom: 12px;
            text-align: center;
            background-color: #2c3e50;
            color: white;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Parameter Sensitivity Analysis</h1>
            <p>Analysis of {param_name} impact on strategy performance</p>
        </div>
        
        <div class="section">
            <h2>Parameter Sensitivity Charts</h2>
            <p>These charts show how different values of {param_name} affect various performance metrics.</p>
            
            <div class="chart-container">
                {"".join(img_tags)}
            </div>
        </div>
        
        <div class="section">
            <h2>Performance Metrics by {param_name}</h2>
            <p>The table below shows the average value of each metric for different values of {param_name}.</p>
            
            {table_html}
        </div>
    </div>
</body>
</html>"""

        return html_template
        
    def _generate_pdf_sensitivity_report(self,
                                      param_name: str,
                                      metrics: List[str],
                                      output_path: str,
                                      **kwargs) -> None:
        """
        生成PDF格式的参数敏感性分析报告
        
        Parameters
        ----------
        param_name : str
            要分析的参数名称
        metrics : List[str]
            要分析的指标列表
        output_path : str
            输出路径
        **kwargs : dict
            其他报告参数
        """
        try:
            import weasyprint
            from jinja2 import Template
        except ImportError:
            raise ImportError("生成PDF报告需要安装weasyprint和jinja2库")
            
        # 首先生成HTML报告
        html_content = self._generate_html_sensitivity_report(
            param_name=param_name,
            metrics=metrics,
            **kwargs
        )
        
        # 使用WeasyPrint将HTML转换为PDF
        weasyprint.HTML(string=html_content).write_pdf(output_path)
        
    def _generate_text_sensitivity_report(self,
                                       param_name: str,
                                       metrics: List[str],
                                       **kwargs) -> str:
        """
        生成文本格式的参数敏感性分析报告
        
        Parameters
        ----------
        param_name : str
            要分析的参数名称
        metrics : List[str]
            要分析的指标列表
        **kwargs : dict
            其他报告参数
            
        Returns
        -------
        str
            文本报告内容
        """
        import tabulate
        
        # 按参数值分组，计算每个指标的平均值
        grouped_data = self.optimization_results.groupby(param_name)[metrics].mean()
        
        # 重置索引，便于表格化
        table_data = grouped_data.reset_index()
        
        # 使用tabulate生成ASCII表格
        table_str = tabulate.tabulate(
            table_data.values,
            headers=table_data.columns,
            tablefmt="grid",
            floatfmt=".4f"
        )
        
        # 生成报告文本
        report_text = f"""
=======================================================
       Parameter Sensitivity Analysis: {param_name}
=======================================================

This report shows how different values of the parameter 
'{param_name}' affect various performance metrics.

Performance Metrics by {param_name}:
-------------------------------------------------------
{table_str}
-------------------------------------------------------

Generated on: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        return report_text
        
    def generate_top_n_reports(self,
                             top_n: int = 5,
                             metric: str = 'sharpe_ratio',
                             ascending: bool = False,
                             output_dir: str = './optimization_reports',
                             output_format: str = 'html',
                             strategy_class: Optional[Type[Strategy]] = None,
                             data: Optional[pd.DataFrame] = None,
                             **kwargs) -> None:
        """
        为排名靠前的参数组合生成报告
        
        Parameters
        ----------
        top_n : int, optional
            生成报告的参数组合数量，默认为5
        metric : str, optional
            用于排序的指标，默认为'sharpe_ratio'
        ascending : bool, optional
            排序顺序，默认为False（降序）
        output_dir : str, optional
            输出目录，默认为'./optimization_reports'
        output_format : str, optional
            输出格式，可选值为'html'、'pdf'或'text'，默认为'html'
        strategy_class : Type[Strategy], optional
            策略类，如果提供，将使用该类和参数运行回测
        data : pd.DataFrame, optional
            回测数据，如需运行回测，必须提供
        **kwargs : dict
            其他参数，如回测引擎参数等
        """
        # 获取排名靠前的参数组合
        top_params = self.get_top_params(
            metric=metric,
            top_n=top_n,
            ascending=ascending
        )
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 如果提供了策略类和数据，可以重新运行回测
        if strategy_class and data is not None:
            logger.info(f"使用前{top_n}组参数重新运行回测...")
            
            # 导入回测引擎
            if self.engine_type == 'backtrader':
                from ..backtrader.core import BacktraderEngine as Engine
            else:  # vectorbt
                from ..vectorbt.engine import VectorBTEngine as Engine
                
            # 初始化引擎
            engine_kwargs = {k: v for k, v in kwargs.items() if k != 'strategy_class'}
            engine_instance = Engine(data, **engine_kwargs)
            
            # 运行回测并收集结果
            results_dict = {}
            for i, params in enumerate(top_params[self.param_columns].to_dict('records')):
                strategy_name = f"{strategy_class.__name__}_{i+1}"
                logger.info(f"运行策略 {strategy_name} 使用参数: {params}")
                
                # 实例化策略并运行回测
                # 只保留策略构造函数中定义的参数
                strategy_params = {}
                import inspect
                sig = inspect.signature(strategy_class.__init__)
                valid_params = [param.name for param in sig.parameters.values() 
                              if param.name != 'self']  # 排除self参数
                
                # 只保留有效的参数
                for param_name, param_value in params.items():
                    if param_name in valid_params:
                        strategy_params[param_name] = param_value
                
                # 使用过滤后的参数创建策略实例
                strategy = strategy_class(**strategy_params)
                results = engine_instance.run(strategy)
                
                # 保存结果
                results_dict[strategy_name] = results
                
            # 创建批处理器
            processor = BatchProcessor()
            processor.add_results(results_dict)
            
            # 生成批量报告
            processor.generate_batch_reports(
                output_dir=output_dir,
                output_format=output_format,
                **kwargs
            )
            
            # 生成汇总报告
            summary_path = os.path.join(output_dir, f"summary.{output_format}")
            processor.generate_summary_report(
                output_path=summary_path,
                **kwargs
            )
        else:
            # 无法运行回测，只生成参数报告
            logger.info("未提供策略类或数据，仅生成参数报告")
            
            # 为每组参数生成报告
            for i, params in enumerate(top_params[self.param_columns].to_dict('records')):
                report_name = f"params_{i+1}.{output_format}"
                output_path = os.path.join(output_dir, report_name)
                
                # 生成HTML报告
                with open(output_path, 'w', encoding='utf-8') as f:
                    # 获取可用的指标列（非参数列）
                    available_metrics = [col for col in top_params.columns 
                                      if col not in self.param_columns]
                    
                    # 构建指标行HTML
                    metric_rows = ""
                    for col in available_metrics:
                        try:
                            metric_value = top_params.iloc[i][col]
                            metric_rows += f"<tr><td>{col}</td><td>{metric_value:.4f}</td></tr>"
                        except (KeyError, ValueError, TypeError, IndexError):
                            continue  # 跳过无法格式化的指标
                    
                    f.write(f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Optimized Parameters #{i+1}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #2c3e50; color: white; }}
        tr:nth-child(even) {{ background-color: #f2f2f2; }}
        .header {{ background-color: #2c3e50; color: white; padding: 15px; margin-bottom: 20px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Optimized Parameters #{i+1}</h1>
    </div>
    
    <h2>Parameters</h2>
    <table>
        <tr>
            <th>Parameter</th>
            <th>Value</th>
        </tr>
        {"".join(f"<tr><td>{k}</td><td>{v}</td></tr>" for k, v in params.items())}
    </table>
    
    <h2>Performance Metrics</h2>
    <table>
        <tr>
            <th>Metric</th>
            <th>Value</th>
        </tr>
        {metric_rows}
    </table>
</body>
</html>""")
                
            # 生成汇总报告
            summary_path = os.path.join(output_dir, f"summary.{output_format}")
            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write(f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Optimization Results Summary</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #2c3e50; color: white; }}
        tr:nth-child(even) {{ background-color: #f2f2f2; }}
        .header {{ background-color: #2c3e50; color: white; padding: 15px; margin-bottom: 20px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Optimization Results Summary</h1>
    </div>
    
    <h2>Top {top_n} Parameter Sets</h2>
    <p>Sorted by {metric} ({'ascending' if ascending else 'descending'} order)</p>
    
    {top_params.to_html(index=False, float_format=lambda x: f"{x:.4f}")}
</body>
</html>""")
                
        logger.info(f"报告已成功生成到: {output_dir}")
        
    def export_results_to_excel(self, output_path: str) -> None:
        """
        将优化结果导出到Excel文件
        
        Parameters
        ----------
        output_path : str
            输出文件路径
        """
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path) or '.', exist_ok=True)
        
        # 导出到Excel，处理可能的导出错误
        try:
            self.optimization_results.to_excel(output_path, index=False)
            logger.info(f"优化结果已导出到: {output_path}")
        except ModuleNotFoundError as e:
            if 'openpyxl' in str(e):
                # 如果缺少openpyxl，使用csv格式代替
                csv_path = output_path.replace('.xlsx', '.csv')
                self.optimization_results.to_csv(csv_path, index=False)
                logger.warning(f"缺少openpyxl模块，无法导出为Excel格式，已导出为CSV格式: {csv_path}")
            else:
                # 其他模块错误
                raise
        except Exception as e:
            logger.error(f"导出Excel时出错: {e}")
            # 尝试使用CSV格式
            try:
                csv_path = output_path.replace('.xlsx', '.csv')
                self.optimization_results.to_csv(csv_path, index=False)
                logger.info(f"已导出为CSV格式: {csv_path}")
            except Exception as e2:
                logger.error(f"导出CSV也失败了: {e2}")
        
    def export_results_to_csv(self, output_path: str) -> None:
        """
        将优化结果导出到CSV文件
        
        Parameters
        ----------
        output_path : str
            输出文件路径
        """
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path) or '.', exist_ok=True)
        
        # 导出到CSV
        self.optimization_results.to_csv(output_path, index=False)
        logger.info(f"优化结果已导出到: {output_path}")


def run_optimization_and_analyze(strategy_class: Type[Strategy],
                              data: pd.DataFrame,
                              param_grid: Dict[str, List],
                              engine_type: str = 'backtrader',
                              metric: str = 'sharpe_ratio',
                              maximize: bool = True,
                              n_jobs: int = 1,
                              top_n: int = 5,
                              generate_reports: bool = True,
                              output_dir: str = './optimization_reports',
                              **engine_kwargs) -> Dict[str, Any]:
    """
    运行参数优化并分析结果
    
    Parameters
    ----------
    strategy_class : Type[Strategy]
        策略类
    data : pd.DataFrame
        回测数据
    param_grid : Dict[str, List]
        参数网格，格式为 {'param_name': [value1, value2, ...]}
    engine_type : str, optional
        使用的回测引擎类型，可选值为'backtrader'或'vectorbt'，默认为'backtrader'
    metric : str, optional
        优化目标指标，默认为'sharpe_ratio'
    maximize : bool, optional
        是否最大化指标，默认为True
    n_jobs : int, optional
        并行任务数，默认为1
    top_n : int, optional
        报告中包含的排名靠前的参数组合数量，默认为5
    generate_reports : bool, optional
        是否生成报告，默认为True
    output_dir : str, optional
        输出目录，默认为'./optimization_reports'
    **engine_kwargs : dict
        回测引擎参数
        
    Returns
    -------
    Dict[str, Any]
        包含优化结果和分析的字典
    """
    # 导入回测引擎
    if engine_type.lower() == 'backtrader':
        from ..backtrader.core import BacktraderEngine as Engine
        optimizer_class = BtParameterOptimizer
    elif engine_type.lower() == 'vectorbt':
        from ..vectorbt.engine import VectorBTEngine as Engine
        optimizer_class = VectorBTOptimizer
    else:
        raise ValueError(f"不支持的引擎类型: {engine_type}，必须为'backtrader'或'vectorbt'")
        
    # 初始化引擎
    engine_instance = Engine(data, **engine_kwargs)
    
    # 创建优化器
    optimizer = optimizer_class(
        engine_instance, 
        strategy_class, 
        data,
        metric=metric,
        maximize=maximize,
        **engine_kwargs
    )
    
    # 运行网格搜索
    logger.info(f"开始参数优化，使用{param_grid}...")
    results_df = optimizer.grid_search(param_grid, n_jobs=n_jobs)
    logger.info(f"参数优化完成，找到{len(results_df)}个结果")
    
    # 创建分析器
    analyzer = OptimizationAnalyzer(results_df, engine_type)
    
    # 获取最佳参数
    best_params = analyzer.get_top_params(metric=metric, top_n=1, ascending=not maximize)
    logger.info(f"最佳参数: {best_params[analyzer._identify_param_columns()].iloc[0].to_dict()}")
    logger.info(f"最佳{metric}: {best_params[metric].iloc[0]:.4f}")
    
    # 生成报告
    if generate_reports:
        analyzer.generate_top_n_reports(
            top_n=top_n,
            metric=metric,
            ascending=not maximize,
            output_dir=output_dir,
            strategy_class=strategy_class,
            data=data,
            **engine_kwargs
        )
        
        # 生成参数敏感性报告
        for param_name in analyzer.param_columns:
            # 只为有多个值的参数生成敏感性报告
            try:
                if len(results_df[param_name].unique()) > 1:
                    output_path = os.path.join(output_dir, f"sensitivity_{param_name}.html")
                    analyzer.generate_sensitivity_report(
                        param_name=param_name,
                        output_path=output_path
                    )
            except Exception as e:
                logger.warning(f"生成{param_name}敏感性报告时出错: {e}")
        
        # 导出结果
        try:
            analyzer.export_results_to_excel(os.path.join(output_dir, "optimization_results.xlsx"))
        except Exception as e:
            logger.warning(f"导出Excel结果时出错: {e}")
        
        try:
            analyzer.export_results_to_csv(os.path.join(output_dir, "optimization_results.csv"))
        except Exception as e:
            logger.warning(f"导出CSV结果时出错: {e}")
    
    # 返回结果
    return {
        'optimizer': optimizer,
        'analyzer': analyzer,
        'results': results_df,
        'best_params': best_params[analyzer._identify_param_columns()].iloc[0].to_dict(),
        'best_metrics': {col: best_params[col].iloc[0] for col in best_params.columns 
                       if col not in analyzer._identify_param_columns()}
    } 