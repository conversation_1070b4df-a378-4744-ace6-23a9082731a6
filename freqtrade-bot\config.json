{"$schema": "https://schema.freqtrade.io/schema.json", "max_open_trades": 15, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "dry_run": true, "dry_run_wallet": 10000, "cancel_open_orders_on_exit": false, "trading_mode": "futures", "margin_mode": "isolated", "liquidation_buffer": 0.05, "strategy": "SMCStrategy", "strategy_path": "E:/newADM/gitRepository/AriQuantification/backtest/strategies/", "unfilledtimeout": {"entry": 120, "exit": 60, "unit": "seconds"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": true, "bids_to_ask_delta": 0.03}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {"options": {"defaultType": "future", "createMarketBuyOrderRequiresPrice": false, "watchOrderBook": false, "watchTicker": false, "watchOHLCV": false, "watchTrades": false, "ws": false, "wss": false, "stream": false, "watchMyTrades": false, "watchOrders": false, "fetchOrderBook": true}, "timeout": 30000, "rateLimit": 1000, "sandbox": false, "ws": true, "enableRateLimit": true, "verify": true}, "ccxt_async_config": {"options": {"defaultType": "future", "createMarketBuyOrderRequiresPrice": false, "watchOrderBook": false, "watchTicker": false, "watchOHLCV": false, "watchTrades": false, "ws": false, "wss": false, "stream": false, "watchMyTrades": false, "watchOrders": false, "fetchOrderBook": true}, "timeout": 30000, "rateLimit": 1000, "sandbox": false, "ws": true, "enableRateLimit": true, "verify": true}, "skip_open_order_update": false, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "XRP/USDT:USDT", "ADA/USDT:USDT", "BNB/USDT:USDT", "DOT/USDT:USDT", "SOL/USDT:USDT", "LINK/USDT:USDT", "UNI/USDT:USDT", "TRX/USDT:USDT", "DOGE/USDT:USDT", "CRV/USDT:USDT"], "pair_blacklist": [".*DOWN/.*", ".*UP/.*"]}, "pairlists": [{"method": "StaticPairList"}], "api_server": {"enabled": true, "listen_ip_address": "127.0.0.1", "listen_port": 8080, "verbosity": "error", "enable_openapi": false, "jwt_secret_key": "db8c839c7e69d7ed25e43b836505ea340d7f6e496eb76175eae10c323e490988095dc7328577f724bc3ebb28db73dd5d0501447087d5fb6a0cd995c37aaf52fe", "ws_token": "50JKyE6VEqvufqvHpM87wMO90snMCXLeuEj7iKa8Sdg", "CORS_origins": [], "username": "AriFreqtrade", "password": "qq123456"}, "bot_name": "AriFreqtrade", "initial_state": "running", "force_entry_enable": true, "internals": {"process_throttle_secs": 1, "heartbeat_interval": 30, "sd_notify": false}, "logging": {"level": "INFO", "disable_warnings": ["urllib3.connectionpool:InsecureRequestWarning", "urllib3.exceptions.InsecureRequestWarning"]}, "dataformat_ohlcv": "json", "dataformat_trades": "jsongz", "process_only_new_candles": false, "process_throttle_secs": 1}