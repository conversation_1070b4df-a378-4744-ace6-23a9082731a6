#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
周期查找器指标模块

实现周期查找器指标，用于识别价格或其他时间序列数据中的周期性模式。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Union, List, Tuple

from ..base import Indicator
from ..utils import validate_data


class CycleFinder(Indicator):
    """
    周期查找器指标
    
    使用自相关方法和其他统计技术识别时间序列数据中的周期性模式。
    可用于发现市场的隐含周期，为交易决策提供时间框架参考。
    """
    
    def __init__(
        self, 
        max_lag: int = 100, 
        column: str = 'close', 
        min_periods: int = 20, 
        detrend: bool = True,
        significance_level: float = 0.05,
        **kwargs
    ):
        """
        初始化周期查找器指标
        
        Parameters
        ----------
        max_lag : int, optional
            最大滞后期数，默认为100
        column : str, optional
            用于计算的列名，默认为'close'
        min_periods : int, optional
            识别周期的最小长度，默认为20
        detrend : bool, optional
            是否在计算前去除趋势，默认为True
        significance_level : float, optional
            显著性水平，用于过滤有意义的周期，默认为0.05
        **kwargs : dict
            其他参数
        """
        if max_lag <= 0:
            raise ValueError("最大滞后期数必须大于0")
        
        if min_periods <= 0:
            raise ValueError("最小周期长度必须大于0")
            
        if not (0 < significance_level < 1):
            raise ValueError("显著性水平必须在0到1之间")
        
        super().__init__(
            name="CycleFinder", 
            category="cycle", 
            max_lag=max_lag,
            column=column,
            min_periods=min_periods,
            detrend=detrend,
            significance_level=significance_level,
            **kwargs
        )

    def _detrend_series(self, series: pd.Series) -> pd.Series:
        """
        去除时间序列的趋势成分
        
        Parameters
        ----------
        series : pd.Series
            输入时间序列
            
        Returns
        -------
        pd.Series
            去趋势后的时间序列
        """
        # 简单的线性去趋势
        x = np.arange(len(series))
        # 处理可能的NaN值
        mask = ~np.isnan(series)
        if np.sum(mask) <= 1:  # 数据点不足
            return series
            
        # 线性回归去趋势
        try:
            slope, intercept = np.polyfit(x[mask], series[mask], 1)
            trend = slope * x + intercept
            return series - trend
        except Exception:
            # 如果去趋势失败，返回原始序列
            return series
    
    def _compute_autocorrelation(self, series: pd.Series, max_lag: int) -> pd.Series:
        """
        计算时间序列的自相关系数
        
        Parameters
        ----------
        series : pd.Series
            输入时间序列
        max_lag : int
            最大滞后期数
            
        Returns
        -------
        pd.Series
            各滞后期的自相关系数
        """
        # 确保最大滞后期不超过序列长度的一半
        max_lag = min(max_lag, len(series) // 2)
        
        # 计算自相关系数
        autocorr = pd.Series(index=range(1, max_lag + 1))
        series = series.dropna()  # 去除NaN值
        
        if len(series) <= 1:
            return pd.Series(index=range(1, max_lag + 1), data=np.nan)
        
        # 中心化数据
        series_centered = series - series.mean()
        # 计算自协方差
        denom = np.sum(series_centered**2)
        
        for lag in range(1, max_lag + 1):
            # 计算滞后序列
            lagged = series_centered.shift(lag).dropna()
            original = series_centered.loc[lagged.index]
            
            if len(lagged) > 0 and denom > 0:
                autocorr[lag] = np.sum(original * lagged) / denom
            else:
                autocorr[lag] = np.nan
        
        return autocorr
    
    def _find_significant_cycles(
        self, 
        autocorr: pd.Series, 
        significance_level: float, 
        min_periods: int
    ) -> List[Tuple[int, float]]:
        """
        从自相关系数中找出显著的周期
        
        Parameters
        ----------
        autocorr : pd.Series
            自相关系数
        significance_level : float
            显著性水平
        min_periods : int
            最小周期长度
            
        Returns
        -------
        List[Tuple[int, float]]
            显著周期及其相关系数，按相关系数强度排序
        """
        n = len(autocorr)
        
        # 计算显著性阈值（使用标准差的近似方法）
        std_error = 1.0 / np.sqrt(n)
        threshold = std_error * np.sqrt(2) * np.abs(np.percentile(np.random.normal(0, 1, 10000), 
                                                               (1 - significance_level/2) * 100))
        
        # 寻找显著的自相关峰
        significant_cycles = []
        
        # 找到局部最大值
        for lag in range(min_periods, len(autocorr) + 1):
            if lag > 1 and lag < len(autocorr):
                # 局部最大值条件：大于两侧且超过阈值
                if (autocorr[lag] > autocorr[lag-1] and 
                    autocorr[lag] > autocorr[lag+1] and 
                    abs(autocorr[lag]) > threshold):
                    significant_cycles.append((lag, autocorr[lag]))
        
        # 按相关系数绝对值排序
        return sorted(significant_cycles, key=lambda x: abs(x[1]), reverse=True)
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算周期查找器指标
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据，需包含用于计算的列
            
        Returns
        -------
        pd.DataFrame
            包含周期分析结果的DataFrame
        """
        column = self.params['column']
        validate_data(data, [column])
        
        df = data.copy()
        max_lag = self.params['max_lag']
        detrend = self.params['detrend']
        min_periods = self.params['min_periods']
        significance_level = self.params['significance_level']
        
        # 获取数据序列
        series = df[column]
        
        # 可选地去除趋势
        if detrend:
            series = self._detrend_series(series)
        
        # 计算自相关系数
        autocorr = self._compute_autocorrelation(series, max_lag)
        
        # 查找显著周期
        significant_cycles = self._find_significant_cycles(
            autocorr, significance_level, min_periods
        )
        
        # 创建结果DataFrame
        result = df.copy()
        
        # 添加自相关系数列（为了可视化和进一步分析）
        for lag in range(1, max_lag + 1):
            result[f'autocorr_{lag}'] = autocorr[lag]
        
        # 分析结果
        cycles_found = pd.DataFrame(significant_cycles, columns=['period', 'strength'])
        
        # 存储额外信息
        self._autocorr = autocorr
        self._cycles = cycles_found
        self._result = result
        
        return result
    
    def get_dominant_cycles(self, n: int = 3) -> pd.DataFrame:
        """
        获取最显著的n个周期
        
        Parameters
        ----------
        n : int, optional
            返回周期的数量，默认为3
            
        Returns
        -------
        pd.DataFrame
            包含周期长度和强度的DataFrame
        """
        if self._cycles is None or len(self._cycles) == 0:
            return pd.DataFrame(columns=['period', 'strength'])
        
        return self._cycles.head(n)
    
    def plot(self, ax=None, **kwargs):
        """
        绘制周期查找器结果
        
        Parameters
        ----------
        ax : matplotlib.axes.Axes, optional
            用于绘图的Axes对象
        **kwargs : dict
            传递给绘图函数的其他参数
            
        Returns
        -------
        matplotlib.axes.Axes
            绘图结果
        """
        if self._result is None or not hasattr(self, '_autocorr'):
            raise ValueError("没有计算结果可供绘制，请先调用calculate方法")
        
        import matplotlib.pyplot as plt
        
        if ax is None:
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=kwargs.get('figsize', (12, 8)))
        else:
            # 如果提供了ax，假设它是一个2x1的子图
            if isinstance(ax, (list, tuple)) and len(ax) >= 2:
                ax1, ax2 = ax[0], ax[1]
            else:
                fig, (ax1, ax2) = plt.subplots(2, 1, figsize=kwargs.get('figsize', (12, 8)))
        
        column = self.params['column']
        max_lag = self.params['max_lag']
        
        # 绘制原始数据和去趋势数据（如果适用）
        ax1.plot(self._result.index, self._result[column], label=column.capitalize(), color='blue')
        if self.params['detrend']:
            # 计算去趋势数据用于可视化
            series = self._result[column]
            detrended = self._detrend_series(series)
            # 使去趋势数据的均值与原始数据相同，以便于比较
            detrended = detrended + series.mean()
            ax1.plot(self._result.index, detrended, label='去趋势数据', color='red', alpha=0.7)
        
        ax1.set_title(f"{column.capitalize()}数据")
        ax1.grid(True)
        ax1.legend()
        
        # 绘制自相关图
        lags = range(1, len(self._autocorr) + 1)
        ax2.bar(lags, self._autocorr, alpha=0.7)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        
        # 计算并绘制显著性阈值线
        n = len(self._result)
        std_error = 1.0 / np.sqrt(n)
        significance_level = self.params['significance_level']
        threshold = std_error * np.sqrt(2) * np.abs(np.percentile(np.random.normal(0, 1, 10000), 
                                                           (1 - significance_level/2) * 100))
        ax2.axhline(y=threshold, color='red', linestyle='--', alpha=0.5, 
                  label=f'显著性阈值 ({significance_level:.2f})')
        ax2.axhline(y=-threshold, color='red', linestyle='--', alpha=0.5)
        
        # 标记显著周期
        if hasattr(self, '_cycles') and len(self._cycles) > 0:
            for period, strength in zip(self._cycles['period'], self._cycles['strength']):
                ax2.plot(period, strength, 'ro', ms=10)
                ax2.annotate(f'{period}', (period, strength), 
                           textcoords="offset points", 
                           xytext=(0, 10), 
                           ha='center')
        
        ax2.set_title("自相关分析")
        ax2.set_xlabel('滞后期数')
        ax2.set_ylabel('自相关系数')
        ax2.set_xlim(0, max_lag + 1)
        ax2.grid(True)
        ax2.legend()
        
        plt.tight_layout()
        
        return (ax1, ax2) 