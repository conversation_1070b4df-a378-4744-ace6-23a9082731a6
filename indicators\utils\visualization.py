#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
指标可视化模块

提供绘制技术指标图表的功能。
"""

from typing import Dict, List, Union, Tuple, Optional, Any, Callable
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from matplotlib.axes import Axes
import matplotlib.dates as mdates
from matplotlib.ticker import MaxNLocator
import matplotlib.patches as mpatches
from matplotlib.lines import Line2D
import mplfinance as mpf
from datetime import datetime, timedelta

from ..base import Indicator


class IndicatorVisualizer:
    """指标可视化基类"""
    
    def __init__(self, backend: str = "matplotlib", theme: str = "default"):
        """
        初始化可视化器
        
        Parameters
        ----------
        backend : str, optional
            可视化后端, 默认为 "matplotlib"
        theme : str, optional
            可视化主题, 默认为 "default"
        """
        self.backend = backend
        self.theme = theme
        self._figure = None
        self._axes = {}
        self._indicators = []
        self._data = None
        self._theme_config = self._get_theme_config(theme)
    
    def _get_theme_config(self, theme: str) -> Dict[str, Any]:
        """
        获取主题配置
        
        Parameters
        ----------
        theme : str
            主题名称
            
        Returns
        -------
        Dict[str, Any]
            主题配置字典
        """
        themes = {
            "default": {
                "figure.figsize": (12, 8),
                "figure.facecolor": "white",
                "axes.facecolor": "white",
                "axes.edgecolor": "#333333",
                "axes.labelcolor": "#333333",
                "axes.grid": True,
                "grid.color": "#dddddd",
                "grid.linestyle": "--",
                "text.color": "#333333",
                "xtick.color": "#333333",
                "ytick.color": "#333333",
                "lines.linewidth": 1.5,
                "lines.markersize": 6,
                "price.up.color": "#26a69a",
                "price.down.color": "#ef5350",
                "volume.up.color": "#26a69a",
                "volume.down.color": "#ef5350",
                "volume.alpha": 0.5,
                "legend.frameon": True,
                "legend.loc": "best",
                "date_format": "%Y-%m-%d"
            },
            "dark": {
                "figure.figsize": (12, 8),
                "figure.facecolor": "#1e1e1e",
                "axes.facecolor": "#1e1e1e",
                "axes.edgecolor": "#bbbbbb",
                "axes.labelcolor": "#eeeeee",
                "axes.grid": True,
                "grid.color": "#333333",
                "grid.linestyle": "--",
                "text.color": "#eeeeee",
                "xtick.color": "#eeeeee",
                "ytick.color": "#eeeeee",
                "lines.linewidth": 1.5,
                "lines.markersize": 6,
                "price.up.color": "#26a69a",
                "price.down.color": "#ef5350",
                "volume.up.color": "#26a69a",
                "volume.down.color": "#ef5350",
                "volume.alpha": 0.5,
                "legend.frameon": True,
                "legend.loc": "best",
                "date_format": "%Y-%m-%d"
            },
            "tradingview": {
                "figure.figsize": (12, 8),
                "figure.facecolor": "#131722",
                "axes.facecolor": "#131722",
                "axes.edgecolor": "#363c4e",
                "axes.labelcolor": "#9db2bd",
                "axes.grid": True,
                "grid.color": "#1f2a3a",
                "grid.linestyle": "-",
                "text.color": "#9db2bd",
                "xtick.color": "#9db2bd",
                "ytick.color": "#9db2bd",
                "lines.linewidth": 1.5,
                "lines.markersize": 6,
                "price.up.color": "#22ab94",
                "price.down.color": "#f23645",
                "volume.up.color": "#22ab94",
                "volume.down.color": "#f23645",
                "volume.alpha": 0.6,
                "legend.frameon": True,
                "legend.loc": "best",
                "date_format": "%Y-%m-%d"
            }
        }
        
        if theme not in themes:
            raise ValueError(f"不支持的主题: {theme}，可用主题: {list(themes.keys())}")
        
        return themes[theme]
    
    def set_theme(self, theme: str) -> 'IndicatorVisualizer':
        """
        设置可视化主题
        
        Parameters
        ----------
        theme : str
            主题名称
            
        Returns
        -------
        IndicatorVisualizer
            可视化器对象本身，支持链式调用
        """
        self._theme_config = self._get_theme_config(theme)
        self.theme = theme
        return self
    
    def create_figure(self, figsize: Optional[Tuple[float, float]] = None, tight_layout: bool = True) -> 'IndicatorVisualizer':
        """
        创建图形对象
        
        Parameters
        ----------
        figsize : Tuple[float, float], optional
            图形大小，默认使用主题配置
        tight_layout : bool, optional
            是否使用紧凑布局，默认为True
            
        Returns
        -------
        IndicatorVisualizer
            可视化器对象本身，支持链式调用
        """
        if figsize is None:
            figsize = self._theme_config["figure.figsize"]
        
        self._figure = plt.figure(figsize=figsize, facecolor=self._theme_config["figure.facecolor"])
        
        if tight_layout:
            self._figure.tight_layout()
        
        return self
    
    def add_subplot(self, name: str, position: Optional[Tuple[int, int, int]] = None, 
                   sharex: Optional[str] = None, height_ratio: float = 1.0, **kwargs) -> 'IndicatorVisualizer':
        """
        添加子图
        
        Parameters
        ----------
        name : str
            子图名称
        position : Tuple[int, int, int], optional
            子图位置 (nrows, ncols, index)
        sharex : str, optional
            共享x轴的子图名称
        height_ratio : float, optional
            高度比例，默认为1.0
        **kwargs : dict
            传递给subplot的其他参数
            
        Returns
        -------
        IndicatorVisualizer
            可视化器对象本身，支持链式调用
        """
        if self._figure is None:
            self.create_figure()
        
        if position is not None:
            ax = self._figure.add_subplot(position[0], position[1], position[2], **kwargs)
        elif sharex is not None and sharex in self._axes:
            ax = self._figure.add_subplot(sharex=self._axes[sharex], **kwargs)
        else:
            ax = self._figure.add_subplot(111, **kwargs)
        
        # 应用主题设置
        ax.set_facecolor(self._theme_config["axes.facecolor"])
        ax.tick_params(axis='both', colors=self._theme_config["xtick.color"])
        ax.spines['bottom'].set_color(self._theme_config["axes.edgecolor"])
        ax.spines['top'].set_color(self._theme_config["axes.edgecolor"])
        ax.spines['left'].set_color(self._theme_config["axes.edgecolor"])
        ax.spines['right'].set_color(self._theme_config["axes.edgecolor"])
        ax.yaxis.label.set_color(self._theme_config["axes.labelcolor"])
        ax.xaxis.label.set_color(self._theme_config["axes.labelcolor"])
        
        if self._theme_config["axes.grid"]:
            ax.grid(True, color=self._theme_config["grid.color"], linestyle=self._theme_config["grid.linestyle"])
        
        self._axes[name] = ax
        return self
    
    def set_data(self, data: pd.DataFrame) -> 'IndicatorVisualizer':
        """
        设置数据
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据，通常包含OHLCV数据
            
        Returns
        -------
        IndicatorVisualizer
            可视化器对象本身，支持链式调用
        """
        self._data = data.copy()
        return self
    
    def add_indicator(self, indicator: Union[Indicator, str]) -> 'IndicatorVisualizer':
        """
        添加指标
        
        Parameters
        ----------
        indicator : Union[Indicator, str]
            要添加的指标，可以是Indicator对象或列名
            
        Returns
        -------
        IndicatorVisualizer
            可视化器对象本身，支持链式调用
        """
        self._indicators.append(indicator)
        return self
    
    def plot_candle(self, ax_name: str = "price", ohlc_cols: List[str] = None) -> 'IndicatorVisualizer':
        """
        绘制蜡烛图
        
        Parameters
        ----------
        ax_name : str, optional
            要绘制的子图名称，默认为"price"
        ohlc_cols : List[str], optional
            OHLC列名，默认为["open", "high", "low", "close"]
            
        Returns
        -------
        IndicatorVisualizer
            可视化器对象本身，支持链式调用
        """
        if self._data is None:
            raise ValueError("请先设置数据")
        
        if ax_name not in self._axes:
            self.add_subplot(ax_name)
        
        ax = self._axes[ax_name]
        
        if ohlc_cols is None:
            ohlc_cols = ["open", "high", "low", "close"]
        
        # 验证列是否存在
        for col in ohlc_cols:
            if col not in self._data.columns:
                raise ValueError(f"数据中缺少列: {col}")
        
        # 提取OHLC数据
        ohlc_data = self._data[ohlc_cols].copy()
        
        # 计算上涨/下跌
        up_mask = ohlc_data[ohlc_cols[3]] > ohlc_data[ohlc_cols[0]]
        width = 0.6  # 蜡烛宽度
        
        # 绘制影线
        for idx, (_, row) in enumerate(ohlc_data.iterrows()):
            if up_mask.iloc[idx]:
                color = self._theme_config["price.up.color"]
            else:
                color = self._theme_config["price.down.color"]
            
            # 绘制上下影线
            ax.plot([idx, idx], [row[ohlc_cols[1]], row[ohlc_cols[2]]], color=color)
        
        # 绘制实体
        for idx, (_, row) in enumerate(ohlc_data.iterrows()):
            if up_mask.iloc[idx]:
                color = self._theme_config["price.up.color"]
                bottom = row[ohlc_cols[0]]
                height = row[ohlc_cols[3]] - row[ohlc_cols[0]]
            else:
                color = self._theme_config["price.down.color"]
                bottom = row[ohlc_cols[3]]
                height = row[ohlc_cols[0]] - row[ohlc_cols[3]]
            
            rect = mpatches.Rectangle((idx - width/2, bottom), width, height, 
                                     fill=True, color=color)
            ax.add_patch(rect)
        
        # 设置x轴刻度和标签
        ax.set_xlim(-0.5, len(self._data) - 0.5)
        if isinstance(self._data.index[0], datetime):
            date_labels = [date.strftime(self._theme_config["date_format"]) for date in self._data.index]
            # 仅显示部分日期标签，避免重叠
            step = max(1, len(date_labels) // 10)
            ax.set_xticks(range(0, len(date_labels), step))
            ax.set_xticklabels([date_labels[i] for i in range(0, len(date_labels), step)], rotation=45)
        
        ax.set_ylabel("Price")
        return self
    
    def plot_line(self, column: str, ax_name: str = "price", 
                 color: Optional[str] = None, label: Optional[str] = None, **kwargs) -> 'IndicatorVisualizer':
        """
        绘制线图
        
        Parameters
        ----------
        column : str
            要绘制的列名
        ax_name : str, optional
            要绘制的子图名称，默认为"price"
        color : str, optional
            线条颜色，默认为None (自动选择)
        label : str, optional
            线条标签，默认为列名
        **kwargs : dict
            传递给plot的其他参数
            
        Returns
        -------
        IndicatorVisualizer
            可视化器对象本身，支持链式调用
        """
        if self._data is None:
            raise ValueError("请先设置数据")
        
        if column not in self._data.columns:
            raise ValueError(f"数据中缺少列: {column}")
        
        if ax_name not in self._axes:
            self.add_subplot(ax_name)
        
        ax = self._axes[ax_name]
        
        if label is None:
            label = column
        
        # 绘制线图
        line = ax.plot(range(len(self._data)), self._data[column], 
                      color=color, label=label, 
                      linewidth=self._theme_config["lines.linewidth"], **kwargs)
        
        # 添加图例
        ax.legend(loc=self._theme_config["legend.loc"], frameon=self._theme_config["legend.frameon"])
        
        return self
    
    def plot_volume(self, ax_name: str = "volume", column: str = "volume") -> 'IndicatorVisualizer':
        """
        绘制成交量柱状图
        
        Parameters
        ----------
        ax_name : str, optional
            要绘制的子图名称，默认为"volume"
        column : str, optional
            成交量列名，默认为"volume"
            
        Returns
        -------
        IndicatorVisualizer
            可视化器对象本身，支持链式调用
        """
        if self._data is None:
            raise ValueError("请先设置数据")
        
        if column not in self._data.columns:
            raise ValueError(f"数据中缺少列: {column}")
        
        if ax_name not in self._axes:
            if "price" in self._axes:
                self.add_subplot(ax_name, sharex="price")
            else:
                self.add_subplot(ax_name)
        
        ax = self._axes[ax_name]
        
        # 计算上涨/下跌
        up_mask = None
        if "close" in self._data.columns and "open" in self._data.columns:
            up_mask = self._data["close"] > self._data["open"]
        
        width = 0.6
        
        # 绘制成交量柱状图
        for idx, (_, row) in enumerate(self._data.iterrows()):
            if up_mask is not None:
                if up_mask.iloc[idx]:
                    color = self._theme_config["volume.up.color"]
                else:
                    color = self._theme_config["volume.down.color"]
            else:
                color = self._theme_config["volume.up.color"]
            
            ax.bar(idx, row[column], width=width, color=color, 
                  alpha=self._theme_config["volume.alpha"])
        
        ax.set_ylabel("Volume")
        return self
    
    def plot_indicator(self, indicator: Union[Indicator, str], 
                     ax_name: Optional[str] = None, **kwargs) -> 'IndicatorVisualizer':
        """
        绘制指标
        
        Parameters
        ----------
        indicator : Union[Indicator, str]
            要绘制的指标，可以是Indicator对象或列名
        ax_name : str, optional
            要绘制的子图名称，默认为新创建的子图
        **kwargs : dict
            传递给绘图函数的其他参数
            
        Returns
        -------
        IndicatorVisualizer
            可视化器对象本身，支持链式调用
        """
        if self._data is None:
            raise ValueError("请先设置数据")
        
        # 处理指标对象
        if isinstance(indicator, Indicator):
            # 确保指标已计算
            if indicator.result is None:
                result_data = indicator.calculate(self._data)
                # 更新内部数据以包含指标结果
                self._data = result_data
            else:
                # 确保使用最新数据计算指标
                result_data = indicator.calculate(self._data)
                self._data = result_data
            
            # 获取指标列（非OHLCV列）
            ohlcv_cols = ['open', 'high', 'low', 'close', 'volume']
            indicator_cols = [col for col in result_data.columns if col not in ohlcv_cols]
            
            # 如果未指定子图，为每个指标创建新的子图
            if ax_name is None:
                ax_name = f"indicator_{len(self._axes)}"
                if "price" in self._axes:
                    self.add_subplot(ax_name, sharex="price")
                else:
                    self.add_subplot(ax_name)
            
            # 绘制所有指标列
            for col in indicator_cols:
                self.plot_line(col, ax_name=ax_name, label=col, **kwargs)
                
        # 处理列名
        elif isinstance(indicator, str):
            if indicator not in self._data.columns:
                raise ValueError(f"数据中缺少列: {indicator}")
            
            # 如果未指定子图，创建新的子图
            if ax_name is None:
                ax_name = f"indicator_{len(self._axes)}"
                if "price" in self._axes:
                    self.add_subplot(ax_name, sharex="price")
                else:
                    self.add_subplot(ax_name)
            
            self.plot_line(indicator, ax_name=ax_name, label=indicator, **kwargs)
        
        return self
    
    def plot_signals(self, signals: pd.DataFrame, ax_name: str = "price",
                   buy_col: str = "buy_signal", sell_col: str = "sell_signal",
                   buy_marker: str = "^", sell_marker: str = "v",
                   buy_color: Optional[str] = None, sell_color: Optional[str] = None,
                   buy_size: int = 100, sell_size: int = 100) -> 'IndicatorVisualizer':
        """
        绘制交易信号
        
        Parameters
        ----------
        signals : pd.DataFrame
            包含交易信号的DataFrame
        ax_name : str, optional
            要绘制的子图名称，默认为"price"
        buy_col : str, optional
            买入信号列名，默认为"buy_signal"
        sell_col : str, optional
            卖出信号列名，默认为"sell_signal"
        buy_marker : str, optional
            买入信号标记，默认为"^"
        sell_marker : str, optional
            卖出信号标记，默认为"v"
        buy_color : str, optional
            买入信号颜色，默认使用主题配置
        sell_color : str, optional
            卖出信号颜色，默认使用主题配置
        buy_size : int, optional
            买入信号大小，默认为100
        sell_size : int, optional
            卖出信号大小，默认为100
            
        Returns
        -------
        IndicatorVisualizer
            可视化器对象本身，支持链式调用
        """
        if self._data is None:
            raise ValueError("请先设置数据")
        
        if ax_name not in self._axes:
            raise ValueError(f"未找到子图: {ax_name}")
        
        ax = self._axes[ax_name]
        
        # 检查信号列是否存在
        if buy_col in signals.columns:
            # 处理买入信号
            if buy_color is None:
                buy_color = self._theme_config["price.up.color"]
            
            for idx, value in enumerate(signals[buy_col]):
                if not pd.isna(value) and value > 0:
                    price = self._data.iloc[idx]["close"] if "close" in self._data.columns else value
                    ax.scatter(idx, price * 0.99, marker=buy_marker, s=buy_size, color=buy_color)
        
        if sell_col in signals.columns:
            # 处理卖出信号
            if sell_color is None:
                sell_color = self._theme_config["price.down.color"]
            
            for idx, value in enumerate(signals[sell_col]):
                if not pd.isna(value) and value > 0:
                    price = self._data.iloc[idx]["close"] if "close" in self._data.columns else value
                    ax.scatter(idx, price * 1.01, marker=sell_marker, s=sell_size, color=sell_color)
        
        # 添加图例
        handles = []
        labels = []
        
        if buy_col in signals.columns:
            handles.append(Line2D([0], [0], marker=buy_marker, color='w', markerfacecolor=buy_color, markersize=10))
            labels.append("Buy Signal")
        
        if sell_col in signals.columns:
            handles.append(Line2D([0], [0], marker=sell_marker, color='w', markerfacecolor=sell_color, markersize=10))
            labels.append("Sell Signal")
        
        if handles and labels:
            ax.legend(handles=handles, labels=labels, loc='best')
        
        return self
    
    def add_grid_layout(self, height_ratios: Optional[List[float]] = None) -> 'IndicatorVisualizer':
        """
        添加网格布局
        
        Parameters
        ----------
        height_ratios : List[float], optional
            每行的高度比例，默认为None (均等分配)
            
        Returns
        -------
        IndicatorVisualizer
            可视化器对象本身，支持链式调用
        """
        if self._figure is None:
            self.create_figure()
        
        # 清除之前的轴和布局
        self._figure.clear()
        self._axes = {}
        
        # 获取网格布局参数
        if height_ratios is None:
            height_ratios = [3, 1]  # 默认价格和成交量比例
        
        n_rows = len(height_ratios)
        
        # 创建网格
        self._grid = gridspec.GridSpec(n_rows, 1, height_ratios=height_ratios, figure=self._figure)
        
        return self
    
    def add_subplot_with_grid(self, name: str, row: int, sharex: Optional[str] = None, **kwargs) -> 'IndicatorVisualizer':
        """
        使用网格布局添加子图
        
        Parameters
        ----------
        name : str
            子图名称
        row : int
            行索引
        sharex : str, optional
            共享x轴的子图名称
        **kwargs : dict
            传递给subplot的其他参数
            
        Returns
        -------
        IndicatorVisualizer
            可视化器对象本身，支持链式调用
        """
        if not hasattr(self, '_grid'):
            raise ValueError("请先调用add_grid_layout方法创建网格布局")
        
        if sharex is not None and sharex in self._axes:
            ax = self._figure.add_subplot(self._grid[row], sharex=self._axes[sharex], **kwargs)
        else:
            ax = self._figure.add_subplot(self._grid[row], **kwargs)
        
        # 应用主题设置
        ax.set_facecolor(self._theme_config["axes.facecolor"])
        ax.tick_params(axis='both', colors=self._theme_config["xtick.color"])
        ax.spines['bottom'].set_color(self._theme_config["axes.edgecolor"])
        ax.spines['top'].set_color(self._theme_config["axes.edgecolor"])
        ax.spines['left'].set_color(self._theme_config["axes.edgecolor"])
        ax.spines['right'].set_color(self._theme_config["axes.edgecolor"])
        ax.yaxis.label.set_color(self._theme_config["axes.labelcolor"])
        ax.xaxis.label.set_color(self._theme_config["axes.labelcolor"])
        
        if self._theme_config["axes.grid"]:
            ax.grid(True, color=self._theme_config["grid.color"], linestyle=self._theme_config["grid.linestyle"])
        
        self._axes[name] = ax
        return self
    
    def set_title(self, title: str, **kwargs) -> 'IndicatorVisualizer':
        """
        设置标题
        
        Parameters
        ----------
        title : str
            标题文本
        **kwargs : dict
            传递给title函数的其他参数
            
        Returns
        -------
        IndicatorVisualizer
            可视化器对象本身，支持链式调用
        """
        if self._figure is None:
            raise ValueError("请先创建图形")
        
        if "price" in self._axes:
            self._axes["price"].set_title(title, color=self._theme_config["text.color"], **kwargs)
        else:
            keys = list(self._axes.keys())
            if keys:
                self._axes[keys[0]].set_title(title, color=self._theme_config["text.color"], **kwargs)
            else:
                self._figure.suptitle(title, color=self._theme_config["text.color"], **kwargs)
        
        return self
    
    def save(self, filename: str, dpi: int = 300, **kwargs) -> 'IndicatorVisualizer':
        """
        保存图形
        
        Parameters
        ----------
        filename : str
            文件名
        dpi : int, optional
            分辨率，默认为300
        **kwargs : dict
            传递给savefig的其他参数
            
        Returns
        -------
        IndicatorVisualizer
            可视化器对象本身，支持链式调用
        """
        if self._figure is None:
            raise ValueError("没有图形可保存")
        
        self._figure.tight_layout()
        self._figure.savefig(filename, dpi=dpi, **kwargs)
        
        return self
    
    def show(self, **kwargs) -> 'IndicatorVisualizer':
        """
        显示图形
        
        Parameters
        ----------
        **kwargs : dict
            传递给show的其他参数
            
        Returns
        -------
        IndicatorVisualizer
            可视化器对象本身，支持链式调用
        """
        if self._figure is None:
            raise ValueError("没有图形可显示")
        
        self._figure.tight_layout()
        plt.show(**kwargs)
        
        return self


# 保留现有的plot_with_indicators函数
def plot_with_indicators(
    data: pd.DataFrame,
    indicators: List[Union[Indicator, str]] = None,
    figsize: Tuple[int, int] = (12, 8),
    price_panel_ratio: float = 3,
    volume_panel_ratio: float = 1,
    max_indicators_per_panel: int = 2,
    separate_indicator_panels: bool = True,
    price_label: str = 'close',
    show_volume: bool = True,
    title: str = "Price with Technical Indicators",
    **kwargs
) -> Tuple[plt.Figure, Dict[str, Axes]]:
    """
    绘制带有技术指标的价格图表
    
    Parameters
    ----------
    data : pd.DataFrame
        包含价格和指标数据的DataFrame
    indicators : List[Union[Indicator, str]], optional
        要绘制的指标列表，可以是Indicator对象或数据中的列名
    figsize : Tuple[int, int], optional
        图表大小，默认为(12, 8)
    price_panel_ratio : float, optional
        价格面板的相对大小，默认为3
    volume_panel_ratio : float, optional
        交易量面板的相对大小，默认为1
    max_indicators_per_panel : int, optional
        每个面板的最大指标数量，默认为2
    separate_indicator_panels : bool, optional
        是否将指标分别绘制在单独的面板上，默认为True
    price_label : str, optional
        用于价格的列名，默认为'close'
    show_volume : bool, optional
        是否显示交易量，默认为True
    title : str, optional
        图表标题，默认为"Price with Technical Indicators"
    **kwargs : dict
        传递给plt.subplots的其他参数
        
    Returns
    -------
    Tuple[plt.Figure, Dict[str, Axes]]
        图表对象和包含所有axes的字典
    """
    # 准备指标数据
    if indicators is None:
        indicators = []
    
    # 计算需要的面板数量
    n_panels = 1  # 价格面板
    
    if show_volume and 'volume' in data.columns:
        n_panels += 1
    
    # 统计指标面板数量
    indicator_cols = []
    for ind in indicators:
        if isinstance(ind, str):
            indicator_cols.append(ind)
        elif isinstance(ind, Indicator):
            # 在需要时计算指标
            if ind.result is None:
                _ = ind.calculate(data)
            
            # 获取非OHLCV列
            ohlcv_cols = ['open', 'high', 'low', 'close', 'volume']
            ind_cols = [col for col in ind.result.columns if col not in ohlcv_cols]
            indicator_cols.extend(ind_cols)
    
    # 决定指标布局
    if separate_indicator_panels:
        # 每个面板最多显示max_indicators_per_panel个指标
        n_indicator_panels = (len(indicator_cols) + max_indicators_per_panel - 1) // max_indicators_per_panel
        n_panels += n_indicator_panels
    else:
        # 所有指标在一个面板中
        if indicator_cols:
            n_panels += 1
    
    # 创建子图
    fig = plt.figure(figsize=figsize)
    
    # 设置网格布局
    ratios = [price_panel_ratio]
    if show_volume and 'volume' in data.columns:
        ratios.append(volume_panel_ratio)
    
    # 添加指标面板的比例
    for _ in range(n_panels - len(ratios)):
        ratios.append(1)
    
    # 创建网格
    gs = gridspec.GridSpec(n_panels, 1, height_ratios=ratios)
    
    # 创建轴对象字典
    axes = {}
    
    # 价格面板
    axes['price'] = plt.subplot(gs[0])
    axes['price'].set_title(title)
    axes['price'].plot(data.index, data[price_label], label=price_label.capitalize())
    axes['price'].set_ylabel('Price')
    axes['price'].grid(True)
    axes['price'].legend()
    
    # 当前面板索引
    panel_idx = 1
    
    # 交易量面板
    if show_volume and 'volume' in data.columns:
        axes['volume'] = plt.subplot(gs[panel_idx], sharex=axes['price'])
        axes['volume'].bar(data.index, data['volume'], width=0.6, alpha=0.7, label='Volume')
        axes['volume'].set_ylabel('Volume')
        axes['volume'].grid(True)
        axes['volume'].legend()
        panel_idx += 1
    
    # 指标面板
    if separate_indicator_panels:
        # 分别绘制指标
        current_panel = None
        current_panel_idx = 0
        
        for i, ind_col in enumerate(indicator_cols):
            # 每max_indicators_per_panel个指标创建一个新面板
            if i % max_indicators_per_panel == 0:
                panel_name = f"indicator_{current_panel_idx}"
                current_panel = plt.subplot(gs[panel_idx], sharex=axes['price'])
                axes[panel_name] = current_panel
                panel_idx += 1
                current_panel_idx += 1
            
            # 在当前面板上绘制指标
            current_panel.plot(data.index, data[ind_col], label=ind_col)
            current_panel.set_ylabel('Value')
            current_panel.grid(True)
            current_panel.legend()
    else:
        # 所有指标在一个面板中
        if indicator_cols:
            axes['indicators'] = plt.subplot(gs[panel_idx], sharex=axes['price'])
            for ind_col in indicator_cols:
                axes['indicators'].plot(data.index, data[ind_col], label=ind_col)
            axes['indicators'].set_ylabel('Indicators')
            axes['indicators'].grid(True)
            axes['indicators'].legend()
    
    # 调整布局
    plt.tight_layout()
    
    return fig, axes 