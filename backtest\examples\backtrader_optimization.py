#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Backtrader参数优化示例

演示如何使用Backtrader引擎的参数优化和Walk Forward Analysis功能。
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import matplotlib.pyplot as plt
import matplotlib
import logging
import sys

# 添加项目根目录到路径，确保正确的绝对路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

# 导入相关模块
from backtest.backtrader.core import BacktraderEngine
from backtest.backtrader.optimization import ParameterOptimizer, WalkForwardAnalysis
from backtest.strategies.templates import MovingAverageCrossover
from data.structures import OHLCV
from data.sources.utils import download_crypto_data

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data(n_days=200):
    """创建测试数据或下载真实数据"""
    try:
        # 尝试下载比特币数据
        logger.info("尝试下载比特币历史数据...")
        data = download_crypto_data("BTC/USDT", "1d", 
                                    start_time=(datetime.now() - timedelta(days=n_days)),
                                    end_time=datetime.now())
        if data is not None and len(data) > 0:
            logger.info(f"成功下载数据，共 {len(data)} 条记录")
            return data
    except Exception as e:
        logger.warning(f"无法下载数据: {e}")
    
    # 如果下载失败，则生成模拟数据
    logger.info("生成模拟数据...")
    # 生成日期序列
    end_date = datetime.now()
    start_date = end_date - timedelta(days=n_days)
    dates = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # 生成价格数据
    np.random.seed(42)
    price = 100
    prices = [price]
    
    for i in range(1, len(dates)):
        change_percent = np.random.normal(0.0005, 0.01)
        price = price * (1 + change_percent)
        prices.append(price)
    
    # 基于收盘价生成OHLCV数据
    closes = np.array(prices)
    highs = closes * (1 + np.random.uniform(0, 0.015, len(dates)))
    lows = closes * (1 - np.random.uniform(0, 0.015, len(dates)))
    opens = lows + np.random.uniform(0, 1, len(dates)) * (highs - lows)
    volumes = np.random.uniform(1000, 10000, len(dates)) * closes
    
    # 创建DataFrame
    df = pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    }, index=dates)
    
    return df

def grid_search_example():
    """参数网格搜索示例"""
    logger.info("运行参数网格搜索示例...")
    
    # 获取数据
    data = create_test_data(n_days=365)
    
    # 创建回测引擎
    engine = BacktraderEngine(data, initial_cash=100000)
    
    # 创建参数优化器
    optimizer = ParameterOptimizer(
        engine=engine,
        strategy_class=MovingAverageCrossover,
        data=data,
        metric='sharpe_ratio',
        maximize=True,
        initial_cash=100000,
        commission=0.001
    )
    
    # 定义参数网格
    param_grid = {
        'short_window': [5, 10, 15, 20],
        'long_window': [30, 40, 50, 60],
        'position_size': [0.1, 0.25, 0.5, 0.75, 1.0]
    }
    
    # 运行网格搜索（使用串行处理避免多进程问题）
    results = optimizer.grid_search(param_grid, n_jobs=1)
    
    # 打印结果
    print("\n参数优化结果（前5）:")
    print(results[['short_window', 'long_window', 'position_size', 'sharpe_ratio', 'total_return', 'max_drawdown', 'win_rate']].head())
    
    # 获取最佳参数
    best_params = optimizer.get_best_params()
    print("\n最佳参数组合:")
    print(best_params)
    
    # 可视化优化结果
    optimizer.plot_optimization_results()
    
    # 使用最佳参数运行策略
    best_strategy = MovingAverageCrossover(**best_params)
    results = engine.run(best_strategy)
    
    # 可视化回测结果
    engine.plot()
    
    return results, best_params

def walk_forward_analysis_example():
    """Walk Forward Analysis示例"""
    logger.info("运行Walk Forward Analysis示例...")
    
    # 获取数据
    data = create_test_data(n_days=730)  # 使用两年数据
    
    # 创建回测引擎
    engine = BacktraderEngine(data, initial_cash=100000)
    
    # 创建Walk Forward Analysis
    wfa = WalkForwardAnalysis(
        engine=engine,
        strategy_class=MovingAverageCrossover,
        data=data,
        train_size=0.7,
        test_size=0.3,
        n_windows=5,
        metric='sharpe_ratio',
        maximize=True,
        initial_cash=100000,
        commission=0.001
    )
    
    # 定义参数网格
    param_grid = {
        'short_window': [5, 10, 15, 20],
        'long_window': [30, 40, 50, 60],
        'position_size': [0.25, 0.5, 0.75]
    }
    
    # 运行Walk Forward Analysis
    results = wfa.run(param_grid, n_jobs=4)
    
    # 打印结果
    print("\nWalk Forward Analysis结果:")
    print(results[['window', 'sharpe_ratio', 'total_return', 'max_drawdown', 'win_rate']])
    
    # 获取稳健参数
    robust_params = wfa.get_robust_params()
    print("\n稳健参数组合:")
    print(robust_params)
    
    # 可视化Walk Forward结果
    wfa.plot_results()
    
    # 使用稳健参数运行策略
    robust_strategy = MovingAverageCrossover(**robust_params)
    final_results = engine.run(robust_strategy)
    
    # 可视化回测结果
    engine.plot()
    
    return results, robust_params

def main():
    """主函数"""
    print("Backtrader参数优化示例")
    print("----------------------")
    print("1. 参数网格搜索")
    print("2. Walk Forward Analysis")
    print("3. 两者都运行")
    
    choice = input("请选择要运行的示例 (1/2/3): ")
    
    if choice == '1':
        grid_search_example()
    elif choice == '2':
        walk_forward_analysis_example()
    elif choice == '3':
        print("\n运行参数网格搜索...")
        grid_results, best_params = grid_search_example()
        
        print("\n运行Walk Forward Analysis...")
        wfa_results, robust_params = walk_forward_analysis_example()
        
        # 比较结果
        print("\n参数比较:")
        print(f"网格搜索最佳参数: {best_params}")
        print(f"WFA稳健参数: {robust_params}")
    else:
        print("无效选择，退出")

if __name__ == "__main__":
    main()