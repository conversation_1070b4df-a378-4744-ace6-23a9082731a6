#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SMC策略修复效果测试脚本
验证修复后的策略性能和连接状态

🔍 测试内容：
1. 网络连接测试
2. 策略加载测试
3. 信号质量测试
4. 回测性能对比
"""

import sys
import json
import logging
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_network_connection():
    """测试网络连接"""
    logger.info("🔍 测试网络连接...")
    
    try:
        import requests
        
        # 测试Binance API
        response = requests.get("https://api.binance.com/api/v3/ping", timeout=10)
        if response.status_code == 200:
            logger.info("✅ Binance API连接正常")
            return True
        else:
            logger.error(f"❌ Binance API连接失败: {response.status_code}")
            return False
    
    except Exception as e:
        logger.error(f"❌ 网络连接测试失败: {e}")
        return False

def test_strategy_loading():
    """测试策略加载"""
    logger.info("🔍 测试策略加载...")
    
    try:
        # 添加策略路径
        strategy_path = Path("backtest/strategies")
        if strategy_path.exists():
            sys.path.insert(0, str(strategy_path))
        
        # 导入策略
        from smc_strategy import SMCStrategy
        
        # 创建策略实例
        strategy = SMCStrategy()
        
        logger.info("✅ 策略加载成功")
        logger.info(f"  时间框架: {strategy.timeframe}")
        logger.info(f"  止损: {strategy.stoploss}")
        logger.info(f"  ROI: {strategy.minimal_roi}")
        logger.info(f"  订单类型: {strategy.order_types}")
        
        return True, strategy
    
    except Exception as e:
        logger.error(f"❌ 策略加载失败: {e}")
        return False, None

def test_signal_quality(strategy):
    """测试信号质量"""
    logger.info("🔍 测试信号质量...")
    
    try:
        # 创建测试数据
        dates = pd.date_range(start='2024-01-01', end='2024-01-02', freq='5min')
        n = len(dates)
        
        # 生成模拟OHLCV数据
        import numpy as np
        np.random.seed(42)
        
        base_price = 50000
        price_changes = np.random.normal(0, 0.001, n).cumsum()
        prices = base_price * (1 + price_changes)
        
        test_data = pd.DataFrame({
            'open': prices * (1 + np.random.normal(0, 0.0001, n)),
            'high': prices * (1 + np.abs(np.random.normal(0, 0.002, n))),
            'low': prices * (1 - np.abs(np.random.normal(0, 0.002, n))),
            'close': prices,
            'volume': np.random.uniform(100, 1000, n)
        }, index=dates)
        
        # 计算指标
        metadata = {'pair': 'BTC/USDT:USDT'}
        test_data = strategy.populate_indicators(test_data, metadata)
        
        # 生成信号
        test_data = strategy.populate_entry_trend(test_data, metadata)
        test_data = strategy.populate_exit_trend(test_data, metadata)
        
        # 统计信号
        long_signals = test_data['enter_long'].sum()
        short_signals = test_data['enter_short'].sum()
        total_signals = long_signals + short_signals
        
        long_exits = test_data['exit_long'].sum()
        short_exits = test_data['exit_short'].sum()
        total_exits = long_exits + short_exits
        
        logger.info("✅ 信号质量测试完成")
        logger.info(f"  入场信号: 多头={long_signals}, 空头={short_signals}, 总计={total_signals}")
        logger.info(f"  出场信号: 多头出场={long_exits}, 空头出场={short_exits}, 总计={total_exits}")
        logger.info(f"  信号频率: {total_signals/len(test_data)*100:.2f}% (每根K线)")
        
        # 检查信号质量
        if total_signals > 0:
            signal_ratio = total_signals / len(test_data)
            if signal_ratio < 0.05:  # 少于5%的K线有信号
                logger.info("✅ 信号频率合理，避免过度交易")
            else:
                logger.warning("⚠️ 信号频率较高，可能存在过度交易")
        
        return True
    
    except Exception as e:
        logger.error(f"❌ 信号质量测试失败: {e}")
        return False

def test_freqtrade_config():
    """测试FreqTrade配置"""
    logger.info("🔍 测试FreqTrade配置...")
    
    config_path = Path("freqtrade-bot/config.json")
    if not config_path.exists():
        logger.error("❌ FreqTrade配置文件不存在")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查关键配置
        checks = [
            ("策略名称", config.get('strategy') == 'SMCStrategy'),
            ("价格配置", config.get('entry_pricing', {}).get('price_side') == 'same'),
            ("最大交易数", config.get('max_open_trades') <= 10),
            ("处理延迟", config.get('internals', {}).get('process_throttle_secs') <= 2),
            ("流动性检查", config.get('entry_pricing', {}).get('check_depth_of_market', {}).get('bids_to_ask_delta', 0) <= 0.05)
        ]
        
        all_passed = True
        for check_name, result in checks:
            if result:
                logger.info(f"  ✅ {check_name}: 通过")
            else:
                logger.warning(f"  ⚠️ {check_name}: 未通过")
                all_passed = False
        
        if all_passed:
            logger.info("✅ FreqTrade配置检查通过")
        else:
            logger.warning("⚠️ FreqTrade配置存在问题")
        
        return all_passed
    
    except Exception as e:
        logger.error(f"❌ FreqTrade配置测试失败: {e}")
        return False

def generate_test_report():
    """生成测试报告"""
    logger.info("📊 生成测试报告...")
    
    report = {
        'test_time': datetime.now().isoformat(),
        'network_connection': False,
        'strategy_loading': False,
        'signal_quality': False,
        'freqtrade_config': False,
        'overall_status': 'FAILED'
    }
    
    # 执行所有测试
    report['network_connection'] = test_network_connection()
    
    strategy_loaded, strategy = test_strategy_loading()
    report['strategy_loading'] = strategy_loaded
    
    if strategy:
        report['signal_quality'] = test_signal_quality(strategy)
    
    report['freqtrade_config'] = test_freqtrade_config()
    
    # 计算总体状态
    if all([report['network_connection'], report['strategy_loading'], 
            report['signal_quality'], report['freqtrade_config']]):
        report['overall_status'] = 'PASSED'
    elif report['network_connection'] and report['strategy_loading']:
        report['overall_status'] = 'PARTIAL'
    
    # 保存报告
    report_path = Path("smc_fix_test_report.json")
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    logger.info(f"📋 测试报告已保存: {report_path}")
    
    return report

def main():
    """主测试流程"""
    logger.info("🚀 开始SMC策略修复效果测试...")
    
    report = generate_test_report()
    
    # 输出测试结果
    logger.info("\n" + "="*50)
    logger.info("📊 测试结果总结")
    logger.info("="*50)
    
    status_emoji = {
        'PASSED': '✅',
        'PARTIAL': '⚠️',
        'FAILED': '❌'
    }
    
    logger.info(f"总体状态: {status_emoji[report['overall_status']]} {report['overall_status']}")
    logger.info(f"网络连接: {'✅' if report['network_connection'] else '❌'}")
    logger.info(f"策略加载: {'✅' if report['strategy_loading'] else '❌'}")
    logger.info(f"信号质量: {'✅' if report['signal_quality'] else '❌'}")
    logger.info(f"配置检查: {'✅' if report['freqtrade_config'] else '❌'}")
    
    if report['overall_status'] == 'PASSED':
        logger.info("\n🎉 所有测试通过！策略修复成功")
        logger.info("💡 建议：")
        logger.info("  1. 启动FreqTrade进行实际测试")
        logger.info("  2. 监控前几笔交易的执行情况")
        logger.info("  3. 根据实际表现微调参数")
    
    elif report['overall_status'] == 'PARTIAL':
        logger.info("\n⚠️ 部分测试通过，需要进一步检查")
        logger.info("💡 建议：")
        logger.info("  1. 检查失败的测试项目")
        logger.info("  2. 修复相关问题后重新测试")
    
    else:
        logger.info("\n❌ 测试失败，需要修复问题")
        logger.info("💡 建议：")
        logger.info("  1. 检查网络连接")
        logger.info("  2. 确认策略文件路径")
        logger.info("  3. 重新运行修复脚本")
    
    return report['overall_status'] == 'PASSED'

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
