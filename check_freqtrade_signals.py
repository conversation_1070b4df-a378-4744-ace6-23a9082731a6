#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FreqTrade信号执行问题诊断脚本
检查为什么有信号但没有实际交易执行
"""

import sys
import json
import pandas as pd
import numpy as np
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_freqtrade_config():
    """检查FreqTrade配置"""
    logger.info("🔍 检查FreqTrade配置...")
    
    config_path = Path("freqtrade-bot/config.json")
    if not config_path.exists():
        logger.error("❌ FreqTrade配置文件不存在")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查关键配置
        issues = []
        
        # 1. 检查资金配置
        if config.get('stake_amount') == 'unlimited':
            if config.get('tradable_balance_ratio', 1.0) < 0.1:
                issues.append("tradable_balance_ratio过低，可能导致无法开仓")
        
        # 2. 检查交易对配置
        pair_whitelist = config.get('exchange', {}).get('pair_whitelist', [])
        if not pair_whitelist:
            issues.append("没有配置交易对白名单")
        
        # 3. 检查订单配置
        entry_pricing = config.get('entry_pricing', {})
        if entry_pricing.get('check_depth_of_market', {}).get('enabled'):
            delta = entry_pricing.get('check_depth_of_market', {}).get('bids_to_ask_delta', 0)
            if delta > 0.05:
                issues.append(f"bids_to_ask_delta过高 ({delta})，可能阻止交易执行")
        
        # 4. 检查最大开仓数
        max_open_trades = config.get('max_open_trades', 0)
        if max_open_trades <= 0:
            issues.append("max_open_trades配置错误")
        
        # 5. 检查干跑模式
        dry_run = config.get('dry_run', False)
        dry_run_wallet = config.get('dry_run_wallet', 0)
        if dry_run and dry_run_wallet <= 0:
            issues.append("干跑模式下钱包余额为0")
        
        logger.info("✅ FreqTrade配置检查完成")
        logger.info(f"  干跑模式: {dry_run}")
        logger.info(f"  干跑钱包: {dry_run_wallet} USDT")
        logger.info(f"  最大开仓: {max_open_trades}")
        logger.info(f"  交易对数量: {len(pair_whitelist)}")
        logger.info(f"  资金配置: {config.get('stake_amount')}")
        logger.info(f"  可交易比例: {config.get('tradable_balance_ratio', 1.0)}")
        
        if issues:
            logger.warning("⚠️ 发现配置问题:")
            for issue in issues:
                logger.warning(f"  - {issue}")
            return False
        else:
            logger.info("✅ 配置检查通过")
            return True
            
    except Exception as e:
        logger.error(f"❌ 配置检查失败: {e}")
        return False

def test_strategy_signals():
    """测试策略信号生成"""
    logger.info("🔍 测试策略信号生成...")
    
    try:
        # 导入策略
        sys.path.insert(0, 'backtest/strategies')
        from smc_strategy import SMCStrategy
        
        # 创建策略实例
        strategy = SMCStrategy()
        
        # 创建测试数据 - 模拟真实市场条件
        dates = pd.date_range(start='2024-01-01', end='2024-01-01 02:00', freq='5min')
        n = len(dates)
        np.random.seed(42)
        
        # 创建有趋势的价格数据
        base_price = 50000
        trend = np.linspace(0, 0.02, n)  # 2%的上升趋势
        noise = np.random.normal(0, 0.005, n).cumsum()
        price_changes = trend + noise * 0.5
        prices = base_price * (1 + price_changes)
        
        test_data = pd.DataFrame({
            'open': prices * (1 + np.random.normal(0, 0.0001, n)),
            'high': prices * (1 + np.abs(np.random.normal(0, 0.002, n))),
            'low': prices * (1 - np.abs(np.random.normal(0, 0.002, n))),
            'close': prices,
            'volume': np.random.uniform(1000, 5000, n)  # 增加成交量
        }, index=dates)
        
        logger.info(f"✅ 测试数据创建: {len(test_data)} 行")
        logger.info(f"  价格范围: {test_data['close'].min():.2f} - {test_data['close'].max():.2f}")
        logger.info(f"  价格变化: {((test_data['close'].iloc[-1] / test_data['close'].iloc[0] - 1) * 100):+.2f}%")
        
        # 测试指标计算
        metadata = {'pair': 'BTC/USDT:USDT'}
        test_data = strategy.populate_indicators(test_data, metadata)
        logger.info("✅ 指标计算完成")
        
        # 测试入场信号
        test_data = strategy.populate_entry_trend(test_data, metadata)
        entry_long = test_data['enter_long'].sum()
        entry_short = test_data['enter_short'].sum()
        logger.info(f"✅ 入场信号: 多头={entry_long}, 空头={entry_short}")
        
        # 测试出场信号
        test_data = strategy.populate_exit_trend(test_data, metadata)
        exit_long = test_data['exit_long'].sum()
        exit_short = test_data['exit_short'].sum()
        logger.info(f"✅ 出场信号: 多头出场={exit_long}, 空头出场={exit_short}")
        
        # 检查信号质量
        if entry_long + entry_short == 0:
            logger.warning("⚠️ 没有生成入场信号，条件可能过于严格")
            
            # 分析原因
            latest_row = test_data.iloc[-1]
            logger.info("📊 最新数据分析:")
            logger.info(f"  EMA20: {latest_row['EMA_20']:.6f}")
            logger.info(f"  EMA50: {latest_row['EMA_50']:.6f}")
            logger.info(f"  RSI: {latest_row['RSI']:.2f}")
            logger.info(f"  价格: {latest_row['close']:.6f}")
            logger.info(f"  成交量: {latest_row['volume']:.2f}")
            
            # 检查各个条件
            trend_up = latest_row['EMA_20'] > latest_row['EMA_50']
            momentum_up = 40 < latest_row['RSI'] < 70
            price_position_up = latest_row['close'] > latest_row['EMA_20']
            
            logger.info("📋 多头条件检查:")
            logger.info(f"  趋势向上: {trend_up}")
            logger.info(f"  动量健康: {momentum_up}")
            logger.info(f"  价格位置: {price_position_up}")
            
            return False
        else:
            logger.info("✅ 信号生成正常")
            return True
            
    except Exception as e:
        logger.error(f"❌ 策略测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_freqtrade_requirements():
    """检查FreqTrade运行要求"""
    logger.info("🔍 检查FreqTrade运行要求...")
    
    requirements = []
    
    # 1. 检查策略文件
    strategy_files = [
        "backtest/strategies/smc_strategy.py",
        "freqtrade-bot/user_data/strategies/SMCStrategy.py"
    ]
    
    for file_path in strategy_files:
        if Path(file_path).exists():
            requirements.append(f"✅ {file_path} 存在")
        else:
            requirements.append(f"❌ {file_path} 不存在")
    
    # 2. 检查配置文件
    if Path("freqtrade-bot/config.json").exists():
        requirements.append("✅ config.json 存在")
    else:
        requirements.append("❌ config.json 不存在")
    
    # 3. 检查数据目录
    data_dir = Path("freqtrade-bot/user_data/data")
    if data_dir.exists():
        requirements.append("✅ 数据目录存在")
    else:
        requirements.append("❌ 数据目录不存在")
    
    for req in requirements:
        logger.info(f"  {req}")
    
    return all("✅" in req for req in requirements)

def main():
    """主检查流程"""
    logger.info("🚀 开始FreqTrade信号执行问题诊断...")
    
    all_passed = True
    
    # 1. 检查运行要求
    if not check_freqtrade_requirements():
        logger.error("❌ FreqTrade运行要求检查失败")
        all_passed = False
    
    # 2. 检查配置
    if not check_freqtrade_config():
        logger.error("❌ FreqTrade配置检查失败")
        all_passed = False
    
    # 3. 测试策略信号
    if not test_strategy_signals():
        logger.error("❌ 策略信号测试失败")
        all_passed = False
    
    logger.info("\n" + "="*60)
    if all_passed:
        logger.info("🎉 所有检查通过！")
        logger.info("💡 建议:")
        logger.info("  1. 重启FreqTrade以应用策略更改")
        logger.info("  2. 监控日志查看是否有新的交易执行")
        logger.info("  3. 检查FreqTrade Web UI (http://127.0.0.1:8080)")
        logger.info("  4. 确认市场条件是否符合策略要求")
    else:
        logger.error("❌ 检查发现问题，需要修复后重试")
    
    logger.info("="*60)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
