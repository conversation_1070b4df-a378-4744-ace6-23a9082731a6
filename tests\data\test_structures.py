"""
测试数据结构模块

测试OHLCV数据结构和相关函数的功能。
"""

import unittest
from datetime import datetime, timedelta

import pandas as pd
import numpy as np

from data.structures import (
    TimeFrame, OHLCVColumns, 
    create_empty_ohlcv_dataframe, 
    validate_ohlcv_dataframe,
    clean_ohlcv_dataframe
)


class TestOHLCVStructures(unittest.TestCase):
    """测试OHLCV数据结构相关功能"""
    
    def test_timeframe_enum(self):
        """测试TimeFrame枚举"""
        # 测试枚举值
        self.assertEqual(TimeFrame.MINUTE_1.value, "1m")
        self.assertEqual(TimeFrame.HOUR_1.value, "1h")
        self.assertEqual(TimeFrame.DAY_1.value, "1d")
        
        # 测试from_string方法
        self.assertEqual(TimeFrame.from_string("1m"), TimeFrame.MINUTE_1)
        self.assertEqual(TimeFrame.from_string("1h"), TimeFrame.HOUR_1)
        self.assertEqual(TimeFrame.from_string("1d"), TimeFrame.DAY_1)
        
        # 测试无效字符串
        with self.assertRaises(ValueError):
            TimeFrame.from_string("invalid")
    
    def test_create_empty_dataframe(self):
        """测试创建空OHLCV DataFrame"""
        df = create_empty_ohlcv_dataframe()
        
        # 验证列名
        self.assertEqual(df.index.name, OHLCVColumns.TIMESTAMP)
        
        # 验证列数据类型
        for col in [OHLCVColumns.OPEN, OHLCVColumns.HIGH, 
                    OHLCVColumns.LOW, OHLCVColumns.CLOSE, 
                    OHLCVColumns.VOLUME]:
            self.assertEqual(df[col].dtype, np.float64)
    
    def test_validate_ohlcv_dataframe(self):
        """测试验证OHLCV DataFrame"""
        # 创建有效的DataFrame
        dates = pd.date_range('2023-01-01', periods=5)
        data = {
            OHLCVColumns.OPEN: [100.0] * 5,
            OHLCVColumns.HIGH: [110.0] * 5,
            OHLCVColumns.LOW: [90.0] * 5,
            OHLCVColumns.CLOSE: [105.0] * 5,
            OHLCVColumns.VOLUME: [1000.0] * 5
        }
        valid_df = pd.DataFrame(data, index=dates)
        valid_df.index.name = OHLCVColumns.TIMESTAMP
        
        # 测试有效DataFrame
        self.assertTrue(validate_ohlcv_dataframe(valid_df))
        
        # 测试缺少列
        invalid_df = valid_df.drop(OHLCVColumns.VOLUME, axis=1)
        self.assertFalse(validate_ohlcv_dataframe(invalid_df))
        
        # 测试timestamp作为列而非索引的情况
        df_with_timestamp_column = valid_df.reset_index()
        self.assertTrue(validate_ohlcv_dataframe(df_with_timestamp_column, require_index=False))
    
    def test_clean_ohlcv_dataframe(self):
        """测试清洗OHLCV DataFrame"""
        # 创建包含缺失值和异常值的DataFrame
        dates = pd.date_range('2023-01-01', periods=5)
        data = {
            OHLCVColumns.OPEN: [100.0, np.nan, 102.0, 103.0, 104.0],
            OHLCVColumns.HIGH: [110.0, 111.0, np.nan, 113.0, 114.0],
            OHLCVColumns.LOW: [90.0, 91.0, 92.0, np.nan, 94.0],
            OHLCVColumns.CLOSE: [105.0, 106.0, 107.0, 108.0, np.nan],
            OHLCVColumns.VOLUME: [1000.0, np.nan, 1200.0, 1300.0, -100.0]  # 负体积是异常值
        }
        df = pd.DataFrame(data, index=dates)
        df.index.name = OHLCVColumns.TIMESTAMP
        
        # 清洗数据
        cleaned_df = clean_ohlcv_dataframe(df)
        
        # 测试缺失值填充
        self.assertFalse(cleaned_df[OHLCVColumns.OPEN].isna().any())
        self.assertFalse(cleaned_df[OHLCVColumns.HIGH].isna().any())
        self.assertFalse(cleaned_df[OHLCVColumns.LOW].isna().any())
        self.assertFalse(cleaned_df[OHLCVColumns.CLOSE].isna().any())
        self.assertFalse(cleaned_df[OHLCVColumns.VOLUME].isna().any())
        
        # 测试异常值处理
        self.assertEqual(cleaned_df[OHLCVColumns.VOLUME].iloc[-1], 0.0)  # 负体积应该被修正为0
        
        # 测试OHLC逻辑修正
        for i in range(len(cleaned_df)):
            row = cleaned_df.iloc[i]
            self.assertGreaterEqual(row[OHLCVColumns.HIGH], row[OHLCVColumns.OPEN])
            self.assertGreaterEqual(row[OHLCVColumns.HIGH], row[OHLCVColumns.CLOSE])
            self.assertLessEqual(row[OHLCVColumns.LOW], row[OHLCVColumns.OPEN])
            self.assertLessEqual(row[OHLCVColumns.LOW], row[OHLCVColumns.CLOSE])


if __name__ == '__main__':
    unittest.main() 