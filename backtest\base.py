#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
回测框架基类模块

定义回测框架中的基础类和接口。
"""

from typing import Dict, Any, Union, Optional, List, Callable
import pandas as pd
import numpy as np
from abc import ABC, abstractmethod


class Strategy(ABC):
    """
    交易策略基类
    
    所有交易策略都应该继承此基类，并实现必要的方法。
    """
    
    def __init__(self, **params):
        """
        初始化策略
        
        Parameters
        ----------
        **params : dict
            策略参数
        """
        self.params = params
        
    @abstractmethod
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号
        
        Parameters
        ----------
        data : pd.DataFrame
            市场数据
            
        Returns
        -------
        pd.DataFrame
            包含交易信号的DataFrame，一般是一列布尔值，
            True表示做多信号，False表示不做多或平仓
        """
        pass
    
    def __str__(self):
        return f"{self.__class__.__name__}({self.params})"


class BacktestResults(ABC):
    """
    回测结果基类
    
    定义回测结果的标准接口。所有回测引擎的结果都应该继承此类。
    """
    
    @property
    def trades(self) -> pd.DataFrame:
        """
        获取交易记录
        
        Returns
        -------
        pd.DataFrame
            包含交易记录的DataFrame
        """
        return pd.DataFrame()
    
    @abstractmethod
    def equity(self) -> pd.Series:
        """
        获取净值曲线
        
        Returns
        -------
        pd.Series
            净值曲线
        """
        pass
    
    @abstractmethod
    def get_returns(self) -> pd.Series:
        """
        获取收益率序列
        
        Returns
        -------
        pd.Series
            收益率序列
        """
        pass
    
    @abstractmethod
    def get_drawdowns(self) -> pd.Series:
        """
        获取回撤序列
        
        Returns
        -------
        pd.Series
            回撤序列
        """
        pass
    
    @property
    def metrics(self) -> Dict[str, Any]:
        """
        获取性能指标
        
        Returns
        -------
        Dict[str, Any]
            性能指标
        """
        return {}
    
    @property
    def positions(self) -> pd.DataFrame:
        """
        获取持仓记录
        
        Returns
        -------
        pd.DataFrame
            持仓记录
        """
        return pd.DataFrame()
    
    def summary(self) -> Dict[str, Any]:
        """
        获取回测摘要
        
        Returns
        -------
        Dict[str, Any]
            回测摘要
        """
        return {
            'trades': len(self.trades),
            'start_date': self.equity().index[0] if not self.equity().empty else None,
            'end_date': self.equity().index[-1] if not self.equity().empty else None,
            'total_return': self.equity().iloc[-1] / self.equity().iloc[0] - 1 if not self.equity().empty else 0
        }
    
    def __str__(self) -> str:
        """
        返回回测结果的字符串表示
        
        Returns
        -------
        str
            回测结果的字符串表示
        """
        summary = self.summary()
        return (
            f"BacktestResults(trades={summary['trades']}, "
            f"start={summary['start_date']}, end={summary['end_date']}, "
            f"return={summary['total_return']:.2%})"
        )


class BacktestEngine(ABC):
    """
    回测引擎基类
    
    定义回测引擎的通用接口，所有具体的回测引擎实现都应该继承此类。
    """
    
    def __init__(self, data: pd.DataFrame, **kwargs):
        """
        初始化回测引擎
        
        Parameters
        ----------
        data : pd.DataFrame
            用于回测的市场数据，应该包含OHLCV等列
        **kwargs : dict
            其他回测参数
        """
        self.data = data
        self.params = kwargs
        self.results = None
    
    @abstractmethod
    def run(self, strategy: Strategy, **kwargs) -> BacktestResults:
        """
        运行回测
        
        Parameters
        ----------
        strategy : Strategy
            交易策略
        **kwargs : dict
            其他运行参数
            
        Returns
        -------
        BacktestResults
            回测结果对象
        """
        pass
    
    def get_results(self) -> Optional[BacktestResults]:
        """
        获取回测结果
        
        Returns
        -------
        BacktestResults or None
            回测结果对象，如果回测尚未运行则返回None
        """
        return self.results
    
    @abstractmethod
    def plot(self, **kwargs) -> None:
        """
        可视化回测结果
        
        Parameters
        ----------
        **kwargs : dict
            可视化参数
        """
        pass 