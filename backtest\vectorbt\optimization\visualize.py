#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化结果可视化工具

提供参数优化和Walk Forward Analysis结果的可视化功能。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from typing import Dict, Any, Union, Optional, List, Tuple, Callable
import seaborn as sns
from mpl_toolkits.mplot3d import Axes3D

def plot_parameter_importance(results_df: pd.DataFrame, metric: str = 'sharpe_ratio', 
                              top_n_params: int = None):
    """
    绘制参数重要性图
    
    Parameters
    ----------
    results_df : pd.DataFrame
        优化结果
    metric : str, optional
        性能指标，默认为'sharpe_ratio'
    top_n_params : int, optional
        显示前N个最重要的参数，默认为None表示全部显示
    """
    # 获取参数列
    metric_cols = [
        'sharpe_ratio', 'max_drawdown', 'max_drawdown_len', 
        'total_return', 'annual_return', 'volatility', 
        'total_trades', 'win_rate', 'loss_rate', 'avg_trade_pnl',
        'max_winner', 'max_loser'
    ]
    param_cols = [col for col in results_df.columns if col not in metric_cols]
    
    # 计算每个参数的重要性
    importance = {}
    
    for param in param_cols:
        # 按参数分组计算指标方差
        group_means = results_df.groupby(param)[metric].mean()
        
        # 计算参数重要性(组间方差)
        importance[param] = group_means.var()
    
    # 转换为DataFrame
    importance_df = pd.DataFrame(list(importance.items()), columns=['parameter', 'importance'])
    importance_df = importance_df.sort_values(by='importance', ascending=False)
    
    # 限制显示的参数数量
    if top_n_params is not None and top_n_params < len(importance_df):
        importance_df = importance_df.head(top_n_params)
    
    # 绘制条形图
    plt.figure(figsize=(10, 6))
    plt.barh(importance_df['parameter'], importance_df['importance'], color='skyblue')
    plt.xlabel('Importance (Variance of Metric)')
    plt.ylabel('Parameter')
    plt.title(f'Parameter Importance for {metric}')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()
    
    return importance_df

def plot_optimization_surface_3d(results_df: pd.DataFrame, param1: str, param2: str, 
                                metric: str = 'sharpe_ratio'):
    """
    绘制3D优化表面图
    
    Parameters
    ----------
    results_df : pd.DataFrame
        优化结果
    param1 : str
        第一个参数名称
    param2 : str
        第二个参数名称
    metric : str, optional
        性能指标，默认为'sharpe_ratio'
    """
    # 获取唯一参数值
    param1_values = sorted(results_df[param1].unique())
    param2_values = sorted(results_df[param2].unique())
    
    # 创建网格
    grid = np.zeros((len(param2_values), len(param1_values)))
    
    # 填充网格
    for i, val2 in enumerate(param2_values):
        for j, val1 in enumerate(param1_values):
            mask = (results_df[param1] == val1) & (results_df[param2] == val2)
            if mask.any():
                grid[i, j] = results_df.loc[mask, metric].iloc[0]
            else:
                grid[i, j] = np.nan
    
    # 创建3D图表
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # 创建网格点
    X, Y = np.meshgrid(range(len(param1_values)), range(len(param2_values)))
    
    # 绘制曲面
    surf = ax.plot_surface(X, Y, grid, cmap='viridis', alpha=0.8, linewidth=0, antialiased=True)
    
    # 设置坐标轴标签
    ax.set_xlabel(param1)
    ax.set_ylabel(param2)
    ax.set_zlabel(metric)
    ax.set_title(f"{param1} vs {param2} - {metric} (3D)")
    
    # 设置坐标轴刻度
    ax.set_xticks(range(len(param1_values)))
    ax.set_xticklabels(param1_values)
    ax.set_yticks(range(len(param2_values)))
    ax.set_yticklabels(param2_values)
    
    # 添加颜色条
    fig.colorbar(surf, ax=ax, shrink=0.5, aspect=5)
    
    plt.tight_layout()
    plt.show()
    
    # 绘制2D热力图
    plt.figure(figsize=(10, 8))
    
    # 绘制热力图
    sns.heatmap(grid, annot=True, fmt=".2f", cmap="viridis", 
               xticklabels=param1_values, yticklabels=param2_values)
    
    plt.xlabel(param1)
    plt.ylabel(param2)
    plt.title(f"{param1} vs {param2} - {metric}")
    
    plt.tight_layout()
    plt.show()

def plot_walk_forward_results(wfa_results: pd.DataFrame, original_data: Optional[pd.DataFrame] = None):
    """
    绘制Walk Forward Analysis结果
    
    Parameters
    ----------
    wfa_results : pd.DataFrame
        Walk Forward Analysis结果
    original_data : pd.DataFrame, optional
        原始价格数据，默认为None
    """
    # 绘制性能指标
    plt.figure(figsize=(12, 8))
    
    # 绘制夏普比率
    plt.subplot(3, 1, 1)
    plt.plot(wfa_results['window'], wfa_results['sharpe_ratio'], 'o-', label='Sharpe Ratio')
    plt.axhline(y=wfa_results['sharpe_ratio'].mean(), color='r', linestyle='--', 
              label=f'Mean: {wfa_results["sharpe_ratio"].mean():.2f}')
    plt.title('Sharpe Ratio by Window')
    plt.xlabel('Window')
    plt.ylabel('Sharpe Ratio')
    plt.grid(True)
    plt.legend()
    
    # 绘制收益率
    plt.subplot(3, 1, 2)
    plt.plot(wfa_results['window'], wfa_results['total_return'], 'o-', label='Total Return')
    plt.axhline(y=wfa_results['total_return'].mean(), color='r', linestyle='--', 
              label=f'Mean: {wfa_results["total_return"].mean():.2%}')
    plt.title('Return by Window')
    plt.xlabel('Window')
    plt.ylabel('Return')
    plt.grid(True)
    plt.legend()
    
    # 绘制最大回撤
    plt.subplot(3, 1, 3)
    plt.plot(wfa_results['window'], wfa_results['max_drawdown'], 'o-', label='Max Drawdown')
    plt.axhline(y=wfa_results['max_drawdown'].mean(), color='r', linestyle='--', 
              label=f'Mean: {wfa_results["max_drawdown"].mean():.2%}')
    plt.title('Max Drawdown by Window')
    plt.xlabel('Window')
    plt.ylabel('Drawdown')
    plt.grid(True)
    plt.legend()
    
    plt.tight_layout()
    plt.show()
    
    # 如果提供了原始数据，绘制训练和测试区间
    if original_data is not None and 'close' in original_data.columns:
        plt.figure(figsize=(12, 6))
        
        # 绘制价格数据
        plt.plot(original_data.index, original_data['close'], color='black', label='Price')
        
        # 为每个窗口创建训练和测试区间
        for i, row in wfa_results.iterrows():
            # 训练区间
            train_start = row['train_start']
            train_end = row['train_end']
            plt.axvspan(train_start, train_end, alpha=0.2, color='blue', label='Training' if i == 0 else "")
            
            # 测试区间
            test_start = row['test_start']
            test_end = row['test_end']
            plt.axvspan(test_start, test_end, alpha=0.2, color='green', label='Testing' if i == 0 else "")
            
            # 添加窗口边界
            plt.axvline(x=train_start, color='gray', linestyle='--', alpha=0.5)
        
        plt.title('Walk Forward Analysis: Training and Testing Periods')
        plt.xlabel('Date')
        plt.ylabel('Price')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()

def plot_parameter_stability(wfa_results: pd.DataFrame):
    """
    绘制参数稳定性图
    
    Parameters
    ----------
    wfa_results : pd.DataFrame
        Walk Forward Analysis结果
    """
    # 提取参数
    if 'best_params' not in wfa_results.columns:
        raise ValueError("结果中缺少'best_params'列")
        
    param_sets = wfa_results['best_params'].tolist()
    window_nums = wfa_results['window'].tolist()
    
    # 获取所有参数名
    all_params = {}
    for param_set in param_sets:
        for key, value in param_set.items():
            if key not in all_params:
                all_params[key] = []
            all_params[key].append(value)
    
    # 创建参数稳定性图
    n_params = len(all_params)
    fig, axes = plt.subplots(n_params, 1, figsize=(12, 3*n_params))
    
    # 单个参数情况处理
    if n_params == 1:
        param = list(all_params.keys())[0]
        values = all_params[param]
        
        axes.plot(window_nums, values, 'o-', label=param)
        
        # 添加均值线
        if all(isinstance(x, (int, float)) for x in values):
            mean_val = np.mean(values)
            axes.axhline(y=mean_val, color='r', linestyle='--', label=f'Mean: {mean_val:.2f}')
        
        axes.set_title(f'{param} by Window')
        axes.set_xlabel('Window')
        axes.set_ylabel('Value')
        axes.grid(True)
        axes.legend()
    else:
        # 多个参数情况
        for i, (param, values) in enumerate(all_params.items()):
            axes[i].plot(window_nums, values, 'o-', label=param)
            
            # 添加均值线
            if all(isinstance(x, (int, float)) for x in values):
                mean_val = np.mean(values)
                axes[i].axhline(y=mean_val, color='r', linestyle='--', label=f'Mean: {mean_val:.2f}')
            
            axes[i].set_title(f'{param} by Window')
            axes[i].set_xlabel('Window')
            axes[i].set_ylabel('Value')
            axes[i].grid(True)
            axes[i].legend()
    
    plt.tight_layout()
    plt.show()
    
    # 计算参数稳定性统计
    stability_stats = {}
    for param, values in all_params.items():
        if all(isinstance(x, (int, float)) for x in values):
            # 数值型参数
            values_array = np.array(values)
            mean_val = np.mean(values_array)
            std_val = np.std(values_array)
            stability_stats[param] = {
                'mean': mean_val,
                'std': std_val,
                'cv': std_val / mean_val if mean_val != 0 else np.nan,  # 变异系数
                'min': np.min(values_array),
                'max': np.max(values_array),
                'range': np.max(values_array) - np.min(values_array),
                'stability': 1 - (std_val / mean_val) if mean_val != 0 else np.nan  # 稳定性指标
            }
        else:
            # 非数值型参数
            from collections import Counter
            counts = Counter(values)
            most_common = counts.most_common()
            most_common_count = most_common[0][1] if most_common else 0
            stability_stats[param] = {
                'unique_values': len(counts),
                'most_common': most_common[0][0] if most_common else None,
                'most_common_count': most_common_count,
                'most_common_pct': most_common_count / len(values) if values else 0,
                'stability': most_common_count / len(values) if values else 0  # 稳定性指标
            }
    
    # 绘制参数稳定性比较图
    stability_data = {}
    for param, stats in stability_stats.items():
        if 'stability' in stats:
            stability_data[param] = stats['stability']
    
    if stability_data:
        plt.figure(figsize=(10, 6))
        
        # 排序并绘制
        sorted_stability = sorted(stability_data.items(), key=lambda x: x[1], reverse=True)
        params = [x[0] for x in sorted_stability]
        values = [x[1] for x in sorted_stability]
        
        plt.barh(params, values, color='skyblue')
        plt.xlabel('Stability (higher is better)')
        plt.ylabel('Parameter')
        plt.title('Parameter Stability Comparison')
        plt.grid(True, alpha=0.3)
        plt.xlim(0, 1)
        
        plt.tight_layout()
        plt.show()
    
    return pd.DataFrame(stability_stats) 