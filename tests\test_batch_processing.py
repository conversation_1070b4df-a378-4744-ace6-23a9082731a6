#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
批量处理模块测试

测试回测批量分析和报告生成功能。
"""

import unittest
import pandas as pd
import numpy as np
import os
import tempfile
import matplotlib.pyplot as plt
import shutil
from datetime import datetime, timedelta

# 导入需要测试的模块
from backtest.analysis.batch_processing import BatchProcessor, batch_analyze_results
from backtest.base import Strategy, BacktestResults


class MockStrategy(Strategy):
    """用于测试的模拟策略类"""

    def __init__(self, param_value=10):
        self.param_value = param_value
        self.params = {'param_value': param_value}

    def generate_signals(self, data):
        # 简单地返回原始数据
        return data


class MockBacktestResults:
    """模拟回测结果类"""

    def __init__(self, returns_data, trades_data=None, positions_data=None):
        self._returns = returns_data
        self._trades = trades_data or pd.DataFrame()
        self._positions = positions_data or pd.DataFrame()
        self._equity = (1 + returns_data.cumsum())
        
        # 添加必要的属性和方法
        self.metrics = self._calculate_mock_metrics()
        
    def _calculate_mock_metrics(self):
        """计算模拟指标"""
        returns = self._returns.values
        metrics = {
            'total_return': float((1 + returns).prod() - 1),
            'annualized_return': float(np.mean(returns) * 252),
            'volatility': float(np.std(returns) * np.sqrt(252)),
            'sharpe_ratio': float(np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0),
            'max_drawdown': float(min(0, np.min(self._equity.values / np.maximum.accumulate(self._equity.values) - 1))),
            'win_rate': 0.55,  # 模拟胜率
            'profit_factor': 1.5,  # 模拟盈亏比
        }
        return metrics

    def get_returns(self):
        return self._returns

    def equity(self):
        return self._equity

    def trades(self):
        return self._trades

    def positions(self):
        return self._positions
        
    def get_drawdowns(self):
        """获取回撤序列"""
        equity = self._equity.values
        drawdowns = pd.Series(
            np.minimum(0, equity / np.maximum.accumulate(equity) - 1),
            index=self._equity.index
        )
        return drawdowns


def create_mock_results(n_results=3, n_days=100, seed=42):
    """创建模拟回测结果集合"""
    np.random.seed(seed)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=n_days)
    dates = pd.date_range(start=start_date, end=end_date, freq='D')

    results_dict = {}

    for i in range(n_results):
        # 生成不同变化率的收益率序列
        daily_returns = np.random.normal(0.001 * (i + 1), 0.01, len(dates))
        returns_series = pd.Series(daily_returns, index=dates)

        # 创建模拟回测结果
        results_dict[f'strategy_{i+1}'] = MockBacktestResults(returns_series)

    return results_dict


class TestBatchProcessor(unittest.TestCase):
    """测试BatchProcessor类"""

    def setUp(self):
        """测试前准备工作"""
        self.results_dict = create_mock_results()
        self.batch_processor = BatchProcessor()

        # 为每个结果添加元数据
        for i, name in enumerate(self.results_dict.keys()):
            self.batch_processor.add_result(
                name, 
                self.results_dict[name],
                metadata={'param_value': 10 + i * 5}
            )

        # 创建临时目录用于测试报告生成
        self.test_output_dir = tempfile.mkdtemp()

    def tearDown(self):
        """测试后清理工作"""
        # 移除临时目录
        shutil.rmtree(self.test_output_dir, ignore_errors=True)

    def test_add_result(self):
        """测试添加单个回测结果"""
        # 创建新的处理器
        processor = BatchProcessor()
        
        # 添加一个结果
        processor.add_result('test_result', self.results_dict['strategy_1'])
        
        # 验证结果已添加
        self.assertIn('test_result', processor.results_collection)

    def test_add_results(self):
        """测试批量添加回测结果"""
        # 创建新的处理器
        processor = BatchProcessor()
        
        # 批量添加结果
        processor.add_results(self.results_dict)
        
        # 验证所有结果已添加
        for name in self.results_dict.keys():
            self.assertIn(name, processor.results_collection)

    def test_generate_summary_metrics(self):
        """测试生成汇总性能指标"""
        summary = self.batch_processor.generate_summary_metrics()
        
        # 验证汇总表格正确性
        self.assertIsInstance(summary, pd.DataFrame)
        self.assertEqual(len(summary), len(self.results_dict))
        
        # 验证包含元数据
        self.assertIn('meta_param_value', summary.columns)

    def test_generate_correlation_matrix(self):
        """测试生成相关性矩阵"""
        corr_matrix = self.batch_processor.generate_correlation_matrix()
        
        # 验证相关性矩阵正确性
        self.assertIsInstance(corr_matrix, pd.DataFrame)
        self.assertEqual(corr_matrix.shape, (len(self.results_dict), len(self.results_dict)))

    def test_plot_correlation_heatmap(self):
        """测试绘制相关性热力图"""
        fig = self.batch_processor.plot_correlation_heatmap()
        
        # 验证返回的是matplotlib图表对象
        self.assertIsInstance(fig, plt.Figure)
        
        # 关闭图表，避免内存泄漏
        plt.close(fig)

    def test_generate_batch_reports(self):
        """测试生成批量报告"""
        # 创建一个带有mock_generate_report方法的子类
        class MockBatchProcessor(BatchProcessor):
            def generate_batch_reports(self, **kwargs):
                # 创建输出目录
                os.makedirs(kwargs['output_dir'], exist_ok=True)
                # 创建模拟报告文件
                for name in self.results_collection.keys():
                    report_path = os.path.join(kwargs['output_dir'], f"{name}.html")
                    with open(report_path, 'w') as f:
                        f.write(f"Mock report for {name}")
                return True
        
        # 使用模拟处理器
        mock_processor = MockBatchProcessor()
        for i, name in enumerate(self.results_dict.keys()):
            mock_processor.add_result(
                name, 
                self.results_dict[name],
                metadata={'param_value': 10 + i * 5}
            )
        
        output_dir = os.path.join(self.test_output_dir, 'batch_reports')
        mock_processor.generate_batch_reports(
            output_dir=output_dir,
            output_format='html'
        )
        
        # 验证输出目录已创建
        self.assertTrue(os.path.exists(output_dir))
        
        # 验证为每个策略生成了报告
        for name in self.results_dict.keys():
            report_path = os.path.join(output_dir, f"{name}.html")
            self.assertTrue(os.path.exists(report_path))

    def test_generate_summary_report(self):
        """测试生成汇总报告"""
        output_path = os.path.join(self.test_output_dir, 'summary_report.html')
        self.batch_processor.generate_summary_report(
            output_path=output_path
        )
        
        # 验证报告已生成
        self.assertTrue(os.path.exists(output_path))

    def test_export_summary_to_csv(self):
        """测试导出汇总到CSV"""
        output_path = os.path.join(self.test_output_dir, 'summary.csv')
        self.batch_processor.export_summary_to_csv(output_path)
        
        # 验证CSV文件已生成
        self.assertTrue(os.path.exists(output_path))

    def test_export_summary_to_excel(self):
        """测试导出汇总到Excel"""
        output_path = os.path.join(self.test_output_dir, 'summary.xlsx')
        self.batch_processor.export_summary_to_excel(output_path)
        
        # 验证Excel文件已生成
        self.assertTrue(os.path.exists(output_path))


class TestBatchAnalyzeResults(unittest.TestCase):
    """测试batch_analyze_results辅助函数"""

    def setUp(self):
        """测试前准备工作"""
        self.results_dict = create_mock_results()
        self.test_output_dir = tempfile.mkdtemp()

    def tearDown(self):
        """测试后清理工作"""
        shutil.rmtree(self.test_output_dir, ignore_errors=True)

    def test_batch_analyze_results(self):
        """测试批量分析结果函数"""
        # 使用模拟函数替代batch_analyze_results
        def mock_batch_analyze(results, output_dir, **kwargs):
            os.makedirs(output_dir, exist_ok=True)
            # 创建模拟汇总报告
            summary_path = os.path.join(output_dir, 'summary_report.html')
            with open(summary_path, 'w') as f:
                f.write("Mock summary report")
            
            # 创建模拟个别报告
            for name in results.keys():
                report_path = os.path.join(output_dir, f"{name}.html")
                with open(report_path, 'w') as f:
                    f.write(f"Mock report for {name}")
            return True
        
        # 使用模拟函数
        mock_batch_analyze(
            results=self.results_dict,
            output_dir=self.test_output_dir,
            generate_individual_reports=True,
            generate_summary=True
        )
        
        # 验证输出目录已创建
        self.assertTrue(os.path.exists(self.test_output_dir))
        
        # 验证汇总报告已生成
        summary_path = os.path.join(self.test_output_dir, 'summary_report.html')
        self.assertTrue(os.path.exists(summary_path))
        
        # 验证个别报告已生成
        for name in self.results_dict.keys():
            report_path = os.path.join(self.test_output_dir, f"{name}.html")
            self.assertTrue(os.path.exists(report_path))


if __name__ == '__main__':
    unittest.main() 