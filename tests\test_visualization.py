#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
指标可视化测试模块

测试指标可视化框架的基本功能和与其他模块的集成。
"""

import os
import sys
import unittest
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 确保能够导入项目模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from indicators.utils.visualization import IndicatorVisualizer, plot_with_indicators
from indicators.trend.moving_averages import SMA, EMA
from indicators.oscillators.rsi import RSI
from indicators.volatility.bollinger_bands import BollingerBands
from indicators.volume.obv import OBV


def generate_test_data(n_samples=100):
    """生成测试数据"""
    np.random.seed(42)  # 固定随机种子，确保测试结果可重现
    
    # 创建日期索引
    end_date = datetime.now()
    start_date = end_date - timedelta(days=n_samples)
    date_range = pd.date_range(start_date, end_date, periods=n_samples)
    
    # 生成价格数据
    close = 100 + np.cumsum(np.random.normal(0, 1, n_samples))
    
    data = pd.DataFrame(index=date_range)
    data['close'] = close
    data['open'] = data['close'].shift(1) * (1 + np.random.normal(0, 0.01, n_samples))
    data['high'] = np.maximum(data['open'], data['close']) * (1 + np.abs(np.random.normal(0, 0.005, n_samples)))
    data['low'] = np.minimum(data['open'], data['close']) * (1 - np.abs(np.random.normal(0, 0.005, n_samples)))
    data['volume'] = np.random.lognormal(10, 1, n_samples)
    
    # 填充第一行的NaN值
    data.iloc[0, data.columns.get_indexer(['open'])] = close[0] * 0.99
    
    return data


class TestIndicatorVisualization(unittest.TestCase):
    """测试指标可视化类"""
    
    def setUp(self):
        """测试前准备"""
        self.data = generate_test_data()
        self.visualizer = IndicatorVisualizer()
        
        # 创建一些指标对象
        self.sma20 = SMA(window=20)
        self.sma50 = SMA(window=50)
        self.ema20 = EMA(window=20)
        self.rsi = RSI(window=14)
        self.bb = BollingerBands(window=20)
        self.obv = OBV()
        
        # 计算指标值
        self.result_data = self.data.copy()
        self.result_data = self.sma20.calculate(self.result_data)
        self.result_data = self.sma50.calculate(self.result_data)
        self.result_data = self.ema20.calculate(self.result_data)
        self.result_data = self.rsi.calculate(self.result_data)
        self.result_data = self.bb.calculate(self.result_data)
        self.result_data = self.obv.calculate(self.result_data)
        
        # 关闭图形显示
        plt.close('all')
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.visualizer.backend, "matplotlib")
        self.assertEqual(self.visualizer.theme, "default")
        self.assertIsNone(self.visualizer._figure)
        self.assertEqual(len(self.visualizer._axes), 0)
    
    def test_theme_setting(self):
        """测试主题设置"""
        themes = ["default", "dark", "tradingview"]
        
        for theme in themes:
            visualizer = IndicatorVisualizer(theme=theme)
            self.assertEqual(visualizer.theme, theme)
            
        with self.assertRaises(ValueError):
            IndicatorVisualizer(theme="nonexistent_theme")
    
    def test_create_figure(self):
        """测试创建图形"""
        self.visualizer.create_figure()
        self.assertIsNotNone(self.visualizer._figure)
        plt.close('all')
    
    def test_add_subplot(self):
        """测试添加子图"""
        self.visualizer.create_figure()
        self.visualizer.add_subplot("test")
        
        self.assertIn("test", self.visualizer._axes)
        self.assertIsNotNone(self.visualizer._axes["test"])
        plt.close('all')
    
    def test_set_data(self):
        """测试设置数据"""
        self.visualizer.set_data(self.data)
        self.assertIsNotNone(self.visualizer._data)
        
        # 验证数据是否被深度复制
        self.assertIsNot(self.visualizer._data, self.data)
        
        # 验证数据内容是否相同
        pd.testing.assert_frame_equal(self.visualizer._data, self.data)
    
    def test_add_indicator(self):
        """测试添加指标"""
        self.visualizer.add_indicator(self.sma20)
        
        self.assertEqual(len(self.visualizer._indicators), 1)
        self.assertIs(self.visualizer._indicators[0], self.sma20)
    
    def test_plot_candle(self):
        """测试绘制蜡烛图"""
        self.visualizer.set_data(self.data)
        self.visualizer.create_figure()
        self.visualizer.add_subplot("price")
        
        self.visualizer.plot_candle()
        
        # 这里主要测试函数不会报错
        self.assertIsNotNone(self.visualizer._axes["price"])
        plt.close('all')
    
    def test_plot_line(self):
        """测试绘制线图"""
        self.visualizer.set_data(self.result_data)
        self.visualizer.create_figure()
        self.visualizer.add_subplot("price")
        
        self.visualizer.plot_line("SMA_20")
        
        # 这里主要测试函数不会报错
        self.assertIsNotNone(self.visualizer._axes["price"])
        plt.close('all')
    
    def test_plot_volume(self):
        """测试绘制成交量图"""
        self.visualizer.set_data(self.data)
        self.visualizer.create_figure()
        self.visualizer.add_subplot("volume")
        
        self.visualizer.plot_volume()
        
        # 这里主要测试函数不会报错
        self.assertIsNotNone(self.visualizer._axes["volume"])
        plt.close('all')
    
    def test_plot_indicator(self):
        """测试绘制指标"""
        self.visualizer.set_data(self.data)
        self.visualizer.create_figure()
        self.visualizer.add_subplot("indicator")
        
        # 测试使用指标对象
        self.visualizer.plot_indicator(self.sma20, ax_name="indicator")
        
        # 测试使用列名
        self.visualizer.set_data(self.result_data)
        self.visualizer.plot_indicator("RSI_14", ax_name="indicator")
        
        # 这里主要测试函数不会报错
        self.assertIsNotNone(self.visualizer._axes["indicator"])
        plt.close('all')
    
    def test_plot_signals(self):
        """测试绘制交易信号"""
        self.visualizer.set_data(self.result_data)
        self.visualizer.create_figure()
        self.visualizer.add_subplot("price")
        
        # 创建信号数据
        signals = pd.DataFrame(index=self.data.index)
        signals['buy_signal'] = 0.0
        signals['sell_signal'] = 0.0
        
        # 添加一些测试信号
        signals.iloc[10, 0] = 1.0  # 买入信号
        signals.iloc[20, 1] = 1.0  # 卖出信号
        
        self.visualizer.plot_signals(signals)
        
        # 这里主要测试函数不会报错
        self.assertIsNotNone(self.visualizer._axes["price"])
        plt.close('all')
    
    def test_add_grid_layout(self):
        """测试添加网格布局"""
        self.visualizer.create_figure()
        self.visualizer.add_grid_layout([2, 1])
        
        # 验证_grid属性是否被创建
        self.assertTrue(hasattr(self.visualizer, '_grid'))
        plt.close('all')
    
    def test_add_subplot_with_grid(self):
        """测试使用网格添加子图"""
        self.visualizer.create_figure()
        self.visualizer.add_grid_layout([2, 1])
        
        self.visualizer.add_subplot_with_grid("price", 0)
        self.visualizer.add_subplot_with_grid("volume", 1, sharex="price")
        
        self.assertIn("price", self.visualizer._axes)
        self.assertIn("volume", self.visualizer._axes)
        plt.close('all')
    
    def test_integration_with_indicators(self):
        """测试与指标的集成"""
        self.visualizer.set_data(self.data)
        self.visualizer.create_figure()
        self.visualizer.add_grid_layout([3, 1, 1])
        self.visualizer.add_subplot_with_grid("price", 0)
        self.visualizer.add_subplot_with_grid("rsi", 1, sharex="price")
        self.visualizer.add_subplot_with_grid("volume", 2, sharex="price")
        
        # 绘制蜡烛图
        self.visualizer.plot_candle()
        
        # 绘制指标
        self.visualizer.plot_indicator(self.sma20, ax_name="price")
        self.visualizer.plot_indicator(self.sma50, ax_name="price")
        self.visualizer.plot_indicator(self.rsi, ax_name="rsi")
        
        # 绘制成交量
        self.visualizer.plot_volume()
        
        # 这里主要测试函数不会报错
        self.assertIsNotNone(self.visualizer._axes["price"])
        self.assertIsNotNone(self.visualizer._axes["rsi"])
        self.assertIsNotNone(self.visualizer._axes["volume"])
        plt.close('all')
    
    def test_original_plot_with_indicators(self):
        """测试原始的plot_with_indicators函数"""
        fig, axes = plot_with_indicators(
            data=self.result_data, 
            indicators=[self.sma20, self.sma50, self.rsi],
            title="测试图表"
        )
        
        # 验证返回的图形和轴对象
        self.assertIsNotNone(fig)
        self.assertIsNotNone(axes)
        self.assertIn("price", axes)
        plt.close('all')
    
    def test_save_figure(self):
        """测试保存图形（不实际写入文件）"""
        self.visualizer.set_data(self.data)
        self.visualizer.create_figure()
        self.visualizer.add_subplot("price")
        self.visualizer.plot_candle()
        
        # 模拟保存，不实际写入文件
        orig_savefig = plt.Figure.savefig
        try:
            # 替换savefig方法
            called = [False]
            def mock_savefig(self, *args, **kwargs):
                called[0] = True
            plt.Figure.savefig = mock_savefig
            
            self.visualizer.save("test.png")
            self.assertTrue(called[0])
        finally:
            # 恢复原始方法
            plt.Figure.savefig = orig_savefig
            plt.close('all')
    
    def test_chain_calls(self):
        """测试链式调用"""
        # 链式调用
        self.visualizer.set_data(self.data)\
            .create_figure()\
            .add_grid_layout([2, 1])\
            .add_subplot_with_grid("price", 0)\
            .plot_candle()\
            .add_subplot_with_grid("volume", 1, sharex="price")\
            .plot_volume()\
            .set_title("链式调用测试")
        
        # 主要测试链式调用不会报错
        self.assertIsNotNone(self.visualizer._axes["price"])
        self.assertIsNotNone(self.visualizer._axes["volume"])
        plt.close('all')


if __name__ == "__main__":
    unittest.main() 