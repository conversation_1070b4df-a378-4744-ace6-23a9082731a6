{"$schema": "https://schema.freqtrade.io/schema.json", "max_open_trades": 15, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "dry_run": true, "dry_run_wallet": 10000, "cancel_open_orders_on_exit": false, "trading_mode": "futures", "margin_mode": "isolated", "liquidation_buffer": 0.05, "strategy": "SMCFreqtradeStrategy", "strategy_path": "user_data/strategies/", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {"options": {"defaultType": "future", "createMarketBuyOrderRequiresPrice": false, "watchOrderBook": false, "watchTicker": false, "watchOHLCV": false, "watchTrades": false, "ws": false, "wss": false, "stream": false, "watchMyTrades": false, "watchOrders": false, "fetchOrderBook": false}, "timeout": 30000, "rateLimit": 2000, "sandbox": false, "ws": false, "enableRateLimit": true}, "ccxt_async_config": {"options": {"defaultType": "future", "createMarketBuyOrderRequiresPrice": false, "watchOrderBook": false, "watchTicker": false, "watchOHLCV": false, "watchTrades": false, "ws": false, "wss": false, "stream": false, "watchMyTrades": false, "watchOrders": false, "fetchOrderBook": false}, "timeout": 30000, "rateLimit": 2000, "sandbox": false, "ws": false, "enableRateLimit": true}, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "XRP/USDT:USDT", "ADA/USDT:USDT", "BNB/USDT:USDT", "DOT/USDT:USDT", "SOL/USDT:USDT", "LINK/USDT:USDT", "UNI/USDT:USDT", "TRX/USDT:USDT", "DOGE/USDT:USDT", "CRV/USDT:USDT"], "pair_blacklist": [".*DOWN/.*", ".*UP/.*"]}, "pairlists": [{"method": "StaticPairList"}], "api_server": {"enabled": true, "listen_ip_address": "127.0.0.1", "listen_port": 8080, "verbosity": "error", "enable_openapi": false, "jwt_secret_key": "something-secret", "CORS_origins": [], "username": "AriFreqtrade", "password": "qq123456789"}, "bot_name": "AriFreqtrade", "initial_state": "running", "force_entry_enable": true, "internals": {"process_throttle_secs": 5}}