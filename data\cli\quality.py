"""
数据质量检查命令行接口

提供数据质量检查和报告生成功能，用于分析数据问题和监控数据质量。
"""

import argparse
import logging
import sys
import os
import json
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple

from data.processing.quality import check_missing_values, check_outliers, check_data_integrity, generate_quality_report
from data.storage.optimized_storage import OptimizedStorage


logger = logging.getLogger(__name__)


def quality_command(args: Optional[argparse.Namespace] = None):
    """
    执行数据质量检查命令
    
    Args:
        args: 命令行参数
    """
    if args is None:
        # 如果没有提供参数，则解析命令行
        parser = argparse.ArgumentParser(description='数据质量检查工具')
        _add_quality_args(parser)
        args = parser.parse_args(sys.argv[2:])  # 跳过第一个参数（脚本名）和第二个参数（子命令）
    
    # 检查是否提供了输入文件
    if not args.input:
        logger.error("未指定输入文件")
        return
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input):
        logger.error(f"输入文件不存在: {args.input}")
        return
    
    # 加载数据
    logger.info(f"正在加载数据: {args.input}")
    try:
        # 统一使用pandas的功能加载数据
        df = pd.read_csv(args.input, index_col=0, parse_dates=True, compression='infer')
    except Exception as e:
        logger.error(f"加载数据失败: {str(e)}")
        return
    
    if df.empty:
        logger.error(f"加载的数据为空: {args.input}")
        return
    
    # 记录数据基本信息
    logger.info(f"数据概况: {len(df)} 行, {len(df.columns)} 列")
    logger.info(f"时间范围: {df.index.min()} 至 {df.index.max()}")
    
    # 生成质量报告
    logger.info("正在生成数据质量报告...")
    quality_result = generate_quality_report(
        df,
        outlier_method=args.outlier_method or 'zscore',
        outlier_threshold=args.outlier_threshold,
        timeframe=args.timeframe
    )
    
    # 打印报告摘要
    total_missing = sum(quality_result.missing_count.values())
    total_outliers = sum(quality_result.outlier_count.values())
    
    logger.info(f"质量检查结果:")
    logger.info(f"- 缺失值总数: {total_missing} ({total_missing/len(df)/len(df.columns)*100:.2f}%)")
    logger.info(f"- 异常值总数: {total_outliers} ({total_outliers/len(df)/len(df.columns)*100:.2f}%)")
    logger.info(f"- 重复行数量: {quality_result.duplicate_count}")
    logger.info(f"- 无效行数量: {len(quality_result.invalid_rows)}")
    logger.info(f"- 时间间隔问题: {'是' if quality_result.has_gaps else '否'}")
    
    if quality_result.has_gaps and args.verbose:
        logger.info(f"- 时间间隔数量: {len(quality_result.gaps)}")
        for start, end in quality_result.gaps[:5]:  # 只显示前5个间隔
            logger.info(f"  * {start} 至 {end}")
        if len(quality_result.gaps) > 5:
            logger.info(f"  * ... 更多间隔 ({len(quality_result.gaps)-5})")
    
    # 如果指定了详细模式，打印更多信息
    if args.verbose:
        logger.info("\n异常值比例最高的列:")
        outlier_percent = {k: v for k, v in quality_result.outlier_percent.items() if v > 0}
        for col, pct in sorted(outlier_percent.items(), key=lambda x: x[1], reverse=True)[:5]:
            logger.info(f"- {col}: {pct:.2f}%")
        
        logger.info("\n缺失值比例最高的列:")
        missing_percent = {k: v for k, v in quality_result.missing_percent.items() if v > 0}
        for col, pct in sorted(missing_percent.items(), key=lambda x: x[1], reverse=True)[:5]:
            logger.info(f"- {col}: {pct:.2f}%")
    
    # 导出质量报告
    if args.output:
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(quality_result.to_dict(), f, indent=2, ensure_ascii=False)
            logger.info(f"质量报告已保存到: {args.output}")
        except Exception as e:
            logger.error(f"保存质量报告失败: {str(e)}")
    
    # 在控制台打印整体质量评分
    # 计算简单的质量分数：100 - (缺失率 + 异常值率 + 重复率 + 无效行率) * 25
    missing_rate = total_missing / (len(df) * len(df.columns))
    outlier_rate = total_outliers / (len(df) * len(df.columns))
    duplicate_rate = quality_result.duplicate_count / len(df)
    invalid_rate = len(quality_result.invalid_rows) / len(df)
    
    quality_score = 100 - (missing_rate + outlier_rate + duplicate_rate + invalid_rate) * 25
    quality_score = max(0, min(100, quality_score))  # 确保分数在0-100之间
    
    logger.info(f"\n数据质量评分: {quality_score:.1f}/100")
    
    # 提供改进建议
    if quality_score < 80:
        logger.info("\n改进建议:")
        if missing_rate > 0.1:
            logger.info("- 考虑填充或处理缺失值")
        if outlier_rate > 0.05:
            logger.info("- 检查并处理异常值")
        if duplicate_rate > 0.01:
            logger.info("- 去除重复行")
        if invalid_rate > 0.01:
            logger.info("- 检查数据逻辑关系")
        if quality_result.has_gaps:
            logger.info("- 处理时间序列中的间隔问题")


def _add_quality_args(parser):
    """
    添加数据质量检查命令的参数
    
    Args:
        parser: 参数解析器
    """
    # 输入输出参数
    parser.add_argument('-i', '--input', required=True, help='输入数据文件路径')
    parser.add_argument('-o', '--output', help='质量报告输出路径（JSON格式）')
    parser.add_argument('-t', '--timeframe', help='时间周期，如1m, 1h, 1d等，用于检查时间间隔')
    
    # 质量检查参数
    parser.add_argument('--outlier-method', choices=['zscore', 'iqr'], default='zscore', help='异常值检测方法')
    parser.add_argument('--outlier-threshold', type=float, default=3.0, help='异常值阈值')
    
    # 通用参数
    parser.add_argument('-v', '--verbose', action='store_true', help='输出详细信息')


if __name__ == '__main__':
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    # 执行命令
    quality_command() 