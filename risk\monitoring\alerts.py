"""
预警系统实现

提供风险预警规则定义和预警触发处理。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, Callable, Set, Pattern
import re
from datetime import datetime, timedelta
from risk.monitoring.base import MonitorBase, MonitoringEvent, EventSeverity, EventHandler


class AlertRule(ABC):
    """
    预警规则抽象基类
    
    定义预警规则的接口，所有具体预警规则必须继承此类。
    """
    
    def __init__(self, name: str, description: str = "", enabled: bool = True, 
                severity: EventSeverity = EventSeverity.WARNING):
        """
        初始化预警规则
        
        Parameters
        ----------
        name : str
            规则名称
        description : str, optional
            规则描述，默认为空字符串
        enabled : bool, optional
            规则是否启用，默认为True
        severity : EventSeverity, optional
            触发时的事件严重性，默认为WARNING
        """
        self.name = name
        self.description = description
        self.enabled = enabled
        self.severity = severity
    
    @abstractmethod
    def evaluate(self, event: MonitoringEvent) -> bool:
        """
        评估事件是否触发预警
        
        Parameters
        ----------
        event : MonitoringEvent
            要评估的事件
        
        Returns
        -------
        bool
            是否触发预警
        """
        pass
    
    def is_enabled(self) -> bool:
        """
        检查规则是否启用
        
        Returns
        -------
        bool
            规则是否启用
        """
        return self.enabled
    
    def enable(self) -> None:
        """启用规则"""
        self.enabled = True
    
    def disable(self) -> None:
        """禁用规则"""
        self.enabled = False


class SeverityBasedAlertRule(AlertRule):
    """
    基于事件严重性的预警规则
    
    当事件严重性达到或超过指定级别时触发预警。
    """
    
    def __init__(self, name: str, min_severity: EventSeverity, 
                description: str = "", enabled: bool = True, 
                severity: EventSeverity = None):
        """
        初始化基于严重性的预警规则
        
        Parameters
        ----------
        name : str
            规则名称
        min_severity : EventSeverity
            最小触发严重性级别
        description : str, optional
            规则描述，默认为空字符串
        enabled : bool, optional
            规则是否启用，默认为True
        severity : EventSeverity, optional
            触发时的事件严重性，默认为None表示使用事件原始严重性
        """
        super().__init__(name, description, enabled, severity or min_severity)
        self.min_severity = min_severity
    
    def evaluate(self, event: MonitoringEvent) -> bool:
        """
        评估事件是否触发预警
        
        当事件严重性大于或等于设定的最小严重性时触发。
        
        Parameters
        ----------
        event : MonitoringEvent
            要评估的事件
        
        Returns
        -------
        bool
            是否触发预警
        """
        # 定义严重性顺序
        severity_order = {
            EventSeverity.INFO: 0,
            EventSeverity.WARNING: 1,
            EventSeverity.ERROR: 2,
            EventSeverity.CRITICAL: 3
        }
        
        # 比较严重性
        return severity_order[event.severity] >= severity_order[self.min_severity]


class EventTypeAlertRule(AlertRule):
    """
    基于事件类型的预警规则
    
    当事件类型匹配指定类型时触发预警。
    """
    
    def __init__(self, name: str, event_types: List[str], 
                description: str = "", enabled: bool = True, 
                severity: EventSeverity = EventSeverity.WARNING):
        """
        初始化基于事件类型的预警规则
        
        Parameters
        ----------
        name : str
            规则名称
        event_types : List[str]
            触发预警的事件类型列表
        description : str, optional
            规则描述，默认为空字符串
        enabled : bool, optional
            规则是否启用，默认为True
        severity : EventSeverity, optional
            触发时的事件严重性，默认为WARNING
        """
        super().__init__(name, description, enabled, severity)
        self.event_types = event_types
    
    def evaluate(self, event: MonitoringEvent) -> bool:
        """
        评估事件是否触发预警
        
        当事件类型在设定的类型列表中时触发。
        
        Parameters
        ----------
        event : MonitoringEvent
            要评估的事件
        
        Returns
        -------
        bool
            是否触发预警
        """
        return event.event_type in self.event_types


class PatternMatchAlertRule(AlertRule):
    """
    基于消息模式匹配的预警规则
    
    当事件消息匹配指定正则表达式时触发预警。
    """
    
    def __init__(self, name: str, pattern: str, 
                description: str = "", enabled: bool = True, 
                severity: EventSeverity = EventSeverity.WARNING,
                case_sensitive: bool = False):
        """
        初始化基于模式匹配的预警规则
        
        Parameters
        ----------
        name : str
            规则名称
        pattern : str
            匹配的正则表达式模式
        description : str, optional
            规则描述，默认为空字符串
        enabled : bool, optional
            规则是否启用，默认为True
        severity : EventSeverity, optional
            触发时的事件严重性，默认为WARNING
        case_sensitive : bool, optional
            是否区分大小写，默认为False
        """
        super().__init__(name, description, enabled, severity)
        flags = 0 if case_sensitive else re.IGNORECASE
        self.pattern = re.compile(pattern, flags)
    
    def evaluate(self, event: MonitoringEvent) -> bool:
        """
        评估事件是否触发预警
        
        当事件消息匹配设定的正则表达式时触发。
        
        Parameters
        ----------
        event : MonitoringEvent
            要评估的事件
        
        Returns
        -------
        bool
            是否触发预警
        """
        return bool(self.pattern.search(event.message))


class CompositeAlertRule(AlertRule):
    """
    组合预警规则
    
    组合多个预警规则，可以是AND或OR逻辑。
    """
    
    def __init__(self, name: str, rules: List[AlertRule], operator: str = "AND", 
                description: str = "", enabled: bool = True, 
                severity: EventSeverity = EventSeverity.WARNING):
        """
        初始化组合预警规则
        
        Parameters
        ----------
        name : str
            规则名称
        rules : List[AlertRule]
            组合的规则列表
        operator : str, optional
            规则组合逻辑，"AND"表示所有规则都必须匹配，"OR"表示任一规则匹配即可，默认为"AND"
        description : str, optional
            规则描述，默认为空字符串
        enabled : bool, optional
            规则是否启用，默认为True
        severity : EventSeverity, optional
            触发时的事件严重性，默认为WARNING
        """
        super().__init__(name, description, enabled, severity)
        self.rules = rules
        
        if operator not in ("AND", "OR"):
            raise ValueError("operator必须是'AND'或'OR'")
        self.operator = operator
    
    def evaluate(self, event: MonitoringEvent) -> bool:
        """
        评估事件是否触发预警
        
        根据组合逻辑(AND/OR)评估所有子规则。
        
        Parameters
        ----------
        event : MonitoringEvent
            要评估的事件
        
        Returns
        -------
        bool
            是否触发预警
        """
        if self.operator == "AND":
            return all(rule.evaluate(event) for rule in self.rules if rule.is_enabled())
        else:  # OR
            return any(rule.evaluate(event) for rule in self.rules if rule.is_enabled())


class ThresholdAlertRule(AlertRule):
    """
    阈值预警规则
    
    当事件相关数据中的特定字段超过阈值时触发预警。
    """
    
    def __init__(self, name: str, field_path: str, threshold: float, operator: str = ">", 
                description: str = "", enabled: bool = True, 
                severity: EventSeverity = EventSeverity.WARNING):
        """
        初始化阈值预警规则
        
        Parameters
        ----------
        name : str
            规则名称
        field_path : str
            相关数据中的字段路径，可以是点分隔的路径如"related_data.value"
        threshold : float
            阈值
        operator : str, optional
            比较操作符，可选 ">", ">=", "<", "<=", "==", "!="，默认为">"
        description : str, optional
            规则描述，默认为空字符串
        enabled : bool, optional
            规则是否启用，默认为True
        severity : EventSeverity, optional
            触发时的事件严重性，默认为WARNING
        """
        super().__init__(name, description, enabled, severity)
        self.field_path = field_path
        self.threshold = threshold
        
        valid_operators = (">", ">=", "<", "<=", "==", "!=")
        if operator not in valid_operators:
            raise ValueError(f"operator必须是以下之一: {valid_operators}")
        self.operator = operator
    
    def _get_field_value(self, obj: Dict[str, Any], path: str) -> Any:
        """
        从对象中获取字段值
        
        Parameters
        ----------
        obj : Dict[str, Any]
            对象
        path : str
            字段路径，点分隔
        
        Returns
        -------
        Any
            字段值，如果路径不存在则返回None
        """
        parts = path.split('.')
        value = obj
        
        for part in parts:
            if isinstance(value, dict) and part in value:
                value = value[part]
            else:
                return None
        
        return value
    
    def evaluate(self, event: MonitoringEvent) -> bool:
        """
        评估事件是否触发预警
        
        当事件相关数据中的字段值与阈值的比较结果符合指定操作符时触发。
        
        Parameters
        ----------
        event : MonitoringEvent
            要评估的事件
        
        Returns
        -------
        bool
            是否触发预警
        """
        # 首先检查是事件还是直接的相关数据
        if self.field_path.startswith('related_data.'):
            # 从相关数据中获取值
            field_path = self.field_path[len('related_data.'):]
            value = self._get_field_value(event.related_data, field_path)
        else:
            # 尝试直接从事件对象获取值
            value = self._get_field_value(event.to_dict(), self.field_path)
        
        # 如果值不存在或不是数字，则不触发
        if value is None or not isinstance(value, (int, float)):
            return False
        
        # 根据操作符比较值和阈值
        if self.operator == ">":
            return value > self.threshold
        elif self.operator == ">=":
            return value >= self.threshold
        elif self.operator == "<":
            return value < self.threshold
        elif self.operator == "<=":
            return value <= self.threshold
        elif self.operator == "==":
            return value == self.threshold
        else:  # !=
            return value != self.threshold


class FrequencyAlertRule(AlertRule):
    """
    频率预警规则
    
    当特定类型的事件在指定时间窗口内出现次数超过阈值时触发预警。
    """
    
    def __init__(self, name: str, event_type: str, max_occurrences: int, time_window: int, 
                description: str = "", enabled: bool = True, 
                severity: EventSeverity = EventSeverity.WARNING):
        """
        初始化频率预警规则
        
        Parameters
        ----------
        name : str
            规则名称
        event_type : str
            要监控的事件类型
        max_occurrences : int
            时间窗口内的最大出现次数
        time_window : int
            时间窗口大小（秒）
        description : str, optional
            规则描述，默认为空字符串
        enabled : bool, optional
            规则是否启用，默认为True
        severity : EventSeverity, optional
            触发时的事件严重性，默认为WARNING
        """
        super().__init__(name, description, enabled, severity)
        self.event_type = event_type
        self.max_occurrences = max_occurrences
        self.time_window = time_window
        
        # 存储事件历史用于频率分析
        self.event_history = []
    
    def add_event(self, event: MonitoringEvent) -> None:
        """
        添加事件到历史记录
        
        Parameters
        ----------
        event : MonitoringEvent
            要添加的事件
        """
        if event.event_type == self.event_type:
            self.event_history.append(event)
            
            # 清理过期的事件
            self._clean_history()
    
    def _clean_history(self) -> None:
        """清理超出时间窗口的事件历史"""
        now = datetime.now()
        cutoff_time = now - timedelta(seconds=self.time_window)
        
        self.event_history = [e for e in self.event_history if e.timestamp >= cutoff_time]
    
    def evaluate(self, event: MonitoringEvent) -> bool:
        """
        评估事件是否触发预警
        
        当事件类型匹配且时间窗口内出现次数超过阈值时触发。
        
        Parameters
        ----------
        event : MonitoringEvent
            要评估的事件
        
        Returns
        -------
        bool
            是否触发预警
        """
        # 添加当前事件
        self.add_event(event)
        
        # 如果事件类型不匹配，直接返回不触发
        if event.event_type != self.event_type:
            return False
        
        # 检查频率
        return len(self.event_history) > self.max_occurrences


class AlertSystem:
    """
    预警系统
    
    管理预警规则和处理预警事件。
    """
    
    def __init__(self):
        """初始化预警系统"""
        self.rules: Dict[str, AlertRule] = {}
        self.handlers: List[EventHandler] = []
    
    def add_rule(self, rule: AlertRule) -> None:
        """
        添加预警规则
        
        Parameters
        ----------
        rule : AlertRule
            要添加的预警规则
        
        Raises
        ------
        ValueError
            如果已存在同名规则
        """
        if rule.name in self.rules:
            raise ValueError(f"规则'{rule.name}'已存在")
        
        self.rules[rule.name] = rule
    
    def remove_rule(self, rule_name: str) -> None:
        """
        移除预警规则
        
        Parameters
        ----------
        rule_name : str
            要移除的规则名称
        
        Raises
        ------
        KeyError
            如果规则不存在
        """
        if rule_name not in self.rules:
            raise KeyError(f"规则'{rule_name}'不存在")
        
        del self.rules[rule_name]
    
    def add_handler(self, handler: EventHandler) -> None:
        """
        添加事件处理器
        
        Parameters
        ----------
        handler : EventHandler
            要添加的事件处理器
        """
        self.handlers.append(handler)
    
    def remove_handler(self, handler_name: str) -> None:
        """
        移除事件处理器
        
        Parameters
        ----------
        handler_name : str
            要移除的处理器名称
        
        Raises
        ------
        ValueError
            如果处理器不存在
        """
        for i, handler in enumerate(self.handlers):
            if handler.name == handler_name:
                self.handlers.pop(i)
                return
        raise ValueError(f"处理器'{handler_name}'不存在")
    
    def process_event(self, event: MonitoringEvent) -> List[MonitoringEvent]:
        """
        处理事件并触发预警
        
        Parameters
        ----------
        event : MonitoringEvent
            要处理的事件
        
        Returns
        -------
        List[MonitoringEvent]
            触发的预警事件列表
        """
        triggered_alerts = []
        
        # 评估每个规则
        for rule in self.rules.values():
            if rule.is_enabled() and rule.evaluate(event):
                # 创建预警事件
                alert_event = MonitoringEvent(
                    event_type="alert",
                    severity=rule.severity,
                    message=f"预警 '{rule.name}' 被触发: {event.message}",
                    related_data={
                        "rule_name": rule.name,
                        "rule_description": rule.description,
                        "original_event": event.to_dict()
                    }
                )
                
                triggered_alerts.append(alert_event)
                
                # 处理预警事件
                for handler in self.handlers:
                    if handler.is_enabled():
                        handler.handle_event(alert_event)
        
        return triggered_alerts


class AlertMonitor(MonitorBase):
    """
    预警监控器
    
    监控系统内的事件并触发预警。
    """
    
    def __init__(self, name: str = "alert_monitor", description: str = "监控系统事件并触发预警", 
                enabled: bool = True, check_interval: int = 1):
        """
        初始化预警监控器
        
        Parameters
        ----------
        name : str, optional
            监控器名称，默认为"alert_monitor"
        description : str, optional
            监控器描述，默认为"监控系统事件并触发预警"
        enabled : bool, optional
            监控器是否启用，默认为True
        check_interval : int, optional
            检查间隔（以秒为单位），默认为1
        """
        super().__init__(name, description, enabled, check_interval)
        self.alert_system = AlertSystem()
    
    def add_rule(self, rule: AlertRule) -> None:
        """
        添加预警规则
        
        Parameters
        ----------
        rule : AlertRule
            要添加的预警规则
        """
        self.alert_system.add_rule(rule)
    
    def remove_rule(self, rule_name: str) -> None:
        """
        移除预警规则
        
        Parameters
        ----------
        rule_name : str
            要移除的规则名称
        """
        self.alert_system.remove_rule(rule_name)
    
    def check(self, context: Dict[str, Any] = None) -> List[MonitoringEvent]:
        """
        检查并处理预警
        
        Parameters
        ----------
        context : Dict[str, Any], optional
            检查上下文，包含最新事件，默认为None
        
        Returns
        -------
        List[MonitoringEvent]
            触发的预警事件列表
        """
        if context is None or 'events' not in context:
            return []
        
        all_alerts = []
        
        # 处理每个事件
        events = context['events']
        if not isinstance(events, list):
            events = [events]
        
        for event in events:
            # 如果传入的是字典，转换为事件对象
            if isinstance(event, dict):
                event = MonitoringEvent.from_dict(event)
            
            # 处理事件
            alerts = self.alert_system.process_event(event)
            all_alerts.extend(alerts)
        
        return all_alerts 