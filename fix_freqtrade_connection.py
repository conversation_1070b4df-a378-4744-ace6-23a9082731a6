#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FreqTrade连接问题修复脚本
解决Binance API连接失败和策略加载问题

🔧 修复内容：
1. 网络连接配置优化
2. 代理设置修复
3. 策略文件路径修正
4. SSL证书问题解决
5. 配置文件优化
"""

import json
import os
import sys
import shutil
import logging
from pathlib import Path
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def backup_config(config_path: Path) -> Path:
    """备份原始配置文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = config_path.parent / f"config_backup_{timestamp}.json"
    shutil.copy2(config_path, backup_path)
    logger.info(f"配置文件已备份到: {backup_path}")
    return backup_path

def fix_network_config(config_path: Path):
    """修复网络连接配置"""
    logger.info("🔧 修复网络连接配置...")
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 1. 修复交易所配置
    if 'exchange' in config:
        exchange_config = config['exchange']
        
        # 移除代理配置（如果存在连接问题）
        if 'ccxt_config' in exchange_config:
            ccxt_config = exchange_config['ccxt_config']
            
            # 移除可能导致连接问题的代理设置
            if 'proxies' in ccxt_config:
                logger.info("移除代理配置以解决连接问题")
                del ccxt_config['proxies']
            
            if 'aiohttp_proxy' in ccxt_config:
                logger.info("移除aiohttp代理配置")
                del ccxt_config['aiohttp_proxy']
            
            # 优化连接设置
            ccxt_config.update({
                'timeout': 30000,
                'rateLimit': 1000,  # 降低请求频率
                'enableRateLimit': True,
                'verify': True,  # 启用SSL验证
                'options': {
                    'defaultType': 'future',
                    'createMarketBuyOrderRequiresPrice': False,
                    # 禁用WebSocket以避免连接问题
                    'watchOrderBook': False,
                    'watchTicker': False,
                    'watchOHLCV': False,
                    'watchTrades': False,
                    'ws': False,
                    'wss': False,
                    'stream': False,
                    'watchMyTrades': False,
                    'watchOrders': False,
                    'fetchOrderBook': True
                }
            })
        
        # 同步异步配置
        if 'ccxt_async_config' in exchange_config:
            exchange_config['ccxt_async_config'] = exchange_config['ccxt_config'].copy()
    
    # 2. 修复策略路径
    strategy_path = Path(config.get('strategy_path', ''))
    if not strategy_path.exists():
        # 尝试找到正确的策略路径
        possible_paths = [
            Path("backtest/strategies/"),
            Path("E:/newADM/gitRepository/AriQuantification/backtest/strategies/"),
            Path("user_data/strategies/")
        ]
        
        for path in possible_paths:
            if path.exists() and (path / "smc_strategy.py").exists():
                config['strategy_path'] = str(path.absolute()) + "/"
                logger.info(f"策略路径修正为: {config['strategy_path']}")
                break
    
    # 3. 确保策略名称正确
    if config.get('strategy') == 'SMCFreqtradeStrategy':
        config['strategy'] = 'SMCStrategy'
        logger.info("策略名称修正为: SMCStrategy")
    
    # 4. 优化其他配置
    config.update({
        'max_open_trades': 8,  # 减少同时交易数量
        'process_throttle_secs': 1,  # 减少处理延迟
    })
    
    # 保存修复后的配置
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    logger.info("✅ 网络连接配置修复完成")

def create_strategy_symlink():
    """创建策略文件符号链接（如果需要）"""
    logger.info("🔧 检查策略文件...")
    
    # 源策略文件
    source_strategy = Path("backtest/strategies/smc_strategy.py")
    
    # FreqTrade用户策略目录
    user_strategies_dir = Path("freqtrade-bot/user_data/strategies")
    user_strategies_dir.mkdir(parents=True, exist_ok=True)
    
    target_strategy = user_strategies_dir / "SMCStrategy.py"
    
    if source_strategy.exists():
        if target_strategy.exists():
            target_strategy.unlink()  # 删除现有文件
        
        # 复制策略文件
        shutil.copy2(source_strategy, target_strategy)
        logger.info(f"策略文件已复制到: {target_strategy}")
    else:
        logger.warning(f"源策略文件不存在: {source_strategy}")

def test_connection():
    """测试网络连接"""
    logger.info("🔍 测试网络连接...")
    
    try:
        import requests
        
        # 测试Binance API连接
        response = requests.get("https://api.binance.com/api/v3/ping", timeout=10)
        if response.status_code == 200:
            logger.info("✅ Binance API连接正常")
        else:
            logger.warning(f"⚠️ Binance API响应异常: {response.status_code}")
    
    except Exception as e:
        logger.error(f"❌ 网络连接测试失败: {e}")
        logger.info("建议检查网络连接或防火墙设置")

def main():
    """主修复流程"""
    logger.info("🚀 开始FreqTrade连接问题修复...")
    
    # 1. 修复配置文件
    config_path = Path("freqtrade-bot/config.json")
    if config_path.exists():
        backup_config(config_path)
        fix_network_config(config_path)
    else:
        logger.error(f"配置文件不存在: {config_path}")
        return False
    
    # 2. 创建策略文件链接
    create_strategy_symlink()
    
    # 3. 测试连接
    test_connection()
    
    logger.info("✅ FreqTrade连接问题修复完成")
    logger.info("\n📋 修复内容总结:")
    logger.info("  1. ✅ 移除代理配置，使用直连")
    logger.info("  2. ✅ 优化连接超时和频率限制")
    logger.info("  3. ✅ 禁用WebSocket，使用REST API")
    logger.info("  4. ✅ 修正策略文件路径和名称")
    logger.info("  5. ✅ 优化交易配置参数")
    
    logger.info("\n🔄 下一步操作:")
    logger.info("  1. 重启FreqTrade服务")
    logger.info("  2. 检查日志确认连接正常")
    logger.info("  3. 监控交易执行情况")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
