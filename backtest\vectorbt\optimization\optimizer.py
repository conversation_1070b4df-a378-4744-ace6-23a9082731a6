#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
VectorBT参数优化器

提供VectorBT策略的参数网格搜索和优化功能。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from typing import Dict, Any, Union, Optional, List, Tuple, Callable
import itertools
import multiprocessing as mp
import time
import logging
from functools import partial

from ...base import Strategy, BacktestResults
from .. import engine

logger = logging.getLogger(__name__)

class VectorBTOptimizer:
    """
    VectorBT策略参数优化器
    
    提供参数网格搜索、参数敏感性分析等功能。
    """
    
    def __init__(self, engine_instance: engine.VectorBTEngine, strategy_class, data: pd.DataFrame,
                 metric: str = 'sharpe_ratio', maximize: bool = True, **engine_kwargs):
        """
        初始化参数优化器
        
        Parameters
        ----------
        engine_instance : engine.VectorBTEngine
            VectorBT引擎实例
        strategy_class : type
            策略类（非实例）
        data : pd.DataFrame
            回测数据
        metric : str, optional
            优化目标指标，默认为'sharpe_ratio'
        maximize : bool, optional
            是否最大化指标，默认为True
        **engine_kwargs : dict
            引擎参数
        """
        self.engine = engine_instance
        self.strategy_class = strategy_class
        self.data = data
        self.metric = metric
        self.maximize = maximize
        self.engine_kwargs = engine_kwargs
        self.results = None
        
        # 用于多进程优化的全局变量
        self._param_combinations = None
        self._param_names = None
        self._strategy_class = None
        self._data = None
        self._engine_kwargs = None
        self._metric = None
        self._maximize = None
    
    def _evaluate_params(self, params):
        """
        评估特定参数组合
        
        Parameters
        ----------
        params : tuple
            参数值的元组
            
        Returns
        -------
        dict
            包含参数和性能指标的字典
        """
        # 创建参数字典
        param_dict = dict(zip(self._param_names, params))
        return self._evaluate_strategy(param_dict)
    
    def _evaluate_strategy(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估特定参数下的策略性能
        
        Parameters
        ----------
        params : dict
            策略参数
            
        Returns
        -------
        dict
            包含参数和性能指标的字典
        """
        try:
            # 创建策略实例
            strategy = self.strategy_class(**params)
            
            # 创建新的VectorBT引擎实例或重用现有实例
            if hasattr(self.engine, 'clone'):
                bt_engine = self.engine.clone()
            else:
                bt_engine = engine.VectorBTEngine(self.data, **self.engine_kwargs)
            
            # 运行回测
            results = bt_engine.run(strategy)
            
            # 获取性能指标
            metrics = results.metrics
            
            # 确保包含必要的指标
            if 'total_return' not in metrics and 'return_pct' in metrics:
                metrics['total_return'] = metrics['return_pct']
            elif 'total_return' not in metrics:
                # 如果没有收益率指标，尝试计算
                if hasattr(results, 'equity') and results.equity is not None:
                    initial_equity = results.equity.iloc[0]
                    final_equity = results.equity.iloc[-1]
                    metrics['total_return'] = (final_equity / initial_equity) - 1
                else:
                    metrics['total_return'] = 0.0
            
            if 'max_drawdown' not in metrics and 'drawdown' in metrics:
                metrics['max_drawdown'] = metrics['drawdown']
            elif 'max_drawdown' not in metrics:
                # 如果没有回撤指标，设置默认值
                metrics['max_drawdown'] = 0.0
            
            if 'win_rate' not in metrics and 'win_ratio' in metrics:
                metrics['win_rate'] = metrics['win_ratio']
            elif 'win_rate' not in metrics and 'winning_rate' in metrics:
                metrics['win_rate'] = metrics['winning_rate']
            elif 'win_rate' not in metrics:
                # 如果没有胜率指标，设置默认值
                metrics['win_rate'] = 0.0
            
            # 整理评估结果
            eval_result = {
                **params,  # 包含所有参数
                **metrics,  # 包含所有性能指标
            }
            
            return eval_result
            
        except Exception as e:
            logger.error(f"评估参数 {params} 时出错: {e}")
            
            # 返回无效结果
            eval_result = {
                **params,
                self.metric: float('-inf') if self.maximize else float('inf'),
                'total_return': 0.0,
                'max_drawdown': 1.0,
                'win_rate': 0.0
            }
            
            return eval_result
    
    def grid_search(self, param_grid: Dict[str, List], n_jobs: int = 1, progress_callback: Callable = None) -> pd.DataFrame:
        """
        网格搜索优化
        
        Parameters
        ----------
        param_grid : dict
            参数网格，格式为 {'param_name': [value1, value2, ...]}
        n_jobs : int, optional
            并行任务数，默认为1
        progress_callback : callable, optional
            进度回调函数，接收(当前进度百分比)参数
            
        Returns
        -------
        pd.DataFrame
            优化结果，包含参数和性能指标
        """
        # 创建参数组合
        self._param_names = list(param_grid.keys())
        param_values = list(param_grid.values())
        param_combinations = list(itertools.product(*param_values))
        self._param_combinations = param_combinations
        total_combinations = len(param_combinations)
        
        # 设置多进程所需的全局变量
        self._strategy_class = self.strategy_class
        self._data = self.data
        self._engine_kwargs = self.engine_kwargs
        self._metric = self.metric
        self._maximize = self.maximize
        
        logger.info(f"开始参数优化，共 {total_combinations} 组参数组合")
        
        # 运行优化
        start_time = time.time()
        
        if n_jobs > 1 and total_combinations > 1:
            try:
                # 设置启动方法
                mp.set_start_method('spawn', force=True)
            except RuntimeError:
                # 如果已经设置过启动方法，忽略错误
                pass
                
            # 如果有进度回调，使用imap替代map以便能报告进度
            if progress_callback is not None:
                results = []
                with mp.Pool(processes=n_jobs) as pool:
                    for i, result in enumerate(pool.imap(self._evaluate_params, param_combinations)):
                        results.append(result)
                        # 更新进度
                        progress = (i + 1) / total_combinations
                        progress_callback(progress)
            else:
                # 并行处理
                with mp.Pool(processes=n_jobs) as pool:
                    results = pool.map(self._evaluate_params, param_combinations)
        else:
            # 串行处理
            results = []
            for i, params in enumerate(param_combinations):
                results.append(self._evaluate_params(params))
                # 更新进度
                if progress_callback is not None:
                    progress = (i + 1) / total_combinations
                    progress_callback(progress)
        
        duration = time.time() - start_time
        logger.info(f"参数优化完成，耗时 {duration:.2f} 秒")
        
        # 整理结果
        results_df = pd.DataFrame(results)
        
        # 排序结果
        if self.maximize:
            results_df = results_df.sort_values(by=self.metric, ascending=False)
        else:
            results_df = results_df.sort_values(by=self.metric, ascending=True)
            
        self.results = results_df
        return results_df
    
    def get_best_params(self) -> Dict[str, Any]:
        """
        获取最佳参数
        
        Returns
        -------
        dict
            最佳参数组合
        """
        if self.results is None:
            raise ValueError("请先运行优化")
            
        # 获取最佳结果行
        best_row = self.results.iloc[0]
        
        # 提取参数列
        metric_cols = [
            'sharpe_ratio', 'max_drawdown', 'max_drawdown_len', 
            'total_return', 'annual_return', 'volatility', 
            'total_trades', 'win_rate', 'loss_rate', 'avg_trade_pnl',
            'max_winner', 'max_loser'
        ]
        
        # 获取所有非指标列作为参数列
        param_cols = [col for col in best_row.index if col not in metric_cols]
        
        # 创建参数字典
        best_params = {param: best_row[param] for param in param_cols}
        
        # 确保参数类型正确
        for param, value in best_params.items():
            # 如果参数名称包含 'window' 且值是数值，转为整数
            if ('window' in param.lower() or 'period' in param.lower()) and isinstance(value, (int, float, np.number)):
                best_params[param] = int(value)
            # 其他浮点数值保持不变
            elif isinstance(value, np.number):
                best_params[param] = float(value)
        
        return best_params
    
    def get_best_metric_value(self) -> float:
        """
        获取最佳指标值
        
        Returns
        -------
        float
            最佳指标值
        """
        if self.results is None:
            raise ValueError("请先运行优化")
            
        # 获取最佳结果行的指标值
        best_metric_value = self.results.iloc[0][self.metric]
        
        # 如果是numpy类型，转换为Python原生float
        if isinstance(best_metric_value, np.number):
            best_metric_value = float(best_metric_value)
            
        return best_metric_value
    
    def plot_optimization_results(self, param_names: Optional[List[str]] = None, top_n: int = 20):
        """
        可视化优化结果
        
        Parameters
        ----------
        param_names : list of str, optional
            要分析的参数名称，默认为None表示所有参数
        top_n : int, optional
            显示前N个结果，默认为20
        """
        if self.results is None:
            raise ValueError("请先运行优化")
            
        # 限制显示结果数量
        results = self.results.head(top_n)
        
        # 如果没有指定参数名称，则使用所有非指标列
        if param_names is None:
            metric_cols = [
                'sharpe_ratio', 'max_drawdown', 'max_drawdown_len', 
                'total_return', 'annual_return', 'volatility', 
                'total_trades', 'win_rate', 'loss_rate', 'avg_trade_pnl',
                'max_winner', 'max_loser'
            ]
            param_names = [col for col in results.columns if col not in metric_cols]
            
        # 创建图表
        n_params = len(param_names)
        fig = plt.figure(figsize=(15, n_params * 3))
        
        gs = gridspec.GridSpec(n_params, 2, width_ratios=[3, 1])
        
        # 对每个参数绘制散点图和箱线图
        for i, param in enumerate(param_names):
            # 散点图
            ax1 = plt.subplot(gs[i, 0])
            ax1.scatter(results[param], results[self.metric], alpha=0.6)
            ax1.set_xlabel(param)
            ax1.set_ylabel(self.metric)
            ax1.set_title(f"{param} vs {self.metric}")
            ax1.grid(True)
            
            # 箱线图（按参数值分组）
            ax2 = plt.subplot(gs[i, 1])
            results.boxplot(column=self.metric, by=param, ax=ax2, grid=False)
            ax2.set_title("")
            ax2.set_xlabel("")
            
        plt.tight_layout()
        plt.show()
        
        # 如果有两个参数，绘制3D热力图
        if len(param_names) >= 2:
            self._plot_heatmap(param_names[0], param_names[1])
    
    def _plot_heatmap(self, param1: str, param2: str):
        """
        绘制两个参数的热力图
        
        Parameters
        ----------
        param1 : str
            第一个参数名称
        param2 : str
            第二个参数名称
        """
        # 获取前两个参数的唯一值
        param1_values = sorted(self.results[param1].unique())
        param2_values = sorted(self.results[param2].unique())
        
        # 创建网格
        grid = np.zeros((len(param2_values), len(param1_values)))
        
        # 填充网格
        for i, val2 in enumerate(param2_values):
            for j, val1 in enumerate(param1_values):
                # 筛选符合条件的行
                mask = (self.results[param1] == val1) & (self.results[param2] == val2)
                if mask.any():
                    grid[i, j] = self.results.loc[mask, self.metric].iloc[0]
                else:
                    grid[i, j] = np.nan
        
        # 绘制热力图
        plt.figure(figsize=(10, 8))
        
        plt.imshow(grid, interpolation='nearest', cmap='viridis')
        plt.colorbar(label=self.metric)
        
        # 设置坐标轴刻度
        plt.xticks(range(len(param1_values)), param1_values)
        plt.yticks(range(len(param2_values)), param2_values)
        
        plt.xlabel(param1)
        plt.ylabel(param2)
        plt.title(f"{param1} vs {param2} - {self.metric}")
        
        # 显示数值
        for i in range(len(param2_values)):
            for j in range(len(param1_values)):
                if not np.isnan(grid[i, j]):
                    plt.text(j, i, f"{grid[i, j]:.2f}", ha="center", va="center", color="w")
        
        plt.tight_layout()
        plt.show()
        
        # 绘制3D曲面图
        from mpl_toolkits.mplot3d import Axes3D
        
        fig = plt.figure(figsize=(12, 10))
        ax = fig.add_subplot(111, projection='3d')
        
        # 创建网格点
        X, Y = np.meshgrid(range(len(param1_values)), range(len(param2_values)))
        
        # 绘制曲面
        surf = ax.plot_surface(X, Y, grid, cmap='viridis', alpha=0.8, linewidth=0, antialiased=True)
        
        # 设置坐标轴标签
        ax.set_xlabel(param1)
        ax.set_ylabel(param2)
        ax.set_zlabel(self.metric)
        ax.set_title(f"{param1} vs {param2} - {self.metric} (3D)")
        
        # 设置坐标轴刻度
        ax.set_xticks(range(len(param1_values)))
        ax.set_xticklabels(param1_values)
        ax.set_yticks(range(len(param2_values)))
        ax.set_yticklabels(param2_values)
        
        # 添加颜色条
        fig.colorbar(surf, ax=ax, shrink=0.5, aspect=5)
        
        plt.tight_layout()
        plt.show()
        
    def sensitivity_analysis(self, param_name: str, n_points: int = 20):
        """
        参数敏感性分析
        
        Parameters
        ----------
        param_name : str
            要分析的参数名称
        n_points : int, optional
            显示的点数，默认为20
        """
        if self.results is None:
            raise ValueError("请先运行优化")
            
        # 按参数分组并计算指标统计
        param_values = sorted(self.results[param_name].unique())
        
        metrics = {}
        
        for val in param_values:
            subset = self.results[self.results[param_name] == val]
            metrics[val] = {
                'mean': subset[self.metric].mean(),
                'std': subset[self.metric].std(),
                'max': subset[self.metric].max(),
                'min': subset[self.metric].min(),
                'count': len(subset)
            }
            
        # 转换为DataFrame
        sens_df = pd.DataFrame(metrics).T
        sens_df.index.name = param_name
        sens_df = sens_df.reset_index()
        
        # 绘制敏感性曲线
        plt.figure(figsize=(12, 6))
        
        plt.errorbar(sens_df[param_name], sens_df['mean'], yerr=sens_df['std'], 
                    fmt='o-', capsize=5, label=f'{self.metric} (均值±标准差)')
        plt.fill_between(sens_df[param_name], sens_df['min'], sens_df['max'], 
                        alpha=0.2, label='最小-最大范围')
        
        plt.xlabel(param_name)
        plt.ylabel(self.metric)
        plt.title(f"{param_name}参数敏感性分析 - {self.metric}")
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # 添加辅助线：最佳值
        best_value = self.get_best_params()[param_name]
        plt.axvline(x=best_value, color='r', linestyle='--', 
                    label=f'最佳值 ({best_value})')
        
        plt.tight_layout()
        plt.show()
        
        return sens_df 