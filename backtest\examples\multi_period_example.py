#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
多周期分析示例脚本

展示如何使用多周期分析功能对回测结果进行不同时间段和时间框架的分析。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from backtest.base import BacktestResults
from backtest.analysis.multi_period import (
    analyze_by_timeframe,
    analyze_by_period,
    analyze_calendar_effects,
    plot_period_performance,
    plot_regime_analysis,
    plot_drawdown_recovery_analysis
)
from backtest.analysis.reporting import generate_report


def create_sample_backtest_results(start_date='2018-01-01', end_date='2022-12-31'):
    """创建示例回测结果数据"""
    # 创建日期范围
    dates = pd.date_range(start=start_date, end=end_date, freq='B')
    
    # 创建模拟净值曲线（带有季节性和趋势）
    np.random.seed(42)
    n = len(dates)
    
    # 基础趋势
    trend = np.linspace(0, 0.5, n)
    
    # 季节性组件
    season = 0.1 * np.sin(np.linspace(0, 8 * np.pi, n))
    
    # 随机波动
    noise = 0.001 * np.random.randn(n).cumsum()
    
    # 组合成收益率
    returns = pd.Series(0.0005 + 0.002 * np.random.randn(n) + np.diff(np.concatenate([[0], trend + season + noise])), index=dates)
    
    # 创建净值曲线
    equity = (1 + returns).cumprod()
    
    # 创建回撤序列
    drawdowns = equity / equity.cummax() - 1
    
    # 创建模拟交易记录
    trades = pd.DataFrame({
        'Entry Time': pd.date_range(start=start_date, end=end_date, freq='W')[:-1],
        'Exit Time': pd.date_range(start=start_date, end=end_date, freq='W')[1:],
        'Entry Price': np.random.uniform(90, 110, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'Exit Price': np.random.uniform(90, 110, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'Size': np.random.randint(1, 10, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'PnL': np.random.uniform(-5, 5, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'Return': np.random.uniform(-0.05, 0.05, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'EntrySignal': np.random.choice(['MA Cross', 'RSI', 'Breakout'], len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'ExitSignal': np.random.choice(['Stop Loss', 'Take Profit', 'Signal Reversal'], len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1)
    })
    
    # 创建模拟持仓记录
    positions = pd.DataFrame(index=dates)
    positions['position'] = np.random.randint(-2, 3, len(dates))
    
    # 创建回测结果对象
    class SampleBacktestResults(BacktestResults):
        @property
        def trades(self):
            return trades
        
        @property
        def positions(self):
            return positions
        
        def get_returns(self):
            return returns
        
        def get_drawdowns(self):
            return drawdowns
        
        def equity(self):
            return equity
        
        @property
        def metrics(self):
            return {}
    
    return SampleBacktestResults()


def main():
    """主函数"""
    # 创建示例回测结果
    print("创建示例回测结果...")
    results = create_sample_backtest_results()
    
    # 设置输出目录
    output_dir = os.path.join(os.path.dirname(__file__), 'output')
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. 按不同时间段分析
    print("\n1. 按年度分析回测结果")
    yearly_analysis = analyze_by_period(results, period_type='year')
    print(yearly_analysis)
    
    print("\n2. 按季度分析回测结果")
    quarterly_analysis = analyze_by_period(results, period_type='quarter')
    print(quarterly_analysis)
    
    print("\n3. 按月度分析回测结果")
    monthly_analysis = analyze_by_period(results, period_type='month')
    print(monthly_analysis.head())
    
    # 2. 按不同时间框架分析
    print("\n4. 分析不同时间框架表现")
    timeframe_analysis = analyze_by_timeframe(results, ['D', 'W', 'M'])
    print(timeframe_analysis)
    
    # 3. 日历效应分析
    print("\n5. 分析日历效应")
    calendar_effects = analyze_calendar_effects(results)
    print(calendar_effects)
    
    # 4. 绘制期间表现对比图
    print("\n6. 绘制期间表现对比图")
    fig1 = plot_period_performance(results, period_type='year')
    fig1.savefig(os.path.join(output_dir, 'period_performance.png'))
    
    # 5. 绘制市场状态分析图
    print("\n7. 绘制市场状态分析图")
    # 创建模拟市场状态数据
    market_data = pd.Series(np.random.randn(len(results.equity().index)), index=results.equity().index)
    fig2 = plot_regime_analysis(results, market_data, n_regimes=3)
    fig2.savefig(os.path.join(output_dir, 'regime_analysis.png'))
    
    # 6. 绘制回撤恢复分析图
    print("\n8. 绘制回撤恢复分析图")
    fig3 = plot_drawdown_recovery_analysis(results)
    fig3.savefig(os.path.join(output_dir, 'drawdown_recovery.png'))
    
    # 7. 生成HTML报告
    print("\n9. 生成HTML报告")
    report_path = os.path.join(output_dir, 'multi_period_analysis.html')
    generate_report(results, output_format='html', output_path=report_path, 
                   strategy_name='多周期分析示例')
    print(f"报告已保存至: {report_path}")
    
    print("\n分析完成，结果已保存至:", output_dir)


if __name__ == "__main__":
    main() 