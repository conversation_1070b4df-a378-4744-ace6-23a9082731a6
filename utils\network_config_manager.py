#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
网络配置管理器

统一管理所有网络连接的代理配置，支持trojan转换器
消除系统中的代理配置冗余和不一致性
"""

import os
import logging
from typing import Dict, Any, Optional, Tuple
from pathlib import Path

# 🔇 禁用SSL警告以避免日志噪音
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

from .proxy_detector import ProxyDetector
from .v2ray_converter import V2RayConverter, auto_setup_converter

logger = logging.getLogger(__name__)


class NetworkConfigManager:
    """
    网络配置管理器
    
    提供统一的网络配置接口，自动处理代理检测、V2Ray转换器配置等
    """
    
    def __init__(self):
        """初始化网络配置管理器"""
        self.proxy_detector = ProxyDetector()
        self.v2ray_converter: Optional[V2RayConverter] = None
        self.current_config: Dict[str, Any] = {}
        
        logger.info("网络配置管理器初始化完成")
    
    def initialize(self) -> bool:
        """
        初始化网络配置
        
        Returns
        -------
        bool
            初始化是否成功
        """
        logger.info("🔍 开始初始化网络配置...")
        
        # 尝试自动设置V2Ray转换器
        self.v2ray_converter = auto_setup_converter()
        
        # 检测可用代理
        proxy_url = self.proxy_detector.detect_proxy(use_cache=False)
        
        # 生成配置
        self.current_config = self._generate_network_config(proxy_url)
        
        success = bool(proxy_url or self.v2ray_converter)
        
        if success:
            logger.info(f"✅ 网络配置初始化成功: {self.current_config}")
        else:
            logger.warning("⚠️ 网络配置初始化完成，使用直连模式")
        
        return success
    
    def _generate_network_config(self, proxy_url: Optional[str]) -> Dict[str, Any]:
        """
        生成网络配置
        
        Parameters
        ----------
        proxy_url : Optional[str]
            检测到的代理URL
            
        Returns
        -------
        Dict[str, Any]
            网络配置字典
        """
        config = {
            'proxy_url': proxy_url,
            'direct_mode': proxy_url is None,
            'v2ray_converter': None,
            'freqtrade_config': {},
            'requests_config': {}
        }
        
        # V2Ray转换器配置
        if self.v2ray_converter and self.v2ray_converter.is_running():
            http_proxy, socks5_proxy = self.v2ray_converter.get_proxy_urls()
            config['v2ray_converter'] = {
                'running': True,
                'http_proxy': http_proxy,
                'socks5_proxy': socks5_proxy,
                'status': self.v2ray_converter.get_status()
            }
            # 优先使用V2Ray转换器
            if not proxy_url:
                proxy_url = http_proxy
                config['proxy_url'] = proxy_url
        
        # FreqTrade配置
        if proxy_url:
            config['freqtrade_config'] = self._generate_freqtrade_config(proxy_url)
        
        # Requests配置
        config['requests_config'] = self._generate_requests_config(proxy_url)
        
        return config
    
    def _generate_freqtrade_config(self, proxy_url: str) -> Dict[str, Any]:
        """
        生成FreqTrade代理配置
        
        Parameters
        ----------
        proxy_url : str
            代理URL
            
        Returns
        -------
        Dict[str, Any]
            FreqTrade配置
        """
        # 🔥 SSL验证控制：代理环境下禁用SSL验证以解决握手失败问题
        ssl_config = {
            "verify": False,              # 禁用SSL证书验证
            "aiohttp_trust_env": False,   # 禁用环境变量SSL配置
        }
        
        return {
            "ccxt_config": {
                "proxies": {
                    "http": proxy_url,
                    "https": proxy_url
                },
                "aiohttp_proxy": proxy_url,
                "timeout": 30000,
                "rateLimit": 2000,
                "enableRateLimit": True,
                **ssl_config
            },
            "ccxt_async_config": {
                "proxies": {
                    "http": proxy_url,
                    "https": proxy_url
                },
                "aiohttp_proxy": proxy_url,
                "timeout": 30000,
                "rateLimit": 2000,
                "enableRateLimit": True,
                **ssl_config
            }
        }
    
    def _generate_requests_config(self, proxy_url: Optional[str]) -> Dict[str, Any]:
        """
        生成Requests代理配置
        
        Parameters
        ----------
        proxy_url : Optional[str]
            代理URL
            
        Returns
        -------
        Dict[str, Any]
            Requests配置
        """
        if proxy_url:
            return {
                'proxies': {
                    'http': proxy_url,
                    'https': proxy_url
                },
                'timeout': 10
            }
        else:
            return {
                'proxies': {},
                'timeout': 10
            }
    
    def get_freqtrade_client_config(self) -> Dict[str, Any]:
        """
        获取FreqtradeClient配置
        
        🔥 修复：区分本地和远程服务器的代理配置
        本地服务器不使用代理，远程服务器使用代理
        
        Returns
        -------
        Dict[str, Any]
            FreqtradeClient构造参数
        """
        # 检查FreqtradeClient是否需要代理
        # 本地API调用不需要代理，远程API调用才需要代理
        proxy_url = self.current_config.get('proxy_url')
        
        # 如果没有配置代理，返回空配置
        if not proxy_url:
            return {}
        
        # 只为远程服务器配置代理
        # FreqtradeClient内部会检查server_url决定是否使用代理
        return {
            'proxy': proxy_url
        }
    
    def get_requests_session_config(self) -> Dict[str, Any]:
        """
        获取requests.Session配置
        
        Returns
        -------
        Dict[str, Any]
            Session配置参数
        """
        return self.current_config.get('requests_config', {})
    
    def get_freqtrade_exchange_config(self) -> Dict[str, Any]:
        """
        获取FreqTrade交易所配置
        
        Returns
        -------
        Dict[str, Any]
            交易所配置
        """
        return self.current_config.get('freqtrade_config', {})
    
    def configure_requests_session(self, session) -> None:
        """
        配置requests.Session
        
        Parameters
        ----------
        session : requests.Session
            要配置的Session对象
        """
        config = self.get_requests_session_config()
        
        if 'proxies' in config and config['proxies']:
            session.proxies.update(config['proxies'])
            logger.debug(f"Session已配置代理: {config['proxies']}")
    
    def test_network_connectivity(self) -> Dict[str, Any]:
        """
        测试网络连通性
        
        Returns
        -------
        Dict[str, Any]
            测试结果
        """
        test_urls = [
            'https://httpbin.org/ip',
            'https://api.binance.com/api/v3/ping',
            'http://127.0.0.1:8080/api/v1/ping'  # 本地FreqTrade
        ]
        
        results = {
            'proxy_url': self.current_config.get('proxy_url'),
            'v2ray_running': bool(self.v2ray_converter and self.v2ray_converter.is_running()),
            'connectivity_tests': {}
        }
        
        import requests
        session = requests.Session()
        self.configure_requests_session(session)
        
        for url in test_urls:
            try:
                response = session.get(url, timeout=5)
                results['connectivity_tests'][url] = {
                    'success': response.status_code == 200,
                    'status_code': response.status_code,
                    'response_time': response.elapsed.total_seconds()
                }
            except Exception as e:
                results['connectivity_tests'][url] = {
                    'success': False,
                    'error': str(e)
                }
        
        return results
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取网络配置状态
        
        Returns
        -------
        Dict[str, Any]
            状态信息
        """
        status = {
            'initialized': bool(self.current_config),
            'proxy_detected': self.current_config.get('proxy_url') is not None,
            'direct_mode': self.current_config.get('direct_mode', True),
            'current_config': self.current_config
        }
        
        # V2Ray转换器状态
        if self.v2ray_converter:
            status['v2ray_converter'] = self.v2ray_converter.get_status()
        
        # 代理检测器状态
        proxy_info = self.proxy_detector.get_proxy_info()
        status['proxy_detector'] = proxy_info
        
        return status
    
    def restart_v2ray_converter(self) -> bool:
        """
        重启V2Ray转换器
        
        Returns
        -------
        bool
            重启是否成功
        """
        if self.v2ray_converter:
            logger.info("🔄 重启V2Ray转换器...")
            self.v2ray_converter.stop()
            
            if self.v2ray_converter.start():
                # 重新生成配置
                proxy_url = self.proxy_detector.detect_proxy(use_cache=False)
                self.current_config = self._generate_network_config(proxy_url)
                logger.info("✅ V2Ray转换器重启成功")
                return True
            else:
                logger.error("❌ V2Ray转换器重启失败")
                return False
        else:
            logger.warning("V2Ray转换器未初始化")
            return False
    
    def cleanup(self) -> None:
        """清理资源"""
        if self.v2ray_converter:
            self.v2ray_converter.stop()
            logger.info("V2Ray转换器已停止")


# 全局网络配置管理器实例
_global_manager: Optional[NetworkConfigManager] = None


def get_network_manager() -> NetworkConfigManager:
    """
    获取全局网络配置管理器
    
    Returns
    -------
    NetworkConfigManager
        网络配置管理器实例
    """
    global _global_manager
    if _global_manager is None:
        _global_manager = NetworkConfigManager()
        _global_manager.initialize()
    return _global_manager


def configure_requests_session(session) -> None:
    """
    便捷函数：配置requests.Session
    
    Parameters
    ----------
    session : requests.Session
        要配置的Session对象
    """
    manager = get_network_manager()
    manager.configure_requests_session(session)


def get_freqtrade_config() -> Dict[str, Any]:
    """
    便捷函数：获取FreqTrade配置
    
    Returns
    -------
    Dict[str, Any]
        FreqTrade配置
    """
    manager = get_network_manager()
    return manager.get_freqtrade_exchange_config()


def test_network() -> Dict[str, Any]:
    """
    便捷函数：测试网络连通性
    
    Returns
    -------
    Dict[str, Any]
        测试结果
    """
    manager = get_network_manager()
    return manager.test_network_connectivity() 