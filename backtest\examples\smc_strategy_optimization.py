#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SMC策略参数优化脚本 - 简化版本

✅ 遵循MyGameNotes.md原则：使用现有smc_optimizer.py，避免重复实现
"""

import os
import sys
import logging
import time
from tqdm import tqdm

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# ✅ 使用现有组件，不重复实现
from data.storage.optimized_storage import OptimizedStorage
from backtest.strategies.optimization.smc_optimizer import SMCOptimizer
from config.smc_strategy_config import SMCConfigManager

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数 - 使用现有优化器"""
    start_time = time.time()
    
    print("SMC策略参数优化系统")
    print("✅ 使用现有smc_optimizer.py，避免重复实现")
    print("=" * 50)
    
    # 加载数据
    print("加载预下载的1分钟数据...")
    storage = OptimizedStorage("./data/storage/data")
    
    symbol = 'BTC_USDT'
    timeframe = '1m'
    
    try:
        if storage.has_data(symbol, timeframe):
            data = storage.load_data(symbol, timeframe)
            
            # 限制数据量（最近30天）
            if len(data) > 43200:
                data = data.tail(43200)
            
            print(f"数据周期: {data.index[0]} 到 {data.index[-1]}")
            print(f"数据点数: {len(data)}")
        else:
            print(f"❌ {symbol} 1分钟数据不存在")
            return
    except Exception as e:
        print(f"数据加载错误: {e}")
        return
    
    # 数据分割
    train_size = int(len(data) * 0.7)
    train_data = data.iloc[:train_size].copy()
    
    print(f"训练集: {len(train_data)} 个数据点")
    
    # ✅ 使用现有优化器，不重复实现
    print("\n=== 开始参数优化 ===")
    
    config_manager = SMCConfigManager()
    
    # ✅ 使用FreqTrade兼容的优化器
    from backtest.strategies.optimization.smc_optimizer import FreqTradeOptimizationEngine
    optimizer = SMCOptimizer(
        data=train_data,
        config_manager=config_manager,
        engine_class=FreqTradeOptimizationEngine  # 明确使用FreqTrade兼容引擎
    )
    
    print("运行网格搜索优化...")
    results = optimizer.run_grid_search(
        max_workers=1,  # 单进程避免序列化问题
        save_results=True,
        results_dir="./backtest/examples/output/enhanced_optimization"
    )
    
    # 显示结果
    print(f"\n[最佳参数配置]:")
    for param, value in results.best_params.items():
        if param != 'config':  # 跳过内部配置
            print(f"  {param}: {value}")
    print(f"最佳得分: {results.best_score:.4f}")
    
    # 保存简化结果
    output_dir = "./backtest/examples/output/enhanced_optimization"
    os.makedirs(output_dir, exist_ok=True)
    
    with open(f"{output_dir}/smc_best_params.txt", "w", encoding='utf-8') as f:
        for param, value in results.best_params.items():
            if param != 'config':
                f.write(f"{param}: {value}\n")
    
    elapsed_time = time.time() - start_time
    hours, remainder = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    print(f"\n总运行时间: {int(hours)}h {int(minutes)}m {seconds:.2f}s")
    print("\n[成功] SMC策略参数优化完成！")
    
    return results.best_params

if __name__ == '__main__':
    try:
        best_params = main()
        print("✅ 优化脚本执行成功")
    except Exception as e:
        print(f"运行错误: {e}")
        import traceback
        traceback.print_exc() 