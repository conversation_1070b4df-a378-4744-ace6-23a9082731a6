#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Backtrader优化模块

提供Backtrader策略参数优化和Walk Forward Analysis功能。
"""

import backtrader as bt
import pandas as pd
import numpy as np
from typing import Dict, Any, Union, Optional, List, Tuple, Callable
import itertools
import multiprocessing as mp
import os
import time
from datetime import datetime
import logging

from ..base import Strategy, BacktestResults
from . import core
from . import analyzers

logger = logging.getLogger(__name__)

class ParameterOptimizer:
    """
    策略参数优化器
    
    提供参数网格搜索、遗传算法优化等功能。
    """
    
    def __init__(self, engine: core.BacktraderEngine, strategy_class, data: pd.DataFrame,
                 metric: str = 'sharpe_ratio', maximize: bool = True, **engine_kwargs):
        """
        初始化参数优化器
        
        Parameters
        ----------
        engine : core.BacktraderEngine
            Backtrader引擎实例
        strategy_class : type
            策略类（非实例）
        data : pd.DataFrame
            回测数据
        metric : str, optional
            优化目标指标，默认为'sharpe_ratio'
        maximize : bool, optional
            是否最大化指标，默认为True
        **engine_kwargs : dict
            Backtrader引擎参数
        """
        self.engine = engine
        self.strategy_class = strategy_class
        self.data = data
        self.metric = metric
        self.maximize = maximize
        self.engine_kwargs = engine_kwargs
        self.results = None
        
        # 用于多进程优化的全局变量
        self._param_names = None
        self._strategy_class = None
        self._data = None
        self._engine_kwargs = None
        self._metric = None
        self._maximize = None
        
    def _evaluate_params(self, params):
        """
        评估特定参数组合
        
        Parameters
        ----------
        params : tuple
            参数值的元组
            
        Returns
        -------
        dict
            包含参数和性能指标的字典
        """
        # 创建参数字典
        param_dict = dict(zip(self._param_names, params))
        return self._evaluate_strategy(param_dict)
        
    def grid_search(self, param_grid: Dict[str, List], n_jobs: int = 1) -> pd.DataFrame:
        """
        网格搜索优化
        
        Parameters
        ----------
        param_grid : dict
            参数网格，格式为 {'param_name': [value1, value2, ...]}
        n_jobs : int, optional
            并行任务数，默认为1
            
        Returns
        -------
        pd.DataFrame
            优化结果，包含参数和性能指标
        """
        # 创建参数组合
        self._param_names = list(param_grid.keys())
        param_values = list(param_grid.values())
        param_combinations = list(itertools.product(*param_values))
        
        # 设置多进程所需的全局变量
        self._strategy_class = self.strategy_class
        self._data = self.data
        self._engine_kwargs = self.engine_kwargs
        self._metric = self.metric
        self._maximize = self.maximize
        
        logger.info(f"开始参数优化，共 {len(param_combinations)} 组参数组合")
        
        # 运行优化
        start_time = time.time()
        
        if n_jobs > 1 and len(param_combinations) > 1:
            # 使用多进程池时，必须在if __name__ == "__main__"下运行
            # 或者使用spawn方法创建进程
            mp.set_start_method('spawn', force=True)
            # 并行处理
            with mp.Pool(n_jobs) as pool:
                results = pool.map(self._evaluate_params, param_combinations)
        else:
            # 串行处理
            results = [self._evaluate_params(params) for params in param_combinations]
        
        duration = time.time() - start_time
        logger.info(f"参数优化完成，耗时 {duration:.2f} 秒")
        
        # 整理结果
        results_df = pd.DataFrame(results)
        
        # 排序结果
        if self.maximize:
            results_df = results_df.sort_values(by=self.metric, ascending=False)
        else:
            results_df = results_df.sort_values(by=self.metric, ascending=True)
            
        self.results = results_df
        return results_df
    
    def _evaluate_strategy(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估特定参数下的策略性能
        
        Parameters
        ----------
        params : dict
            策略参数
            
        Returns
        -------
        dict
            包含参数和性能指标的字典
        """
        try:
            # 创建策略实例
            strategy = self.strategy_class(**params)
            
            # 创建新的Backtrader引擎实例
            bt_engine = core.BacktraderEngine(self.data, **self.engine_kwargs)
            
            # 运行回测
            results = bt_engine.run(strategy)
            
            # 获取性能指标
            metrics = results.metrics
            
            # 整理评估结果
            eval_result = {
                **params,  # 包含所有参数
                **metrics,  # 包含所有性能指标
            }
            
            return eval_result
            
        except Exception as e:
            logger.error(f"评估参数 {params} 时出错: {e}")
            
            # 返回无效结果
            eval_result = {
                **params,
                self.metric: float('-inf') if self.maximize else float('inf'),
            }
            
            return eval_result
    
    def get_best_params(self) -> Dict[str, Any]:
        """
        获取最佳参数
        
        Returns
        -------
        dict
            最佳参数组合
        """
        if self.results is None:
            raise ValueError("请先运行优化")
            
        # 获取最佳结果行
        best_row = self.results.iloc[0]
        
        # 提取参数
        param_cols = [col for col in best_row.index if col not in 
                      ['sharpe_ratio', 'max_drawdown', 'max_drawdown_len', 
                       'total_return', 'annual_return', 'volatility', 
                       'total_trades', 'win_rate', 'loss_rate', 'avg_trade_pnl',
                       'max_winner', 'max_loser']]
        
        # 创建参数字典
        best_params = {param: best_row[param] for param in param_cols}
        
        return best_params
    
    def plot_optimization_results(self, param_names: Optional[List[str]] = None, top_n: int = 20):
        """
        可视化优化结果
        
        Parameters
        ----------
        param_names : list of str, optional
            要分析的参数名称，默认为None表示所有参数
        top_n : int, optional
            显示前N个结果，默认为20
        """
        if self.results is None:
            raise ValueError("请先运行优化")
            
        # 限制显示结果数量
        results = self.results.head(top_n)
        
        # 如果没有指定参数名称，则使用所有非指标列
        if param_names is None:
            metric_cols = ['sharpe_ratio', 'max_drawdown', 'max_drawdown_len', 
                           'total_return', 'annual_return', 'volatility', 
                           'total_trades', 'win_rate', 'loss_rate', 'avg_trade_pnl',
                           'max_winner', 'max_loser']
            param_names = [col for col in results.columns if col not in metric_cols]
            
        # 创建图表
        import matplotlib.pyplot as plt
        import matplotlib.gridspec as gridspec
        
        n_params = len(param_names)
        fig = plt.figure(figsize=(15, n_params * 3))
        
        gs = gridspec.GridSpec(n_params, 2, width_ratios=[3, 1])
        
        # 对每个参数绘制散点图和箱线图
        for i, param in enumerate(param_names):
            # 散点图
            ax1 = plt.subplot(gs[i, 0])
            ax1.scatter(results[param], results[self.metric], alpha=0.6)
            ax1.set_xlabel(param)
            ax1.set_ylabel(self.metric)
            ax1.set_title(f"{param} vs {self.metric}")
            ax1.grid(True)
            
            # 箱线图（按参数值分组）
            ax2 = plt.subplot(gs[i, 1])
            results.boxplot(column=self.metric, by=param, ax=ax2, grid=False)
            ax2.set_title("")
            ax2.set_xlabel("")
            
        plt.tight_layout()
        plt.show()


class WalkForwardAnalysis:
    """
    Walk Forward Analysis实现
    
    提供滚动窗口的策略参数优化和测试功能。
    """
    
    def __init__(self, engine: core.BacktraderEngine, strategy_class, data: pd.DataFrame,
                 train_size: float = 0.6, test_size: float = 0.4, n_windows: int = 5,
                 metric: str = 'sharpe_ratio', maximize: bool = True, **engine_kwargs):
        """
        初始化Walk Forward Analysis
        
        Parameters
        ----------
        engine : core.BacktraderEngine
            Backtrader引擎实例
        strategy_class : type
            策略类（非实例）
        data : pd.DataFrame
            回测数据
        train_size : float, optional
            训练集比例，默认为0.6
        test_size : float, optional
            测试集比例，默认为0.4
        n_windows : int, optional
            窗口数量，默认为5
        metric : str, optional
            优化目标指标，默认为'sharpe_ratio'
        maximize : bool, optional
            是否最大化指标，默认为True
        **engine_kwargs : dict
            Backtrader引擎参数
        """
        self.engine = engine
        self.strategy_class = strategy_class
        self.data = data
        self.train_size = train_size
        self.test_size = test_size
        self.n_windows = n_windows
        self.metric = metric
        self.maximize = maximize
        self.engine_kwargs = engine_kwargs
        self.results = None
        
    def run(self, param_grid: Dict[str, List], n_jobs: int = 1) -> pd.DataFrame:
        """
        运行Walk Forward Analysis
        
        Parameters
        ----------
        param_grid : dict
            参数网格，格式为 {'param_name': [value1, value2, ...]}
        n_jobs : int, optional
            并行任务数，默认为1
            
        Returns
        -------
        pd.DataFrame
            测试窗口结果
        """
        # 计算每个窗口的大小
        full_size = len(self.data)
        window_size = int(full_size / self.n_windows)
        
        # 存储窗口结果
        window_results = []
        
        for i in range(self.n_windows):
            logger.info(f"处理窗口 {i+1}/{self.n_windows}")
            
            # 计算窗口边界
            start_idx = i * window_size
            train_end_idx = start_idx + int(window_size * self.train_size)
            test_end_idx = min(train_end_idx + int(window_size * self.test_size), full_size)
            
            # 获取训练和测试数据
            train_data = self.data.iloc[start_idx:train_end_idx].copy()
            test_data = self.data.iloc[train_end_idx:test_end_idx].copy()
            
            # 创建优化器并优化训练数据
            optimizer = ParameterOptimizer(
                self.engine, 
                self.strategy_class, 
                train_data,
                metric=self.metric,
                maximize=self.maximize,
                **self.engine_kwargs
            )
            
            # 运行网格搜索
            optimizer.grid_search(param_grid, n_jobs=n_jobs)
            
            # 获取最佳参数
            best_params = optimizer.get_best_params()
            
            # 在测试数据上评估
            test_engine = core.BacktraderEngine(test_data, **self.engine_kwargs)
            best_strategy = self.strategy_class(**best_params)
            test_results = test_engine.run(best_strategy)
            
            # 记录窗口结果
            window_result = {
                'window': i+1,
                'train_start': self.data.index[start_idx],
                'train_end': self.data.index[train_end_idx-1],
                'test_start': self.data.index[train_end_idx],
                'test_end': self.data.index[test_end_idx-1] if test_end_idx < len(self.data) else self.data.index[-1],
                'best_params': best_params,
                **test_results.metrics
            }
            
            window_results.append(window_result)
            
        # 转换为DataFrame
        results_df = pd.DataFrame(window_results)
        self.results = results_df
        
        return results_df
    
    def plot_results(self):
        """绘制Walk Forward Analysis结果"""
        if self.results is None:
            raise ValueError("请先运行分析")
            
        import matplotlib.pyplot as plt
        
        # 创建图表
        fig, axes = plt.subplots(3, 1, figsize=(12, 12))
        
        # 绘制每个窗口的性能指标
        window_nums = self.results['window']
        
        # 绘制夏普比率
        axes[0].plot(window_nums, self.results['sharpe_ratio'], 'o-', label='Sharpe Ratio')
        axes[0].set_title('Sharpe Ratio by Window')
        axes[0].set_xlabel('Window')
        axes[0].set_ylabel('Sharpe Ratio')
        axes[0].grid(True)
        
        # 绘制收益率
        axes[1].plot(window_nums, self.results['total_return'], 'o-', label='Total Return')
        axes[1].set_title('Return by Window')
        axes[1].set_xlabel('Window')
        axes[1].set_ylabel('Return')
        axes[1].grid(True)
        
        # 绘制最大回撤
        axes[2].plot(window_nums, self.results['max_drawdown'], 'o-', label='Max Drawdown')
        axes[2].set_title('Max Drawdown by Window')
        axes[2].set_xlabel('Window')
        axes[2].set_ylabel('Drawdown')
        axes[2].grid(True)
        
        plt.tight_layout()
        plt.show()
        
        # 参数稳定性分析
        param_keys = list(self.results['best_params'].iloc[0].keys())
        
        # 创建参数变化图
        fig, axes = plt.subplots(len(param_keys), 1, figsize=(12, 3*len(param_keys)))
        
        for i, param in enumerate(param_keys):
            param_values = [window_params[param] for window_params in self.results['best_params']]
            axes[i].plot(window_nums, param_values, 'o-', label=param)
            axes[i].set_title(f'{param} by Window')
            axes[i].set_xlabel('Window')
            axes[i].set_ylabel('Value')
            axes[i].grid(True)
            
        plt.tight_layout()
        plt.show()
        
    def get_robust_params(self) -> Dict[str, Any]:
        """
        获取稳健的参数组合
        
        根据测试窗口的表现，计算最稳健的参数组合。
        
        Returns
        -------
        dict
            稳健的参数组合
        """
        if self.results is None:
            raise ValueError("请先运行分析")
            
        # 提取每个窗口的最佳参数
        param_sets = self.results['best_params'].tolist()
        
        # 获取所有参数名
        all_params = {}
        for param_set in param_sets:
            for key, value in param_set.items():
                if key not in all_params:
                    all_params[key] = []
                all_params[key].append(value)
        
        # 计算每个参数的众数或平均值
        robust_params = {}
        for param, values in all_params.items():
            # 对于数值型参数，取平均值
            if all(isinstance(x, (int, float)) for x in values):
                if all(isinstance(x, int) for x in values):
                    # 整数参数取四舍五入的平均值
                    robust_params[param] = int(round(np.mean(values)))
                else:
                    # 浮点数参数取平均值
                    robust_params[param] = np.mean(values)
            else:
                # 对于非数值型参数，取众数
                from scipy import stats
                try:
                    mode_result = stats.mode(values)
                    # SciPy 1.10.0+ 返回ModeResult对象
                    if hasattr(mode_result, 'mode') and hasattr(mode_result.mode, '__len__'):
                        robust_params[param] = mode_result.mode[0]
                    # 较新版本可能返回值而不是数组
                    elif hasattr(mode_result, 'mode'):
                        robust_params[param] = mode_result.mode
                    # 回退方案
                    else:
                        # 手动计算众数
                        from collections import Counter
                        counts = Counter(values)
                        robust_params[param] = counts.most_common(1)[0][0]
                except Exception as e:
                    # 如果众数计算失败，使用第一个值
                    robust_params[param] = values[0]
                
        return robust_params
