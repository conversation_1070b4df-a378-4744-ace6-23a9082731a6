"""
数据模块命令行接口

提供数据获取和管理的命令行工具。
"""

import argparse
import logging
import sys

from data.cli.download import download_command
from data.cli.info import info_command
from data.cli.clean import clean_command
from data.cli.quality import quality_command
from data.cli.backtest_report import main as backtest_report_command

__all__ = ['download_command', 'info_command', 'clean_command', 'quality_command', 'backtest_report_command']


def setup_logging(level=logging.INFO):
    """设置日志配置"""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        stream=sys.stdout
    )


def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(description='量化交易系统工具')
    
    # 设置子命令
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 下载命令
    download_parser = subparsers.add_parser('download', help='下载历史市场数据')
    download_parser.add_argument('-e', '--exchange', required=True, help='交易所ID，如binance, huobi等')
    download_parser.add_argument('-s', '--symbol', required=True, help='交易对，如BTC/USDT')
    download_parser.add_argument('-t', '--timeframe', required=True, help='时间周期，如1m, 1h, 1d等')
    download_parser.add_argument('--start', help='开始时间，格式: YYYY-MM-DD或YYYY-MM-DD HH:MM:SS')
    download_parser.add_argument('--end', help='结束时间，格式: YYYY-MM-DD或YYYY-MM-DD HH:MM:SS')
    download_parser.add_argument('--api-key', help='API密钥（如需要）')
    download_parser.add_argument('--secret', help='API密钥（如需要）')
    download_parser.add_argument('--proxy', help='HTTP代理')
    download_parser.add_argument('--compress', action='store_true', help='启用数据压缩')
    download_parser.add_argument('-v', '--verbose', action='store_true', help='输出详细日志')
    
    # 信息命令
    info_parser = subparsers.add_parser('info', help='显示数据信息')
    info_parser.add_argument('-e', '--exchange', help='交易所ID，如binance, huobi等')
    info_parser.add_argument('-s', '--symbol', help='交易对，如BTC/USDT')
    info_parser.add_argument('-t', '--timeframe', help='时间周期，如1m, 1h, 1d等')
    info_parser.add_argument('--list-symbols', action='store_true', help='列出支持的交易对')
    info_parser.add_argument('--list-timeframes', action='store_true', help='列出支持的时间周期')
    info_parser.add_argument('--list-exchanges', action='store_true', help='列出支持的交易所')
    info_parser.add_argument('--check-integrity', action='store_true', help='检查数据完整性')
    info_parser.add_argument('-v', '--verbose', action='store_true', help='输出详细日志')
    
    # 清洗命令
    clean_parser = subparsers.add_parser('clean', help='清洗和预处理数据')
    from data.cli.clean import _add_clean_args
    _add_clean_args(clean_parser)
    
    # 质量检查命令
    quality_parser = subparsers.add_parser('quality', help='检查数据质量并生成报告')
    from data.cli.quality import _add_quality_args
    _add_quality_args(quality_parser)
    
    # 回测报告命令
    backtest_report_parser = subparsers.add_parser('backtest-report', help='生成回测报告')
    backtest_report_parser.add_argument('subcommand', choices=['single', 'batch', 'optimize'], help='子命令')
    backtest_report_parser.add_argument('-v', '--verbose', action='store_true', help='输出详细日志')
    
    return parser


def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    # 设置日志级别
    log_level = logging.DEBUG if getattr(args, 'verbose', False) else logging.INFO
    setup_logging(log_level)
    
    # 根据命令执行对应的函数
    if args.command == 'download':
        download_command()
    elif args.command == 'info':
        info_command()
    elif args.command == 'clean':
        clean_command(args)
    elif args.command == 'quality':
        quality_command(args)
    elif args.command == 'backtest-report':
        # 将control传递给专门的报告生成命令
        sys.argv = [sys.argv[0]] + sys.argv[2:]  # 移除"backtest-report"子命令
        backtest_report_command()
    else:
        parser.print_help()


if __name__ == '__main__':
    main() 