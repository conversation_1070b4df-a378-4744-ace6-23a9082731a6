"""
数据存储基类

定义了所有数据存储的通用功能和接口。
"""

import os
import logging
from abc import abstractmethod
from datetime import datetime
from typing import List, Dict, Optional, Any, Set

import pandas as pd

from data.base import DataStorage
from data.structures import OHLCVColumns


class BaseDataStorage(DataStorage):
    """
    数据存储基类
    
    实现了DataStorage接口的通用功能。
    特定的存储实现类应该继承此类并实现具体的存储和加载方法。
    """
    
    def __init__(self, data_dir: str):
        """
        初始化数据存储
        
        Args:
            data_dir: 数据存储的根目录
        """
        self.data_dir = data_dir
        os.makedirs(data_dir, exist_ok=True)
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
    
    @abstractmethod
    def save_data(self, data: pd.DataFrame, symbol: str, timeframe: str) -> None:
        """
        保存市场数据
        
        Args:
            data: 包含OHLCV数据的DataFrame
            symbol: 交易对或资产代码
            timeframe: 时间周期
        """
        pass
    
    @abstractmethod
    def load_data(self, symbol: str, timeframe: str, 
                 start_time: Optional[datetime] = None, 
                 end_time: Optional[datetime] = None) -> pd.DataFrame:
        """
        加载市场数据
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            start_time: 开始时间 (可选)
            end_time: 结束时间 (可选)
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        pass
    
    @abstractmethod
    def has_data(self, symbol: str, timeframe: str) -> bool:
        """
        检查是否有指定的市场数据
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            
        Returns:
            如果存在数据返回True，否则返回False
        """
        pass
    
    @abstractmethod
    def get_data_info(self, symbol: str, timeframe: str) -> Dict[str, Any]:
        """
        获取数据信息
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            
        Returns:
            包含数据信息的字典，如开始时间、结束时间、数据点数量等
        """
        pass
    
    def get_symbols(self) -> List[str]:
        """
        获取存储中的所有交易对
        
        Returns:
            交易对代码列表
        """
        # 默认实现：查找数据目录下的所有子目录
        symbols = []
        if os.path.exists(self.data_dir):
            for item in os.listdir(self.data_dir):
                item_path = os.path.join(self.data_dir, item)
                if os.path.isdir(item_path):
                    symbols.append(item)
        return sorted(symbols)
    
    def get_timeframes(self, symbol: Optional[str] = None) -> List[str]:
        """
        获取存储中的所有时间周期
        
        Args:
            symbol: 交易对或资产代码 (可选)
            
        Returns:
            时间周期列表
        """
        # 默认实现：必须由子类重写
        raise NotImplementedError("子类必须实现此方法")
    
    def update_data(self, data: pd.DataFrame, symbol: str, timeframe: str) -> None:
        """
        更新现有数据，合并新数据并去除重复项
        
        Args:
            data: 新的OHLCV数据
            symbol: 交易对或资产代码
            timeframe: 时间周期
        """
        if data.empty:
            self.logger.warning(f"尝试更新空数据: {symbol} {timeframe}")
            return
        
        # 确保数据索引是datetime类型
        if not isinstance(data.index, pd.DatetimeIndex):
            if OHLCVColumns.TIMESTAMP in data.columns:
                data = data.set_index(OHLCVColumns.TIMESTAMP)
            else:
                raise ValueError("数据必须包含时间戳列或以时间戳为索引")
        
        # 尝试加载现有数据
        if self.has_data(symbol, timeframe):
            existing_data = self.load_data(symbol, timeframe)
            
            # 合并数据
            if not existing_data.empty:
                # 确保索引不冲突
                combined_data = pd.concat([existing_data, data])
                # 按时间排序
                combined_data = combined_data.sort_index()
                # 删除重复的索引，保留最新的数据
                combined_data = combined_data[~combined_data.index.duplicated(keep='last')]
                
                # 保存合并后的数据
                self.save_data(combined_data, symbol, timeframe)
                self.logger.info(f"更新数据: {symbol} {timeframe}, 新增{len(data)}行，合并后{len(combined_data)}行")
            else:
                # 没有现有数据，直接保存新数据
                self.save_data(data, symbol, timeframe)
                self.logger.info(f"新增数据: {symbol} {timeframe}, {len(data)}行")
        else:
            # 没有现有数据，直接保存新数据
            self.save_data(data, symbol, timeframe)
            self.logger.info(f"新增数据: {symbol} {timeframe}, {len(data)}行")
    
    def delete_data(self, symbol: str, timeframe: Optional[str] = None) -> bool:
        """
        删除指定的数据
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期 (可选，如果不指定则删除所有时间周期)
            
        Returns:
            如果删除成功返回True，否则返回False
        """
        # 默认实现：必须由子类重写
        raise NotImplementedError("子类必须实现此方法")
    
    def get_symbol_dir(self, symbol: str) -> str:
        """
        获取交易对的数据目录
        
        Args:
            symbol: 交易对或资产代码
            
        Returns:
            数据目录路径
        """
        symbol_dir = os.path.join(self.data_dir, symbol)
        os.makedirs(symbol_dir, exist_ok=True)
        return symbol_dir 