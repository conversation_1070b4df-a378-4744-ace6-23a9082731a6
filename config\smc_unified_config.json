{"meta": {"name": "SMC统一配置", "description": "Smart Money Concepts策略的统一配置文件", "version": "1.0.0", "created": "2024-01-01", "note": "✅ 遵循MyGameNotes.md原则：统一配置管理，避免重复定义"}, "strategy": {"name": "SMCStrategy", "description": "基于ICT方法论的Smart Money Concepts策略", "timeframe": "1m", "features": ["Order Blocks识别", "Fair Value Gaps检测", "Market Structure分析", "Kill Zones时段过滤", "Premium/Discount Arrays"], "parameters": {"swing_threshold": 0.005, "bos_threshold": 0.002, "ob_lookback": 8, "fvg_threshold": 0.002, "risk_reward_ratio": 1.5, "atr_periods": 10, "atr_multiplier": 1.8, "htf_bias_periods": 30}}, "freqtrade": {"integration": {"enabled": true, "professional_indicators": {"vfi_length": 130, "stc_fast": 23, "stc_slow": 50, "ichimoku_enable": true}}, "trading": {"minimal_roi": 0.08, "stoploss": -0.025, "timeframe": "1m", "can_short": true, "startup_candle_count": 200}, "order_types": {"entry": "market", "exit": "market", "stoploss": "market"}}, "risk_management": {"stop_loss_pct": 2.0, "take_profit_pct": 4.0, "max_position_pct": 5.0, "max_drawdown_limit": 20.0, "kelly_fraction": 0.2, "max_trades_per_day": 10}, "signal_filter": {"min_volume_ratio": 1.5, "max_spread_pct": 0.1, "min_atr_ratio": 0.8, "rsi_oversold": 30, "rsi_overbought": 70, "use_session_filter": true}, "optimization": {"enabled": true, "target_metric": "sharpe_ratio", "grid": {"swing_threshold": [0.005, 0.008, 0.015], "bos_threshold": [0.002, 0.005, 0.01], "ob_lookback": [8, 10, 15], "fvg_threshold": [0.001, 0.005, 0.01], "risk_reward_ratio": [1.5, 2.5, 3.5], "atr_periods": [14, 20], "atr_multiplier": [2.0, 2.5], "stop_loss_pct": [2.0, 2.5], "take_profit_pct": [4.0, 5.0], "min_volume_ratio": [1.5, 2.0], "rsi_oversold": [25, 35], "rsi_overbought": [65, 75]}, "constraints": {"max_combinations": 1000, "min_trades_required": 10, "max_optimization_time": 3600}}, "monitoring": {"signal_monitor": {"enabled": true, "max_signals_per_interval": 10, "min_signal_interval": 5, "check_interval": 1}, "capital_monitor": {"enabled": true, "max_drawdown_pct": 5.0, "max_hourly_drawdown_pct": 10.0, "check_interval": 5}, "price_monitor": {"enabled": true, "max_short_term_change_pct": 3.0, "max_hourly_change_pct": 8.0, "check_interval": 5}}, "frequi_dashboard": {"alert_rules": [{"name": "SMC_Signal_Generated", "type": "entry_signal", "condition": "enter_long OR enter_short", "severity": "info", "message": "SMC策略生成交易信号"}, {"name": "Structure_Break", "type": "market_structure", "condition": "bos_bullish OR bos_bearish", "severity": "info", "message": "市场结构突破"}], "dashboard_metrics": ["total_trades", "win_rate", "avg_profit_pct", "max_drawdown", "smc_signals_count"], "chart_indicators": ["swing_high", "swing_low", "bullish_ob", "bearish_ob", "bullish_fvg", "bearish_fvg"]}, "data": {"storage_dir": "data/storage/data", "cache_enabled": true, "preferred_pairs": ["BTC_USDT", "ETH_USDT", "BNB_USDT"], "min_volume_filter": 1000000}, "alerts": {"log_file": "data/monitoring/smc/alerts.log", "storage_dir": "data/monitoring/smc/monitoring_data"}, "performance_targets": {"daily_profit_target": 3.0, "max_daily_drawdown": 5.0, "min_win_rate": 60.0, "max_consecutive_losses": 5}}