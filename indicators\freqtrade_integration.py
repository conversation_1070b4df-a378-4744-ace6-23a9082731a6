#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
freqtrade/technical库集成模块

整合freqtrade/technical库的专业量化交易指标，提供更准确和专业的技术分析功能。
本模块作为现有指标系统的补充，专注于引入行业标准的高级指标。
"""

from typing import Dict, Any, Union, Optional, Tuple
import pandas as pd
import numpy as np
import warnings

from .base import Indicator
from .utils.validation import validate_data

# 尝试导入freqtrade/technical库  
try:
    from technical.indicators import (
        ichimoku, laguerre, mmar, madrid_sqz, vfi, stc, 
        vpci, fibonacci_retracements, TKE, vwmacd
    )

    FREQTRADE_AVAILABLE = True
except ImportError:
    FREQTRADE_AVAILABLE = False
    warnings.warn(
        "freqtrade technical库未安装。请运行 'pip install technical' 来启用高级指标功能。",
        ImportWarning
    )


class FreqtradeIntegrationMixin:
    """freqtrade/technical库集成混入类"""
    
    @staticmethod
    def is_available() -> bool:
        """检查freqtrade/technical库是否可用"""
        return FREQTRADE_AVAILABLE
    
    @staticmethod
    def require_freqtrade():
        """检查并要求freqtrade/technical库可用"""
        if not FREQTRADE_AVAILABLE:
            raise ImportError(
                "此功能需要freqtrade technical库。请运行 'pip install technical' 安装。"
            )


class VFI(Indicator, FreqtradeIntegrationMixin):
    """
    Volume Flow Indicator (VFI) - 改进版OBV
    
    由Markos Katsanos创建的改进版在平衡成交量(OBV)指标，
    能够更好地解释当前市场趋势。
    """
    
    def __init__(
        self,
        length: int = 130,
        coef: float = 0.2,
        vcoef: float = 2.5,
        signal_length: int = 5,
        smooth_vfi: bool = False,
        **kwargs
    ):
        """
        初始化VFI指标
        
        Parameters
        ----------
        length : int, optional
            VFI长度，默认为130
        coef : float, optional
            价格系数，默认为0.2
        vcoef : float, optional
            最大成交量倍数，默认为2.5
        signal_length : int, optional
            信号线长度，默认为5
        smooth_vfi : bool, optional
            是否平滑VFI，默认为False
        """
        name = kwargs.pop('name', 'VFI')
        super().__init__(name, 'volume', length=length, coef=coef, 
                        vcoef=vcoef, signal_length=signal_length, 
                        smooth_vfi=smooth_vfi, **kwargs)
        self.require_freqtrade()
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算VFI指标"""
        validate_data(data, ['high', 'low', 'close', 'volume'])
        
        result = data.copy()
        vfi_val, vfima, vfi_hist = vfi(
            dataframe=result,
            length=self.params['length'],
            coef=self.params['coef'],
            vcoef=self.params['vcoef'],
            signalLength=self.params['signal_length'],
            smoothVFI=self.params['smooth_vfi']
        )
        
        result['vfi'] = vfi_val
        result['vfima'] = vfima  
        result['vfi_hist'] = vfi_hist
        
        self._result = result
        return result


class MMAR(Indicator, FreqtradeIntegrationMixin):
    """
    Madrid Moving Average Ribbon (MMAR)
    
    使用不同长度的多个移动平均线将市场趋势分为4个不同类别的指标。
    """
    
    def __init__(self, matype: str = "EMA", src: str = "close", debug: bool = False, **kwargs):
        """初始化MMAR指标"""
        name = kwargs.pop('name', 'MMAR')
        super().__init__(name, 'trend', matype=matype, src=src, debug=debug, **kwargs)
        self.require_freqtrade()
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算MMAR指标"""
        validate_data(data, ['close'])
        
        result = data.copy()
        mmar_result = mmar(
            dataframe=result,
            matype=self.params['matype'],
            src=self.params['src'],
            debug=self.params['debug']
        )
        
        # mmar返回的是一个元组，包含多个颜色分类
        (result['leadMA'], result['ma10_c'], result['ma20_c'], result['ma30_c'],
         result['ma40_c'], result['ma50_c'], result['ma60_c'], result['ma70_c'],
         result['ma80_c'], result['ma90_c']) = mmar_result
        
        self._result = result
        return result


class MadridSqz(Indicator, FreqtradeIntegrationMixin):
    """
    Madrid Squeeze指标
    
    使用多个移动平均线将市场趋势分为6个不同类别并识别挤压的指标。
    """
    
    def __init__(self, length: int = 34, src: str = "close", ref: int = 13, sqz_len: int = 5, **kwargs):
        """初始化Madrid Squeeze指标"""
        name = kwargs.pop('name', 'MadridSqz')
        super().__init__(name, 'trend', length=length, src=src, ref=ref, sqz_len=sqz_len, **kwargs)
        self.require_freqtrade()
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算Madrid Squeeze指标"""
        validate_data(data, ['close'])
        
        result = data.copy()
        sqz_cma_c, sqz_rma_c, sqz_sma_c = madrid_sqz(
            result,
            length=self.params['length'],
            src=self.params['src'],
            ref=self.params['ref'],
            sqzLen=self.params['sqz_len']
        )
        
        result['sqz_cma_c'] = sqz_cma_c
        result['sqz_rma_c'] = sqz_rma_c  
        result['sqz_sma_c'] = sqz_sma_c
        
        self._result = result
        return result


class STC(Indicator, FreqtradeIntegrationMixin):
    """
    Schaff Trend Cycle (STC)
    
    结合了移动平均收敛背离(MACD)和随机震荡器特性的高级趋势指标。
    """
    
    def __init__(
        self,
        fast: int = 23,
        slow: int = 50,
        length: int = 10,
        **kwargs
    ):
        """
        初始化STC指标
        
        Parameters
        ----------
        fast : int, optional
            快速EMA周期，默认为23
        slow : int, optional
            慢速EMA周期，默认为50
        length : int, optional
            随机震荡器长度，默认为10
        """
        name = kwargs.pop('name', 'STC')
        super().__init__(name, 'oscillator', fast=fast, 
                        slow=slow, length=length, **kwargs)
        self.require_freqtrade()
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算STC指标"""
        validate_data(data, ['close'])
        
        result = data.copy()
        result['stc'] = stc(
            dataframe=result,
            fast=self.params['fast'],
            slow=self.params['slow'],
            length=self.params['length']
        )
        
        self._result = result
        return result


class IchimokuCloud(Indicator, FreqtradeIntegrationMixin):
    """
    Ichimoku Cloud (一目均衡图)
    
    一个全面的技术分析系统，提供趋势方向、支撑阻力和动量信息。
    """
    
    def __init__(
        self,
        conversion_line_period: int = 9,
        base_line_periods: int = 26,
        laggin_span: int = 52,
        displacement: int = 26,
        **kwargs
    ):
        """
        初始化Ichimoku Cloud指标
        
        Parameters
        ----------
        conversion_line_period : int, optional
            转换线周期，默认为9
        base_line_periods : int, optional
            基准线周期，默认为26
        laggin_span : int, optional
            延迟跨度，默认为52
        displacement : int, optional
            位移，默认为26
        """
        name = kwargs.pop('name', 'IchimokuCloud')
        super().__init__(name, 'trend', 
                        conversion_line_period=conversion_line_period,
                        base_line_periods=base_line_periods,
                        laggin_span=laggin_span,
                        displacement=displacement, **kwargs)
        self.require_freqtrade()
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算Ichimoku Cloud指标"""
        validate_data(data, ['high', 'low', 'close'])
        
        result = data.copy()
        ichimoku_result = ichimoku(
            dataframe=result,
            conversion_line_period=self.params['conversion_line_period'],
            base_line_periods=self.params['base_line_periods'],
            laggin_span=self.params['laggin_span'],
            displacement=self.params['displacement']
        )
        
        # ichimoku函数返回字典，需要将各个组件添加到结果中
        for key, value in ichimoku_result.items():
            result[key] = value
        
        self._result = result
        return result


class Laguerre(Indicator, FreqtradeIntegrationMixin):
    """
    Laguerre RSI
    
    由John Ehlers开发的指标，作为减少常规RSI噪音和滞后的方法。
    """
    
    def __init__(self, gamma: float = 0.7, **kwargs):
        """
        初始化Laguerre RSI指标
        
        Parameters
        ----------
        gamma : float, optional
            Gamma参数，默认为0.7
        """
        name = kwargs.pop('name', 'Laguerre')
        super().__init__(name, 'oscillator', gamma=gamma, **kwargs)
        self.require_freqtrade()
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算Laguerre RSI指标"""
        validate_data(data, ['close'])
        
        result = data.copy()
        result['laguerre'] = laguerre(
            dataframe=result,
            gamma=self.params['gamma']
        )
        
        self._result = result
        return result


class VPCI(Indicator, FreqtradeIntegrationMixin):
    """
    Volume Price Confirmation Indicator (VPCI)
    
    成交量价格确认指标，用于确认价格趋势的强度。
    """
    
    def __init__(self, period_short: int = 5, period_long: int = 20, **kwargs):
        """初始化VPCI指标"""
        name = kwargs.pop('name', 'VPCI')
        super().__init__(name, 'volume', period_short=period_short, period_long=period_long, **kwargs)
        self.require_freqtrade()
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算VPCI指标"""
        validate_data(data, ['high', 'low', 'close', 'volume'])
        
        result = data.copy()
        result['vpci'] = vpci(
            dataframe=result,
            period_short=self.params['period_short'],
            period_long=self.params['period_long']
        )
        
        self._result = result
        return result


class FibonacciRetracements(Indicator, FreqtradeIntegrationMixin):
    """
    Fibonacci Retracements
    
    显示每个蜡烛图超过的斐波那契水平的指标。
    """
    
    def __init__(self, field: str = "close", **kwargs):
        """初始化Fibonacci Retracements指标"""
        name = kwargs.pop('name', 'FibonacciRetracements')
        super().__init__(name, 'trend', field=field, **kwargs)
        self.require_freqtrade()
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算Fibonacci Retracements指标"""
        validate_data(data, ['high', 'low', 'close'])
        
        result = data.copy()
        result['fibonacci_retracements'] = fibonacci_retracements(
            df=result, 
            field=self.params['field']
        )
        
        self._result = result
        return result


class TKEIndicator(Indicator, FreqtradeIntegrationMixin):
    """
    TKE Indicator
    
    7个震荡器的算术平均值。
    """
    
    def __init__(self, length: int = 14, ema_period: int = 5, **kwargs):
        """初始化TKE指标"""
        name = kwargs.pop('name', 'TKE')
        super().__init__(name, 'oscillator', length=length, ema_period=ema_period, **kwargs)
        self.require_freqtrade()
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算TKE指标"""
        validate_data(data, ['high', 'low', 'close', 'volume'])
        
        result = data.copy()
        tke_val, tke_ema = TKE(
            dataframe=result,
            length=self.params['length'],
            emaperiod=self.params['ema_period']
        )
        
        result['TKE'] = tke_val
        result['TKEema'] = tke_ema
        
        self._result = result
        return result


class VolumeWeightedMACD(Indicator, FreqtradeIntegrationMixin):
    """
    Volume Weighted MACD
    
    成交量加权的MACD指标。
    """
    
    def __init__(
        self,
        fast_period: int = 12,
        slow_period: int = 26,
        signal_period: int = 9,
        **kwargs
    ):
        """
        初始化Volume Weighted MACD指标
        
        Parameters
        ----------
        fast_period : int, optional
            快线周期，默认为12
        slow_period : int, optional
            慢线周期，默认为26
        signal_period : int, optional
            信号线周期，默认为9
        """
        name = kwargs.pop('name', 'VolumeWeightedMACD')
        super().__init__(name, 'trend', fast_period=fast_period,
                        slow_period=slow_period, signal_period=signal_period, **kwargs)
        self.require_freqtrade()
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算Volume Weighted MACD指标"""
        validate_data(data, ['close', 'volume'])
        
        result = data.copy()
        vwmacd_result = vwmacd(
            dataframe=result,
            fastperiod=self.params['fast_period'],
            slowperiod=self.params['slow_period'],
            signalperiod=self.params['signal_period']
        )
        
        # 合并结果到原始数据框
        for col in vwmacd_result.columns:
            if col not in result.columns:
                result[col] = vwmacd_result[col]
        
        self._result = result
        return result





# 工具函数
def get_available_indicators() -> Dict[str, str]:
    """
    获取可用的freqtrade指标列表
    
    Returns
    -------
    Dict[str, str]
        指标名称和描述的字典
    """
    if not FREQTRADE_AVAILABLE:
        warnings.warn("freqtrade technical库未安装", ImportWarning)
        return {}
    
    return {
        'VFI': 'Volume Flow Indicator - 成交量流向指标',
        'MMAR': 'Madrid Moving Average Ribbon - 马德里移动平均线组',
        'MadridSqz': 'Madrid Squeeze - 马德里挤压指标',
        'STC': 'Schaff Trend Cycle - 沙夫趋势循环',
        'IchimokuCloud': 'Ichimoku Cloud - 一目均衡图',
        'Laguerre': 'Laguerre RSI - 拉盖尔RSI',
        'VPCI': 'Volume Price Confirmation Indicator - 成交量价格确认指标',
        'FibonacciRetracements': 'Fibonacci Retracements - 斐波那契回撤',
        'TKEIndicator': 'TKE Indicator - TKE震荡器',
        'VolumeWeightedMACD': 'Volume Weighted MACD - 成交量加权MACD'
    }


def get_indicator_classes() -> Dict[str, Any]:
    """
    获取指标类的字典
    
    Returns
    -------
    Dict[str, Any]
        指标名称和类的字典
    """
    if not FREQTRADE_AVAILABLE:
        return {}
    
    return {
        'VFI': VFI,
        'MMAR': MMAR,
        'MadridSqz': MadridSqz,
        'STC': STC,
        'IchimokuCloud': IchimokuCloud,
        'Laguerre': Laguerre,
        'VPCI': VPCI,
        'FibonacciRetracements': FibonacciRetracements,
        'TKEIndicator': TKEIndicator,
        'VolumeWeightedMACD': VolumeWeightedMACD
    }


# 导出的类和函数
__all__ = [
    'FreqtradeIntegrationMixin',
    'VFI',
    'MMAR', 
    'MadridSqz',
    'STC',
    'IchimokuCloud',
    'Laguerre',
    'VPCI',
    'FibonacciRetracements',
    'TKEIndicator',
    'VolumeWeightedMACD',
    'get_available_indicators',
    'get_indicator_classes'
] 