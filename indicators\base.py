#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基础技术指标模块 - 指标基类定义

定义了技术指标的基类和通用接口，所有具体指标都应继承该基类。
"""

from typing import Dict, Any, List, Optional, Union, Tuple
import pandas as pd
import numpy as np
from abc import ABC, abstractmethod


class Indicator(ABC):
    """
    技术指标基类
    
    所有具体指标继承该类，并实现calculate方法
    """
    
    def __init__(self, name: str, category: str, **kwargs):
        """
        初始化指标
        
        Parameters
        ----------
        name : str
            指标名称
        category : str
            指标类别 (trend, oscillator, volume, volatility, cycle等)
        **kwargs : dict
            指标参数
        """
        self.name = name
        self.category = category
        self.params = kwargs
        self._result = None
        
    @abstractmethod
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算指标值
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据，通常包含OHLCV数据
            
        Returns
        -------
        pd.DataFrame
            包含原始数据和计算的指标值的DataFrame
        """
        pass
    
    def __call__(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        使指标对象可调用，方便使用
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据
            
        Returns
        -------
        pd.DataFrame
            计算结果
        """
        return self.calculate(data)
    
    @property
    def description(self) -> str:
        """
        获取指标描述
        
        Returns
        -------
        str
            指标描述
        """
        return f"{self.name} ({self.category})"
    
    @property
    def result(self) -> Optional[pd.DataFrame]:
        """
        获取最近一次计算的结果
        
        Returns
        -------
        pd.DataFrame or None
            指标计算结果
        """
        return self._result
    
    def update_params(self, **kwargs) -> 'Indicator':
        """
        更新指标参数
        
        Parameters
        ----------
        **kwargs : dict
            新的参数值
            
        Returns
        -------
        Indicator
            指标对象本身，支持链式调用
        """
        self.params.update(kwargs)
        return self
    
    def plot(self, ax=None, **kwargs) -> Any:
        """
        绘制指标图表
        
        Parameters
        ----------
        ax : matplotlib.axes.Axes, optional
            用于绘图的Axes对象
        **kwargs : dict
            传递给绘图函数的其他参数
            
        Returns
        -------
        matplotlib.axes.Axes
            绘图结果
        """
        if self._result is None:
            raise ValueError("没有计算结果可供绘制，请先调用calculate方法")
        
        # 默认实现，子类可以重写以提供更专门的绘图
        import matplotlib.pyplot as plt
        
        if ax is None:
            fig, ax = plt.subplots(figsize=kwargs.get('figsize', (10, 6)))
        
        # 默认仅绘制指标列，非OHLCV列
        ohlcv_cols = ['open', 'high', 'low', 'close', 'volume']
        indicator_cols = [col for col in self._result.columns if col.lower() not in ohlcv_cols]
        
        for col in indicator_cols:
            ax.plot(self._result.index, self._result[col], label=col, **kwargs)
        
        ax.set_title(f"{self.name} - {self.description}")
        ax.legend()
        ax.grid(True)
        
        return ax


class CompositeIndicator(Indicator):
    """
    复合指标类，用于组合多个指标
    """
    
    def __init__(self, name: str, indicators: List[Indicator], **kwargs):
        """
        初始化复合指标
        
        Parameters
        ----------
        name : str
            复合指标名称
        indicators : List[Indicator]
            组成复合指标的指标列表
        **kwargs : dict
            其他参数
        """
        super().__init__(name, "composite", **kwargs)
        self.indicators = indicators
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算复合指标值
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据
            
        Returns
        -------
        pd.DataFrame
            包含所有组成指标计算结果的DataFrame
        """
        result = data.copy()
        
        # 依次计算每个组成指标
        for indicator in self.indicators:
            temp_result = indicator.calculate(result)
            
            # 合并新计算的指标列
            for col in temp_result.columns:
                if col not in result.columns:
                    result[col] = temp_result[col]
        
        self._result = result
        return result 