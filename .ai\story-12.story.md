# Epic-4: 风控系统开发
# Story-1: 基础风控规则实现

## Story

**作为** 量化交易系统开发者和用户  
**我想要** 实现一套基础的风控规则框架  
**以便于** 在回测和实盘交易中应用风险管理策略，保障交易安全  

## 状态

已完成

## 上下文

在完成回测引擎开发后，我们需要转向风控系统的实现。风控系统是量化交易的核心保障，良好的风险控制能够防止过度损失、优化资金利用并提高整体交易稳定性。基础风控规则是整个风控系统的基石，为后续的资金管理和风险监控奠定基础。

风控规则需要灵活、可配置，并能与回测引擎和未来的实盘交易系统无缝集成。这些规则应该涵盖交易频率限制、单笔交易限额、止损策略、利润保护和滑点控制等多个方面，并提供简洁的API接口供用户使用。

## 估算

Story Points: 5

## 任务

1. - [x] 设计风控系统的基础架构
   1. - [x] 定义风控规则接口和抽象基类
   2. - [x] 设计规则配置和参数验证机制
   3. - [x] 制定规则组合和链式应用方案
   4. - [x] 规划规则优先级和冲突解决机制

2. - [x] 实现基本交易限制规则
   1. - [x] 交易频率限制规则
   2. - [x] 最大持仓量规则
   3. - [x] 单笔交易规模限制规则
   4. - [x] 交易时间窗口限制规则

3. - [x] 实现止损和利润保护规则
   1. - [x] 固定止损规则
   2. - [x] 追踪止损规则
   3. - [x] 利润锁定规则
   4. - [x] 最大回撤限制规则

4. - [x] 实现市场条件过滤规则
   1. - [x] 波动性过滤规则
   2. - [x] 流动性检查规则
   3. - [x] 价格突破验证规则
   4. - [x] 趋势确认规则

5. - [x] 开发规则组合和管理工具
   1. - [x] 规则组合器
   2. - [x] 条件规则应用器
   3. - [x] 规则优先级管理器
   4. - [x] 规则配置生成器

6. - [x] 与回测引擎集成
   1. - [x] 为VectorBT回测引擎添加风控规则支持
   2. - [x] 为Backtrader回测引擎添加风控规则支持
   3. - [x] 实现回测中的规则性能分析
   4. - [x] 添加规则可视化和报告功能

7. - [x] 测试与文档
   1. - [x] 编写单元测试
   2. - [x] 创建使用示例和教程
   3. - [x] 撰写规则文档
   4. - [x] 进行集成测试

## 约束

- 风控规则框架应保持高度模块化，允许用户自定义规则
- 规则执行性能需要优化，尤其在高频回测场景中
- 需要提供清晰的API文档和使用示例
- 规则参数应支持动态调整和优化
- 所有规则应兼容系统现有的数据结构和接口
- 风控规则需要可序列化，以支持回测状态保存和恢复

## 数据模型

```python
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, Callable
import pandas as pd
import numpy as np

class RiskRule(ABC):
    """
    风控规则抽象基类
    
    所有具体风控规则必须继承此类并实现相应方法。
    """
    
    def __init__(self, name: str, description: str = "", enabled: bool = True, **params):
        """
        初始化风控规则
        
        Parameters
        ----------
        name : str
            规则名称
        description : str, optional
            规则描述，默认为空字符串
        enabled : bool, optional
            规则是否启用，默认为True
        **params : dict
            规则参数
        """
        self.name = name
        self.description = description
        self.enabled = enabled
        self.params = params
        self.validate_params()
    
    @abstractmethod
    def validate_params(self) -> None:
        """
        验证规则参数有效性
        
        每个具体规则类必须实现此方法来验证其特定参数。
        
        Raises
        ------
        ValueError
            如果参数无效
        """
        pass
    
    @abstractmethod
    def apply(self, context: Dict[str, Any], data: pd.DataFrame) -> Dict[str, Any]:
        """
        应用风控规则
        
        Parameters
        ----------
        context : Dict[str, Any]
            交易上下文，包含当前持仓、资金等信息
        data : pd.DataFrame
            市场数据
        
        Returns
        -------
        Dict[str, Any]
            包含规则应用结果的字典，通常包括是否允许交易、调整后的交易规模等信息
        """
        pass
    
    def is_enabled(self) -> bool:
        """
        检查规则是否启用
        
        Returns
        -------
        bool
            规则是否启用
        """
        return self.enabled
    
    def enable(self) -> None:
        """启用规则"""
        self.enabled = True
    
    def disable(self) -> None:
        """禁用规则"""
        self.enabled = False
    
    def update_params(self, **params) -> None:
        """
        更新规则参数
        
        Parameters
        ----------
        **params : dict
            要更新的参数
        
        Raises
        ------
        ValueError
            如果参数无效
        """
        self.params.update(params)
        self.validate_params()

class RiskManager:
    """
    风险管理器，用于管理和应用多个风控规则
    """
    
    def __init__(self):
        """初始化风险管理器"""
        self.rules: Dict[str, RiskRule] = {}
        self.rule_priorities: Dict[str, int] = {}
        self.default_context: Dict[str, Any] = {}
    
    def add_rule(self, rule: RiskRule, priority: int = 0) -> None:
        """
        添加风控规则
        
        Parameters
        ----------
        rule : RiskRule
            要添加的风控规则
        priority : int, optional
            规则优先级，数值越小优先级越高，默认为0
        
        Raises
        ------
        ValueError
            如果已存在同名规则
        """
        if rule.name in self.rules:
            raise ValueError(f"规则'{rule.name}'已存在")
        self.rules[rule.name] = rule
        self.rule_priorities[rule.name] = priority
    
    def remove_rule(self, rule_name: str) -> None:
        """
        移除风控规则
        
        Parameters
        ----------
        rule_name : str
            要移除的规则名称
        
        Raises
        ------
        KeyError
            如果规则不存在
        """
        if rule_name not in self.rules:
            raise KeyError(f"规则'{rule_name}'不存在")
        del self.rules[rule_name]
        del self.rule_priorities[rule_name]
    
    def get_rule(self, rule_name: str) -> RiskRule:
        """
        获取风控规则
        
        Parameters
        ----------
        rule_name : str
            规则名称
        
        Returns
        -------
        RiskRule
            风控规则对象
        
        Raises
        ------
        KeyError
            如果规则不存在
        """
        if rule_name not in self.rules:
            raise KeyError(f"规则'{rule_name}'不存在")
        return self.rules[rule_name]
    
    def apply_rules(self, context: Dict[str, Any], data: pd.DataFrame) -> Dict[str, Any]:
        """
        应用所有启用的风控规则
        
        按优先级顺序依次应用规则，前一规则的结果将作为后一规则的输入。
        
        Parameters
        ----------
        context : Dict[str, Any]
            交易上下文
        data : pd.DataFrame
            市场数据
        
        Returns
        -------
        Dict[str, Any]
            应用所有规则后的结果
        """
        # 合并默认上下文和传入的上下文
        current_context = {**self.default_context, **context}
        
        # 按优先级排序规则
        sorted_rules = sorted(
            [(name, rule) for name, rule in self.rules.items() if rule.is_enabled()],
            key=lambda x: self.rule_priorities[x[0]]
        )
        
        # 依次应用规则
        for _, rule in sorted_rules:
            result = rule.apply(current_context, data)
            current_context.update(result)
        
        return current_context
    
    def set_default_context(self, context: Dict[str, Any]) -> None:
        """
        设置默认上下文
        
        Parameters
        ----------
        context : Dict[str, Any]
            默认上下文
        """
        self.default_context = context
    
    def get_enabled_rules(self) -> List[RiskRule]:
        """
        获取所有启用的规则
        
        Returns
        -------
        List[RiskRule]
            启用的规则列表
        """
        return [rule for rule in self.rules.values() if rule.is_enabled()]
    
    def enable_rule(self, rule_name: str) -> None:
        """
        启用指定规则
        
        Parameters
        ----------
        rule_name : str
            规则名称
        
        Raises
        ------
        KeyError
            如果规则不存在
        """
        self.get_rule(rule_name).enable()
    
    def disable_rule(self, rule_name: str) -> None:
        """
        禁用指定规则
        
        Parameters
        ----------
        rule_name : str
            规则名称
        
        Raises
        ------
        KeyError
            如果规则不存在
        """
        self.get_rule(rule_name).disable()
    
    def update_rule_params(self, rule_name: str, **params) -> None:
        """
        更新规则参数
        
        Parameters
        ----------
        rule_name : str
            规则名称
        **params : dict
            要更新的参数
        
        Raises
        ------
        KeyError
            如果规则不存在
        ValueError
            如果参数无效
        """
        self.get_rule(rule_name).update_params(**params)
``` 

## 实现进度

### 已完成
1. 风控系统基础架构，包括：
   - RiskRule抽象基类，定义了风控规则的接口和基本方法
   - RiskManager类，管理和应用多个风控规则

2. 基本交易限制规则：
   - MaxPositionSizeRule：限制单个资产的最大持仓量
   - MaxTradeFrequencyRule：限制在指定时间窗口内的最大交易次数
   - MaxTradeSizeRule：限制单笔交易的最大规模
   - TradeTimeWindowRule：限制交易只能在指定的时间窗口内进行

3. 止损和利润保护规则：
   - FixedStopLossRule：固定止损规则，根据预设的止损价格或百分比触发平仓信号
   - TrailingStopLossRule：追踪止损规则，根据市场价格的波动动态调整止损价格
   - ProfitLockRule：利润锁定规则，当利润达到一定水平时锁定部分或全部利润
   - MaxDrawdownRule：最大回撤限制规则，当回撤超过预设限制时触发平仓信号

4. 市场条件过滤规则：
   - VolatilityFilterRule：波动性过滤规则，避免在波动性过高或过低的市场环境下交易
   - LiquidityCheckRule：流动性检查规则，避免在流动性不足的市场环境下交易
   - BreakoutVerificationRule：价格突破验证规则，验证价格突破的有效性，避免假突破
   - TrendConfirmationRule：趋势确认规则，根据各种技术指标确认市场趋势，只允许顺势交易

5. 规则组合和管理工具：
   - 风控规则预设配置（保守型、中等型、激进型、加密货币专用）
   - 自定义风控配置器，支持灵活定制规则参数

6. 与回测引擎集成：
   - 为VectorBT回测引擎添加了风控规则支持，通过VectorBTRiskAdapter和VectorBTRiskManager类实现
   - 为Backtrader回测引擎添加了风控规则支持，通过BacktraderRiskRule和integrate_risk_manager函数实现
   - 实现了风控规则性能分析，通过RiskRuleAnalyzer类提供详细的规则触发和影响分析
   - 添加了规则可视化和报告功能，通过RiskRuleVisualizer类生成交互式仪表板和HTML报告

7. 示例和文档：
   - 各种类型规则的示例代码（basic_limits_example.py, stop_loss_example.py, market_conditions_example.py）
   - 预设规则组合的示例代码（presets_example.py）
   - 详细的API文档和使用说明
   - 单元测试和集成测试，包括风控规则与回测引擎集成的示例（risk_rules_integration.py）

### 状态更新
所有任务已经完成，风控系统基础规则功能已全部实现并与回测引擎进行了集成。系统支持多种风控规则的灵活组合和配置，并提供了详细的规则性能分析和可视化工具。 