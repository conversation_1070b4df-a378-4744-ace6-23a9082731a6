"""
资金管理系统使用示例

演示如何使用资金管理系统进行仓位规模计算和资金分配。
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List
import sys
import os
import datetime as dt

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from risk.money_management.base import MoneyManager, PositionSizer
from risk.money_management.position_sizing import (
    FixedAmountSizer,
    FixedPercentSizer,
    KellyCriterionSizer,
    OptimalFSizer,
    VolatilityAdjustedSizer,
    MaxDrawdownSizer
)
from risk.money_management.capital_allocation import (
    EqualAllocationStrategy,
    VolatilityAdjustedAllocation,
    PerformanceBasedAllocation
)


def generate_sample_data() -> Dict[str, pd.DataFrame]:
    """
    生成示例市场数据
    
    Returns
    -------
    Dict[str, pd.DataFrame]
        不同品种的市场数据
    """
    # 时间序列
    date_range = pd.date_range(start='2022-01-01', end='2022-12-31', freq='D')
    
    # 生成不同波动率的数据
    data = {}
    np.random.seed(42)  # 保证可重复性
    
    # BTC: 高波动率
    btc_returns = np.random.normal(0.001, 0.03, len(date_range))
    btc_price = 100 * (1 + btc_returns).cumprod()
    btc_data = pd.DataFrame({
        'open': btc_price * 0.99,
        'high': btc_price * 1.02,
        'low': btc_price * 0.98,
        'close': btc_price,
        'volume': np.random.lognormal(10, 1, len(date_range))
    }, index=date_range)
    data['BTC'] = btc_data
    
    # ETH: 中高波动率
    eth_returns = np.random.normal(0.0008, 0.025, len(date_range))
    eth_price = 100 * (1 + eth_returns).cumprod()
    eth_data = pd.DataFrame({
        'open': eth_price * 0.995,
        'high': eth_price * 1.015,
        'low': eth_price * 0.985,
        'close': eth_price,
        'volume': np.random.lognormal(9, 1, len(date_range))
    }, index=date_range)
    data['ETH'] = eth_data
    
    # SOL: 中波动率
    sol_returns = np.random.normal(0.0005, 0.02, len(date_range))
    sol_price = 100 * (1 + sol_returns).cumprod()
    sol_data = pd.DataFrame({
        'open': sol_price * 0.997,
        'high': sol_price * 1.01,
        'low': sol_price * 0.99,
        'close': sol_price,
        'volume': np.random.lognormal(8, 1, len(date_range))
    }, index=date_range)
    data['SOL'] = sol_data
    
    # USDT: 低波动率
    usdt_returns = np.random.normal(0.0001, 0.001, len(date_range))
    usdt_price = 100 * (1 + usdt_returns).cumprod()
    usdt_data = pd.DataFrame({
        'open': usdt_price * 0.999,
        'high': usdt_price * 1.001,
        'low': usdt_price * 0.999,
        'close': usdt_price,
        'volume': np.random.lognormal(7, 1, len(date_range))
    }, index=date_range)
    data['USDT'] = usdt_data
    
    return data


def generate_sample_trades() -> Dict[str, List[Dict]]:
    """
    生成示例交易记录
    
    Returns
    -------
    Dict[str, List[Dict]]
        不同品种的交易记录
    """
    np.random.seed(42)  # 保证可重复性
    
    trades = {}
    
    # BTC: 高胜率但波动大
    btc_trades = []
    for i in range(50):
        is_win = np.random.random() < 0.65  # 65%胜率
        pnl = np.random.normal(50, 20) if is_win else -np.random.normal(30, 15)
        entry_price = 50000 + np.random.normal(0, 1000)
        exit_price = entry_price + pnl
        stop_loss_price = entry_price - np.random.uniform(500, 1500)
        
        btc_trades.append({
            'entry_time': dt.datetime(2022, 1, 1) + dt.timedelta(days=i),
            'exit_time': dt.datetime(2022, 1, 1) + dt.timedelta(days=i+1),
            'entry_price': entry_price,
            'exit_price': exit_price,
            'stop_loss_price': stop_loss_price,
            'pnl': pnl,
            'initial_risk': entry_price - stop_loss_price,
            'trade_type': 'long' if entry_price < exit_price else 'short',
            'symbol': 'BTC'
        })
    trades['BTC'] = btc_trades
    
    # ETH: 中等胜率
    eth_trades = []
    for i in range(40):
        is_win = np.random.random() < 0.55  # 55%胜率
        pnl = np.random.normal(40, 15) if is_win else -np.random.normal(25, 10)
        entry_price = 3000 + np.random.normal(0, 100)
        exit_price = entry_price + pnl
        stop_loss_price = entry_price - np.random.uniform(50, 150)
        
        eth_trades.append({
            'entry_time': dt.datetime(2022, 1, 1) + dt.timedelta(days=i),
            'exit_time': dt.datetime(2022, 1, 1) + dt.timedelta(days=i+1),
            'entry_price': entry_price,
            'exit_price': exit_price,
            'stop_loss_price': stop_loss_price,
            'pnl': pnl,
            'initial_risk': entry_price - stop_loss_price,
            'trade_type': 'long' if entry_price < exit_price else 'short',
            'symbol': 'ETH'
        })
    trades['ETH'] = eth_trades
    
    # SOL: 低胜率但平均盈利较高
    sol_trades = []
    for i in range(35):
        is_win = np.random.random() < 0.45  # 45%胜率
        pnl = np.random.normal(60, 20) if is_win else -np.random.normal(20, 10)
        entry_price = 100 + np.random.normal(0, 5)
        exit_price = entry_price + pnl
        stop_loss_price = entry_price - np.random.uniform(5, 15)
        
        sol_trades.append({
            'entry_time': dt.datetime(2022, 1, 1) + dt.timedelta(days=i),
            'exit_time': dt.datetime(2022, 1, 1) + dt.timedelta(days=i+1),
            'entry_price': entry_price,
            'exit_price': exit_price,
            'stop_loss_price': stop_loss_price,
            'pnl': pnl,
            'initial_risk': entry_price - stop_loss_price,
            'trade_type': 'long' if entry_price < exit_price else 'short',
            'symbol': 'SOL'
        })
    trades['SOL'] = sol_trades
    
    return trades


def calculate_equity_curves(trades: Dict[str, List[Dict]], initial_capital: float = 10000) -> Dict[str, List[float]]:
    """
    计算资金曲线
    
    Parameters
    ----------
    trades : Dict[str, List[Dict]]
        交易记录
    initial_capital : float, optional
        初始资金，默认为10000
    
    Returns
    -------
    Dict[str, List[float]]
        资金曲线
    """
    equity_curves = {}
    
    for symbol, symbol_trades in trades.items():
        equity = [initial_capital]
        
        for trade in symbol_trades:
            pnl = trade.get('pnl', 0)
            equity.append(equity[-1] + pnl)
        
        equity_curves[symbol] = equity
    
    return equity_curves


def demo_position_sizers():
    """演示各种仓位规模计算器的使用"""
    print("\n===== 仓位规模计算器演示 =====")
    
    # 生成示例数据
    market_data = generate_sample_data()
    sample_trades = generate_sample_trades()
    equity_curves = calculate_equity_curves(sample_trades)
    
    # 准备上下文
    context = {
        'capital': 10000,  # 可用资金
        'trades': sample_trades.get('BTC', []),  # 历史交易
        'equity_curve': equity_curves.get('BTC', []),  # 资金曲线
    }
    
    # 固定金额
    fixed_amount_sizer = FixedAmountSizer(amount=1000)
    size = fixed_amount_sizer.calculate_position_size(context)
    print(f"固定金额策略: ${size:.2f}")
    
    # 固定百分比
    fixed_percent_sizer = FixedPercentSizer(percent=5)
    size = fixed_percent_sizer.calculate_position_size(context)
    print(f"固定百分比策略 (5%): ${size:.2f}")
    
    # 凯利准则
    kelly_sizer = KellyCriterionSizer(fraction=0.5)  # 使用半凯利以降低风险
    size = kelly_sizer.calculate_position_size(context)
    print(f"凯利准则策略 (半凯利): ${size:.2f}")
    
    # 最优F值
    optimal_f_sizer = OptimalFSizer(risk_multiple=0.5)
    size = optimal_f_sizer.calculate_position_size(context)
    print(f"最优F值策略: ${size:.2f}")
    
    # 波动率调整
    volatility_sizer = VolatilityAdjustedSizer(target_risk_pct=0.5)
    size = volatility_sizer.calculate_position_size(context, market_data['BTC'])
    print(f"波动率调整策略: ${size:.2f}")
    
    # 最大回撤调整
    drawdown_sizer = MaxDrawdownSizer(max_allowed_dd_pct=15, base_position_pct=10)
    size = drawdown_sizer.calculate_position_size(context)
    print(f"最大回撤调整策略: ${size:.2f}")


def demo_allocation_strategies():
    """演示各种资金分配策略的使用"""
    print("\n===== 资金分配策略演示 =====")
    
    # 生成示例数据
    market_data = generate_sample_data()
    sample_trades = generate_sample_trades()
    equity_curves = calculate_equity_curves(sample_trades)
    
    symbols = ['BTC', 'ETH', 'SOL', 'USDT']
    total_capital = 50000
    
    # 准备上下文
    context = {
        'capital': total_capital,
        'trades': sample_trades,
        'equity_curves': equity_curves
    }
    
    # 等比例分配
    equal_strategy = EqualAllocationStrategy()
    equal_allocation = equal_strategy.allocate(symbols, total_capital, context)
    print("等比例分配策略:")
    for symbol, amount in equal_allocation.items():
        print(f"  {symbol}: ${amount:.2f} ({amount/total_capital*100:.1f}%)")
    
    # 波动率调整分配
    volatility_strategy = VolatilityAdjustedAllocation(
        volatility_lookback=20,
        max_allocation_pct=95,
        min_allocation_pct=5,
        inverse_weight=True  # 波动率越低，分配越多
    )
    volatility_allocation = volatility_strategy.allocate(symbols, total_capital, context, market_data)
    print("\n波动率调整分配策略 (低波动率优先):")
    for symbol, amount in volatility_allocation.items():
        # 计算并显示波动率
        vol = volatility_strategy._calculate_volatility(market_data[symbol])
        if vol == float('inf'):
            vol_str = "N/A"
        else:
            vol_str = f"{vol:.2f}%"
        print(f"  {symbol} (波动率: {vol_str}): ${amount:.2f} ({amount/total_capital*100:.1f}%)")
    
    # 绩效分配策略
    performance_strategy = PerformanceBasedAllocation(
        lookback_periods=50,
        max_allocation_pct=90,
        min_allocation_pct=5,
        metrics={"win_rate": 0.4, "avg_profit": 0.4, "sharpe": 0.2}  # 自定义指标权重
    )
    performance_allocation = performance_strategy.allocate(symbols, total_capital, context, market_data)
    print("\n绩效分配策略:")
    for symbol, amount in performance_allocation.items():
        # 计算胜率
        symbol_trades = sample_trades.get(symbol, [])
        if symbol_trades:
            wins = [t for t in symbol_trades if t.get("pnl", 0) > 0]
            win_rate = len(wins) / len(symbol_trades) * 100
            win_rate_str = f"{win_rate:.1f}%"
        else:
            win_rate_str = "N/A"
        print(f"  {symbol} (胜率: {win_rate_str}): ${amount:.2f} ({amount/total_capital*100:.1f}%)")


def demo_money_manager():
    """演示资金管理器的使用"""
    print("\n===== 资金管理器演示 =====")
    
    # 初始化资金管理器
    initial_capital = 100000
    money_manager = MoneyManager(initial_capital)
    
    # 添加仓位规模计算器
    money_manager.add_position_sizer(FixedPercentSizer(percent=2), is_default=True)
    money_manager.add_position_sizer(KellyCriterionSizer(fraction=0.3))
    money_manager.add_position_sizer(VolatilityAdjustedSizer(target_risk_pct=0.5))
    
    # 设置资金分配策略
    money_manager.set_allocation_strategy(
        VolatilityAdjustedAllocation(
            volatility_lookback=20,
            max_allocation_pct=90
        )
    )
    
    # 获取示例数据
    market_data = generate_sample_data()
    sample_trades = generate_sample_trades()
    equity_curves = calculate_equity_curves(sample_trades)
    
    # 准备交易上下文
    context = {
        'capital': initial_capital,
        'trades': sample_trades,
        'equity_curves': equity_curves
    }
    
    # 按波动率分配资金
    symbols = ['BTC', 'ETH', 'SOL', 'USDT']
    allocations = money_manager.allocate_capital(symbols, context, market_data)
    
    print("资金分配结果:")
    for symbol, amount in allocations.items():
        print(f"  {symbol}: ${amount:.2f}")
    
    # 计算各品种的仓位规模
    print("\n使用不同仓位规模计算器:")
    
    # 使用默认计算器（固定百分比）
    btc_size_default = money_manager.calculate_position_size('BTC', {**context, 'capital': allocations['BTC']})
    
    # 使用凯利准则计算器
    btc_size_kelly = money_manager.calculate_position_size(
        'BTC', 
        {**context, 'capital': allocations['BTC'], 'trades': sample_trades['BTC']},
        sizer_name="Kelly"
    )
    
    # 使用波动率调整计算器
    btc_size_vol = money_manager.calculate_position_size(
        'BTC', 
        {**context, 'capital': allocations['BTC']},
        data=market_data['BTC'],
        sizer_name="VolatilityAdjusted"
    )
    
    print(f"BTC 仓位规模:")
    print(f"  固定百分比 (2%): ${btc_size_default:.2f}")
    print(f"  凯利准则 (0.3倍): ${btc_size_kelly:.2f}")
    print(f"  波动率调整: ${btc_size_vol:.2f}")
    
    # 更新投资组合信息
    for symbol in symbols:
        position_info = {
            'allocated_capital': allocations[symbol],
            'position_size': money_manager.calculate_position_size(
                symbol, 
                {**context, 'capital': allocations[symbol]}
            ),
            'current_price': market_data[symbol]['close'].iloc[-1],
            'entry_date': pd.Timestamp.now()
        }
        money_manager.update_portfolio(symbol, position_info)
    
    # 获取投资组合信息
    portfolio = money_manager.get_portfolio()
    print("\n当前投资组合:")
    for symbol, info in portfolio.items():
        print(f"  {symbol}: 分配资金 ${info['allocated_capital']:.2f}, 仓位大小 ${info['position_size']:.2f}")
    
    # 获取资金管理指标
    metrics = money_manager.get_metrics()
    print("\n资金管理指标:")
    for metric, value in metrics.items():
        print(f"  {metric}: {value}")


def plot_position_sizes_comparison():
    """比较不同仓位规模计算器随波动率变化的表现"""
    print("\n绘制仓位规模比较图...")
    
    # 准备不同波动率的数据
    volatilities = np.linspace(0.5, 5, 20)  # 从0.5%到5%
    capital = 10000
    
    # 初始化仓位规模计算器
    fixed_amount = FixedAmountSizer(amount=1000)
    fixed_percent = FixedPercentSizer(percent=5)
    volatility_adjusted = VolatilityAdjustedSizer(target_risk_pct=1.0)
    
    # 计算不同波动率下的仓位规模
    fixed_amount_sizes = []
    fixed_percent_sizes = []
    volatility_adjusted_sizes = []
    
    for vol in volatilities:
        # 创建模拟的市场数据
        dates = pd.date_range('2022-01-01', periods=30)
        returns = np.random.normal(0, vol/100, len(dates))
        prices = 100 * (1 + returns).cumprod()
        market_data = pd.DataFrame({
            'open': prices * 0.99,
            'high': prices * 1.02,
            'low': prices * 0.98,
            'close': prices,
            'volume': np.random.lognormal(10, 1, len(dates))
        }, index=dates)
        
        # 计算各策略的仓位规模
        context = {'capital': capital}
        fixed_amount_sizes.append(fixed_amount.calculate_position_size(context))
        fixed_percent_sizes.append(fixed_percent.calculate_position_size(context))
        volatility_adjusted_sizes.append(volatility_adjusted.calculate_position_size(context, market_data))
    
    # 绘制比较图
    plt.figure(figsize=(10, 6))
    plt.plot(volatilities, fixed_amount_sizes, label='固定金额 ($1,000)', marker='o')
    plt.plot(volatilities, fixed_percent_sizes, label='固定百分比 (5%)', marker='s')
    plt.plot(volatilities, volatility_adjusted_sizes, label='波动率调整 (目标风险1%)', marker='^')
    
    plt.xlabel('波动率 (%)')
    plt.ylabel('仓位规模 ($)')
    plt.title('不同波动率下的仓位规模比较')
    plt.grid(True)
    plt.legend()
    plt.tight_layout()
    
    # 保存图表
    output_dir = os.path.join(os.path.dirname(__file__), 'output')
    os.makedirs(output_dir, exist_ok=True)
    plt.savefig(os.path.join(output_dir, 'position_size_comparison.png'))
    print(f"图表已保存到: {os.path.join(output_dir, 'position_size_comparison.png')}")


def plot_allocation_comparison():
    """比较不同资金分配策略的分配结果"""
    print("\n绘制资金分配比较图...")
    
    # 生成示例数据
    market_data = generate_sample_data()
    sample_trades = generate_sample_trades()
    equity_curves = calculate_equity_curves(sample_trades)
    
    symbols = ['BTC', 'ETH', 'SOL', 'USDT']
    total_capital = 100000
    
    # 准备上下文
    context = {
        'capital': total_capital,
        'trades': sample_trades,
        'equity_curves': equity_curves
    }
    
    # 各种分配策略
    strategies = {
        '等比例分配': EqualAllocationStrategy(),
        '波动率调整(低优先)': VolatilityAdjustedAllocation(inverse_weight=True),
        '波动率调整(高优先)': VolatilityAdjustedAllocation(inverse_weight=False),
        '绩效分配': PerformanceBasedAllocation()
    }
    
    # 计算各策略的分配结果
    allocations = {}
    for name, strategy in strategies.items():
        allocations[name] = strategy.allocate(symbols, total_capital, context, market_data)
    
    # 计算百分比分配
    percentages = {}
    for strategy_name, allocation in allocations.items():
        percentages[strategy_name] = {symbol: amount/total_capital*100 for symbol, amount in allocation.items()}
    
    # 绘制比较图
    fig, ax = plt.subplots(figsize=(12, 8))
    
    width = 0.2
    x = np.arange(len(symbols))
    
    for i, (strategy_name, allocation) in enumerate(percentages.items()):
        values = [allocation[symbol] for symbol in symbols]
        ax.bar(x + i*width - width*1.5, values, width, label=strategy_name)
    
    ax.set_ylabel('资金分配比例 (%)')
    ax.set_title('不同资金分配策略的比较')
    ax.set_xticks(x)
    ax.set_xticklabels(symbols)
    ax.legend()
    
    # 添加数值标签
    for i, (strategy_name, allocation) in enumerate(percentages.items()):
        for j, symbol in enumerate(symbols):
            value = allocation[symbol]
            ax.text(j + i*width - width*1.5, value + 0.5, f'{value:.1f}%', 
                    ha='center', va='bottom', fontsize=8)
    
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    plt.tight_layout()
    
    # 保存图表
    output_dir = os.path.join(os.path.dirname(__file__), 'output')
    os.makedirs(output_dir, exist_ok=True)
    plt.savefig(os.path.join(output_dir, 'allocation_comparison.png'))
    print(f"图表已保存到: {os.path.join(output_dir, 'allocation_comparison.png')}")


if __name__ == "__main__":
    print("=" * 50)
    print("资金管理系统示例")
    print("=" * 50)
    
    # 演示仓位规模计算器
    demo_position_sizers()
    
    # 演示资金分配策略
    demo_allocation_strategies()
    
    # 演示资金管理器
    demo_money_manager()
    
    # 绘制比较图
    plot_position_sizes_comparison()
    plot_allocation_comparison()
    
    print("\n示例运行完成!") 