"""
资金管理系统基础类

定义资金管理系统的核心接口和基类，包括资金管理器和仓位规模计算器。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, Tuple
import pandas as pd
import numpy as np


class PositionSizer(ABC):
    """
    仓位规模计算器抽象基类
    
    所有具体仓位规模计算策略必须继承此类并实现相应方法。
    """
    
    def __init__(self, name: str, description: str = "", **params):
        """
        初始化仓位规模计算器
        
        Parameters
        ----------
        name : str
            计算器名称
        description : str, optional
            计算器描述，默认为空字符串
        **params : dict
            计算器参数
        """
        self.name = name
        self.description = description
        self.params = params
        self.validate_params()
    
    @abstractmethod
    def validate_params(self) -> None:
        """
        验证参数有效性
        
        每个具体计算器类必须实现此方法来验证其特定参数。
        
        Raises
        ------
        ValueError
            如果参数无效
        """
        pass
    
    @abstractmethod
    def calculate_position_size(self, 
                               context: Dict[str, Any], 
                               data: pd.DataFrame = None,
                               risk_amount: float = None) -> float:
        """
        计算仓位规模
        
        Parameters
        ----------
        context : Dict[str, Any]
            交易上下文，包含当前资金、持仓等信息
        data : pd.DataFrame, optional
            相关市场数据，默认为None
        risk_amount : float, optional
            愿意承担的风险金额，默认为None
        
        Returns
        -------
        float
            计算出的仓位规模（可以是金额、数量或百分比，取决于实现）
        """
        pass
    
    def update_params(self, **params) -> None:
        """
        更新参数
        
        Parameters
        ----------
        **params : dict
            要更新的参数
        
        Raises
        ------
        ValueError
            如果参数无效
        """
        self.params.update(params)
        self.validate_params()


class MoneyManager:
    """
    资金管理器，用于管理资金分配和仓位规模
    """
    
    def __init__(self, initial_capital: float):
        """
        初始化资金管理器
        
        Parameters
        ----------
        initial_capital : float
            初始资金
        """
        self.initial_capital = initial_capital
        self.capital = initial_capital
        self.position_sizers: Dict[str, PositionSizer] = {}
        self.default_sizer: Optional[str] = None
        self.allocation_strategy = None
        self.portfolio: Dict[str, Dict[str, Any]] = {}
        
    def add_position_sizer(self, sizer: PositionSizer, is_default: bool = False) -> None:
        """
        添加仓位规模计算器
        
        Parameters
        ----------
        sizer : PositionSizer
            要添加的仓位规模计算器
        is_default : bool, optional
            是否设为默认计算器，默认为False
        
        Raises
        ------
        ValueError
            如果已存在同名计算器
        """
        if sizer.name in self.position_sizers:
            raise ValueError(f"计算器'{sizer.name}'已存在")
        
        self.position_sizers[sizer.name] = sizer
        
        if is_default or self.default_sizer is None:
            self.default_sizer = sizer.name
    
    def remove_position_sizer(self, sizer_name: str) -> None:
        """
        移除仓位规模计算器
        
        Parameters
        ----------
        sizer_name : str
            要移除的计算器名称
        
        Raises
        ------
        KeyError
            如果计算器不存在
        ValueError
            如果尝试移除默认计算器
        """
        if sizer_name not in self.position_sizers:
            raise KeyError(f"计算器'{sizer_name}'不存在")
        
        if sizer_name == self.default_sizer:
            raise ValueError(f"不能移除默认计算器'{sizer_name}'，请先设置其他默认计算器")
        
        del self.position_sizers[sizer_name]
    
    def get_position_sizer(self, sizer_name: str = None) -> PositionSizer:
        """
        获取仓位规模计算器
        
        Parameters
        ----------
        sizer_name : str, optional
            计算器名称，如果为None则返回默认计算器
        
        Returns
        -------
        PositionSizer
            仓位规模计算器对象
        
        Raises
        ------
        KeyError
            如果计算器不存在
        ValueError
            如果未设置默认计算器且未指定计算器名称
        """
        if sizer_name is None:
            if self.default_sizer is None:
                raise ValueError("未设置默认计算器")
            sizer_name = self.default_sizer
            
        if sizer_name not in self.position_sizers:
            raise KeyError(f"计算器'{sizer_name}'不存在")
            
        return self.position_sizers[sizer_name]
    
    def set_default_position_sizer(self, sizer_name: str) -> None:
        """
        设置默认仓位规模计算器
        
        Parameters
        ----------
        sizer_name : str
            计算器名称
        
        Raises
        ------
        KeyError
            如果计算器不存在
        """
        if sizer_name not in self.position_sizers:
            raise KeyError(f"计算器'{sizer_name}'不存在")
            
        self.default_sizer = sizer_name
    
    def set_allocation_strategy(self, strategy) -> None:
        """
        设置资金分配策略
        
        Parameters
        ----------
        strategy
            资金分配策略对象
        """
        self.allocation_strategy = strategy
    
    def calculate_position_size(self, 
                               symbol: str,
                               context: Dict[str, Any], 
                               data: pd.DataFrame = None,
                               risk_amount: float = None,
                               sizer_name: str = None) -> float:
        """
        计算仓位规模
        
        Parameters
        ----------
        symbol : str
            交易品种
        context : Dict[str, Any]
            交易上下文
        data : pd.DataFrame, optional
            相关市场数据，默认为None
        risk_amount : float, optional
            愿意承担的风险金额，默认为None
        sizer_name : str, optional
            要使用的计算器名称，如果为None则使用默认计算器
        
        Returns
        -------
        float
            计算出的仓位规模
        """
        sizer = self.get_position_sizer(sizer_name)
        size = sizer.calculate_position_size(context, data, risk_amount)
        return size
    
    def allocate_capital(self, symbols: List[str], context: Dict[str, Any], data: Dict[str, pd.DataFrame] = None) -> Dict[str, float]:
        """
        为多个交易品种分配资金
        
        Parameters
        ----------
        symbols : List[str]
            交易品种列表
        context : Dict[str, Any]
            交易上下文
        data : Dict[str, pd.DataFrame], optional
            各交易品种的市场数据，默认为None
        
        Returns
        -------
        Dict[str, float]
            各交易品种的资金分配，格式为 {symbol: allocation}
        """
        if self.allocation_strategy is None:
            # 如果未设置分配策略，则平均分配
            allocation = {symbol: self.capital / len(symbols) for symbol in symbols}
        else:
            allocation = self.allocation_strategy.allocate(symbols, self.capital, context, data)
        
        return allocation
    
    def update_portfolio(self, symbol: str, position_info: Dict[str, Any]) -> None:
        """
        更新投资组合信息
        
        Parameters
        ----------
        symbol : str
            交易品种
        position_info : Dict[str, Any]
            持仓信息
        """
        self.portfolio[symbol] = position_info
    
    def get_portfolio(self) -> Dict[str, Dict[str, Any]]:
        """
        获取当前投资组合
        
        Returns
        -------
        Dict[str, Dict[str, Any]]
            当前投资组合信息
        """
        return self.portfolio
    
    def update_capital(self, new_capital: float) -> None:
        """
        更新当前资金
        
        Parameters
        ----------
        new_capital : float
            新的资金量
        """
        self.capital = new_capital
    
    def get_metrics(self) -> Dict[str, float]:
        """
        获取资金管理相关指标
        
        Returns
        -------
        Dict[str, float]
            资金管理相关指标
        """
        return {
            "initial_capital": self.initial_capital,
            "current_capital": self.capital,
            "growth_rate": (self.capital / self.initial_capital - 1) * 100,
            "total_positions": len(self.portfolio),
        } 