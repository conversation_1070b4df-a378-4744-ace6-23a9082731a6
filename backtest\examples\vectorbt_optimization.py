#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
VectorBT参数优化示例

演示如何使用VectorBT引擎的参数优化和Walk Forward Analysis功能。
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import matplotlib.pyplot as plt
import logging
import sys
from typing import Dict, Any

# 添加项目根目录到路径，确保正确的绝对路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

# 导入相关模块
from backtest.vectorbt.engine import VectorBTEngine
from backtest.vectorbt.optimization import VectorBTOptimizer, VectorBTWalkForward
from backtest.vectorbt.optimization import visualize
from backtest.strategies.templates import MovingAverageCrossover
from data.structures import OHLCV
from data.sources.utils import download_crypto_data

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data(n_days=200):
    """创建测试数据或下载真实数据"""
    try:
        # 尝试下载比特币数据
        logger.info("尝试下载比特币历史数据...")
        data = download_crypto_data("BTC/USDT", "1d", 
                                    start_time=(datetime.now() - timedelta(days=n_days)),
                                    end_time=datetime.now())
        if data is not None and len(data) > 0:
            logger.info(f"成功下载数据，共 {len(data)} 条记录")
            # 确保数据有freq属性
            data.index.freq = pd.infer_freq(data.index)
            if data.index.freq is None:
                data = data.asfreq('D')  # 设置为日频率
            return data
    except Exception as e:
        logger.warning(f"无法下载数据: {e}")
    
    # 如果下载失败，则生成模拟数据
    logger.info("生成模拟数据...")
    # 生成日期序列
    end_date = datetime.now()
    start_date = end_date - timedelta(days=n_days)
    dates = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # 生成价格数据
    np.random.seed(42)
    price = 100
    prices = [price]
    
    for i in range(1, len(dates)):
        change_percent = np.random.normal(0.0005, 0.01)
        price = price * (1 + change_percent)
        prices.append(price)
    
    # 基于收盘价生成OHLCV数据
    closes = np.array(prices)
    highs = closes * (1 + np.random.uniform(0, 0.015, len(dates)))
    lows = closes * (1 - np.random.uniform(0, 0.015, len(dates)))
    opens = lows + np.random.uniform(0, 1, len(dates)) * (highs - lows)
    volumes = np.random.uniform(1000, 10000, len(dates)) * closes
    
    # 创建DataFrame
    df = pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    }, index=dates)
    
    # 确保数据频率已设置
    df.index.freq = 'D'
    
    return df

def grid_search_example():
    """参数网格搜索示例"""
    logger.info("运行VectorBT参数网格搜索示例...")
    
    # 获取数据
    data = create_test_data(n_days=365)
    
    # 创建回测引擎
    engine = VectorBTEngine(data, initial_capital=100000, freq='D')
    
    # 创建参数优化器
    optimizer = VectorBTOptimizer(
        engine_instance=engine,
        strategy_class=MovingAverageCrossover,
        data=data,
        metric='sharpe_ratio',
        maximize=True,
        initial_capital=100000,
        commission_rate=0.001,
        freq='D'
    )
    
    # 定义参数网格
    param_grid = {
        'short_window': [5, 10, 15, 20],
        'long_window': [30, 40, 50, 60],
        'position_size': [0.1, 0.25, 0.5, 0.75, 1.0]
    }
    
    # 运行网格搜索
    results = optimizer.grid_search(param_grid, n_jobs=1)
    
    # 打印结果
    print("\nVectorBT参数优化结果（前5）:")
    print(results[['short_window', 'long_window', 'position_size', 'sharpe_ratio', 'total_return', 'max_drawdown', 'win_rate']].head())
    
    # 获取最佳参数
    best_params = optimizer.get_best_params()
    print("\n最佳参数组合:")
    print(best_params)
    
    # 确保参数类型正确
    best_params['short_window'] = int(best_params['short_window'])
    best_params['long_window'] = int(best_params['long_window'])
    print("\n类型修正后的最佳参数:")
    print(best_params)
    
    # 可视化优化结果
    optimizer.plot_optimization_results()
    
    # 参数敏感性分析
    optimizer.sensitivity_analysis('short_window')
    optimizer.sensitivity_analysis('long_window')
    
    # 使用可视化模块绘制参数重要性
    visualize.plot_parameter_importance(results, metric='sharpe_ratio')
    
    # 如果有多个参数，绘制3D优化表面
    visualize.plot_optimization_surface_3d(results, 'short_window', 'long_window', metric='sharpe_ratio')
    
    # 使用最佳参数运行策略
    best_strategy = MovingAverageCrossover(**best_params)
    results = engine.run(best_strategy)
    
    # 可视化回测结果
    engine.plot()
    
    return results, best_params

def walk_forward_analysis_example():
    """Walk Forward Analysis示例"""
    logger.info("运行VectorBT Walk Forward Analysis示例...")
    
    # 获取数据
    data = create_test_data(n_days=730)  # 使用两年数据
    
    # 创建回测引擎
    engine = VectorBTEngine(data, initial_capital=100000, freq='D')
    
    # 创建Walk Forward Analysis
    wfa = VectorBTWalkForward(
        engine_instance=engine,
        strategy_class=MovingAverageCrossover,
        data=data,
        train_size=0.7,
        test_size=0.3,
        n_windows=5,
        metric='sharpe_ratio',
        maximize=True,
        initial_capital=100000,
        commission_rate=0.001,
        freq='D'
    )
    
    # 定义参数网格
    param_grid = {
        'short_window': [5, 10, 15, 20],
        'long_window': [30, 40, 50, 60],
        'position_size': [0.25, 0.5, 0.75]
    }
    
    # 运行Walk Forward Analysis
    results = wfa.run(param_grid, n_jobs=4)
    
    # 打印结果
    print("\nVectorBT Walk Forward Analysis结果:")
    print(results[['window', 'sharpe_ratio', 'total_return', 'max_drawdown', 'win_rate']])
    
    # 获取稳健参数
    robust_params = wfa.get_robust_params()
    print("\n稳健参数组合:")
    print(robust_params)
    
    # 可视化Walk Forward结果
    wfa.plot_results()
    
    # 分析参数稳定性
    stability_stats = wfa.analyze_parameter_stability()
    print("\n参数稳定性分析:")
    print(stability_stats)
    
    # 使用可视化模块绘制参数稳定性
    visualize.plot_parameter_stability(results)
    
    # 使用可视化模块绘制WFA结果
    visualize.plot_walk_forward_results(results, data)
    
    # 蒙特卡洛模拟预测
    forecast = wfa.monte_carlo_forecast(robust_params, n_simulations=50, forecast_periods=100)
    
    # 使用稳健参数运行策略
    robust_strategy = MovingAverageCrossover(**robust_params)
    final_results = engine.run(robust_strategy)
    
    # 可视化回测结果
    engine.plot()
    
    return results, robust_params

def optimization_comparison_example():
    """不同引擎优化结果比较示例"""
    logger.info("运行优化结果比较示例...")
    
    # 此部分需要在实现完整的比较功能后补充
    # 可以调用两个引擎的优化功能，然后比较结果
    print("此功能尚未实现，将在未来版本中添加")

def main():
    """主函数"""
    print("VectorBT参数优化示例")
    print("----------------------")
    print("1. 参数网格搜索")
    print("2. Walk Forward Analysis")
    print("3. 两者都运行")
    print("4. 优化结果比较（未实现）")
    
    try:
        choice = input("请选择要运行的示例 (1/2/3/4): ")
        
        if choice == '1':
            grid_search_example()
        elif choice == '2':
            walk_forward_analysis_example()
        elif choice == '3':
            print("\n运行参数网格搜索...")
            grid_results, best_params = grid_search_example()
            
            print("\n运行Walk Forward Analysis...")
            wfa_results, robust_params = walk_forward_analysis_example()
            
            # 比较结果
            print("\n参数比较:")
            print(f"网格搜索最佳参数: {best_params}")
            print(f"WFA稳健参数: {robust_params}")
        elif choice == '4':
            optimization_comparison_example()
        else:
            print("无效选择，请输入1、2、3或4")
    except KeyboardInterrupt:
        print("\n用户中断了操作")
    except Exception as e:
        print(f"\n运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 