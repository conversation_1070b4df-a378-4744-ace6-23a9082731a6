#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试自定义Backtrader数据源

测试我们实现的PandasDataFeed类。
"""

import backtrader as bt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

# 导入测试目标模块
from backtest.backtrader.data_feeds import PandasDataFeed, DataSourceFeed

def create_test_data(n_days=200):
    """创建测试用的价格数据"""
    # 生成日期序列
    end_date = datetime.now()
    start_date = end_date - timedelta(days=n_days)
    dates = pd.date_range(start=start_date, end=end_date, freq='B')
    
    # 生成价格数据
    np.random.seed(42)
    price = 100
    prices = [price]
    
    for i in range(1, len(dates)):
        change_percent = np.random.normal(0.0005, 0.01)
        price = price * (1 + change_percent)
        prices.append(price)
    
    # 基于收盘价生成OHLCV数据
    closes = np.array(prices)
    highs = closes * (1 + np.random.uniform(0, 0.015, len(dates)))
    lows = closes * (1 - np.random.uniform(0, 0.015, len(dates)))
    opens = lows + np.random.uniform(0, 1, len(dates)) * (highs - lows)
    volumes = np.random.uniform(1000, 10000, len(dates)) * closes
    
    # 创建DataFrame
    df = pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    }, index=dates)
    
    return df

# 测试自定义PandasDataFeed
def test_custom_pandas_data():
    print("测试自定义PandasDataFeed类")
    
    # 创建测试数据
    data = create_test_data()
    print(f"数据示例：\n{data.head()}")
    
    # 创建cerebro实例
    cerebro = bt.Cerebro()
    
    # 将数据添加到cerebro
    print("\n使用自定义PandasDataFeed创建数据源...")
    custom_data = PandasDataFeed(dataname=data)
    cerebro.adddata(custom_data)
    
    # 设置初始资金
    cerebro.broker.setcash(100000.0)
    
    # 运行回测
    print("\n运行回测...")
    result = cerebro.run()
    
    print("回测完成!")
    
    return result

# 测试DataSourceFeed.from_dataframe
def test_data_source_feed():
    print("\n测试DataSourceFeed.from_dataframe")
    
    # 创建测试数据
    data = create_test_data()
    
    # 创建cerebro实例
    cerebro = bt.Cerebro()
    
    # 使用DataSourceFeed.from_dataframe
    print("使用DataSourceFeed.from_dataframe创建数据源...")
    bt_data = DataSourceFeed.from_dataframe(data)
    cerebro.adddata(bt_data)
    
    # 设置初始资金
    cerebro.broker.setcash(100000.0)
    
    # 创建简单的移动平均线策略
    class SmaCross(bt.Strategy):
        params = (
            ('fast', 10),
            ('slow', 30),
        )
        
        def __init__(self):
            self.fast_sma = bt.indicators.SMA(self.data.close, period=self.params.fast)
            self.slow_sma = bt.indicators.SMA(self.data.close, period=self.params.slow)
            self.crossover = bt.indicators.CrossOver(self.fast_sma, self.slow_sma)
        
        def next(self):
            if not self.position:  # 没有持仓
                if self.crossover > 0:  # 金叉，买入信号
                    self.buy()
            else:  # 已有持仓
                if self.crossover < 0:  # 死叉，卖出信号
                    self.sell()
    
    # 添加策略
    cerebro.addstrategy(SmaCross)
    
    # 运行回测
    print("\n运行回测...")
    result = cerebro.run()
    
    # 打印最终资金
    print(f"初始资金: 100000.00")
    print(f"最终资金: {cerebro.broker.getvalue():.2f}")
    
    # 绘制结果
    # cerebro.plot()
    
    print("回测完成!")
    
    return result


if __name__ == "__main__":
    # 测试自定义PandasDataFeed
    test_custom_pandas_data()
    
    # 测试DataSourceFeed.from_dataframe
    test_data_source_feed() 