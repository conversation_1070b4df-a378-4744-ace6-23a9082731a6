"""
安全令牌生成脚本

为FreqTrade生成安全的JWT密钥和WebSocket令牌
"""

import secrets
import json
from datetime import datetime

def generate_secure_tokens():
    """生成安全的认证令牌"""
    
    print("🔐 生成FreqTrade安全认证令牌...")
    
    # 生成128位JWT密钥
    jwt_secret = secrets.token_hex(64)
    print(f"   ✅ JWT密钥已生成 (128位)")
    
    # 生成WebSocket令牌
    ws_token = secrets.token_urlsafe(32)
    print(f"   ✅ WebSocket令牌已生成")
    
    # 生成强密码建议
    secure_password = secrets.token_hex(16)
    print(f"   ✅ 建议的安全密码已生成")
    
    tokens = {
        "jwt_secret_key": jwt_secret,
        "ws_token": ws_token,
        "suggested_password": secure_password,
        "generated_at": datetime.now().isoformat()
    }
    
    # 保存到文件
    with open("secure_tokens.json", "w") as f:
        json.dump(tokens, f, indent=2)
    
    print(f"\n📋 令牌配置信息:")
    print(f"   JWT密钥: {jwt_secret[:20]}...（已截断显示）")
    print(f"   WebSocket令牌: {ws_token}")
    print(f"   建议密码: {secure_password}")
    print(f"\n💾 完整令牌已保存到 secure_tokens.json")
    
    return tokens

if __name__ == "__main__":
    tokens = generate_secure_tokens()
    
    print(f"\n🔧 接下来将使用这些令牌更新配置文件...") 