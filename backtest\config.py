#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
回测配置和默认参数

提供回测引擎的默认配置和参数值。
"""

from typing import Dict, Any

# 默认回测参数
DEFAULT_BACKTEST_PARAMS = {
    # 初始资金
    'initial_capital': 10000.0,
    
    # 交易成本参数
    'commission_rate': 0.001,  # 0.1% 手续费
    'slippage': 0.0005,        # 0.05% 滑点
    
    # 执行参数
    'trade_on_close': False,   # 是否在当前K线收盘价交易
    'hedging': False,          # 是否允许对冲仓位
    
    # 杠杆参数
    'leverage': 1.0,           # 杠杆倍数
    'margin_requirement': 1.0, # 保证金要求
    
    # 风控参数
    'risk_free_rate': 0.0,     # 无风险利率
    'max_drawdown_allowed': 0.25,  # 最大允许回撤
    'max_risk_per_trade': 0.02,    # 单笔交易最大风险
    
    # 仓位管理
    'position_size_type': 'fixed',  # 仓位类型: 'fixed', 'percent', 'risk_based'
    'position_size': 1.0,           # 仓位大小 (对应类型的单位)
    
    # 回测报告和可视化参数
    'benchmark': None,             # 基准指数
    'show_trade_details': True,    # 显示交易详情
    'show_performance_metrics': True,  # 显示性能指标
    'plot_equity_curve': True,     # 绘制净值曲线
    'plot_drawdowns': True,        # 绘制回撤
    'plot_monthly_returns': True,  # 绘制月度收益
    
    # 调试参数
    'verbose': False,              # 是否显示详细日志
    'debug': False,                # 是否开启调试模式
}

# VectorBT特有参数
DEFAULT_VECTORBT_PARAMS = {
    # 排序参数
    'order_records': True,       # 是否记录订单
    'use_caching': True,         # 是否使用缓存
    'freq': None,                # 数据频率
    
    # 绘图参数
    'plot_type': 'basic',        # 绘图类型: 'basic', 'detailed', 'full'
    'subplots': True,            # 是否使用子图
}

# Backtrader特有参数（占位，将在Backtrader实现中扩展）
DEFAULT_BACKTRADER_PARAMS = {
    'cerebro_kwargs': {},        # Cerebro初始化参数
}


def merge_parameters(user_params: Dict[str, Any], default_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    合并用户参数和默认参数
    
    Parameters
    ----------
    user_params : Dict[str, Any]
        用户提供的参数
    default_params : Dict[str, Any]
        默认参数
        
    Returns
    -------
    Dict[str, Any]
        合并后的参数
    """
    merged_params = default_params.copy()
    merged_params.update(user_params)
    return merged_params 