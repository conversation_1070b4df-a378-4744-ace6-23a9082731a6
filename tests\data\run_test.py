"""
运行测试并输出结果
"""

import os
import sys
import unittest

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from tests.data.test_optimized_storage import TestOptimizedStorage

if __name__ == '__main__':
    suite = unittest.TestLoader().loadTestsFromTestCase(TestOptimizedStorage)
    result = unittest.TextTestRunner(verbosity=2).run(suite)
    
    print(f"\n运行了 {result.testsRun} 个测试")
    if result.failures:
        print(f"失败: {len(result.failures)}")
        for i, (test, traceback) in enumerate(result.failures):
            print(f"\n=== 失败 {i+1} ===")
            print(f"测试: {test}")
            print(f"错误: {traceback}")
    
    if result.errors:
        print(f"错误: {len(result.errors)}")
        for i, (test, traceback) in enumerate(result.errors):
            print(f"\n=== 错误 {i+1} ===")
            print(f"测试: {test}")
            print(f"错误: {traceback}")
    
    print(f"\n成功: {result.testsRun - len(result.failures) - len(result.errors)}") 