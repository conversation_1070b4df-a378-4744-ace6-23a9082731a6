#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据转换模块

提供各种时间序列数据转换功能，包括移位、滚动计算、差分、百分比变化等。
这些转换可以用于创建复杂的自定义指标。
"""

from typing import Dict, Any, List, Optional, Union, Callable
import pandas as pd
import numpy as np
from abc import ABC, abstractmethod

from ..utils.validation import validate_data


class Transform(ABC):
    """
    数据转换基类
    
    定义数据转换的接口，所有具体转换都继承自该类。
    """
    
    def __init__(self, name: str, columns: Union[str, List[str]], **kwargs):
        """
        初始化转换
        
        Parameters
        ----------
        name : str
            转换名称
        columns : str或List[str]
            要转换的列
        **kwargs : dict
            转换参数
        """
        self.name = name
        self.columns = columns if isinstance(columns, list) else [columns]
        self.params = kwargs
    
    @abstractmethod
    def apply(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        应用转换
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据
            
        Returns
        -------
        pd.DataFrame
            转换结果
        """
        pass
    
    def _validate_columns(self, data: pd.DataFrame):
        """
        验证列是否存在
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据
            
        Raises
        ------
        ValueError
            如果有列不存在
        """
        missing_cols = [col for col in self.columns if col not in data.columns]
        if missing_cols:
            raise ValueError(f"以下列在数据中不存在: {missing_cols}")


class Shift(Transform):
    """移位转换"""
    
    def __init__(self, columns: Union[str, List[str]], periods: int = 1, suffix: str = None):
        """
        初始化移位转换
        
        Parameters
        ----------
        columns : str或List[str]
            要移位的列
        periods : int, optional
            移位周期，正值为向后移位，负值为向前移位，默认为1
        suffix : str, optional
            结果列的后缀，默认为None，自动生成
        """
        suffix = suffix or f"_shift_{periods}"
        super().__init__("Shift", columns, periods=periods, suffix=suffix)
    
    def apply(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        应用移位转换
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据
            
        Returns
        -------
        pd.DataFrame
            转换结果
        """
        result = data.copy()
        self._validate_columns(result)
        
        periods = self.params['periods']
        suffix = self.params['suffix']
        
        for col in self.columns:
            result[f"{col}{suffix}"] = result[col].shift(periods)
        
        return result


class RollingTransform(Transform):
    """滚动计算转换"""
    
    def __init__(self, columns: Union[str, List[str]], window: int, function: str = 'mean',
                min_periods: Optional[int] = None, suffix: str = None, **kwargs):
        """
        初始化滚动计算转换
        
        Parameters
        ----------
        columns : str或List[str]
            要计算的列
        window : int
            窗口大小
        function : str, optional
            滚动计算函数，可选值：'mean', 'median', 'std', 'min', 'max', 'sum'等
            默认为'mean'
        min_periods : int, optional
            窗口内最小非NaN值的数量，默认为None(等于window)
        suffix : str, optional
            结果列的后缀，默认为None，自动生成
        **kwargs : dict
            传递给滚动计算函数的其他参数
        """
        suffix = suffix or f"_{function}_{window}"
        super().__init__("RollingTransform", columns, window=window, function=function,
                        min_periods=min_periods, suffix=suffix, **kwargs)
    
    def apply(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        应用滚动计算转换
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据
            
        Returns
        -------
        pd.DataFrame
            转换结果
        """
        result = data.copy()
        self._validate_columns(result)
        
        window = self.params['window']
        function = self.params['function']
        min_periods = self.params['min_periods'] or window
        suffix = self.params['suffix']
        
        # 获取其他参数
        extra_params = {k: v for k, v in self.params.items() 
                       if k not in ['window', 'function', 'min_periods', 'suffix']}
        
        for col in self.columns:
            # 获取滚动对象
            rolling_obj = result[col].rolling(window=window, min_periods=min_periods)
            
            # 应用指定的函数
            if function == 'mean':
                result[f"{col}{suffix}"] = rolling_obj.mean()
            elif function == 'median':
                result[f"{col}{suffix}"] = rolling_obj.median()
            elif function == 'std':
                result[f"{col}{suffix}"] = rolling_obj.std()
            elif function == 'min':
                result[f"{col}{suffix}"] = rolling_obj.min()
            elif function == 'max':
                result[f"{col}{suffix}"] = rolling_obj.max()
            elif function == 'sum':
                result[f"{col}{suffix}"] = rolling_obj.sum()
            elif hasattr(rolling_obj, function):
                # 尝试使用动态查找的方法
                result[f"{col}{suffix}"] = getattr(rolling_obj, function)(**extra_params)
            else:
                raise ValueError(f"未知的滚动函数: {function}")
        
        return result


class DiffTransform(Transform):
    """差分转换"""
    
    def __init__(self, columns: Union[str, List[str]], periods: int = 1, suffix: str = None):
        """
        初始化差分转换
        
        Parameters
        ----------
        columns : str或List[str]
            要计算差分的列
        periods : int, optional
            差分周期，默认为1
        suffix : str, optional
            结果列的后缀，默认为None，自动生成
        """
        suffix = suffix or f"_diff_{periods}"
        super().__init__("DiffTransform", columns, periods=periods, suffix=suffix)
    
    def apply(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        应用差分转换
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据
            
        Returns
        -------
        pd.DataFrame
            转换结果
        """
        result = data.copy()
        self._validate_columns(result)
        
        periods = self.params['periods']
        suffix = self.params['suffix']
        
        for col in self.columns:
            result[f"{col}{suffix}"] = result[col].diff(periods)
        
        return result


class PctChangeTransform(Transform):
    """百分比变化转换"""
    
    def __init__(self, columns: Union[str, List[str]], periods: int = 1, suffix: str = None,
                fill_method: str = None):
        """
        初始化百分比变化转换
        
        Parameters
        ----------
        columns : str或List[str]
            要计算百分比变化的列
        periods : int, optional
            变化周期，默认为1
        suffix : str, optional
            结果列的后缀，默认为None，自动生成
        fill_method : str, optional
            填充NaN值的方法，如'ffill', 'bfill'，默认为None
        """
        suffix = suffix or f"_pct_{periods}"
        super().__init__("PctChangeTransform", columns, periods=periods, suffix=suffix,
                         fill_method=fill_method)
    
    def apply(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        应用百分比变化转换
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据
            
        Returns
        -------
        pd.DataFrame
            转换结果
        """
        result = data.copy()
        self._validate_columns(result)
        
        periods = self.params['periods']
        suffix = self.params['suffix']
        fill_method = self.params['fill_method']
        
        for col in self.columns:
            pct_change = result[col].pct_change(periods)
            if fill_method:
                pct_change = pct_change.fillna(method=fill_method)
            result[f"{col}{suffix}"] = pct_change
        
        return result


def create_transform(transform_type: str, columns: Union[str, List[str]], **params) -> Transform:
    """
    创建数据转换
    
    Parameters
    ----------
    transform_type : str
        转换类型，可选值：'shift', 'rolling', 'diff', 'pct_change'
    columns : str或List[str]
        要转换的列
    **params : dict
        转换参数
        
    Returns
    -------
    Transform
        创建的转换对象
    """
    if transform_type == 'shift':
        return Shift(columns, periods=params.get('periods', 1), suffix=params.get('suffix'))
    elif transform_type == 'rolling':
        return RollingTransform(columns, window=params['window'],
                               function=params.get('function', 'mean'),
                               min_periods=params.get('min_periods'),
                               suffix=params.get('suffix'),
                               **{k: v for k, v in params.items() 
                                 if k not in ['window', 'function', 'min_periods', 'suffix']})
    elif transform_type == 'diff':
        return DiffTransform(columns, periods=params.get('periods', 1), suffix=params.get('suffix'))
    elif transform_type == 'pct_change':
        return PctChangeTransform(columns, periods=params.get('periods', 1),
                                 suffix=params.get('suffix'),
                                 fill_method=params.get('fill_method'))
    else:
        raise ValueError(f"未知的转换类型: {transform_type}") 