"""
数据分析示例

展示如何使用数据API进行市场数据分析。
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from data.api.market_data import MarketDataAPI
from data.api.operations import DataOperationsAPI
from data.storage.optimized_storage import OptimizedStorage
from data.sources.ccxt_source import CCXTDataSource, CCXTConfig


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def analyze_price_distribution(data: pd.DataFrame, symbol: str):
    """分析价格分布"""
    print("\n===== 价格分布分析 =====")
    
    # 计算价格统计信息
    price_stats = data['close'].describe()
    print("价格统计信息:")
    print(price_stats)
    
    # 绘制价格分布直方图
    plt.figure(figsize=(12, 6))
    
    plt.subplot(1, 2, 1)
    sns.histplot(data['close'], kde=True)
    plt.title(f"{symbol} 价格分布")
    plt.xlabel("价格")
    plt.ylabel("频率")
    
    plt.subplot(1, 2, 2)
    sns.boxplot(y=data['close'])
    plt.title(f"{symbol} 价格箱线图")
    plt.ylabel("价格")
    
    plt.tight_layout()
    output_path = os.path.join(os.path.dirname(__file__), 'price_distribution.png')
    plt.savefig(output_path)
    print(f"价格分布图已保存到: {output_path}")


def analyze_returns(data: pd.DataFrame, symbol: str):
    """分析收益率"""
    print("\n===== 收益率分析 =====")
    
    # 计算日收益率
    returns = data['close'].pct_change().dropna()
    
    # 计算收益率统计信息
    returns_stats = returns.describe()
    print("收益率统计信息:")
    print(returns_stats)
    
    # 计算年化收益率和波动率
    annual_return = returns.mean() * 252
    annual_volatility = returns.std() * np.sqrt(252)
    sharpe_ratio = annual_return / annual_volatility if annual_volatility != 0 else 0
    
    print(f"年化收益率: {annual_return:.4f}")
    print(f"年化波动率: {annual_volatility:.4f}")
    print(f"夏普比率: {sharpe_ratio:.4f}")
    
    # 绘制收益率分布图
    plt.figure(figsize=(12, 6))
    
    plt.subplot(1, 2, 1)
    sns.histplot(returns, kde=True)
    plt.title(f"{symbol} 日收益率分布")
    plt.xlabel("日收益率")
    plt.ylabel("频率")
    
    plt.subplot(1, 2, 2)
    returns.cumsum().plot()
    plt.title(f"{symbol} 累计收益率")
    plt.xlabel("日期")
    plt.ylabel("累计收益率")
    plt.grid(True)
    
    plt.tight_layout()
    output_path = os.path.join(os.path.dirname(__file__), 'returns_analysis.png')
    plt.savefig(output_path)
    print(f"收益率分析图已保存到: {output_path}")


def analyze_volatility(data: pd.DataFrame, symbol: str):
    """分析波动率"""
    print("\n===== 波动率分析 =====")
    
    # 计算日波动率 (高-低)/收盘价
    data['daily_volatility'] = (data['high'] - data['low']) / data['close']
    
    # 计算波动率的移动平均
    data['volatility_ma10'] = data['daily_volatility'].rolling(window=10).mean()
    
    # 计算波动率统计信息
    vol_stats = data['daily_volatility'].describe()
    print("日内波动率统计信息:")
    print(vol_stats)
    
    # 绘制波动率图
    plt.figure(figsize=(12, 6))
    
    plt.subplot(2, 1, 1)
    plt.plot(data.index, data['close'])
    plt.title(f"{symbol} 价格")
    plt.grid(True)
    
    plt.subplot(2, 1, 2)
    plt.plot(data.index, data['daily_volatility'], label='日波动率')
    plt.plot(data.index, data['volatility_ma10'], label='10日移动平均')
    plt.title(f"{symbol} 波动率")
    plt.xlabel("日期")
    plt.ylabel("波动率")
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    output_path = os.path.join(os.path.dirname(__file__), 'volatility_analysis.png')
    plt.savefig(output_path)
    print(f"波动率分析图已保存到: {output_path}")


def analyze_volume(data: pd.DataFrame, symbol: str):
    """分析成交量"""
    print("\n===== 成交量分析 =====")
    
    # 计算成交量统计信息
    volume_stats = data['volume'].describe()
    print("成交量统计信息:")
    print(volume_stats)
    
    # 计算成交量的移动平均
    data['volume_ma10'] = data['volume'].rolling(window=10).mean()
    
    # 绘制成交量图
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 1, 1)
    plt.plot(data.index, data['close'])
    plt.title(f"{symbol} 价格")
    plt.grid(True)
    
    plt.subplot(2, 1, 2)
    plt.bar(data.index, data['volume'], alpha=0.5, label='成交量')
    plt.plot(data.index, data['volume_ma10'], color='red', label='10日移动平均')
    plt.title(f"{symbol} 成交量")
    plt.xlabel("日期")
    plt.ylabel("成交量")
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    output_path = os.path.join(os.path.dirname(__file__), 'volume_analysis.png')
    plt.savefig(output_path)
    print(f"成交量分析图已保存到: {output_path}")


def analyze_correlation(market_api: MarketDataAPI, symbols: list, timeframe: str, 
                       start_time: datetime, end_time: datetime):
    """分析多个交易对之间的相关性"""
    print("\n===== 相关性分析 =====")
    
    # 获取多个交易对的收盘价数据
    price_data = {}
    for symbol in symbols:
        data = market_api.get_data(symbol, timeframe, start_time, end_time)
        if not data.empty:
            price_data[symbol] = data['close']
    
    if len(price_data) < 2:
        print("没有足够的数据进行相关性分析")
        return
    
    # 创建价格DataFrame
    prices_df = pd.DataFrame(price_data)
    print("价格数据预览:")
    print(prices_df.head())
    
    # 计算收益率
    returns_df = prices_df.pct_change().dropna()
    
    # 计算相关系数矩阵
    correlation = returns_df.corr()
    print("收益率相关系数矩阵:")
    print(correlation)
    
    # 绘制相关性热力图
    plt.figure(figsize=(10, 8))
    sns.heatmap(correlation, annot=True, cmap='coolwarm', vmin=-1, vmax=1)
    plt.title("交易对收益率相关性")
    plt.tight_layout()
    
    output_path = os.path.join(os.path.dirname(__file__), 'correlation_analysis.png')
    plt.savefig(output_path)
    print(f"相关性分析图已保存到: {output_path}")


def main():
    """主函数"""
    setup_logging()
    
    # 创建存储对象
    storage_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../data/storage/data'))
    os.makedirs(storage_path, exist_ok=True)
    storage = OptimizedStorage(storage_path)
    
    # 创建数据源
    exchange_id = 'binance'
    ccxt_config = CCXTConfig(exchange_id=exchange_id)
    ccxt_source = CCXTDataSource(ccxt_config)
    
    # 创建API对象
    market_api = MarketDataAPI(storage)
    operations_api = DataOperationsAPI()
    
    # 注册数据源
    market_api.register_data_source('binance', ccxt_source, is_default=True)
    
    # 设置分析参数
    symbol = 'BTC/USDT'
    timeframe = '1d'
    end_time = datetime.now()
    start_time = end_time - timedelta(days=180)  # 分析半年数据
    
    print(f"获取 {symbol} {timeframe} 数据，从 {start_time} 到 {end_time}")
    data = market_api.get_data(symbol, timeframe, start_time, end_time)
    
    if data.empty:
        print("未获取到数据，无法进行分析")
        return
    
    print(f"获取到 {len(data)} 条数据")
    
    # 数据清洗
    data = operations_api.clean_data(data, fill_method="ffill")
    
    # 进行各种分析
    analyze_price_distribution(data, symbol)
    analyze_returns(data, symbol)
    analyze_volatility(data, symbol)
    analyze_volume(data, symbol)
    
    # 相关性分析
    symbols = ['BTC/USDT', 'ETH/USDT', 'XRP/USDT', 'ADA/USDT']
    analyze_correlation(market_api, symbols, timeframe, start_time, end_time)


if __name__ == "__main__":
    main() 