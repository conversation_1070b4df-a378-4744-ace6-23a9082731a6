"""
数据预处理模块

提供数据清洗、质量检查、转换和特征工程功能，以提高数据质量，
为策略开发和回测提供准备。
"""

from data.processing.quality import (
    check_missing_values,
    check_outliers,
    check_data_integrity,
    generate_quality_report
)

from data.processing.cleaner import (
    fill_missing_values,
    handle_outliers,
    remove_duplicates,
    align_timestamps
)

from data.processing.transformer import (
    normalize_data,
    standardize_data,
    extract_time_features,
    calculate_returns
)

from data.processing.features import (
    calculate_volatility,
    calculate_moving_averages,
    calculate_rsi,
    calculate_macd
)

from data.processing.pipeline import (
    ProcessingPipeline,
    CleaningStep,
    TransformStep,
    FeatureEngineeringStep
)

__version__ = '0.1.0' 