#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
freqtrade保护系统演示示例

展示如何使用freqtrade专业保护功能替换现有风控系统，提供更准确和专业的风控保护。
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

# 确保可以导入模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from risk.freqtrade_protections import (
    CooldownPeriodProtection, MaxDrawdownProtection, 
    StoplossGuardProtection, LowProfitPairsProtection,
    FreqtradeProtectionManager
)


def generate_sample_trades(num_trades: int = 50) -> list:
    """
    生成示例交易数据
    
    Parameters
    ----------
    num_trades : int
        交易数量
        
    Returns
    -------
    list
        交易数据列表
    """
    np.random.seed(42)
    trades = []
    
    pairs = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'DOT/USDT', 'LINK/USDT']
    
    base_time = datetime.now() - timedelta(days=30)
    
    for i in range(num_trades):
        # 随机生成交易数据
        pair = np.random.choice(pairs)
        side = np.random.choice(['long', 'short'])
        
        # 60%盈利，40%亏损的交易分布
        is_profit = np.random.random() > 0.4
        if is_profit:
            profit_ratio = np.random.uniform(0.01, 0.08)  # 1%-8%盈利
            exit_reason = np.random.choice(['roi', 'exit_signal', 'trailing_stop'])
        else:
            profit_ratio = np.random.uniform(-0.15, -0.01)  # 1%-15%亏损
            exit_reason = np.random.choice(['stop_loss', 'emergency_exit'])
        
        trade_time = base_time + timedelta(hours=i*2)
        
        trades.append({
            'pair': pair,
            'side': side,
            'profit_ratio': profit_ratio,
            'exit_reason': exit_reason,
            'exit_time': trade_time,
            'close_time': trade_time
        })
    
    return trades


def generate_equity_curve(trades: list, initial_capital: float = 10000) -> pd.Series:
    """
    根据交易生成净值曲线
    
    Parameters
    ----------
    trades : list
        交易数据
    initial_capital : float
        初始资金
        
    Returns
    -------
    pd.Series
        净值曲线
    """
    # 按时间排序交易
    sorted_trades = sorted(trades, key=lambda x: x['exit_time'])
    
    equity_data = []
    current_capital = initial_capital
    
    for trade in sorted_trades:
        current_capital *= (1 + trade['profit_ratio'])
        equity_data.append({
            'timestamp': trade['exit_time'],
            'equity': current_capital
        })
    
    df = pd.DataFrame(equity_data)
    df.set_index('timestamp', inplace=True)
    
    return df['equity']


def demo_cooldown_protection():
    """演示冷却期保护"""
    print("\n=== 冷却期保护演示 ===")
    
    # 创建冷却期保护，退出后2个蜡烛图不能重新进入
    protection = CooldownPeriodProtection(stop_duration_candles=2)
    
    # 模拟交易退出
    exit_time = datetime.now() - timedelta(minutes=30)
    protection.register_exit('BTC/USDT', exit_time)
    
    # 检查是否被保护
    trade_context = {
        'pair': 'BTC/USDT',
        'current_time': datetime.now() - timedelta(minutes=15),  # 15分钟后
        'candle_duration': timedelta(minutes=15)
    }
    
    should_stop, reason = protection.should_stop(trade_context)
    print(f"冷却期保护检查: {should_stop}")
    if should_stop:
        print(f"保护原因: {reason}")
    
    # 检查冷却期结束后
    trade_context['current_time'] = datetime.now()  # 现在
    should_stop, reason = protection.should_stop(trade_context)
    print(f"冷却期结束后: {should_stop}")


def demo_max_drawdown_protection():
    """演示最大回撤保护"""
    print("\n=== 最大回撤保护演示 ===")
    
    # 创建最大回撤保护，回撤超过15%则停止12个蜡烛图
    protection = MaxDrawdownProtection(
        lookback_period_candles=48,
        trade_limit=10,
        stop_duration_candles=12,
        max_allowed_drawdown=0.15
    )
    
    # 生成示例交易和净值曲线
    trades = generate_sample_trades(30)
    equity_curve = generate_equity_curve(trades, 10000)
    
    # 人为制造大回撤
    max_equity = equity_curve.max()
    current_equity = max_equity * 0.80  # 20%回撤
    
    # 模拟回撤场景
    equity_curve.iloc[-1] = current_equity
    
    trade_context = {
        'current_time': datetime.now(),
        'candle_duration': timedelta(minutes=15),
        'trades_history': trades,
        'equity_curve': equity_curve
    }
    
    should_stop, reason = protection.should_stop(trade_context)
    print(f"最大回撤保护检查: {should_stop}")
    if should_stop:
        print(f"保护原因: {reason}")
        print(f"当前回撤: {(max_equity - current_equity) / max_equity:.2%}")


def demo_stoploss_guard_protection():
    """演示止损保护"""
    print("\n=== 止损保护演示 ===")
    
    # 创建止损保护，24个蜡烛图内4次止损就停止4个蜡烛图
    protection = StoplossGuardProtection(
        lookback_period_candles=24,
        trade_limit=4,
        stop_duration_candles=4,
        required_profit=0.0,
        only_per_pair=False
    )
    
    # 生成包含多个止损的交易
    trades = []
    base_time = datetime.now() - timedelta(hours=5)
    
    # 添加5个止损交易
    for i in range(5):
        trades.append({
            'pair': 'BTC/USDT',
            'side': 'long',
            'profit_ratio': -0.05,  # 5%亏损
            'exit_reason': 'stop_loss',
            'exit_time': base_time + timedelta(hours=i),
            'close_time': base_time + timedelta(hours=i)
        })
    
    trade_context = {
        'current_time': datetime.now(),
        'candle_duration': timedelta(minutes=15),
        'pair': 'ETH/USDT',
        'side': 'long',
        'trades_history': trades
    }
    
    should_stop, reason = protection.should_stop(trade_context)
    print(f"止损保护检查: {should_stop}")
    if should_stop:
        print(f"保护原因: {reason}")


def demo_low_profit_pairs_protection():
    """演示低利润交易对保护"""
    print("\n=== 低利润交易对保护演示 ===")
    
    # 创建低利润保护，6个蜡烛图内平均利润<2%就停止60个蜡烛图
    protection = LowProfitPairsProtection(
        lookback_period_candles=6,
        trade_limit=3,
        stop_duration_candles=60,
        required_profit=0.02,  # 要求2%利润
        only_per_pair=True
    )
    
    # 生成低利润交易
    trades = []
    base_time = datetime.now() - timedelta(hours=2)
    
    # 添加低利润交易
    for i in range(4):
        trades.append({
            'pair': 'ADA/USDT',
            'side': 'long',
            'profit_ratio': 0.005,  # 0.5%利润（低于要求的2%）
            'exit_reason': 'roi',
            'exit_time': base_time + timedelta(minutes=i*30),
            'close_time': base_time + timedelta(minutes=i*30)
        })
    
    trade_context = {
        'current_time': datetime.now(),
        'candle_duration': timedelta(minutes=15),
        'pair': 'ADA/USDT',
        'trades_history': trades
    }
    
    should_stop, reason = protection.should_stop(trade_context)
    print(f"低利润保护检查: {should_stop}")
    if should_stop:
        print(f"保护原因: {reason}")


def demo_protection_manager():
    """演示保护管理器"""
    print("\n=== 保护管理器演示 ===")
    
    # 创建freqtrade标准保护配置
    freqtrade_config = [
        {
            "method": "CooldownPeriod",
            "stop_duration_candles": 5
        },
        {
            "method": "MaxDrawdown",
            "lookback_period_candles": 48,
            "trade_limit": 20,
            "stop_duration_candles": 4,
            "max_allowed_drawdown": 0.2
        },
        {
            "method": "StoplossGuard",
            "lookback_period_candles": 24,
            "trade_limit": 4,
            "stop_duration_candles": 2,
            "only_per_pair": False
        },
        {
            "method": "LowProfitPairs",
            "lookback_period_candles": 6,
            "trade_limit": 2,
            "stop_duration_candles": 60,
            "required_profit": 0.02
        }
    ]
    
    # 从配置创建保护管理器
    manager = FreqtradeProtectionManager.from_freqtrade_config(freqtrade_config)
    
    print(f"已加载 {len(manager.protections)} 个保护方法:")
    for protection in manager.protections:
        print(f"  - {protection.method_name}")
    
    # 测试保护检查
    trades = generate_sample_trades(25)
    equity_curve = generate_equity_curve(trades, 10000)
    
    trade_context = {
        'current_time': datetime.now(),
        'candle_duration': timedelta(minutes=15),
        'pair': 'BTC/USDT',
        'side': 'long',
        'trades_history': trades,
        'equity_curve': equity_curve
    }
    
    is_protected, reasons = manager.check_protections(trade_context)
    print(f"\n保护状态检查: {'被保护' if is_protected else '允许交易'}")
    if is_protected:
        print("保护原因:")
        for reason in reasons:
            print(f"  - {reason}")


def demo_migration_from_existing_system():
    """演示从现有风控系统迁移"""
    print("\n=== 系统迁移演示 ===")
    
    print("原有系统风控规则:")
    print("  - 最大回撤监控")
    print("  - 交易频率限制")
    print("  - 资金管理规则")
    print("  - 自定义止损逻辑")
    
    print("\nfreqtrade保护系统优势:")
    print("  ✓ 经过市场验证的专业保护方法")
    print("  ✓ 统一的配置格式和管理接口")  
    print("  ✓ 可与现有系统无缝集成")
    print("  ✓ 支持多种保护策略组合")
    print("  ✓ 自动化的风控决策机制")
    
    print("\n迁移步骤:")
    print("  1. 保留核心业务逻辑不变")
    print("  2. 用freqtrade保护替换现有风控规则")
    print("  3. 配置专业的保护参数")
    print("  4. 集成到交易决策流程")
    print("  5. 监控和调优保护效果")


def main():
    """主演示函数"""
    print("freqtrade专业风控保护系统演示")
    print("=" * 50)
    
    # 演示各个保护方法
    demo_cooldown_protection()
    demo_max_drawdown_protection()
    demo_stoploss_guard_protection()
    demo_low_profit_pairs_protection()
    
    # 演示保护管理器
    demo_protection_manager()
    
    # 演示系统迁移
    demo_migration_from_existing_system()
    
    print("\n" + "=" * 50)
    print("演示完成！")
    print("\n总结:")
    print("freqtrade保护系统提供了经过市场验证的专业风控功能，")
    print("可以有效替换现有的风控实现，提供更准确和专业的保护。")


if __name__ == "__main__":
    main() 