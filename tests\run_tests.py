#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试运行脚本

用于运行项目中的测试用例，并确保Python路径设置正确。
"""

import sys
import os
import pytest


def setup_python_path():
    """设置Python路径，确保能正确导入项目模块"""
    # 获取当前文件所在的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 获取项目的根目录(向上一级)
    project_root = os.path.abspath(os.path.join(current_dir, ".."))
    
    # 将项目根目录添加到Python路径
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
        print(f"已将项目根目录添加到Python路径: {project_root}")


def run_tests():
    """运行项目中的所有测试"""
    # 设置Python路径
    setup_python_path()
    
    # 获取测试目录
    test_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 运行测试
    print("正在运行所有测试...")
    pytest.main([test_dir, "-v"])


def run_specific_tests(pattern):
    """
    运行特定的测试用例
    
    Parameters
    ----------
    pattern : str
        测试文件或函数的模式
    """
    # 设置Python路径
    setup_python_path()
    
    # 运行特定测试
    print(f"正在运行匹配 '{pattern}' 的测试...")
    pytest.main(["-v", "-k", pattern])


if __name__ == "__main__":
    # 如果提供了命令行参数，则运行特定测试
    if len(sys.argv) > 1:
        run_specific_tests(sys.argv[1])
    else:
        # 否则运行所有测试
        run_tests() 