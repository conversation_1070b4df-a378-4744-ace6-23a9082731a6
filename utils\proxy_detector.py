#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
代理检测模块

提供统一的代理检测、测试和管理功能。
支持动态检测多种代理配置，自动验证代理可用性。
"""

import os
import logging
from typing import Optional, List, Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)


class ProxyDetector:
    """
    代理检测器
    
    提供统一的代理检测和验证服务，支持：
    - 环境变量代理检测
    - 常见代理端口扫描
    - 系统代理设置检测
    - 代理连接测试和验证
    """
    
    def __init__(self, test_timeout: int = 8, max_retries: int = 2):
        """
        初始化代理检测器
        
        Parameters
        ----------
        test_timeout : int, default=8
            代理测试超时时间（秒）
        max_retries : int, default=2
            代理测试重试次数
        """
        self.test_timeout = test_timeout
        self.max_retries = max_retries
        self._proxy_cache = None
        self._cache_valid = False
        
        logger.debug(f"代理检测器初始化完成，超时: {test_timeout}s, 重试: {max_retries}次")
    
    def detect_proxy(self, use_cache: bool = True) -> Optional[str]:
        """
        检测可用代理
        
        Parameters
        ----------
        use_cache : bool, default=True
            是否使用缓存结果
            
        Returns
        -------
        Optional[str]
            检测到的代理URL，如果未找到则返回None
        """
        if use_cache and self._cache_valid and self._proxy_cache:
            logger.debug(f"使用缓存的代理: {self._proxy_cache}")
            return self._proxy_cache
        
        logger.info("🔍 开始检测代理配置...")
        
        # 🔥 动态检测代理候选列表
        proxy_candidates = self._get_proxy_candidates()
        
        for proxy_url in proxy_candidates:
            if proxy_url:
                # 🔥 优先测试SOCKS5代理，其次测试HTTP代理
                is_available = False
                if proxy_url.startswith('socks5://'):
                    is_available = self._test_socks5_connection(proxy_url)
                else:
                    is_available = self._test_proxy_connection(proxy_url)
                
                if is_available:
                    logger.info(f"✅ 发现可用代理: {proxy_url}")
                    self._proxy_cache = proxy_url
                    self._cache_valid = True
                    return proxy_url
                else:
                    logger.debug(f"❌ 代理测试失败: {proxy_url}")
        
        # 🔥 尝试检测系统代理设置
        system_proxy = self._detect_system_proxy()
        if system_proxy and self._test_proxy_connection(system_proxy):
            logger.info(f"✅ 发现系统代理: {system_proxy}")
            self._proxy_cache = system_proxy
            self._cache_valid = True
            return system_proxy
        
        logger.warning("🚫 未发现可用代理，将使用直连")
        self._cache_valid = True
        return None
    
    def _get_proxy_candidates(self) -> List[Optional[str]]:
        """获取代理候选列表 - 优先V2Ray转换器"""
        return [
            # 环境变量配置
            os.getenv('HTTPS_PROXY'),
            os.getenv('HTTP_PROXY'),
            os.getenv('http_proxy'),
            os.getenv('https_proxy'),
            
            # V2Ray转换器端口 - 最高优先级
            'http://127.0.0.1:10809',   # V2Ray HTTP端口
            'socks5://127.0.0.1:10808', # V2Ray SOCKS5端口
            
            # 常见代理配置
            'http://127.0.0.1:7890',    # Clash默认
            'socks5://127.0.0.1:7891',
            'http://127.0.0.1:1080',    # 通用代理
            'socks5://127.0.0.1:1080',
            
            # 其他端口
            'http://127.0.0.1:7897',
            'http://127.0.0.1:8080',
            'http://127.0.0.1:10810',
            'http://127.0.0.1:8888',
            'http://127.0.0.1:8889'
        ]
    
    def _detect_system_proxy(self) -> Optional[str]:
        """检测系统代理设置"""
        try:
            import winreg
            import urllib.request
            
            # 尝试从Windows注册表获取代理设置
            try:
                with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                                  r"Software\Microsoft\Windows\CurrentVersion\Internet Settings") as key:
                    proxy_enable = winreg.QueryValueEx(key, "ProxyEnable")[0]
                    if proxy_enable:
                        proxy_server = winreg.QueryValueEx(key, "ProxyServer")[0]
                        if proxy_server:
                            return f"http://{proxy_server}"
            except:
                pass
            
            # 尝试通过urllib获取系统代理
            try:
                proxy_handler = urllib.request.ProxyHandler()
                if proxy_handler.proxies:
                    for scheme, proxy in proxy_handler.proxies.items():
                        if scheme in ['http', 'https'] and proxy:
                            return proxy
            except:
                pass
                
        except ImportError:
            # 非Windows系统
            pass
        
        return None
    
    def _test_proxy_connection(self, proxy_url: str) -> bool:
        """
        测试代理连接 - 增强版检测
        
        Parameters
        ----------
        proxy_url : str
            要测试的代理URL
            
        Returns
        -------
        bool
            代理是否可用
        """
        try:
            import requests
            
            # 🔥 测试多个API端点以确保代理有效性
            test_urls = [
                'https://api.binance.com/api/v3/ping',  # Binance API
                'https://httpbin.org/ip',               # IP检测
                'https://www.google.com',               # 通用连接测试
            ]
            
            proxy_dict = {'http': proxy_url, 'https': proxy_url}
            
            for attempt in range(self.max_retries):
                for test_url in test_urls:
                    try:
                        response = requests.get(
                            test_url,
                            proxies=proxy_dict,
                            timeout=self.test_timeout,
                            headers={
                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                            }
                        )
                        
                        if response.status_code == 200:
                            logger.debug(f"✅ 代理测试成功: {proxy_url} -> {test_url}")
                            return True
                            
                    except requests.exceptions.RequestException as e:
                        logger.debug(f"❌ 代理测试失败: {proxy_url} -> {test_url}: {e}")
                        continue
                
                if attempt < self.max_retries - 1:
                    logger.debug(f"代理测试重试 {attempt + 1}/{self.max_retries}: {proxy_url}")
            
            return False
            
        except ImportError:
            logger.error("❌ requests库未安装，无法测试代理")
            return False
        except Exception as e:
            logger.debug(f"❌ 代理测试异常: {proxy_url}: {e}")
            return False
    
    def _test_socks5_connection(self, proxy_url: str) -> bool:
        """
        专门测试SOCKS5代理连接
        
        Parameters
        ----------
        proxy_url : str
            SOCKS5代理URL（格式：socks5://host:port）
            
        Returns
        -------
        bool
            SOCKS5代理是否可用
        """
        try:
            import aiohttp
            import aiohttp_socks
            import asyncio
            
            # 解析SOCKS5代理URL
            if not proxy_url.startswith('socks5://'):
                return False
            
            proxy_parts = proxy_url.replace('socks5://', '').split(':')
            if len(proxy_parts) != 2:
                return False
            
            host, port = proxy_parts[0], int(proxy_parts[1])
            
            async def test_socks5():
                """异步测试SOCKS5连接"""
                connector = aiohttp_socks.ProxyConnector.from_url(proxy_url)
                
                try:
                    async with aiohttp.ClientSession(connector=connector) as session:
                        # 测试Binance API连接
                        async with session.get(
                            'https://api.binance.com/api/v3/ping',
                            timeout=aiohttp.ClientTimeout(total=self.test_timeout)
                        ) as response:
                            return response.status == 200
                except Exception:
                    return False
                finally:
                    await connector.close()
            
            # 运行异步测试
            try:
                # 检查是否已有事件循环运行
                try:
                    loop = asyncio.get_running_loop()
                    # 如果已有循环，使用线程池运行
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(self._run_async_test, test_socks5)
                        result = future.result(timeout=self.test_timeout + 2)
                except RuntimeError:
                    # 没有运行的循环，可以直接运行
                    result = asyncio.run(test_socks5())
                
                if result:
                    logger.debug(f"✅ SOCKS5代理测试成功: {proxy_url}")
                else:
                    logger.debug(f"❌ SOCKS5代理测试失败: {proxy_url}")
                
                return result
                
            except Exception as e:
                logger.debug(f"SOCKS5异步测试失败: {proxy_url}: {e}")
                return False
                
        except ImportError:
            logger.debug("aiohttp-socks库未安装，跳过SOCKS5测试")
            return False
        except Exception as e:
            logger.debug(f"SOCKS5代理测试异常: {proxy_url}: {e}")
            return False
    
    def _run_async_test(self, async_func):
        """在新的事件循环中运行异步函数"""
        return asyncio.run(async_func())
    
    def test_proxy(self, proxy_url: str) -> Dict[str, Any]:
        """
        测试指定代理的详细信息
        
        Parameters
        ----------
        proxy_url : str
            要测试的代理URL
            
        Returns
        -------
        Dict[str, Any]
            包含测试结果的详细信息
        """
        result = {
            'proxy_url': proxy_url,
            'is_available': False,
            'response_times': [],
            'successful_endpoints': [],
            'failed_endpoints': [],
            'error_messages': []
        }
        
        try:
            import requests
            import time
            
            test_urls = [
                'https://api.binance.com/api/v3/ping',
                'https://httpbin.org/ip',
                'https://www.google.com'
            ]
            
            proxy_dict = {'http': proxy_url, 'https': proxy_url}
            
            for test_url in test_urls:
                try:
                    start_time = time.time()
                    response = requests.get(
                        test_url,
                        proxies=proxy_dict,
                        timeout=self.test_timeout,
                        headers={
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                        }
                    )
                    end_time = time.time()
                    
                    if response.status_code == 200:
                        response_time = round((end_time - start_time) * 1000, 2)
                        result['response_times'].append(response_time)
                        result['successful_endpoints'].append(test_url)
                        result['is_available'] = True
                    else:
                        result['failed_endpoints'].append(test_url)
                        result['error_messages'].append(f"{test_url}: HTTP {response.status_code}")
                        
                except requests.exceptions.RequestException as e:
                    result['failed_endpoints'].append(test_url)
                    result['error_messages'].append(f"{test_url}: {str(e)}")
            
            if result['response_times']:
                result['avg_response_time'] = round(sum(result['response_times']) / len(result['response_times']), 2)
            
        except Exception as e:
            result['error_messages'].append(f"测试异常: {str(e)}")
        
        return result
    
    def invalidate_cache(self) -> None:
        """清除代理缓存，强制重新检测"""
        self._cache_valid = False
        self._proxy_cache = None
        logger.debug("代理缓存已清除")
    
    def get_proxy_info(self) -> Dict[str, Any]:
        """
        获取当前代理信息
        
        Returns
        -------
        Dict[str, Any]
            代理信息字典
        """
        proxy = self.detect_proxy()
        if proxy:
            test_result = self.test_proxy(proxy)
            return {
                'detected_proxy': proxy,
                'is_cached': self._cache_valid,
                'test_result': test_result
            }
        else:
            return {
                'detected_proxy': None,
                'is_cached': self._cache_valid,
                'message': '未检测到可用代理'
            }


# 全局代理检测器实例
_global_detector = None


def get_proxy_detector() -> ProxyDetector:
    """
    获取全局代理检测器实例
    
    Returns
    -------
    ProxyDetector
        代理检测器实例
    """
    global _global_detector
    if _global_detector is None:
        _global_detector = ProxyDetector()
    return _global_detector


def detect_proxy(use_cache: bool = True) -> Optional[str]:
    """
    便捷函数：检测可用代理
    
    Parameters
    ----------
    use_cache : bool, default=True
        是否使用缓存结果
        
    Returns
    -------
    Optional[str]
        检测到的代理URL
    """
    detector = get_proxy_detector()
    return detector.detect_proxy(use_cache=use_cache)


def test_proxy(proxy_url: str) -> Dict[str, Any]:
    """
    便捷函数：测试指定代理
    
    Parameters
    ----------
    proxy_url : str
        要测试的代理URL
        
    Returns
    -------
    Dict[str, Any]
        测试结果
    """
    detector = get_proxy_detector()
    return detector.test_proxy(proxy_url) 