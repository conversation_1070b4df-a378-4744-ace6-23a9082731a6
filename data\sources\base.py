"""
数据源基类

定义了所有数据源的通用功能和接口。
"""

import abc
import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Tuple

import pandas as pd

from data.base import DataSource
from data.structures import OHLCVColumns, TimeFrame, clean_ohlcv_dataframe
from data.utils import timeframe_to_timedelta


class BaseDataSource(DataSource):
    """
    数据源基类
    
    根本性修复：完全移除重试机制，严格遵循系统完整性规则。
    """
    
    def __init__(self, name: str, description: str = "", timeout: int = 30):
        """
        初始化数据源
        
        Args:
            name: 数据源名称
            description: 数据源描述
            timeout: 请求超时时间（秒）- 根本修复：移除重试相关配置
        """
        self.name = name
        self.description = description
        self.timeout = timeout
        
        # 🚀 根本性修复：完全移除重试相关配置
        # 根据MyGameNotes.md要求，不使用任何fallback机制
        
        # 设置日志
        self.logger = logging.getLogger(f"DataSource.{name}")
        
        # 初始化缓存
        self._cache = {}
        self._cache_ttl = 60  # 缓存60秒
        
    def get_data(self, symbol: str, timeframe: str, 
                start_time: Optional[datetime] = None,
                end_time: Optional[datetime] = None,
                limit: Optional[int] = None) -> pd.DataFrame:
        """
        获取数据 - 根本性修复：单次获取，无重试机制
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期  
            start_time: 开始时间
            end_time: 结束时间
            limit: 数据点数量限制
            
        Returns:
            包含市场数据的DataFrame
            
        Raises:
            RuntimeError: 数据获取失败时直接抛出异常
        """
        
        # 🚀 根本性修复：直接调用fetch_data，不使用重试机制
        try:
            data = self.fetch_data(symbol, timeframe, start_time, end_time)
            
            if data.empty:
                raise RuntimeError(f"未获取到数据: {symbol}")
            
            # 应用数量限制
            if limit and len(data) > limit:
                data = data.tail(limit)
            
            return data
            
        except Exception as e:
            # 🚀 根本性修复：直接抛出异常，不进行重试
            self.logger.error(f"数据获取失败 {symbol}: {e}")
            raise RuntimeError(f"数据源{self.name}获取数据失败: {e}") from e
    
    def fetch_data(self, symbol: str, timeframe: str, 
                   start_time: datetime, end_time: datetime) -> pd.DataFrame:
        """
        抽象方法：获取指定时间范围的市场数据
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期，如"1m", "1h", "1d"等
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        raise NotImplementedError("子类必须实现fetch_data方法")
    
    def get_symbols(self) -> List[str]:
        """
        获取支持的交易对列表
        
        Returns:
            交易对代码列表
        """
        raise NotImplementedError("子类必须实现get_symbols方法")
    
    def get_timeframes(self) -> List[str]:
        """
        获取支持的时间周期列表
        
        Returns:
            时间周期列表
        """
        raise NotImplementedError("子类必须实现get_timeframes方法")
    
    def _validate_params(self, symbol: str, timeframe: str, 
                       start_time: datetime, end_time: datetime) -> None:
        """
        验证请求参数
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            start_time: 开始时间
            end_time: 结束时间
            
        Raises:
            ValueError: 如果参数无效
        """
        # 验证交易对
        if not self.validate_symbol(symbol):
            raise ValueError(f"不支持的交易对: {symbol}")
        
        # 验证时间周期
        if not self.validate_timeframe(timeframe):
            raise ValueError(f"不支持的时间周期: {timeframe}")
        
        # 验证时间范围
        if start_time >= end_time:
            raise ValueError(f"开始时间必须早于结束时间: {start_time} >= {end_time}")
        
        # 验证时间范围不超过合理限制
        time_delta = end_time - start_time
        if time_delta > timedelta(days=365):
            self.logger.warning(f"请求的时间范围超过一年: {time_delta.days}天，可能导致性能问题")
    
    def split_time_range(self, start_time: datetime, end_time: datetime, 
                       max_days: int = 30) -> List[Tuple[datetime, datetime]]:
        """
        将长时间范围分割为多个较短的时间段
        
        用于处理一些API对单次请求的时间范围有限制的情况
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            max_days: 每个时间段的最大天数
            
        Returns:
            时间范围元组的列表
        """
        ranges = []
        current_start = start_time
        
        while current_start < end_time:
            # 使用整数类型的天数
            max_days_int = int(max_days)
            current_end = min(current_start + timedelta(days=max_days_int), end_time)
            ranges.append((current_start, current_end))
            current_start = current_end
        
        return ranges 