#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
批量分析和报告生成示例

展示如何使用批量分析模块处理多个回测结果，并生成汇总报告。
"""

import pandas as pd
import numpy as np
import os
import sys
import pickle
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

# 添加项目根目录到路径
import os
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from backtest.base import Strategy
from backtest.vectorbt.engine import VectorBTEngine
from backtest.analysis import (
    BatchProcessor, 
    batch_analyze_results,
    OptimizationAnalyzer,
    run_optimization_and_analyze
)
from data.sources.utils import download_crypto_data  # 导入加密货币数据下载函数


class SimpleMovingAverageCrossover(Strategy):
    """
    简单移动平均线交叉策略
    
    当短期移动平均线上穿长期移动平均线时做多，
    当短期移动平均线下穿长期移动平均线时平仓。
    """
    
    def __init__(self, short_window=20, long_window=50):
        """
        初始化策略
        
        Parameters
        ----------
        short_window : int, optional
            短期窗口，默认为20
        long_window : int, optional
            长期窗口，默认为50
        """
        self.short_window = short_window
        self.long_window = long_window
        # 以属性方式存储参数，便于在策略中使用和显示
        self.params = {
            'short_window': short_window,
            'long_window': long_window
        }
        
    def __str__(self):
        """返回策略的字符串表示"""
        return f"SMA({self.short_window},{self.long_window})"
    
    def generate_signals(self, data):
        """
        生成交易信号
        
        Parameters
        ----------
        data : pd.DataFrame
            市场数据，包含OHLCV列
            
        Returns
        -------
        pd.DataFrame
            包含交易信号的DataFrame
        """
        # 计算短期和长期移动平均线
        data['short_ma'] = data['close'].rolling(window=self.short_window).mean()
        data['long_ma'] = data['close'].rolling(window=self.long_window).mean()
        
        # 初始化信号列
        data['signal'] = 0.0
        
        # 生成信号：短期上穿长期做多，下穿平仓
        data['signal'] = np.where(data['short_ma'] > data['long_ma'], 1.0, 0.0)
        
        # 仅在穿越点产生交易信号
        data['position'] = data['signal'].diff()
        
        # 添加VectorBT引擎需要的entries列
        data['entries'] = data['position'].copy()
        data.loc[data['entries'] <= 0, 'entries'] = 0  # 只保留做多信号
        
        # 添加exits列(可选，用于平仓信号)
        data['exits'] = data['position'].copy()
        data.loc[data['exits'] >= 0, 'exits'] = 0  # 只保留平仓信号
        data['exits'] = abs(data['exits'])  # 转换为正值
        
        return data


def download_sample_data(symbol='BTC/USDT', start_date='2018-01-01', end_date='2023-01-01'):
    """
    下载加密货币样本数据，如果下载失败则创建模拟数据
    
    Parameters
    ----------
    symbol : str
        交易对符号，例如'BTC/USDT'，默认为'BTC/USDT'
    start_date : str
        起始日期，默认为'2018-01-01'
    end_date : str
        结束日期，默认为'2023-01-01'
        
    Returns
    -------
    pd.DataFrame
        包含OHLCV数据的DataFrame
    """
    # 将日期字符串转换为datetime对象
    start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
    end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
    
    # 尝试从Binance获取加密货币数据
    print(f"尝试从Binance下载{symbol}数据...")
    try:
        # 使用download_crypto_data函数下载数据
        data = download_crypto_data(
            symbol=symbol, 
            timeframe='1d',  # 使用日线数据
            start_time=start_datetime,
            end_time=end_datetime
        )
        
        # 检查数据是否成功获取
        if data is not None and len(data) > 0:
            print(f"成功从Binance获取{symbol}数据，共{len(data)}条记录")
            return data
        else:
            print("未能从Binance获取数据，将创建模拟数据")
    except Exception as e:
        print(f"从Binance获取数据失败: {e}")
    
    # 如果下载失败或数据有问题，创建模拟数据
    print("创建模拟数据...")
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')  # 使用日频率，对齐加密货币数据
    
    np.random.seed(42)
    price = 10000  # 比特币起始价格
    prices = [price]
    
    for _ in range(1, len(date_range)):
        change_percent = np.random.normal(0.0005, 0.02)  # 加密货币波动更大
        price *= (1 + change_percent)
        prices.append(price)
    
    data = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': [np.random.randint(1000000, 10000000) for _ in prices],
    }, index=date_range)
    
    # 确保频率正确设置
    data.index.freq = 'D'
    
    print(f"已创建模拟数据，形状: {data.shape}")
    return data


def run_batch_analysis_example():
    """
    运行批量分析示例
    """
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), 'output', 'strategies')
    os.makedirs(output_dir, exist_ok=True)
    
    # 下载或加载示例数据
    data_path = os.path.join(output_dir, 'sample_crypto_data.pkl')
    if os.path.exists(data_path):
        print(f"正在加载缓存数据: {data_path}")
        try:
            with open(data_path, 'rb') as f:
                data = pickle.load(f)
            print(f"成功加载缓存数据，形状: {data.shape}")
        except Exception as e:
            print(f"加载缓存数据失败: {e}，将重新下载")
            data = download_sample_data()
            # 保存数据以便将来使用
            with open(data_path, 'wb') as f:
                pickle.dump(data, f)
    else:
        data = download_sample_data()
        # 保存数据以便将来使用
        with open(data_path, 'wb') as f:
            pickle.dump(data, f)
    
    # 创建不同参数的策略实例
    strategies = {
        'SMA_10_30': SimpleMovingAverageCrossover(10, 30),
        'SMA_20_50': SimpleMovingAverageCrossover(20, 50),
        'SMA_50_200': SimpleMovingAverageCrossover(50, 200),
        'SMA_15_45': SimpleMovingAverageCrossover(15, 45),
        'SMA_5_20': SimpleMovingAverageCrossover(5, 20)
    }
    
    # 初始化引擎并运行回测
    engine = VectorBTEngine(data)
    results = {}
    
    for name, strategy in strategies.items():
        print(f"正在运行策略: {name}")
        result = engine.run(strategy)
        results[name] = result
    
    # 示例1：使用BatchProcessor进行批量分析
    print("\n示例1：使用BatchProcessor进行批量分析")
    processor = BatchProcessor(parallel=True)
    processor.add_results(results)
    
    # 计算汇总指标
    summary_metrics = processor.generate_summary_metrics()
    print("汇总指标:")
    print(summary_metrics)
    
    # 计算相关性矩阵
    correlation_matrix = processor.generate_correlation_matrix()
    print("\n策略相关性矩阵:")
    print(correlation_matrix)
    
    # 生成批量报告
    batch_reports_dir = os.path.join(output_dir, 'batch_reports')
    processor.generate_batch_reports(
        output_dir=batch_reports_dir,
        output_format='html'
    )
    print(f"已生成批量报告到: {batch_reports_dir}")
    
    # 生成汇总报告
    summary_path = os.path.join(output_dir, 'summary_report.html')
    processor.generate_summary_report(
        output_path=summary_path,
        include_correlation=True,
        include_returns_chart=True,
        include_drawdowns_chart=True
    )
    print(f"已生成汇总报告到: {summary_path}")
    
    # 导出汇总指标到CSV
    csv_path = os.path.join(output_dir, 'summary_metrics.csv')
    processor.export_summary_to_csv(csv_path)
    print(f"已导出汇总指标到: {csv_path}")
    
    # 示例2：使用batch_analyze_results函数进行批量分析
    print("\n示例2：使用batch_analyze_results函数进行批量分析")
    batch_output_dir = os.path.join(output_dir, 'batch_analysis')
    batch_analyze_results(
        results=results,
        output_dir=batch_output_dir,
        generate_individual_reports=True,
        generate_summary=True,
        parallel=True
    )
    print(f"已生成批量分析报告到: {batch_output_dir}")
    
    # 示例3：参数优化与报告生成集成
    print("\n示例3：参数优化与报告生成集成")
    param_grid = {
        'short_window': [5, 10, 15],  # 减少参数数量
        'long_window': [30, 50, 100]  # 减少参数数量
    }
    
    # 运行优化并分析
    optimization_output_dir = os.path.join(output_dir, 'optimization')
    optimization_results = run_optimization_and_analyze(
        strategy_class=SimpleMovingAverageCrossover,
        data=data,
        param_grid=param_grid,
        engine_type='vectorbt',
        metric='sharpe_ratio',
        maximize=True,
        n_jobs=2,  # 并行进程数
        generate_reports=True,
        output_dir=optimization_output_dir
    )
    
    print(f"优化完成，找到 {len(optimization_results['results'])} 个结果")
    print(f"最佳参数: {optimization_results['best_params']}")
    print(f"最佳指标: {optimization_results['best_metrics']}")
    print(f"已生成优化分析报告到: {optimization_output_dir}")
    
    print("\n批量分析示例完成!")


if __name__ == "__main__":
    run_batch_analysis_example() 