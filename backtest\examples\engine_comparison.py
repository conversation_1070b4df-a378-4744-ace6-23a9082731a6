#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
回测引擎对比示例

比较VectorBT和Backtrader回测引擎的性能和结果差异。
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import matplotlib.pyplot as plt
import time
import logging
import sys
from typing import Dict, Any, Tuple

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

# 导入相关模块
from backtest.vectorbt.engine import VectorBTEngine
from backtest.backtrader.core import BacktraderEngine
from backtest.base import Strategy, BacktestResults
from indicators.trend import moving_averages
from indicators.oscillators import rsi
from data.sources.utils import download_crypto_data

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleMACrossoverStrategy(Strategy):
    """
    简单的移动平均线交叉策略
    """
    
    def __init__(self, short_window=20, long_window=50, position_size=1.0):
        """
        初始化策略参数
        
        Parameters
        ----------
        short_window : int
            短期均线周期，默认为20
        long_window : int
            长期均线周期，默认为50
        position_size : float
            持仓规模，取值0-1，默认为1.0
        """
        self.short_window = short_window
        self.long_window = long_window
        self.position_size = position_size
        
        # 添加params属性，兼容回测引擎API
        self.params = {
            'name': f'SimpleMACrossover({short_window},{long_window})',
            'short_window': short_window,
            'long_window': long_window,
            'position_size': position_size
        }
        
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号
        
        Parameters
        ----------
        data : pd.DataFrame
            市场数据
            
        Returns
        -------
        pd.DataFrame
            包含交易信号的DataFrame
        """
        signals = pd.DataFrame(index=data.index)
        
        # 计算技术指标
        from indicators.trend import moving_averages
        
        # 计算短期和长期移动平均线
        sma_short = moving_averages.SMA(window=self.short_window, column='close')
        sma_long = moving_averages.SMA(window=self.long_window, column='close')
        
        # 计算SMA值
        result_short = sma_short.calculate(data)
        result_long = sma_long.calculate(data)
        
        # 将SMA值添加到信号DataFrame
        signals['short_ma'] = result_short[f'SMA_{self.short_window}']
        signals['long_ma'] = result_long[f'SMA_{self.long_window}']
        
        # 计算信号
        # 金叉: 短期均线上穿长期均线
        # 死叉: 短期均线下穿长期均线
        signals['cross_above'] = (signals['short_ma'] > signals['long_ma']) & (signals['short_ma'].shift(1) <= signals['long_ma'].shift(1))
        signals['cross_below'] = (signals['short_ma'] < signals['long_ma']) & (signals['short_ma'].shift(1) >= signals['long_ma'].shift(1))
        
        # 生成进出场信号
        signals['entries'] = signals['cross_above']
        signals['exits'] = signals['cross_below']
        
        return signals
    
    def __str__(self):
        return f"SimpleMACrossover({self.short_window}, {self.long_window})"


class SimpleRSIStrategy(Strategy):
    """
    简单RSI策略
    """
    
    def __init__(self, rsi_period: int = 14, overbought: int = 70, oversold: int = 30, **kwargs):
        """初始化策略"""
        # 初始化params字典
        self.params = {
            'name': 'SimpleRSIStrategy',
            'rsi_period': rsi_period,
            'overbought': overbought,
            'oversold': oversold
        }
        self.params.update(kwargs)
        
        self.rsi_period = rsi_period
        self.overbought = overbought
        self.oversold = oversold
        self.indicators = {}
        
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        signals = pd.DataFrame(index=data.index)
        
        # 计算RSI
        # 修复：使用RSI类计算而不是尝试调用不存在的rsi函数
        rsi_indicator = rsi.RSI(window=self.rsi_period, column='close')
        result = rsi_indicator.calculate(data)
        signals['rsi'] = result[f'RSI_{self.rsi_period}']
        self.indicators['RSI'] = signals['rsi']
        
        # 生成交易信号 (1 = 做多, 0 = 平仓)
        signals['position'] = 0
        signals.loc[signals['rsi'] < self.oversold, 'position'] = 1  # 超卖买入
        signals.loc[signals['rsi'] > self.overbought, 'position'] = 0  # 超买卖出
        
        # 填充缺失值
        signals['position'] = signals['position'].ffill().fillna(0)
        
        return signals
    
    def get_indicator(self, name: str) -> pd.Series:
        """获取指标数据"""
        return self.indicators.get(name)


def create_sample_data(n_days=365):
    """创建样本数据"""
    try:
        # 尝试下载比特币数据
        logger.info("尝试下载比特币历史数据...")
        data = download_crypto_data(
            "BTC/USDT", 
            "1d", 
            start_time=(datetime.now() - timedelta(days=n_days)),
            end_time=datetime.now(),
            # 不使用代理
            proxy=None  # 设置为None不使用代理
        )
        if data is not None and len(data) > 0:
            logger.info(f"成功下载数据，共 {len(data)} 条记录")
            return data
    except Exception as e:
        logger.warning(f"无法下载数据: {e}")
    
    # 如果下载失败，则生成模拟数据
    logger.info("生成模拟数据...")
    # 生成日期序列
    end_date = datetime.now()
    start_date = end_date - timedelta(days=n_days)
    dates = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # 生成价格数据
    np.random.seed(42)
    price = 100
    prices = [price]
    
    for i in range(1, len(dates)):
        change_percent = np.random.normal(0.0005, 0.01)
        price = price * (1 + change_percent)
        prices.append(price)
    
    # 基于收盘价生成OHLCV数据
    closes = np.array(prices)
    highs = closes * (1 + np.random.uniform(0, 0.015, len(dates)))
    lows = closes * (1 - np.random.uniform(0, 0.015, len(dates)))
    opens = lows + np.random.uniform(0, 1, len(dates)) * (highs - lows)
    volumes = np.random.uniform(1000, 10000, len(dates)) * closes
    
    # 创建DataFrame
    df = pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    }, index=dates)
    
    return df


def run_vectorbt_backtest(data: pd.DataFrame, strategy: Strategy) -> Tuple[BacktestResults, float]:
    """
    运行VectorBT回测
    
    Parameters
    ----------
    data : pd.DataFrame
        回测数据
    strategy : Strategy
        回测策略
        
    Returns
    -------
    tuple
        (回测结果, 运行时间)
    """
    # 获取策略名称
    strategy_name = getattr(strategy, 'params', {}).get('name', str(strategy))
    logger.info(f"使用VectorBT引擎运行{strategy_name}策略回测...")
    
    # 创建VectorBT引擎
    engine = VectorBTEngine(
        data=data,
        initial_capital=100000,
        commission_rate=0.001
    )
    
    # 记录开始时间
    start_time = time.time()
    
    # 运行回测
    results = engine.run(strategy)
    
    # 计算运行时间
    run_time = time.time() - start_time
    logger.info(f"VectorBT回测完成，耗时: {run_time:.4f}秒")
    
    return results, run_time


def run_backtrader_backtest(data: pd.DataFrame, strategy: Strategy) -> Tuple[BacktestResults, float]:
    """
    运行Backtrader回测
    
    Parameters
    ----------
    data : pd.DataFrame
        回测数据
    strategy : Strategy
        回测策略
        
    Returns
    -------
    tuple
        (回测结果, 运行时间)
    """
    # 获取策略名称
    strategy_name = getattr(strategy, 'params', {}).get('name', str(strategy))
    logger.info(f"使用Backtrader引擎运行{strategy_name}策略回测...")
    
    # 创建Backtrader引擎
    engine = BacktraderEngine(
        data=data,
        initial_cash=100000,
        commission=0.001
    )
    
    # 记录开始时间
    start_time = time.time()
    
    # 运行回测
    results = engine.run(strategy)
    
    # 计算运行时间
    run_time = time.time() - start_time
    logger.info(f"Backtrader回测完成，耗时: {run_time:.4f}秒")
    
    return results, run_time


def compare_metrics(vbt_results: BacktestResults, bt_results: BacktestResults) -> pd.DataFrame:
    """
    比较两个回测引擎的性能指标
    
    Parameters
    ----------
    vbt_results : BacktestResults
        VectorBT回测结果
    bt_results : BacktestResults
        Backtrader回测结果
        
    Returns
    -------
    pd.DataFrame
        性能指标比较
    """
    # 提取指标
    metrics = [
        'sharpe_ratio',
        'total_return',
        'annual_return',
        'max_drawdown',
        'win_rate',
        'volatility'
    ]
    
    # 创建比较DataFrame
    comparison = pd.DataFrame(index=metrics, columns=['VectorBT', 'Backtrader', '差异百分比'])
    
    for metric in metrics:
        vbt_value = vbt_results.metrics.get(metric, 0)
        bt_value = bt_results.metrics.get(metric, 0)
        
        # 处理None值
        if vbt_value is None:
            vbt_value = 0.0
        if bt_value is None:
            bt_value = 0.0
        
        # 计算差异百分比
        if vbt_value != 0:
            diff_pct = (bt_value - vbt_value) / abs(vbt_value) * 100
        else:
            diff_pct = 0.0 if bt_value == 0 else float('inf')
        
        comparison.loc[metric, 'VectorBT'] = vbt_value
        comparison.loc[metric, 'Backtrader'] = bt_value
        comparison.loc[metric, '差异百分比'] = diff_pct
    
    return comparison


def visualize_comparison(vbt_results: BacktestResults, bt_results: BacktestResults, 
                        vbt_time: float, bt_time: float, strategy_name: str):
    """
    可视化对比结果
    
    Parameters
    ----------
    vbt_results : BacktestResults
        VectorBT回测结果
    bt_results : BacktestResults
        Backtrader回测结果
    vbt_time : float
        VectorBT运行时间
    bt_time : float
        Backtrader运行时间
    strategy_name : str
        策略名称
    """
    plt.figure(figsize=(15, 12))
    
    # 1. 权益曲线对比
    plt.subplot(3, 1, 1)
    
    # 检查equity是否为函数，如果是则调用它
    vbt_equity = vbt_results.equity() if callable(vbt_results.equity) else vbt_results.equity
    bt_equity = bt_results.equity() if callable(bt_results.equity) else bt_results.equity
    
    # 检查是否有权益数据
    if hasattr(vbt_equity, 'index') and len(vbt_equity) > 0:
        plt.plot(vbt_equity.index, vbt_equity, label='VectorBT')
    else:
        logger.warning("VectorBT权益数据不可用")
    
    if hasattr(bt_equity, 'index') and len(bt_equity) > 0:
        plt.plot(bt_equity.index, bt_equity, label='Backtrader')
    else:
        logger.warning("Backtrader权益数据不可用")
    
    plt.title(f'{strategy_name} - 权益曲线对比')
    plt.xlabel('日期')
    plt.ylabel('账户净值')
    plt.legend()
    plt.grid(True)
    
    # 2. 收益率对比
    plt.subplot(3, 1, 2)
    
    # 安全地获取收益率
    try:
        vbt_returns = vbt_results.get_returns()
        if vbt_returns is not None and len(vbt_returns) > 0:
            plt.plot(vbt_returns.index, vbt_returns.cumsum(), label='VectorBT')
    except Exception as e:
        logger.warning(f"无法获取VectorBT收益率: {e}")
    
    try:
        bt_returns = bt_results.get_returns()
        if bt_returns is not None and len(bt_returns) > 0:
            plt.plot(bt_returns.index, bt_returns.cumsum(), label='Backtrader')
    except Exception as e:
        logger.warning(f"无法获取Backtrader收益率: {e}")
    
    plt.title(f'{strategy_name} - 累计收益率对比')
    plt.xlabel('日期')
    plt.ylabel('累计收益率')
    plt.legend()
    plt.grid(True)
    
    # 3. 性能指标对比
    plt.subplot(3, 1, 3)
    metrics = compare_metrics(vbt_results, bt_results)
    
    # 只比较选定的指标
    selected_metrics = ['sharpe_ratio', 'total_return', 'max_drawdown', 'win_rate']
    pos = np.arange(len(selected_metrics))
    width = 0.35
    
    # 获取指标值并确保都是数值类型
    vbt_values = []
    bt_values = []
    for m in selected_metrics:
        vbt_val = metrics.loc[m, 'VectorBT']
        bt_val = metrics.loc[m, 'Backtrader']
        # 处理None值
        vbt_values.append(0.0 if vbt_val is None else vbt_val)
        bt_values.append(0.0 if bt_val is None else bt_val)
    
    plt.bar(pos - width/2, vbt_values, width, label='VectorBT')
    plt.bar(pos + width/2, bt_values, width, label='Backtrader')
    
    plt.title(f'{strategy_name} - 性能指标对比')
    plt.xticks(pos, selected_metrics)
    plt.ylabel('数值')
    plt.legend()
    
    # 添加运行时间对比
    if vbt_time > 0 and bt_time > 0:
        speed_ratio = bt_time / vbt_time
        plt.figtext(0.5, 0.01, f'运行时间: VectorBT: {vbt_time:.4f}秒, Backtrader: {bt_time:.4f}秒, 速度比: {speed_ratio:.2f}x', 
                  ha='center', fontsize=12, bbox=dict(facecolor='yellow', alpha=0.5))
    else:
        plt.figtext(0.5, 0.01, f'运行时间: VectorBT: {vbt_time:.4f}秒, Backtrader: {bt_time:.4f}秒', 
                  ha='center', fontsize=12, bbox=dict(facecolor='yellow', alpha=0.5))
    
    plt.tight_layout(rect=[0, 0.03, 1, 0.98])
    plt.show()


def compare_ma_strategy():
    """比较移动平均线策略在两个引擎上的表现"""
    print("\nMA交叉策略引擎对比测试")
    print("=======================")
    
    # 获取数据
    data = create_sample_data()
    
    # 创建策略
    strategy = SimpleMACrossoverStrategy(short_window=10, long_window=50)
    
    # 运行VectorBT回测
    vbt_results, vbt_time = run_vectorbt_backtest(data, strategy)
    
    # 运行Backtrader回测
    bt_results, bt_time = run_backtrader_backtest(data, strategy)
    
    # 比较性能指标
    metrics_comparison = compare_metrics(vbt_results, bt_results)
    print("\n性能指标比较:")
    print(metrics_comparison)
    
    # 比较运行时间
    print("\n运行时间比较:")
    print(f"VectorBT: {vbt_time:.4f}秒")
    print(f"Backtrader: {bt_time:.4f}秒")
    if bt_time > 0:
        print(f"VectorBT比Backtrader快 {bt_time/vbt_time:.2f}x")
    
    # 可视化对比
    visualize_comparison(vbt_results, bt_results, vbt_time, bt_time, "MA交叉策略")
    
    return vbt_results, bt_results, metrics_comparison


def compare_rsi_strategy():
    """比较RSI策略在两个引擎上的表现"""
    print("\nRSI策略引擎对比测试")
    print("=======================")
    
    # 获取数据
    data = create_sample_data()
    
    # 创建策略
    strategy = SimpleRSIStrategy(rsi_period=14, overbought=70, oversold=30)
    
    # 运行VectorBT回测
    vbt_results, vbt_time = run_vectorbt_backtest(data, strategy)
    
    # 运行Backtrader回测
    bt_results, bt_time = run_backtrader_backtest(data, strategy)
    
    # 比较性能指标
    metrics_comparison = compare_metrics(vbt_results, bt_results)
    print("\n性能指标比较:")
    print(metrics_comparison)
    
    # 比较运行时间
    print("\n运行时间比较:")
    print(f"VectorBT: {vbt_time:.4f}秒")
    print(f"Backtrader: {bt_time:.4f}秒")
    if bt_time > 0:
        print(f"VectorBT比Backtrader快 {bt_time/vbt_time:.2f}x")
    
    # 可视化对比
    visualize_comparison(vbt_results, bt_results, vbt_time, bt_time, "RSI策略")
    
    return vbt_results, bt_results, metrics_comparison


def scalability_test():
    """测试两个引擎在不同数据规模下的扩展性"""
    print("\n扩展性测试 - 不同数据规模")
    print("=======================")
    
    # 测试不同数据规模
    data_sizes = [100, 250, 500, 1000, 2000]
    vbt_times = []
    bt_times = []
    
    for size in data_sizes:
        print(f"\n测试数据规模: {size}天")
        
        # 获取数据
        data = create_sample_data(n_days=size)
        
        # 创建策略
        strategy = SimpleMACrossoverStrategy(short_window=10, long_window=50)
        
        # 运行VectorBT回测
        _, vbt_time = run_vectorbt_backtest(data, strategy)
        vbt_times.append(vbt_time)
        
        # 运行Backtrader回测
        _, bt_time = run_backtrader_backtest(data, strategy)
        bt_times.append(bt_time)
    
    # 可视化结果
    plt.figure(figsize=(10, 6))
    plt.plot(data_sizes, vbt_times, 'o-', label='VectorBT')
    plt.plot(data_sizes, bt_times, 's-', label='Backtrader')
    plt.title('回测引擎扩展性测试')
    plt.xlabel('数据大小 (天)')
    plt.ylabel('运行时间 (秒)')
    plt.legend()
    plt.grid(True)
    plt.show()
    
    # 计算速度比
    speed_ratios = [b/v if v > 0 else float('inf') for v, b in zip(vbt_times, bt_times)]
    
    # 显示结果表格
    results = pd.DataFrame({
        '数据大小': data_sizes,
        'VectorBT时间': vbt_times,
        'Backtrader时间': bt_times,
        'VectorBT/Backtrader速度比': speed_ratios
    })
    
    print("\n扩展性测试结果:")
    print(results)
    
    return results


def main():
    """主函数"""
    print("回测引擎对比测试")
    print("=================")
    print("本测试比较VectorBT和Backtrader回测引擎的性能和结果差异。")
    
    # 检查是否导入了Backtrader引擎
    backtrader_available = 'BacktraderEngine' in globals()
    
    if not backtrader_available:
        print("\n注意: Backtrader引擎未导入，只能运行VectorBT引擎测试")
    
    print("\n选项:")
    print("1. 比较MA交叉策略")
    print("2. 比较RSI策略")
    print("3. 扩展性测试(不同数据规模)")
    print("4. 运行所有测试")
    
    choice = input("\n请选择测试 (1-4): ")
    
    try:
        if choice == '1':
            # 如果Backtrader不可用，只运行VectorBT部分
            if not backtrader_available:
                print("\n只运行VectorBT引擎的MA交叉策略测试...")
                data = create_sample_data()
                strategy = SimpleMACrossoverStrategy(short_window=10, long_window=50)
                vbt_results, vbt_time = run_vectorbt_backtest(data, strategy)
                print(f"\nVectorBT测试完成，耗时: {vbt_time:.4f}秒")
            else:
                compare_ma_strategy()
        elif choice == '2':
            # 如果Backtrader不可用，只运行VectorBT部分
            if not backtrader_available:
                print("\n只运行VectorBT引擎的RSI策略测试...")
                data = create_sample_data()
                strategy = SimpleRSIStrategy(rsi_period=14, overbought=70, oversold=30)
                vbt_results, vbt_time = run_vectorbt_backtest(data, strategy)
                print(f"\nVectorBT测试完成，耗时: {vbt_time:.4f}秒")
            else:
                compare_rsi_strategy()
        elif choice == '3':
            if not backtrader_available:
                print("\n扩展性测试需要两个引擎都可用。")
                print("跳过扩展性测试...")
            else:
                scalability_test()
        elif choice == '4':
            # 如果Backtrader不可用，只运行VectorBT部分
            if not backtrader_available:
                print("\n由于Backtrader引擎不可用，只能运行VectorBT部分测试...")
                
                print("\n运行VectorBT引擎的MA交叉策略测试...")
                data = create_sample_data()
                strategy1 = SimpleMACrossoverStrategy(short_window=10, long_window=50)
                vbt_results1, vbt_time1 = run_vectorbt_backtest(data, strategy1)
                
                print("\n运行VectorBT引擎的RSI策略测试...")
                strategy2 = SimpleRSIStrategy(rsi_period=14, overbought=70, oversold=30)
                vbt_results2, vbt_time2 = run_vectorbt_backtest(data, strategy2)
                
                print("\nVectorBT测试完成")
            else:
                compare_ma_strategy()
                compare_rsi_strategy()
                scalability_test()
        else:
            print("无效选择，请输入1-4")
    except Exception as e:
        print(f"\n运行时出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()