"""
资金分配策略模块

实现各种资金分配策略，用于在多个交易标的之间分配资金。
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Union
from abc import ABC, abstractmethod


class AllocationStrategy(ABC):
    """
    资金分配策略抽象基类
    
    所有具体资金分配策略必须继承此类并实现相应方法。
    """
    
    def __init__(self, name: str, description: str = "", **params):
        """
        初始化资金分配策略
        
        Parameters
        ----------
        name : str
            策略名称
        description : str, optional
            策略描述，默认为空字符串
        **params : dict
            策略参数
        """
        self.name = name
        self.description = description
        self.params = params
        self.validate_params()
    
    @abstractmethod
    def validate_params(self) -> None:
        """
        验证参数有效性
        
        每个具体策略类必须实现此方法来验证其特定参数。
        
        Raises
        ------
        ValueError
            如果参数无效
        """
        pass
    
    @abstractmethod
    def allocate(self, 
                symbols: List[str], 
                total_capital: float,
                context: Dict[str, Any],
                data: Dict[str, pd.DataFrame] = None) -> Dict[str, float]:
        """
        为多个交易品种分配资金
        
        Parameters
        ----------
        symbols : List[str]
            交易品种列表
        total_capital : float
            总资金
        context : Dict[str, Any]
            交易上下文
        data : Dict[str, pd.DataFrame], optional
            各交易品种的市场数据，默认为None
        
        Returns
        -------
        Dict[str, float]
            各交易品种的资金分配，格式为 {symbol: allocation}
        """
        pass
    
    def update_params(self, **params) -> None:
        """
        更新参数
        
        Parameters
        ----------
        **params : dict
            要更新的参数
        
        Raises
        ------
        ValueError
            如果参数无效
        """
        self.params.update(params)
        self.validate_params()


class EqualAllocationStrategy(AllocationStrategy):
    """
    等比例资金分配策略
    
    将资金平均分配给每个交易品种。
    """
    
    def __init__(self, 
                name: str = "EqualAllocation", 
                description: str = "", 
                max_allocation_pct: float = 100.0):
        """
        初始化等比例资金分配策略
        
        Parameters
        ----------
        name : str, optional
            策略名称，默认为"EqualAllocation"
        description : str, optional
            策略描述，默认为空字符串
        max_allocation_pct : float, optional
            最大资金分配百分比，默认为100.0（使用全部资金）
        """
        super().__init__(name, description, max_allocation_pct=max_allocation_pct)
    
    def validate_params(self) -> None:
        """
        验证参数有效性
        
        Raises
        ------
        ValueError
            如果参数无效
        """
        max_alloc = self.params.get("max_allocation_pct", 100.0)
        if max_alloc <= 0 or max_alloc > 100:
            raise ValueError("最大资金分配百分比必须在(0, 100]范围内")
    
    def allocate(self, 
                symbols: List[str], 
                total_capital: float,
                context: Dict[str, Any],
                data: Dict[str, pd.DataFrame] = None) -> Dict[str, float]:
        """
        为多个交易品种平均分配资金
        
        Parameters
        ----------
        symbols : List[str]
            交易品种列表
        total_capital : float
            总资金
        context : Dict[str, Any]
            交易上下文
        data : Dict[str, pd.DataFrame], optional
            各交易品种的市场数据，默认为None
        
        Returns
        -------
        Dict[str, float]
            各交易品种的资金分配，格式为 {symbol: allocation}
        """
        if not symbols:
            return {}
        
        max_alloc_pct = self.params.get("max_allocation_pct", 100.0)
        usable_capital = total_capital * (max_alloc_pct / 100)
        
        allocation_per_symbol = usable_capital / len(symbols)
        
        return {symbol: allocation_per_symbol for symbol in symbols}


class VolatilityAdjustedAllocation(AllocationStrategy):
    """
    波动率调整资金分配策略
    
    根据各交易品种的波动率反比例分配资金，波动率越低分配越多。
    """
    
    def __init__(self, 
                name: str = "VolatilityAdjusted", 
                description: str = "", 
                volatility_lookback: int = 20,
                max_allocation_pct: float = 100.0,
                min_allocation_pct: float = 5.0,
                inverse_weight: bool = True):
        """
        初始化波动率调整资金分配策略
        
        Parameters
        ----------
        name : str, optional
            策略名称，默认为"VolatilityAdjusted"
        description : str, optional
            策略描述，默认为空字符串
        volatility_lookback : int, optional
            计算波动率的回溯周期数，默认为20
        max_allocation_pct : float, optional
            最大资金分配百分比，默认为100.0
        min_allocation_pct : float, optional
            单个品种最小分配百分比，默认为5.0
        inverse_weight : bool, optional
            是否使用波动率的倒数作为权重，默认为True
        """
        super().__init__(
            name, 
            description, 
            volatility_lookback=volatility_lookback,
            max_allocation_pct=max_allocation_pct,
            min_allocation_pct=min_allocation_pct,
            inverse_weight=inverse_weight
        )
    
    def validate_params(self) -> None:
        """
        验证参数有效性
        
        Raises
        ------
        ValueError
            如果参数无效
        """
        lookback = self.params.get("volatility_lookback")
        if lookback <= 0:
            raise ValueError("波动率回溯周期必须为正整数")
            
        max_alloc = self.params.get("max_allocation_pct")
        if max_alloc <= 0 or max_alloc > 100:
            raise ValueError("最大资金分配百分比必须在(0, 100]范围内")
            
        min_alloc = self.params.get("min_allocation_pct")
        if min_alloc < 0 or min_alloc >= max_alloc:
            raise ValueError(f"最小分配百分比必须在[0, {max_alloc})范围内")
    
    def _calculate_volatility(self, data: pd.DataFrame) -> float:
        """
        计算波动率
        
        Parameters
        ----------
        data : pd.DataFrame
            市场数据，需要包含收盘价
        
        Returns
        -------
        float
            波动率（百分比）
        """
        if data is None or len(data) == 0:
            return float('inf')
        
        lookback = self.params.get("volatility_lookback")
        
        # 确保数据足够
        if len(data) < lookback:
            lookback = len(data)
        
        # 计算日收益率
        if 'close' in data.columns:
            returns = data['close'].pct_change().dropna()
            
            # 计算波动率（标准差）
            volatility = returns.std() * 100  # 转换为百分比
            
            return max(0.01, volatility)  # 确保波动率不为零
        
        return float('inf')
    
    def allocate(self, 
                symbols: List[str], 
                total_capital: float,
                context: Dict[str, Any],
                data: Dict[str, pd.DataFrame] = None) -> Dict[str, float]:
        """
        根据波动率调整资金分配
        
        Parameters
        ----------
        symbols : List[str]
            交易品种列表
        total_capital : float
            总资金
        context : Dict[str, Any]
            交易上下文
        data : Dict[str, pd.DataFrame], optional
            各交易品种的市场数据，默认为None
        
        Returns
        -------
        Dict[str, float]
            各交易品种的资金分配，格式为 {symbol: allocation}
        """
        if not symbols:
            return {}
            
        if data is None:
            # 如果没有市场数据，则平均分配
            return EqualAllocationStrategy().allocate(symbols, total_capital, context)
        
        # 计算每个品种的波动率
        volatilities = {}
        for symbol in symbols:
            symbol_data = data.get(symbol)
            if symbol_data is not None:
                volatilities[symbol] = self._calculate_volatility(symbol_data)
            else:
                # 如果没有数据，假设极高波动率
                volatilities[symbol] = float('inf')
        
        # 根据波动率计算权重
        inverse_weight = self.params.get("inverse_weight", True)
        weights = {}
        
        if inverse_weight:
            # 波动率越低，权重越高
            for symbol, vol in volatilities.items():
                weights[symbol] = 1 / vol if vol != float('inf') else 0
        else:
            # 波动率越高，权重越高
            for symbol, vol in volatilities.items():
                weights[symbol] = vol if vol != float('inf') else 0
        
        # 计算总权重
        total_weight = sum(weights.values())
        
        # 如果总权重为零，则平均分配
        if total_weight == 0:
            return EqualAllocationStrategy().allocate(symbols, total_capital, context)
        
        # 计算分配比例
        max_alloc_pct = self.params.get("max_allocation_pct", 100.0)
        min_alloc_pct = self.params.get("min_allocation_pct", 5.0)
        usable_capital = total_capital * (max_alloc_pct / 100)
        
        allocation = {}
        for symbol in symbols:
            # 计算初始分配比例
            ratio = weights[symbol] / total_weight
            
            # 应用最小分配限制
            min_ratio = min_alloc_pct / 100
            ratio = max(ratio, min_ratio)
            
            allocation[symbol] = usable_capital * ratio
        
        # 调整以确保总分配不超过可用资金
        total_allocated = sum(allocation.values())
        if total_allocated > usable_capital:
            scale_factor = usable_capital / total_allocated
            allocation = {symbol: amount * scale_factor for symbol, amount in allocation.items()}
        
        return allocation


class PerformanceBasedAllocation(AllocationStrategy):
    """
    基于历史表现的资金分配策略
    
    根据各交易品种的历史表现分配资金，表现越好分配越多。
    """
    
    def __init__(self, 
                name: str = "PerformanceBased", 
                description: str = "", 
                lookback_periods: int = 20,
                max_allocation_pct: float = 100.0,
                min_allocation_pct: float = 5.0,
                metrics: Dict[str, float] = None):
        """
        初始化基于历史表现的资金分配策略
        
        Parameters
        ----------
        name : str, optional
            策略名称，默认为"PerformanceBased"
        description : str, optional
            策略描述，默认为空字符串
        lookback_periods : int, optional
            回溯期数，默认为20
        max_allocation_pct : float, optional
            最大资金分配百分比，默认为100.0
        min_allocation_pct : float, optional
            单个品种最小分配百分比，默认为5.0
        metrics : Dict[str, float], optional
            评估指标及其权重，默认为{"sharpe": 0.4, "win_rate": 0.3, "avg_profit": 0.3}
        """
        if metrics is None:
            metrics = {"sharpe": 0.4, "win_rate": 0.3, "avg_profit": 0.3}
            
        super().__init__(
            name, 
            description, 
            lookback_periods=lookback_periods,
            max_allocation_pct=max_allocation_pct,
            min_allocation_pct=min_allocation_pct,
            metrics=metrics
        )
    
    def validate_params(self) -> None:
        """
        验证参数有效性
        
        Raises
        ------
        ValueError
            如果参数无效
        """
        lookback = self.params.get("lookback_periods")
        if lookback <= 0:
            raise ValueError("回溯期数必须为正整数")
            
        max_alloc = self.params.get("max_allocation_pct")
        if max_alloc <= 0 or max_alloc > 100:
            raise ValueError("最大资金分配百分比必须在(0, 100]范围内")
            
        min_alloc = self.params.get("min_allocation_pct")
        if min_alloc < 0 or min_alloc >= max_alloc:
            raise ValueError(f"最小分配百分比必须在[0, {max_alloc})范围内")
            
        metrics = self.params.get("metrics")
        if not metrics:
            raise ValueError("必须指定至少一个评估指标")
            
        if sum(metrics.values()) != 1.0:
            raise ValueError("评估指标权重之和必须为1.0")
    
    def _calculate_performance_score(self, symbol: str, context: Dict[str, Any]) -> float:
        """
        计算表现得分
        
        Parameters
        ----------
        symbol : str
            交易品种
        context : Dict[str, Any]
            交易上下文，包含历史交易数据
        
        Returns
        -------
        float
            表现得分
        """
        # 获取历史交易记录
        trades = context.get("trades", {}).get(symbol, [])
        lookback = self.params.get("lookback_periods")
        
        # 取最近N笔交易
        recent_trades = trades[-lookback:] if len(trades) > lookback else trades
        
        if not recent_trades:
            return 0  # 如果没有交易记录，返回零得分
        
        # 计算各项指标
        metrics = {}
        
        # 胜率
        wins = [t for t in recent_trades if t.get("pnl", 0) > 0]
        win_rate = len(wins) / len(recent_trades) if recent_trades else 0
        metrics["win_rate"] = win_rate
        
        # 平均盈利
        avg_profit = sum(t.get("pnl", 0) for t in recent_trades) / len(recent_trades)
        metrics["avg_profit"] = max(0, avg_profit)  # 确保非负
        
        # 夏普比率（简化计算）
        if len(recent_trades) > 1:
            profits = [t.get("pnl", 0) for t in recent_trades]
            mean_profit = np.mean(profits)
            std_profit = np.std(profits) if len(profits) > 1 else 1.0
            sharpe = mean_profit / std_profit if std_profit > 0 else 0
            metrics["sharpe"] = max(0, sharpe)  # 确保非负
        else:
            metrics["sharpe"] = 0
        
        # 最大回撤
        equity_curve = context.get("equity_curves", {}).get(symbol, [])
        if equity_curve:
            peak = max(equity_curve)
            valley = min(equity_curve[equity_curve.index(peak):]) if peak in equity_curve else min(equity_curve)
            max_dd = (peak - valley) / peak if peak > 0 else 1.0
            metrics["max_drawdown"] = 1.0 - max_dd  # 转换为正向指标
        else:
            metrics["max_drawdown"] = 0
        
        # 计算加权得分
        metric_weights = self.params.get("metrics")
        score = 0
        for metric, weight in metric_weights.items():
            if metric in metrics:
                score += metrics[metric] * weight
        
        return score
    
    def allocate(self, 
                symbols: List[str], 
                total_capital: float,
                context: Dict[str, Any],
                data: Dict[str, pd.DataFrame] = None) -> Dict[str, float]:
        """
        根据历史表现分配资金
        
        Parameters
        ----------
        symbols : List[str]
            交易品种列表
        total_capital : float
            总资金
        context : Dict[str, Any]
            交易上下文，包含历史交易数据
        data : Dict[str, pd.DataFrame], optional
            各交易品种的市场数据，默认为None
        
        Returns
        -------
        Dict[str, float]
            各交易品种的资金分配，格式为 {symbol: allocation}
        """
        if not symbols:
            return {}
        
        # 计算每个品种的表现得分
        scores = {}
        for symbol in symbols:
            scores[symbol] = self._calculate_performance_score(symbol, context)
        
        # 计算总得分
        total_score = sum(scores.values())
        
        # 如果总得分为零，则平均分配
        if total_score == 0:
            return EqualAllocationStrategy().allocate(symbols, total_capital, context)
        
        # 计算分配比例
        max_alloc_pct = self.params.get("max_allocation_pct", 100.0)
        min_alloc_pct = self.params.get("min_allocation_pct", 5.0)
        usable_capital = total_capital * (max_alloc_pct / 100)
        
        allocation = {}
        for symbol in symbols:
            # 计算初始分配比例
            ratio = scores[symbol] / total_score
            
            # 应用最小分配限制
            min_ratio = min_alloc_pct / 100
            ratio = max(ratio, min_ratio)
            
            allocation[symbol] = usable_capital * ratio
        
        # 调整以确保总分配不超过可用资金
        total_allocated = sum(allocation.values())
        if total_allocated > usable_capital:
            scale_factor = usable_capital / total_allocated
            allocation = {symbol: amount * scale_factor for symbol, amount in allocation.items()}
        
        return allocation 