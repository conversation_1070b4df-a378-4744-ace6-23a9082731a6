"""
风险评估模块

提供策略风险评估、评分和报告生成功能，包括各种风险指标的计算和综合分析。
"""

from risk.assessment.base import RiskAssessor, RiskReport
from risk.assessment.metrics import (
    VolatilityMetric,
    DrawdownMetric,
    SharpeRatioMetric,
    ValueAtRiskMetric,
    CalmarRatioMetric,
    SortinoRatioMetric,
    ConcentrationMetric,
    LeverageMetric
)
# 已移除scoring模块，使用freqtrade保护系统替代
# 已移除reports模块，减少冗余功能

__all__ = [
    # 核心组件
    'RiskAssessor',
    'RiskReport',
    
    # 风险指标
    'VolatilityMetric',
    'DrawdownMetric',
    'SharpeRatioMetric',
    'ValueAtRiskMetric',
    'CalmarRatioMetric',
    'SortinoRatioMetric',
    'ConcentrationMetric',
    'LeverageMetric',
    
    # 已移除评分模型和报告生成，使用freqtrade保护系统替代
]