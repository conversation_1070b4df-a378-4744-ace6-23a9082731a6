#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
性能指标计算模块

提供各种回测性能指标的计算函数。
"""

from typing import Dict, Any, Union, Optional, List, Tuple
import pandas as pd
import numpy as np
import scipy.stats
from datetime import datetime

from ..base import BacktestResults


def calculate_metrics(results: BacktestResults, 
                      risk_free_rate: float = 0.0,
                      benchmark: Optional[pd.Series] = None) -> Dict[str, Any]:
    """
    计算回测性能指标
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    risk_free_rate : float, optional
        无风险利率，默认为0.0
    benchmark : pd.Series, optional
        基准指数，默认为None
        
    Returns
    -------
    Dict[str, Any]
        包含各种性能指标的字典
    """
    # 已有的性能指标
    metrics = results.metrics.copy() if results.metrics else {}
    
    # 获取收益率序列
    get_returns = results.get_returns
    returns = get_returns() if callable(get_returns) else get_returns
    
    # 计算回撤相关指标
    get_drawdowns = results.get_drawdowns
    drawdowns = get_drawdowns() if callable(get_drawdowns) else get_drawdowns
    
    # 获取净值曲线
    equity = results.equity() if callable(results.equity) else results.equity
    
    # 计算常用性能指标
    # 基础统计
    metrics.update({
        'start_date': equity.index[0],
        'end_date': equity.index[-1],
        'trading_days': len(equity),
        'total_return': calculate_total_return(equity),
        'annualized_return': calculate_annualized_return(returns),
        'daily_sharpe': calculate_sharpe_ratio(returns, risk_free_rate=risk_free_rate, periods_per_year=252),
        'daily_sortino': calculate_sortino_ratio(returns, risk_free_rate=risk_free_rate, periods_per_year=252),
        'volatility': calculate_volatility(returns),
        'calmar_ratio': calculate_calmar_ratio(returns, drawdowns),
        'max_drawdown': calculate_max_drawdown(drawdowns),
        'avg_drawdown': calculate_avg_drawdown(drawdowns),
        'max_drawdown_duration': calculate_max_drawdown_duration(drawdowns, equity.index),
    })
    
    # 交易相关指标
    if not results.trades.empty:
        metrics.update({
            'total_trades': len(results.trades),
            'win_rate': calculate_win_rate(results.trades),
            'profit_factor': calculate_profit_factor(results.trades),
            'avg_profit_per_trade': calculate_avg_profit_per_trade(results.trades),
            'avg_win': calculate_avg_win(results.trades),
            'avg_loss': calculate_avg_loss(results.trades),
            'largest_win': calculate_largest_win(results.trades),
            'largest_loss': calculate_largest_loss(results.trades),
            'avg_holding_period': calculate_avg_holding_period(results.trades),
            'expectancy': calculate_expectancy(results.trades),
        })
    
    # 基准比较指标
    if benchmark is not None:
        metrics.update(calculate_benchmark_metrics(returns, benchmark))
    
    # 统计性指标
    metrics.update({
        'skew': returns.skew(),
        'kurtosis': returns.kurtosis(),
        'var_95': calculate_value_at_risk(returns, 0.95),
        'var_99': calculate_value_at_risk(returns, 0.99),
        'cvar_95': calculate_conditional_value_at_risk(returns, 0.95),
        'omega_ratio': calculate_omega_ratio(returns, risk_free_rate)
    })
    
    # 高级风险评估指标
    metrics.update({
        'ulcer_index': calculate_ulcer_index(equity),
        'sterling_ratio': calculate_sterling_ratio(returns, drawdowns),
        'burke_ratio': calculate_burke_ratio(returns, drawdowns),
        'kappa_ratio': calculate_kappa_ratio(returns, risk_free_rate, 3),
        'tail_ratio': calculate_tail_ratio(returns),
        'downside_deviation': calculate_downside_deviation(returns),
        'max_consecutive_losses': calculate_max_consecutive_losses(returns)
    })
    
    # 计算月度收益和年度收益
    if len(returns) > 20:  # 确保数据足够
        monthly_returns = calculate_monthly_returns(returns)
        yearly_returns = calculate_yearly_returns(returns)
        
        metrics['monthly_returns'] = monthly_returns
        metrics['yearly_returns'] = yearly_returns
    
    return metrics


# 基础指标计算函数
def calculate_total_return(equity: Union[pd.Series, pd.DataFrame]) -> float:
    """
    计算总回报率
    
    Parameters
    ----------
    equity : pd.Series or pd.DataFrame
        净值曲线
        
    Returns
    -------
    float
        总回报率
    """
    if len(equity) < 2:
        return 0.0
    
    # 处理DataFrame情况
    if isinstance(equity, pd.DataFrame):
        if equity.shape[1] == 1:
            equity = equity.iloc[:, 0]
        else:
            # 使用第一列
            equity = equity.iloc[:, 0]
            print("警告：多列净值数据，使用第一列")
    
    first_value = equity.iloc[0]
    last_value = equity.iloc[-1]
    
    return (last_value / first_value) - 1.0


def calculate_annualized_return(returns: Union[pd.Series, pd.DataFrame], periods_per_year: int = 252) -> float:
    """
    计算年化收益率
    
    Parameters
    ----------
    returns : pd.Series or pd.DataFrame
        收益率序列
    periods_per_year : int, optional
        年度周期数，默认为252（交易日）
        
    Returns
    -------
    float
        年化收益率
    """
    if len(returns) < 1:
        return 0.0
    
    # 处理DataFrame情况
    if isinstance(returns, pd.DataFrame):
        if returns.shape[1] == 1:
            returns = returns.iloc[:, 0]
        else:
            # 使用第一列
            returns = returns.iloc[:, 0]
            print("警告：多列收益率数据，使用第一列")
    
    return (1 + returns.mean()) ** periods_per_year - 1


def calculate_sharpe_ratio(returns: Union[pd.Series, pd.DataFrame], 
                         risk_free_rate: float = 0.0, 
                         periods_per_year: int = 252) -> float:
    """
    计算Sharpe比率
    
    Parameters
    ----------
    returns : pd.Series or pd.DataFrame
        收益率序列
    risk_free_rate : float, optional
        无风险利率，默认为0.0
    periods_per_year : int, optional
        年度周期数，默认为252（交易日）
        
    Returns
    -------
    float
        Sharpe比率
    """
    if len(returns) < 2:
        return 0.0
    
    # 处理DataFrame情况
    if isinstance(returns, pd.DataFrame):
        if returns.shape[1] == 1:
            returns = returns.iloc[:, 0]
        else:
            # 使用第一列
            returns = returns.iloc[:, 0]
            print("警告：多列收益率数据，使用第一列")
    
    # 计算超额收益
    excess_returns = returns - risk_free_rate / periods_per_year
    
    # 计算年化Sharpe比率
    annual_factor = np.sqrt(periods_per_year)
    std_excess = excess_returns.std()
    if std_excess == 0:
        return 0.0
        
    return excess_returns.mean() / std_excess * annual_factor


def calculate_sortino_ratio(returns: Union[pd.Series, pd.DataFrame], 
                          risk_free_rate: float = 0.0, 
                          periods_per_year: int = 252) -> float:
    """
    计算Sortino比率
    
    Parameters
    ----------
    returns : pd.Series or pd.DataFrame
        收益率序列
    risk_free_rate : float, optional
        无风险利率，默认为0.0
    periods_per_year : int, optional
        年度周期数，默认为252（交易日）
        
    Returns
    -------
    float
        Sortino比率
    """
    if len(returns) < 2:
        return 0.0
    
    # 处理DataFrame情况
    if isinstance(returns, pd.DataFrame):
        if returns.shape[1] == 1:
            returns = returns.iloc[:, 0]
        else:
            # 使用第一列
            returns = returns.iloc[:, 0]
            print("警告：多列收益率数据，使用第一列")
    
    # 计算超额收益
    excess_returns = returns - risk_free_rate / periods_per_year
    
    # 只考虑负收益的标准差
    downside_returns = excess_returns.copy()
    downside_returns[downside_returns > 0] = 0
    
    # 计算下行风险
    downside_std = downside_returns.std()
    
    # 计算年化Sortino比率
    annual_factor = np.sqrt(periods_per_year)
    if downside_std == 0:
        return 0.0
        
    return excess_returns.mean() / downside_std * annual_factor


def calculate_volatility(returns: Union[pd.Series, pd.DataFrame], periods_per_year: int = 252) -> float:
    """
    计算波动率
    
    Parameters
    ----------
    returns : pd.Series or pd.DataFrame
        收益率序列
    periods_per_year : int, optional
        年度周期数，默认为252（交易日）
        
    Returns
    -------
    float
        年化波动率
    """
    if len(returns) < 2:
        return 0.0
    
    # 处理DataFrame情况
    if isinstance(returns, pd.DataFrame):
        if returns.shape[1] == 1:
            returns = returns.iloc[:, 0]
        else:
            # 使用第一列
            returns = returns.iloc[:, 0]
            print("警告：多列收益率数据，使用第一列")
    
    return returns.std() * np.sqrt(periods_per_year)


def calculate_max_drawdown(drawdowns: Union[pd.Series, pd.DataFrame]) -> float:
    """
    计算最大回撤
    
    Parameters
    ----------
    drawdowns : pd.Series or pd.DataFrame
        回撤序列
        
    Returns
    -------
    float
        最大回撤
    """
    if len(drawdowns) < 1:
        return 0.0
    
    # 处理DataFrame情况
    if isinstance(drawdowns, pd.DataFrame):
        if drawdowns.shape[1] == 1:
            drawdowns = drawdowns.iloc[:, 0]
        else:
            # 使用第一列
            drawdowns = drawdowns.iloc[:, 0]
            print("警告：多列回撤数据，使用第一列")
    
    return drawdowns.min()


def calculate_avg_drawdown(drawdowns: Union[pd.Series, pd.DataFrame]) -> float:
    """
    计算平均回撤
    
    Parameters
    ----------
    drawdowns : pd.Series or pd.DataFrame
        回撤序列
        
    Returns
    -------
    float
        平均回撤
    """
    if len(drawdowns) < 1:
        return 0.0
    
    # 处理DataFrame情况
    if isinstance(drawdowns, pd.DataFrame):
        if drawdowns.shape[1] == 1:
            drawdowns = drawdowns.iloc[:, 0]
        else:
            # 使用第一列
            drawdowns = drawdowns.iloc[:, 0]
            print("警告：多列回撤数据，使用第一列")
    
    # 只考虑负值
    negative_drawdowns = drawdowns[drawdowns < 0]
    if len(negative_drawdowns) == 0:
        return 0.0
    
    return negative_drawdowns.mean()


def calculate_max_drawdown_duration(drawdowns: Union[pd.Series, pd.DataFrame], index: pd.DatetimeIndex) -> int:
    """
    计算最大回撤持续时间
    
    Parameters
    ----------
    drawdowns : pd.Series or pd.DataFrame
        回撤序列
    index : pd.DatetimeIndex
        日期时间索引
        
    Returns
    -------
    int
        最大回撤持续天数
    """
    if len(drawdowns) < 1:
        return 0
    
    # 处理DataFrame情况
    if isinstance(drawdowns, pd.DataFrame):
        if drawdowns.shape[1] == 1:
            drawdowns = drawdowns.iloc[:, 0]
        else:
            # 使用第一列
            drawdowns = drawdowns.iloc[:, 0]
            print("警告：多列回撤数据，使用第一列")
    
    # 对drawdowns进行排序，以防索引乱序
    drawdowns = drawdowns.sort_index()
    
    # 找到最大回撤点
    max_dd_idx = drawdowns.idxmin()
    if max_dd_idx is None:
        return 0
    
    # 通过位置直接获取索引位置，避免get_indexer可能因重复索引而失败
    max_dd_pos = drawdowns.index.get_loc(max_dd_idx)
    if isinstance(max_dd_pos, slice) or isinstance(max_dd_pos, np.ndarray):
        # 如果返回的是一个切片或数组（对于重复索引），取第一个位置
        max_dd_pos = max_dd_pos.start if isinstance(max_dd_pos, slice) else max_dd_pos[0]
    
    # 找到前一个0回撤点（高点）
    start_pos = 0
    for i in range(max_dd_pos, 0, -1):
        if drawdowns.iloc[i-1] == 0:
            start_pos = i-1
            break
    
    # 找到后一个0回撤点（恢复点）
    end_pos = len(drawdowns) - 1
    for i in range(max_dd_pos, len(drawdowns)):
        if drawdowns.iloc[i] == 0:
            end_pos = i
            break
    
    # 计算持续时间（如果是datetime索引）
    if isinstance(drawdowns.index, pd.DatetimeIndex):
        start_date = drawdowns.index[start_pos]
        end_date = drawdowns.index[end_pos]
        duration = (end_date - start_date).days
        return duration
    
    # 如果不是datetime索引，返回索引数量
    return end_pos - start_pos


def calculate_calmar_ratio(returns: Union[pd.Series, pd.DataFrame], 
                          drawdowns: Union[pd.Series, pd.DataFrame], 
                          periods_per_year: int = 252) -> float:
    """
    计算Calmar比率
    
    Parameters
    ----------
    returns : pd.Series or pd.DataFrame
        收益率序列
    drawdowns : pd.Series or pd.DataFrame
        回撤序列
    periods_per_year : int, optional
        年度周期数，默认为252（交易日）
        
    Returns
    -------
    float
        Calmar比率
    """
    # 处理DataFrame情况
    if isinstance(returns, pd.DataFrame):
        if returns.shape[1] == 1:
            returns = returns.iloc[:, 0]
        else:
            returns = returns.iloc[:, 0]
            print("警告：多列收益率数据，使用第一列")
    
    if isinstance(drawdowns, pd.DataFrame):
        if drawdowns.shape[1] == 1:
            drawdowns = drawdowns.iloc[:, 0]
        else:
            drawdowns = drawdowns.iloc[:, 0]
            print("警告：多列回撤数据，使用第一列")
    
    # 计算年化收益率
    annual_return = (1 + returns.mean()) ** periods_per_year - 1
    
    # 计算最大回撤（正数表示）
    max_dd = abs(drawdowns.min())
    
    # 计算Calmar比率
    if max_dd == 0:  # 避免除以零
        return 0.0 if annual_return == 0 else float('inf')
        
    return annual_return / max_dd


# 交易相关指标
def calculate_win_rate(trades: pd.DataFrame) -> float:
    """
    计算胜率
    
    Parameters
    ----------
    trades : pd.DataFrame
        交易记录
        
    Returns
    -------
    float
        胜率
    """
    if len(trades) == 0:
        return 0.0
    
    # 根据VectorBT的交易记录结构调整
    if 'PnL' in trades.columns:
        winning_trades = trades[trades['PnL'] > 0]
    elif 'Return' in trades.columns:
        winning_trades = trades[trades['Return'] > 0]
    else:
        # 如果找不到PnL或Return列，返回0
        return 0.0
    
    return len(winning_trades) / len(trades)


def calculate_profit_factor(trades: pd.DataFrame) -> float:
    """
    计算盈亏比
    
    Parameters
    ----------
    trades : pd.DataFrame
        交易记录
        
    Returns
    -------
    float
        盈亏比
    """
    if len(trades) == 0:
        return 0.0
    
    # 根据VectorBT的交易记录结构调整
    if 'PnL' in trades.columns:
        pnl_col = 'PnL'
    elif 'Return' in trades.columns:
        pnl_col = 'Return'
    else:
        # 如果找不到PnL或Return列，返回0
        return 0.0
    
    # 计算总盈利和总亏损
    gross_profit = trades.loc[trades[pnl_col] > 0, pnl_col].sum()
    gross_loss = abs(trades.loc[trades[pnl_col] < 0, pnl_col].sum())
    
    if gross_loss == 0:
        return float('inf') if gross_profit > 0 else 0.0
    
    return gross_profit / gross_loss


def calculate_avg_profit_per_trade(trades: pd.DataFrame) -> float:
    """
    计算每笔交易的平均盈亏
    
    Parameters
    ----------
    trades : pd.DataFrame
        交易记录
        
    Returns
    -------
    float
        平均盈亏
    """
    if len(trades) == 0:
        return 0.0
    
    # 根据VectorBT的交易记录结构调整
    if 'PnL' in trades.columns:
        return trades['PnL'].mean()
    elif 'Return' in trades.columns:
        return trades['Return'].mean()
    else:
        # 如果找不到PnL或Return列，返回0
        return 0.0


def calculate_avg_win(trades: pd.DataFrame) -> float:
    """
    计算平均盈利
    
    Parameters
    ----------
    trades : pd.DataFrame
        交易记录
        
    Returns
    -------
    float
        平均盈利
    """
    if len(trades) == 0:
        return 0.0
    
    # 根据VectorBT的交易记录结构调整
    if 'PnL' in trades.columns:
        winning_trades = trades[trades['PnL'] > 0]
        if len(winning_trades) == 0:
            return 0.0
        return winning_trades['PnL'].mean()
    elif 'Return' in trades.columns:
        winning_trades = trades[trades['Return'] > 0]
        if len(winning_trades) == 0:
            return 0.0
        return winning_trades['Return'].mean()
    else:
        # 如果找不到PnL或Return列，返回0
        return 0.0


def calculate_avg_loss(trades: pd.DataFrame) -> float:
    """
    计算平均亏损
    
    Parameters
    ----------
    trades : pd.DataFrame
        交易记录
        
    Returns
    -------
    float
        平均亏损
    """
    if len(trades) == 0:
        return 0.0
    
    # 根据VectorBT的交易记录结构调整
    if 'PnL' in trades.columns:
        losing_trades = trades[trades['PnL'] < 0]
        if len(losing_trades) == 0:
            return 0.0
        return losing_trades['PnL'].mean()
    elif 'Return' in trades.columns:
        losing_trades = trades[trades['Return'] < 0]
        if len(losing_trades) == 0:
            return 0.0
        return losing_trades['Return'].mean()
    else:
        # 如果找不到PnL或Return列，返回0
        return 0.0


def calculate_largest_win(trades: pd.DataFrame) -> float:
    """
    计算最大盈利
    
    Parameters
    ----------
    trades : pd.DataFrame
        交易记录
        
    Returns
    -------
    float
        最大盈利
    """
    if len(trades) == 0:
        return 0.0
    
    # 根据VectorBT的交易记录结构调整
    if 'PnL' in trades.columns:
        if (trades['PnL'] > 0).any():
            return trades['PnL'].max()
        return 0.0
    elif 'Return' in trades.columns:
        if (trades['Return'] > 0).any():
            return trades['Return'].max()
        return 0.0
    else:
        # 如果找不到PnL或Return列，返回0
        return 0.0


def calculate_largest_loss(trades: pd.DataFrame) -> float:
    """
    计算最大亏损
    
    Parameters
    ----------
    trades : pd.DataFrame
        交易记录
        
    Returns
    -------
    float
        最大亏损
    """
    if len(trades) == 0:
        return 0.0
    
    # 根据VectorBT的交易记录结构调整
    if 'PnL' in trades.columns:
        if (trades['PnL'] < 0).any():
            return trades['PnL'].min()
        return 0.0
    elif 'Return' in trades.columns:
        if (trades['Return'] < 0).any():
            return trades['Return'].min()
        return 0.0
    else:
        # 如果找不到PnL或Return列，返回0
        return 0.0


def calculate_avg_holding_period(trades: pd.DataFrame) -> float:
    """
    计算平均持仓周期
    
    Parameters
    ----------
    trades : pd.DataFrame
        交易记录
        
    Returns
    -------
    float
        平均持仓周期（天）
    """
    if len(trades) == 0:
        return 0.0
    
    # 根据VectorBT的交易记录结构调整
    if 'Entry Time' in trades.columns and 'Exit Time' in trades.columns:
        # 计算每笔交易的持仓周期
        holding_periods = [(exit_time - entry_time).days + 
                          (exit_time - entry_time).seconds / 86400 
                          for entry_time, exit_time in 
                          zip(trades['Entry Time'], trades['Exit Time'])]
        
        if len(holding_periods) == 0:
            return 0.0
        
        return sum(holding_periods) / len(holding_periods)
    else:
        # 如果找不到相应的列，返回0
        return 0.0


def calculate_expectancy(trades: pd.DataFrame) -> float:
    """
    计算期望值
    
    Parameters
    ----------
    trades : pd.DataFrame
        交易记录
        
    Returns
    -------
    float
        期望值
    """
    if len(trades) == 0:
        return 0.0
    
    # 计算胜率
    win_rate = calculate_win_rate(trades)
    
    # 计算平均盈利和平均亏损
    avg_win = calculate_avg_win(trades)
    avg_loss = calculate_avg_loss(trades)
    
    # 如果没有亏损，返回平均盈利
    if avg_loss == 0:
        return avg_win * win_rate
    
    # 计算期望值
    return win_rate * avg_win + (1 - win_rate) * avg_loss


# 基准比较相关指标
def calculate_benchmark_metrics(returns: pd.Series, benchmark: pd.Series) -> Dict[str, float]:
    """
    计算与基准相比的性能指标
    
    Parameters
    ----------
    returns : pd.Series
        策略收益率序列
    benchmark : pd.Series
        基准收益率序列
        
    Returns
    -------
    Dict[str, float]
        包含基准比较指标的字典
    """
    # 确保两个序列具有相同的索引
    returns, benchmark = returns.align(benchmark, join='inner')
    
    if len(returns) < 2:
        return {
            'alpha': 0.0,
            'beta': 0.0,
            'r_squared': 0.0,
            'tracking_error': 0.0,
            'information_ratio': 0.0,
            'excess_return': 0.0
        }
    
    # 计算超额收益
    excess_returns = returns - benchmark
    
    # 计算beta和alpha
    covariance = np.cov(returns, benchmark)[0, 1]
    benchmark_variance = np.var(benchmark)
    
    beta = covariance / benchmark_variance if benchmark_variance != 0 else 0.0
    alpha = returns.mean() - beta * benchmark.mean()
    
    # 计算R方
    correlation = np.corrcoef(returns, benchmark)[0, 1]
    r_squared = correlation ** 2
    
    # 计算跟踪误差
    tracking_error = excess_returns.std()
    
    # 计算信息比率
    information_ratio = excess_returns.mean() / tracking_error if tracking_error != 0 else 0.0
    
    # 计算总超额收益
    total_excess_return = (1 + returns).prod() - (1 + benchmark).prod()
    
    return {
        'alpha': alpha * 252,  # 年化alpha
        'beta': beta,
        'r_squared': r_squared,
        'tracking_error': tracking_error * np.sqrt(252),  # 年化跟踪误差
        'information_ratio': information_ratio * np.sqrt(252),  # 年化信息比率
        'excess_return': total_excess_return
    }


# 统计性指标
def calculate_value_at_risk(returns: pd.Series, confidence: float = 0.95) -> float:
    """
    计算历史Value at Risk
    
    Parameters
    ----------
    returns : pd.Series
        收益率序列
    confidence : float, optional
        置信水平，默认为0.95
        
    Returns
    -------
    float
        Value at Risk
    """
    if len(returns) < 2:
        return 0.0
    
    # 计算给定置信水平的分位数
    var = returns.quantile(1 - confidence)
    
    return var


def calculate_conditional_value_at_risk(returns: pd.Series, confidence: float = 0.95) -> float:
    """
    计算条件Value at Risk（Expected Shortfall）
    
    Parameters
    ----------
    returns : pd.Series
        收益率序列
    confidence : float, optional
        置信水平，默认为0.95
        
    Returns
    -------
    float
        Conditional Value at Risk
    """
    if len(returns) < 2:
        return 0.0
    
    # 计算VaR
    var = calculate_value_at_risk(returns, confidence)
    
    # 计算CVaR
    cvar = returns[returns <= var].mean()
    
    # 修复：检查是否为标量值或空值
    if pd.isna(cvar):
        return var
    
    # 如果是Series，则转换为标量
    if hasattr(cvar, 'iloc'):
        if len(cvar) > 0:
            return cvar.iloc[0]
        else:
            return var
            
    return cvar


def calculate_omega_ratio(returns: pd.Series, threshold: float = 0.0) -> float:
    """
    计算Omega比率
    
    Parameters
    ----------
    returns : pd.Series
        收益率序列
    threshold : float, optional
        阈值，默认为0.0
        
    Returns
    -------
    float
        Omega比率
    """
    if len(returns) < 2:
        return 0.0
    
    # 计算超过阈值的收益和低于阈值的损失
    excess_returns = returns - threshold
    positive_excess = excess_returns[excess_returns > 0].sum()
    negative_excess = abs(excess_returns[excess_returns < 0].sum())
    
    if negative_excess == 0:
        return float('inf') if positive_excess > 0 else 0.0
    
    return positive_excess / negative_excess


# 周期性收益计算
def calculate_monthly_returns(returns: Union[pd.Series, pd.DataFrame]) -> pd.Series:
    """
    计算月度收益率
    
    Parameters
    ----------
    returns : pd.Series or pd.DataFrame
        日收益率序列
        
    Returns
    -------
    pd.Series
        月度收益率序列
    """
    # 处理DataFrame情况
    if isinstance(returns, pd.DataFrame):
        if returns.shape[1] == 1:
            returns = returns.iloc[:, 0]
        else:
            # 使用第一列
            returns = returns.iloc[:, 0]
            print("警告：多列收益率数据，使用第一列")
    
    if not isinstance(returns.index, pd.DatetimeIndex):
        return pd.Series(dtype=float)
    
    # 计算月度收益率
    monthly_returns = returns.resample('ME').apply(lambda x: (1 + x).prod() - 1)
    
    return monthly_returns


def calculate_yearly_returns(returns: Union[pd.Series, pd.DataFrame]) -> pd.Series:
    """
    计算年度收益率
    
    Parameters
    ----------
    returns : pd.Series or pd.DataFrame
        日收益率序列
        
    Returns
    -------
    pd.Series
        年度收益率序列
    """
    # 处理DataFrame情况
    if isinstance(returns, pd.DataFrame):
        if returns.shape[1] == 1:
            returns = returns.iloc[:, 0]
        else:
            # 使用第一列
            returns = returns.iloc[:, 0]
            print("警告：多列收益率数据，使用第一列")
    
    if not isinstance(returns.index, pd.DatetimeIndex):
        return pd.Series(dtype=float)
    
    # 计算年度收益率，使用YE替代已弃用的Y
    yearly_returns = returns.resample('YE').apply(lambda x: (1 + x).prod() - 1)
    
    return yearly_returns


# 添加高级风险评估指标计算函数
def calculate_ulcer_index(equity: pd.Series) -> float:
    """
    计算Ulcer指数 - 衡量回撤的平方和的平方根
    
    Parameters
    ----------
    equity : pd.Series
        净值序列
        
    Returns
    -------
    float
        Ulcer指数
    """
    if len(equity) < 2:
        return 0.0
    
    # 计算历史最高点
    rolling_max = equity.cummax()
    
    # 计算回撤百分比
    drawdown_pct = (equity / rolling_max - 1.0)
    
    # 计算平方和的平方根
    return np.sqrt(np.mean(drawdown_pct ** 2))


def calculate_sterling_ratio(returns: pd.Series, drawdowns: pd.Series, periods_per_year: int = 252) -> float:
    """
    计算Sterling比率 - 年化收益除以平均最大回撤
    
    Parameters
    ----------
    returns : pd.Series
        收益率序列
    drawdowns : pd.Series
        回撤序列
    periods_per_year : int, optional
        年度周期数，默认为252（交易日）
        
    Returns
    -------
    float
        Sterling比率
    """
    if len(returns) < 2 or len(drawdowns) < 2:
        return 0.0
    
    # 计算年化收益
    ann_return = calculate_annualized_return(returns, periods_per_year)
    
    # 取最大回撤的绝对值
    max_dd = abs(calculate_max_drawdown(drawdowns))
    
    if max_dd == 0:
        return 0.0  # 避免除零错误
    
    return ann_return / max_dd


def calculate_burke_ratio(returns: pd.Series, drawdowns: pd.Series, periods_per_year: int = 252) -> float:
    """
    计算Burke比率 - 年化收益除以回撤的平方根和
    
    Parameters
    ----------
    returns : pd.Series
        收益率序列
    drawdowns : pd.Series
        回撤序列
    periods_per_year : int, optional
        年度周期数，默认为252（交易日）
        
    Returns
    -------
    float
        Burke比率
    """
    if len(returns) < 2 or len(drawdowns) < 2:
        return 0.0
    
    # 计算年化收益
    ann_return = calculate_annualized_return(returns, periods_per_year)
    
    # 找出所有回撤期间
    drawdown_periods = drawdowns[drawdowns < 0]
    
    if len(drawdown_periods) == 0:
        return 0.0  # 没有回撤
    
    # 计算回撤的平方根和
    sqrt_sum_squared_dd = np.sqrt(np.sum(drawdown_periods ** 2))
    
    if sqrt_sum_squared_dd == 0:
        return 0.0  # 避免除零错误
    
    return ann_return / sqrt_sum_squared_dd


def calculate_kappa_ratio(returns: pd.Series, threshold: float = 0.0, order: int = 3, periods_per_year: int = 252) -> float:
    """
    计算Kappa比率 - 扩展的Sortino比率，使用更高阶的风险计算
    
    Parameters
    ----------
    returns : pd.Series
        收益率序列
    threshold : float, optional
        收益率阈值，默认为0.0（无风险利率）
    order : int, optional
        风险计算阶数，默认为3
    periods_per_year : int, optional
        年度周期数，默认为252（交易日）
        
    Returns
    -------
    float
        Kappa比率
    """
    if len(returns) < 2:
        return 0.0
    
    # 计算年化收益率
    ann_return = calculate_annualized_return(returns, periods_per_year)
    
    # 计算超过阈值的部分
    excess_return = ann_return - threshold
    
    # 计算低于阈值的收益率
    downside = returns[returns < threshold] - threshold
    
    if len(downside) == 0:
        return float('inf')  # 没有下行波动
    
    # 计算下行离差的n阶矩
    lpm = np.mean((-downside) ** order)
    
    if lpm == 0:
        return float('inf')  # 避免除零错误
    
    # 计算下行风险
    downside_risk = lpm ** (1.0 / order)
    
    return excess_return / downside_risk


def calculate_tail_ratio(returns: pd.Series) -> float:
    """
    计算尾部比率 - 右尾（收益）与左尾（损失）的比率
    
    Parameters
    ----------
    returns : pd.Series
        收益率序列
        
    Returns
    -------
    float
        尾部比率
    """
    if len(returns) < 2:
        return 0.0
    
    # 计算95%分位数和5%分位数
    q_right = np.abs(np.percentile(returns, 95))
    q_left = np.abs(np.percentile(returns, 5))
    
    if q_left == 0:
        return float('inf')  # 避免除零错误
    
    return q_right / q_left


def calculate_downside_deviation(returns: pd.Series, threshold: float = 0.0) -> float:
    """
    计算下行偏差 - 低于阈值的收益率的标准差
    
    Parameters
    ----------
    returns : pd.Series
        收益率序列
    threshold : float, optional
        收益率阈值，默认为0.0
        
    Returns
    -------
    float
        下行偏差
    """
    if len(returns) < 2:
        return 0.0
    
    # 计算低于阈值的收益率
    downside = returns[returns < threshold]
    
    if len(downside) == 0:
        return 0.0  # 没有下行波动
    
    # 计算下行标准差
    return np.sqrt(np.mean((downside - threshold) ** 2))


def calculate_max_consecutive_losses(returns: pd.Series) -> int:
    """
    计算最大连续亏损次数
    
    Parameters
    ----------
    returns : pd.Series
        收益率序列
        
    Returns
    -------
    int
        最大连续亏损次数
    """
    if len(returns) == 0:
        return 0
    
    # 创建损益序列
    is_loss = returns < 0
    
    # 计算连续亏损次数
    consecutive_losses = []
    count = 0
    
    for loss in is_loss:
        if loss:
            count += 1
        else:
            if count > 0:
                consecutive_losses.append(count)
            count = 0
    
    # 添加最后一段连续亏损（如果有）
    if count > 0:
        consecutive_losses.append(count)
    
    # 返回最大连续亏损次数
    return max(consecutive_losses) if consecutive_losses else 0


def calculate_drawdowns(returns: pd.Series) -> pd.Series:
    """
    计算回撤序列
    
    Parameters
    ----------
    returns : pd.Series
        收益率序列
        
    Returns
    -------
    pd.Series
        回撤序列
    """
    if len(returns) == 0:
        return pd.Series()
    
    # 计算累积收益
    wealth_index = (1 + returns).cumprod()
    
    # 计算累积新高
    cumulative_max = wealth_index.cummax()
    
    # 计算回撤
    drawdowns = wealth_index / cumulative_max - 1
    
    return drawdowns


def calculate_alpha_beta(returns: pd.Series, benchmark: pd.Series) -> Tuple[float, float]:
    """
    计算Alpha和Beta系数

    Parameters
    ----------
    returns : pd.Series
        策略收益率序列
    benchmark : pd.Series
        基准指数收益率序列
        
    Returns
    -------
    Tuple[float, float]
        (alpha, beta) 元组
    """
    # 确保日期对齐
    common_index = returns.index.intersection(benchmark.index)
    if len(common_index) == 0:
        return 0.0, 0.0
    
    strategy_returns = returns.loc[common_index]
    benchmark_returns = benchmark.loc[common_index]
    
    # 计算年化Alpha和Beta
    X = benchmark_returns.values
    X = np.vstack([np.ones(len(X)), X]).T
    y = strategy_returns.values
    
    try:
        # 使用最小二乘法求解线性回归
        alpha, beta = np.linalg.lstsq(X, y, rcond=None)[0]
        
        # 转换为年化Alpha（假设数据为日频）
        alpha = (1 + alpha) ** 252 - 1
        
        return alpha, beta
    except:
        return 0.0, 0.0


def calculate_information_ratio(returns: pd.Series, benchmark: pd.Series, periods_per_year: int = 252) -> float:
    """
    计算信息比率

    Parameters
    ----------
    returns : pd.Series
        策略收益率序列
    benchmark : pd.Series
        基准指数收益率序列
    periods_per_year : int, optional
        年度周期数，默认为252（交易日）
        
    Returns
    -------
    float
        信息比率
    """
    # 确保日期对齐
    common_index = returns.index.intersection(benchmark.index)
    if len(common_index) == 0:
        return 0.0
    
    strategy_returns = returns.loc[common_index]
    benchmark_returns = benchmark.loc[common_index]
    
    # 计算超额收益
    excess_returns = strategy_returns - benchmark_returns
    
    # 计算跟踪误差
    tracking_error = calculate_tracking_error(strategy_returns, benchmark_returns)
    
    if tracking_error == 0:
        return 0.0
    
    # 计算信息比率
    return excess_returns.mean() * np.sqrt(periods_per_year) / tracking_error


def calculate_tracking_error(returns: pd.Series, benchmark: pd.Series, periods_per_year: int = 252) -> float:
    """
    计算跟踪误差

    Parameters
    ----------
    returns : pd.Series
        策略收益率序列
    benchmark : pd.Series
        基准指数收益率序列
    periods_per_year : int, optional
        年度周期数，默认为252（交易日）
        
    Returns
    -------
    float
        跟踪误差
    """
    # 确保日期对齐
    common_index = returns.index.intersection(benchmark.index)
    if len(common_index) == 0:
        return 0.0
    
    strategy_returns = returns.loc[common_index]
    benchmark_returns = benchmark.loc[common_index]
    
    # 计算超额收益
    excess_returns = strategy_returns - benchmark_returns
    
    # 计算跟踪误差
    tracking_error = excess_returns.std() * np.sqrt(periods_per_year)
    
    return tracking_error


def calculate_metrics_from_returns(returns: pd.Series, 
                                risk_free_rate: float = 0.0,
                                benchmark: Optional[pd.Series] = None) -> Dict[str, Any]:
    """
    直接从收益率序列计算性能指标
    
    Parameters
    ----------
    returns : pd.Series
        收益率序列
    risk_free_rate : float, optional
        无风险利率，默认为0.0
    benchmark : pd.Series, optional
        基准收益率序列，默认为None
        
    Returns
    -------
    Dict[str, Any]
        包含各种性能指标的字典
    """
    # 初始化指标字典
    metrics = {}
    
    # 检查数据有效性
    if len(returns) < 2:
        return metrics
    
    # 计算回撤序列
    drawdowns = calculate_drawdowns(returns)
    
    # 计算净值曲线
    equity = (1 + returns).cumprod()
    
    # 计算基础统计指标
    metrics.update({
        'start_date': returns.index[0],
        'end_date': returns.index[-1],
        'trading_days': len(returns),
        'total_return': calculate_total_return(equity),
        'annualized_return': calculate_annualized_return(returns),
        'daily_sharpe': calculate_sharpe_ratio(returns, risk_free_rate=risk_free_rate, periods_per_year=252),
        'daily_sortino': calculate_sortino_ratio(returns, risk_free_rate=risk_free_rate, periods_per_year=252),
        'volatility': calculate_volatility(returns),
        'calmar_ratio': calculate_calmar_ratio(returns, drawdowns),
        'max_drawdown': calculate_max_drawdown(drawdowns),
        'avg_drawdown': calculate_avg_drawdown(drawdowns),
        'max_drawdown_duration': calculate_max_drawdown_duration(drawdowns, returns.index),
    })
    
    # 基准比较指标
    if benchmark is not None:
        metrics.update(calculate_benchmark_metrics(returns, benchmark))
    
    # 统计性指标
    metrics.update({
        'skew': returns.skew() if len(returns) > 2 else 0,
        'kurtosis': returns.kurtosis() if len(returns) > 3 else 0,
        'var_95': calculate_value_at_risk(returns, 0.95),
        'var_99': calculate_value_at_risk(returns, 0.99),
        'cvar_95': calculate_conditional_value_at_risk(returns, 0.95),
        'omega_ratio': calculate_omega_ratio(returns, risk_free_rate)
    })
    
    # 高级风险评估指标
    metrics.update({
        'ulcer_index': calculate_ulcer_index(equity),
        'sterling_ratio': calculate_sterling_ratio(returns, drawdowns),
        'burke_ratio': calculate_burke_ratio(returns, drawdowns),
        'kappa_ratio': calculate_kappa_ratio(returns, risk_free_rate, 3),
        'tail_ratio': calculate_tail_ratio(returns),
        'downside_deviation': calculate_downside_deviation(returns),
        'max_consecutive_losses': calculate_max_consecutive_losses(returns)
    })
    
    # 计算月度收益和年度收益
    if len(returns) > 20:  # 确保数据足够
        monthly_returns = calculate_monthly_returns(returns)
        yearly_returns = calculate_yearly_returns(returns)
        
        metrics['monthly_returns'] = monthly_returns
        metrics['yearly_returns'] = yearly_returns
    
    return metrics 