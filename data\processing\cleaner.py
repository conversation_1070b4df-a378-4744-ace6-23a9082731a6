"""
数据清洗模块

提供对数据进行清洗的功能，包括缺失值填充、异常值处理、重复数据删除和时间戳对齐等。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any, Callable, Literal

from data.structures import OHLCVColumns


def fill_missing_values(df: pd.DataFrame, method: str = 'ffill', columns: Optional[List[str]] = None) -> pd.DataFrame:
    """
    填充DataFrame中的缺失值
    
    Args:
        df: 要处理的DataFrame
        method: 填充方法，可选 'ffill'(前向填充), 'bfill'(后向填充), 'interpolate'(插值), 'zero'(零值填充), 'mean'(均值填充)
        columns: 要填充的列，默认为None表示处理所有列
        
    Returns:
        填充后的DataFrame
    """
    # 复制DataFrame以避免修改原始数据
    df_filled = df.copy()
    
    # 如果未指定列，则处理所有列
    if columns is None:
        target_columns = df_filled.columns
    else:
        # 确保指定的列存在于DataFrame中
        target_columns = [col for col in columns if col in df_filled.columns]
        if not target_columns:
            return df_filled  # 如果没有有效的列，则直接返回原始数据
    
    # 根据method选择填充方法
    if method == 'ffill':
        df_filled[target_columns] = df_filled[target_columns].ffill()
    elif method == 'bfill':
        df_filled[target_columns] = df_filled[target_columns].bfill()
    elif method == 'interpolate':
        df_filled[target_columns] = df_filled[target_columns].interpolate(method='linear')
    elif method == 'zero':
        df_filled[target_columns] = df_filled[target_columns].fillna(0)
    elif method == 'mean':
        # 对每一列计算均值并填充
        for col in target_columns:
            if pd.api.types.is_numeric_dtype(df_filled[col]):
                mean_value = df_filled[col].mean()
                df_filled[col] = df_filled[col].fillna(mean_value)
    else:
        raise ValueError(f"不支持的填充方法: {method}")
    
    # 对于OHLCV数据的特殊处理
    if all(col in df_filled.columns for col in [OHLCVColumns.OPEN, OHLCVColumns.HIGH, OHLCVColumns.LOW, OHLCVColumns.CLOSE]):
        # 确保HIGH >= OPEN, CLOSE, LOW
        df_filled[OHLCVColumns.HIGH] = df_filled[[OHLCVColumns.HIGH, OHLCVColumns.OPEN, OHLCVColumns.CLOSE]].max(axis=1)
        # 确保LOW <= OPEN, CLOSE, HIGH
        df_filled[OHLCVColumns.LOW] = df_filled[[OHLCVColumns.LOW, OHLCVColumns.OPEN, OHLCVColumns.CLOSE]].min(axis=1)
    
    return df_filled


def remove_outliers(df: pd.DataFrame, 
                   method: str = 'zscore', 
                   threshold: float = 3.0, 
                   action: str = 'clip', 
                   columns: Optional[List[str]] = None) -> pd.DataFrame:
    """
    处理DataFrame中的异常值
    
    Args:
        df: 要处理的DataFrame
        method: 异常值检测方法，'zscore' 或 'iqr'
        threshold: 异常值阈值
        action: 处理方法，'clip'(截断), 'remove'(移除值替换为NaN), 'replace'(替换为NaN后再填充)
        columns: 要处理的列，默认为None表示处理所有数值列
        
    Returns:
        处理后的DataFrame
    """
    # 复制DataFrame以避免修改原始数据
    df_cleaned = df.copy()
    
    # 如果未指定列，则处理所有数值列
    if columns is None:
        target_columns = df_cleaned.select_dtypes(include=[np.number]).columns.tolist()
    else:
        # 确保指定的列存在于DataFrame中且为数值类型
        target_columns = [col for col in columns 
                         if col in df_cleaned.columns and pd.api.types.is_numeric_dtype(df_cleaned[col])]
        if not target_columns:
            return df_cleaned  # 如果没有有效的列，则直接返回原始数据
    
    # 对每一列进行异常值检测和处理
    for col in target_columns:
        series = df_cleaned[col]
        mask = pd.Series(False, index=series.index)  # 初始化为全False的掩码
        
        # 使用z-score方法检测异常值
        if method == 'zscore':
            z_scores = (series - series.mean()) / series.std()
            mask = abs(z_scores) > threshold
        # 使用IQR方法检测异常值
        elif method == 'iqr':
            q1 = series.quantile(0.25)
            q3 = series.quantile(0.75)
            iqr = q3 - q1
            lower_bound = q1 - threshold * iqr
            upper_bound = q3 + threshold * iqr
            mask = (series < lower_bound) | (series > upper_bound)
        else:
            raise ValueError(f"不支持的异常值检测方法: {method}")
        
        # 处理异常值
        if action == 'clip':
            # 根据检测方法获取上下界
            if method == 'zscore':
                mean = series.mean()
                std = series.std()
                lower_bound = mean - threshold * std
                upper_bound = mean + threshold * std
            # 已经在IQR中计算了上下界
            
            # 截断超出范围的值
            df_cleaned.loc[df_cleaned.index[mask], col] = np.clip(
                series[mask], lower_bound, upper_bound
            )
        elif action == 'remove':
            # 将异常值替换为NaN，不删除行
            df_cleaned.loc[df_cleaned.index[mask], col] = np.nan
        elif action == 'replace':
            # 将异常值替换为NaN，然后使用前向填充方法填充
            df_cleaned.loc[df_cleaned.index[mask], col] = np.nan
            df_cleaned[col] = df_cleaned[col].ffill().bfill()
        else:
            raise ValueError(f"不支持的异常值处理方法: {action}")
    
    return df_cleaned


def handle_outliers(df: pd.DataFrame, 
                   method: str = 'zscore', 
                   threshold: float = 3.0, 
                   action: str = 'clip', 
                   columns: Optional[List[str]] = None) -> pd.DataFrame:
    """
    处理DataFrame中的异常值
    
    Args:
        df: 要处理的DataFrame
        method: 异常值检测方法，'zscore' 或 'iqr'
        threshold: 异常值阈值
        action: 处理方法，'clip'(截断), 'remove'(移除), 'replace'(替换为NaN后再填充)
        columns: 要处理的列，默认为None表示处理所有数值列
        
    Returns:
        处理后的DataFrame
    """
    # 调用remove_outliers函数，保持向后兼容
    return remove_outliers(df, method=method, threshold=threshold, action=action, columns=columns)


def remove_duplicates(df: pd.DataFrame, keep: Union[Literal['first', 'last'], Literal[False]] = 'last') -> pd.DataFrame:
    """
    移除DataFrame中的重复行
    
    Args:
        df: 要处理的DataFrame
        keep: 保留哪个重复项，'first'(第一个), 'last'(最后一个), False(全部删除)
        
    Returns:
        处理后的DataFrame
    """
    # 检查是否有重复的索引
    if df.index.duplicated().any():
        return df[~df.index.duplicated(keep=keep)]
    return df.copy()


def align_timestamps(df: pd.DataFrame, freq: str, method: str = 'nearest') -> pd.DataFrame:
    """
    对齐时间戳到固定频率
    
    Args:
        df: 要处理的DataFrame
        freq: 目标频率，如'1H', '1D', '5min'等
        method: 对齐方法，'nearest'(最近值), 'forward'(后向填充), 'backward'(前向填充)
        
    Returns:
        处理后的DataFrame
    """
    # 确保DataFrame索引为时间类型
    if not isinstance(df.index, pd.DatetimeIndex):
        raise ValueError("DataFrame必须以时间为索引")
    
    # 简单的频率转换，处理字符串形式的offset
    if isinstance(freq, str):
        clean_freq = freq
    else:
        # 如果是pandas offset对象，转换为标准字符串格式
        try:
            clean_freq = freq.freqstr if hasattr(freq, 'freqstr') else str(freq)
        except Exception:
            # 如果转换失败，使用一个安全的默认值
            clean_freq = '1D'
    
    # 创建理想的时间范围
    start_time = df.index.min()
    end_time = df.index.max()
    
    try:
        ideal_range = pd.date_range(start=start_time, end=end_time, freq=clean_freq)
    except ValueError as e:
        # 如果频率解析失败，尝试使用更安全的频率
        print(f"警告: 频率 '{freq}' 解析失败: {e}. 使用默认频率 '1D'")
        ideal_range = pd.date_range(start=start_time, end=end_time, freq='1D')
    
    # 如果没有数据点需要对齐，直接返回原始数据
    if len(ideal_range) == 0:
        return df.copy()
    
    # 使用不同的对齐方法
    if method == 'nearest':
        # 使用最近邻方法
        # 对于每一个理想时间点，找到数据中最接近的时间点
        result = pd.DataFrame(index=ideal_range)
        
        for col in df.columns:
            # 对齐每一列
            result[col] = df[col].reindex(ideal_range, method='nearest')
    
    # 对于'forward'和'backward'方法，使用asof合并
    else:
        # 安全获取频率的总秒数
        try:
            freq_seconds = pd.Timedelta(clean_freq).total_seconds()  # 获取频率的总秒数
        except ValueError:
            # 如果获取失败，使用一个默认值
            freq_seconds = pd.Timedelta('1D').total_seconds()
            
        half_freq_seconds = int(freq_seconds / 2)  # 取整数，避免float转换问题
        
        if method == 'forward':
            direction = 'forward'
            tolerance = pd.Timedelta(seconds=half_freq_seconds)
        elif method == 'backward':
            direction = 'backward'
            tolerance = pd.Timedelta(seconds=half_freq_seconds)
        else:
            raise ValueError(f"不支持的对齐方法: {method}")
        
        # 创建结果DataFrame
        result = pd.DataFrame(index=ideal_range)
        
        # 使用merge_asof进行对齐
        df_sorted = df.sort_index()
        for col in df.columns:
            temp_df = pd.DataFrame({col: df_sorted[col]})
            temp_df = temp_df.reset_index()
            temp_df.columns = ['timestamp', col]
            
            # 准备理想时间范围
            ideal_df = pd.DataFrame({'timestamp': ideal_range})
            
            # 使用merge_asof对齐
            aligned = pd.merge_asof(
                ideal_df, temp_df, on='timestamp',
                direction=direction, tolerance=tolerance
            )
            
            # 更新结果
            result[col] = aligned[col].values
        
        return result
    
    return result 