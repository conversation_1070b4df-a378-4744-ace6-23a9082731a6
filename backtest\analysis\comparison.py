#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
回测结果比较分析模块

提供回测结果的比较分析功能，包括多策略比较、基准对比、参数敏感性分析等。
"""

from typing import Dict, Any, Union, Optional, List, Tuple, Callable
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings

from ..base import BacktestResults
from .metrics import calculate_metrics


class StrategyComparison:
    """
    策略比较分析器
    
    用于比较多个回测结果的性能和特征。
    """
    
    def __init__(self, 
               strategies: Dict[str, BacktestResults] = None,
               benchmark: Optional[pd.Series] = None,
               risk_free_rate: float = 0.0):
        """
        初始化策略比较分析器
        
        Parameters
        ----------
        strategies : Dict[str, BacktestResults], optional
            策略名称和回测结果的字典，默认为None
        benchmark : pd.Series, optional
            基准指数，默认为None
        risk_free_rate : float, optional
            无风险利率，默认为0.0
        """
        self.strategies = strategies or {}
        self.benchmark = benchmark
        self.risk_free_rate = risk_free_rate
        self._metrics_cache = {}
        
    def add_strategy(self, name: str, results: BacktestResults) -> None:
        """
        添加策略
        
        Parameters
        ----------
        name : str
            策略名称
        results : BacktestResults
            回测结果
        """
        self.strategies[name] = results
        # 清除缓存
        self._metrics_cache = {}
        
    def set_benchmark(self, benchmark: pd.Series, name: str = 'Benchmark') -> None:
        """
        设置基准指数
        
        Parameters
        ----------
        benchmark : pd.Series
            基准指数
        name : str, optional
            基准名称，默认为'Benchmark'
        """
        self.benchmark = benchmark
        # 清除缓存
        self._metrics_cache = {}
        
    def compare_returns(self, 
                      normalize: bool = True, 
                      figsize: tuple = (12, 8),
                      title: str = 'Strategy Returns Comparison') -> plt.Figure:
        """
        比较收益曲线
        
        Parameters
        ----------
        normalize : bool, optional
            是否规范化收益，默认为True
        figsize : tuple, optional
            图表大小，默认为(12, 8)
        title : str, optional
            图表标题，默认为'Strategy Returns Comparison'
            
        Returns
        -------
        plt.Figure
            图表对象
        """
        if not self.strategies:
            raise ValueError("No strategies to compare")
            
        fig, ax = plt.subplots(figsize=figsize)
        
        for name, results in self.strategies.items():
            # 获取净值曲线
            equity = results.equity() if callable(results.equity) else results.equity
            
            # 规范化净值
            if normalize:
                equity = equity / equity.iloc[0]
                
            # 绘制曲线
            ax.plot(equity.index, equity, label=name, linewidth=2, alpha=0.8)
        
        # 如果有基准指数，绘制基准
        if self.benchmark is not None:
            benchmark_curve = self.benchmark.copy()
            
            if normalize:
                benchmark_curve = benchmark_curve / benchmark_curve.iloc[0]
                
            ax.plot(benchmark_curve.index, benchmark_curve, 
                   label='Benchmark', linewidth=2, alpha=0.7, linestyle='--')
        
        # 设置图表属性
        ax.set_title(title, fontsize=15)
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('Normalized Value' if normalize else 'Value', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend(loc='best')
        
        plt.tight_layout()
        return fig
    
    def compare_drawdowns(self, 
                        figsize: tuple = (12, 8),
                        title: str = 'Strategy Drawdowns Comparison') -> plt.Figure:
        """
        比较回撤曲线
        
        Parameters
        ----------
        figsize : tuple, optional
            图表大小，默认为(12, 8)
        title : str, optional
            图表标题，默认为'Strategy Drawdowns Comparison'
            
        Returns
        -------
        plt.Figure
            图表对象
        """
        if not self.strategies:
            raise ValueError("No strategies to compare")
            
        fig, ax = plt.subplots(figsize=figsize)
        
        for name, results in self.strategies.items():
            # 获取回撤序列
            get_drawdowns = results.get_drawdowns
            drawdowns = get_drawdowns() if callable(get_drawdowns) else get_drawdowns
            
            # 绘制曲线
            ax.plot(drawdowns.index, drawdowns, label=name, linewidth=2, alpha=0.8)
        
        # 如果有基准指数，计算并绘制基准回撤
        if self.benchmark is not None:
            from .metrics import calculate_drawdowns
            benchmark_drawdowns = calculate_drawdowns(self.benchmark)
            ax.plot(benchmark_drawdowns.index, benchmark_drawdowns, 
                   label='Benchmark', linewidth=2, alpha=0.7, linestyle='--')
        
        # 设置图表属性
        ax.set_title(title, fontsize=15)
        ax.set_xlabel('Date', fontsize=12)
        ax.set_ylabel('Drawdown', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend(loc='best')
        
        plt.tight_layout()
        return fig
    
    def compare_metrics(self, 
                      metrics: Optional[List[str]] = None) -> pd.DataFrame:
        """
        比较性能指标
        
        Parameters
        ----------
        metrics : List[str], optional
            要比较的指标列表，默认为None（比较所有核心指标）
            
        Returns
        -------
        pd.DataFrame
            比较结果表格
        """
        if not self.strategies:
            raise ValueError("No strategies to compare")
            
        # 默认比较指标
        default_metrics = [
            'total_return', 'annualized_return', 'daily_sharpe', 'daily_sortino',
            'max_drawdown', 'volatility', 'calmar_ratio', 'win_rate'
        ]
        
        metrics_to_compare = metrics or default_metrics
        
        # 计算所有策略的指标
        if not self._metrics_cache:
            for name, results in self.strategies.items():
                self._metrics_cache[name] = calculate_metrics(
                    results, risk_free_rate=self.risk_free_rate, benchmark=self.benchmark
                )
        
        # 提取要比较的指标
        comparison_data = {}
        for name, metrics_dict in self._metrics_cache.items():
            available_metrics = {
                k: metrics_dict.get(k) 
                for k in metrics_to_compare 
                if k in metrics_dict
            }
            comparison_data[name] = available_metrics
        
        # 创建比较表格
        df = pd.DataFrame(comparison_data).T
        
        # 格式化指标
        format_dict = {
            'total_return': '{:.2%}',
            'annualized_return': '{:.2%}',
            'max_drawdown': '{:.2%}',
            'volatility': '{:.2%}',
            'win_rate': '{:.2%}',
            'daily_sharpe': '{:.2f}',
            'daily_sortino': '{:.2f}',
            'calmar_ratio': '{:.2f}'
        }
        
        for col in df.columns:
            if col in format_dict:
                df[col] = df[col].map(lambda x: format_dict[col].format(x) if pd.notnull(x) else 'N/A')
        
        # 重命名列名以保持兼容性
        column_mapping = {
            'daily_sharpe': 'sharpe_ratio',
            'daily_sortino': 'sortino_ratio',
            'avg_profit_per_trade': 'profit_factor'  # 如果没有计算profit_factor，使用avg_profit_per_trade
        }
        
        # 应用列名映射
        df = df.rename(columns=column_mapping)
        
        # 如果存在计算好的profit_factor，优先使用
        for name, metrics_dict in self._metrics_cache.items():
            if 'profit_factor' in metrics_dict and 'profit_factor' not in df.columns:
                # 添加profit_factor列
                profit_factors = {
                    name: metrics_dict.get('profit_factor', 'N/A') 
                    for name, metrics_dict in self._metrics_cache.items()
                }
                df['profit_factor'] = pd.Series(profit_factors)
        
        return df
    
    def plot_metrics_comparison(self, 
                              metrics: Optional[List[str]] = None,
                              kind: str = 'bar',
                              figsize: tuple = (15, 10),
                              **kwargs) -> plt.Figure:
        """
        绘制指标比较图
        
        Parameters
        ----------
        metrics : List[str], optional
            要比较的指标列表，默认为None（比较所有核心指标）
        kind : str, optional
            图表类型，可选值为'bar'、'radar'，默认为'bar'
        figsize : tuple, optional
            图表大小，默认为(15, 10)
        **kwargs : dict
            其他绘图参数
            
        Returns
        -------
        plt.Figure
            图表对象
        """
        if not self.strategies:
            raise ValueError("No strategies to compare")
            
        # 默认比较指标
        default_metrics = [
            'total_return', 'annualized_return', 'daily_sharpe', 'daily_sortino',
            'max_drawdown', 'volatility', 'calmar_ratio'
        ]
        
        metrics_to_compare = metrics or default_metrics
        
        # 计算所有策略的指标
        if not self._metrics_cache:
            for name, results in self.strategies.items():
                self._metrics_cache[name] = calculate_metrics(
                    results, risk_free_rate=self.risk_free_rate, benchmark=self.benchmark
                )
        
        # 提取要比较的指标
        comparison_data = {}
        for name, metrics_dict in self._metrics_cache.items():
            available_metrics = {
                k: metrics_dict.get(k) 
                for k in metrics_to_compare 
                if k in metrics_dict
            }
            comparison_data[name] = available_metrics
        
        # 创建比较表格
        df = pd.DataFrame(comparison_data).T
        
        # 处理缺失值
        df = df.fillna(0)
        
        if kind == 'bar':
            return self._plot_bar_comparison(df, figsize=figsize, **kwargs)
        elif kind == 'radar':
            return self._plot_radar_comparison(df, figsize=figsize, **kwargs)
        else:
            raise ValueError(f"Unsupported chart type: {kind}, must be 'bar' or 'radar'")
    
    def _plot_bar_comparison(self, 
                           df: pd.DataFrame, 
                           figsize: tuple = (15, 10),
                           **kwargs) -> plt.Figure:
        """
        绘制柱状图比较
        
        Parameters
        ----------
        df : pd.DataFrame
            比较数据
        figsize : tuple, optional
            图表大小，默认为(15, 10)
        **kwargs : dict
            其他绘图参数
            
        Returns
        -------
        plt.Figure
            图表对象
        """
        n_metrics = len(df.columns)
        n_strategies = len(df)
        
        # 设置绘图风格
        sns.set_style("whitegrid")
        
        # 创建图表
        fig, axes = plt.subplots(n_metrics, 1, figsize=figsize)
        if n_metrics == 1:
            axes = [axes]
        
        # 设置颜色
        colors = sns.color_palette("husl", n_strategies)
        
        # 为每个指标绘制一个子图
        for i, metric in enumerate(df.columns):
            sns.barplot(x=df.index, y=df[metric], ax=axes[i], palette=colors)
            
            axes[i].set_title(f"{metric.replace('_', ' ').title()}", fontsize=12)
            axes[i].set_xlabel('Strategy', fontsize=10)
            axes[i].set_ylabel('Value', fontsize=10)
            
            # 旋转x轴标签
            for label in axes[i].get_xticklabels():
                label.set_rotation(45)
                label.set_ha('right')
        
        plt.tight_layout()
        return fig
    
    def _plot_radar_comparison(self,
                             df: pd.DataFrame, 
                             figsize: tuple = (10, 10),
                             **kwargs) -> plt.Figure:
        """
        绘制雷达图比较
        
        Parameters
        ----------
        df : pd.DataFrame
            比较数据
        figsize : tuple, optional
            图表大小，默认为(10, 10)
        **kwargs : dict
            其他绘图参数
            
        Returns
        -------
        plt.Figure
            图表对象
        """
        # 标准化数据
        df_scaled = df.copy()
        
        # 优化指标方向（确保所有指标都是越大越好）
        for col in df_scaled.columns:
            if col in ['max_drawdown', 'volatility']:
                df_scaled[col] = -df_scaled[col]
        
        # Min-Max标准化
        for col in df_scaled.columns:
            min_val = df_scaled[col].min()
            max_val = df_scaled[col].max()
            if max_val - min_val > 0:
                df_scaled[col] = (df_scaled[col] - min_val) / (max_val - min_val)
            else:
                df_scaled[col] = 0.5
        
        # 设置绘图风格
        plt.style.use('default')
        
        # 创建图表
        fig, ax = plt.subplots(figsize=figsize, subplot_kw=dict(polar=True))
        
        # 获取特征和策略名称
        categories = df_scaled.columns
        n_categories = len(categories)
        strategies = df_scaled.index
        
        # 计算每个类别的角度
        angles = np.linspace(0, 2*np.pi, n_categories, endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形
        
        # 设置雷达图的角度和标签
        ax.set_theta_offset(np.pi / 2)
        ax.set_theta_direction(-1)
        
        # 设置类别标签
        plt.xticks(angles[:-1], categories, size=12)
        
        # 设置y轴刻度
        ax.set_ylim(0, 1)
        ax.set_yticks([0.2, 0.4, 0.6, 0.8])
        ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8'])
        
        # 设置网格样式
        ax.grid(True, linestyle='-', alpha=0.2)
        
        # 设置颜色
        colors = sns.color_palette("husl", len(strategies))
        
        # 绘制每个策略的数据
        for i, strategy in enumerate(strategies):
            values = df_scaled.loc[strategy].values.tolist()
            values += values[:1]  # 闭合图形
            
            ax.plot(angles, values, linewidth=2, linestyle='solid', label=strategy, color=colors[i])
            ax.fill(angles, values, alpha=0.1, color=colors[i])
        
        # 添加图例
        plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
        
        plt.tight_layout()
        return fig
    
    def parameter_sensitivity(self, 
                            strategy_name: str,
                            parameter_name: str,
                            parameter_values: List[Any],
                            metric: str = 'annualized_return',
                            use_cached_results: bool = False) -> pd.DataFrame:
        """
        参数敏感性分析
        
        Parameters
        ----------
        strategy_name : str
            策略名称（结果中包含的策略名称应遵循"策略名称 - 参数值"的格式）
        parameter_name : str
            参数名称（用于显示）
        parameter_values : List[Any]
            参数取值列表
        metric : str, optional
            评估指标，默认为'annualized_return'
        use_cached_results : bool, optional
            是否使用缓存的结果，默认为False
            
        Returns
        -------
        pd.DataFrame
            参数敏感性结果表格
        """
        if not use_cached_results:
            if not self.strategies:
                raise ValueError("No strategies to analyze")
            
            # 检查策略名称格式
            valid_strategies = [name for name in self.strategies.keys() 
                             if name.startswith(strategy_name)]
            
            if not valid_strategies:
                raise ValueError(f"No strategies found with base name '{strategy_name}'")
        
        # 提取参数值和对应的指标
        param_values = []
        metric_values = []
        
        for value in parameter_values:
            strategy_key = f"{strategy_name} - {value}"
            
            if strategy_key in self.strategies:
                results = self.strategies[strategy_key]
                
                # 计算指标
                metrics = calculate_metrics(results, risk_free_rate=self.risk_free_rate)
                
                if metric in metrics:
                    param_values.append(value)
                    metric_values.append(metrics[metric])
        
        # 创建参数敏感性表格
        sensitivity_df = pd.DataFrame({
            parameter_name: param_values,
            metric: metric_values
        })
        
        return sensitivity_df
    
    def plot_parameter_sensitivity(self,
                                 strategy_name: str,
                                 parameter_name: str,
                                 parameter_values: List[Any],
                                 metrics: List[str] = None,
                                 figsize: tuple = (12, 8),
                                 **kwargs) -> plt.Figure:
        """
        绘制参数敏感性图
        
        Parameters
        ----------
        strategy_name : str
            策略名称
        parameter_name : str
            参数名称
        parameter_values : List[Any]
            参数取值列表
        metrics : List[str], optional
            评估指标列表，默认为None（使用['annualized_return', 'sharpe_ratio', 'max_drawdown']）
        figsize : tuple, optional
            图表大小，默认为(12, 8)
        **kwargs : dict
            其他绘图参数
            
        Returns
        -------
        plt.Figure
            图表对象
        """
        if not metrics:
            metrics = ['annualized_return', 'daily_sharpe', 'max_drawdown']
        
        # 创建图表
        fig, axes = plt.subplots(len(metrics), 1, figsize=figsize)
        if len(metrics) == 1:
            axes = [axes]
        
        for i, metric in enumerate(metrics):
            try:
                sensitivity_df = self.parameter_sensitivity(
                    strategy_name, parameter_name, parameter_values, metric
                )
                
                # 绘制参数敏感性曲线
                sns.lineplot(x=parameter_name, y=metric, data=sensitivity_df, 
                          ax=axes[i], marker='o', linewidth=2)
                
                # 设置标题和标签
                axes[i].set_title(f"{metric.replace('_', ' ').title()} vs {parameter_name}", fontsize=12)
                axes[i].set_xlabel(parameter_name, fontsize=10)
                axes[i].set_ylabel(metric, fontsize=10)
                axes[i].grid(True, alpha=0.3)
                
                # 对于回撤，反转y轴（使值越小显示越高）
                if 'drawdown' in metric.lower():
                    axes[i].invert_yaxis()
            
            except Exception as e:
                axes[i].text(0.5, 0.5, f"Error: {str(e)}", 
                          horizontalalignment='center',
                          verticalalignment='center',
                          transform=axes[i].transAxes)
        
        plt.tight_layout()
        return fig


def compare_strategies(strategies: Dict[str, BacktestResults],
                     benchmark: Optional[pd.Series] = None,
                     risk_free_rate: float = 0.0,
                     metrics: Optional[List[str]] = None,
                     output_format: str = 'dataframe') -> Union[pd.DataFrame, Dict[str, Any]]:
    """
    比较多个策略的性能
    
    Parameters
    ----------
    strategies : Dict[str, BacktestResults]
        策略名称和回测结果的字典
    benchmark : pd.Series, optional
        基准指数，默认为None
    risk_free_rate : float, optional
        无风险利率，默认为0.0
    metrics : List[str], optional
        要比较的指标列表，默认为None（比较所有核心指标）
    output_format : str, optional
        输出格式，可选值为'dataframe'或'dict'，默认为'dataframe'
        
    Returns
    -------
    Union[pd.DataFrame, Dict[str, Any]]
        比较结果
    """
    comparison = StrategyComparison(strategies, benchmark, risk_free_rate)
    
    # 获取比较表格
    comparison_df = comparison.compare_metrics(metrics)
    
    if output_format.lower() == 'dataframe':
        return comparison_df
    elif output_format.lower() == 'dict':
        return comparison_df.to_dict()
    else:
        raise ValueError(f"Unsupported output format: {output_format}, must be 'dataframe' or 'dict'")


def compare_time_periods(results: BacktestResults,
                      periods: Dict[str, Tuple[datetime, datetime]],
                      risk_free_rate: float = 0.0,
                      metrics: Optional[List[str]] = None) -> pd.DataFrame:
    """
    比较同一策略在不同时间段的表现
    
    Parameters
    ----------
    results : BacktestResults
        回测结果
    periods : Dict[str, Tuple[datetime, datetime]]
        时间段名称和起止时间的字典
    risk_free_rate : float, optional
        无风险利率，默认为0.0
    metrics : List[str], optional
        要比较的指标列表，默认为None（比较所有核心指标）
        
    Returns
    -------
    pd.DataFrame
        不同时间段的比较结果
    """
    # 默认比较指标
    default_metrics = [
        'total_return', 'annualized_return', 'daily_sharpe', 'daily_sortino',
        'max_drawdown', 'volatility', 'calmar_ratio'
    ]
    
    metrics_to_compare = metrics or default_metrics
    
    # 获取净值曲线
    equity = results.equity() if callable(results.equity) else results.equity
    
    # 获取收益率序列
    get_returns = results.get_returns
    returns = get_returns() if callable(get_returns) else get_returns
    
    # 计算不同时间段的指标
    period_metrics = {}
    
    for period_name, (start_date, end_date) in periods.items():
        try:
            # 提取时间段内的数据
            period_equity = equity.loc[start_date:end_date].copy()
            period_returns = returns.loc[start_date:end_date].copy()
            
            # 创建子区间回测结果
            from copy import deepcopy
            sub_results = deepcopy(results)
            sub_results.equity = period_equity
            
            # 计算指标
            metrics_dict = calculate_metrics(sub_results, risk_free_rate=risk_free_rate)
            
            # 提取要比较的指标
            period_metrics[period_name] = {
                metric: metrics_dict.get(metric) 
                for metric in metrics_to_compare 
                if metric in metrics_dict
            }
            
        except Exception as e:
            warnings.warn(f"Error calculating metrics for period {period_name}: {str(e)}")
            period_metrics[period_name] = {metric: None for metric in metrics_to_compare}
    
    # 创建比较表格
    comparison_df = pd.DataFrame(period_metrics).T
    
    # 格式化指标
    format_dict = {
        'total_return': '{:.2%}',
        'annualized_return': '{:.2%}',
        'max_drawdown': '{:.2%}',
        'volatility': '{:.2%}',
        'daily_sharpe': '{:.2f}',
        'daily_sortino': '{:.2f}',
        'calmar_ratio': '{:.2f}'
    }
    
    for col in comparison_df.columns:
        if col in format_dict:
            comparison_df[col] = comparison_df[col].map(
                lambda x: format_dict[col].format(x) if pd.notnull(x) else 'N/A'
            )
    
    return comparison_df


def compare_with_market_regimes(results: BacktestResults,
                              market_regimes: Dict[str, pd.Series],
                              risk_free_rate: float = 0.0) -> pd.DataFrame:
    """
    比较策略在不同市场环境下的表现
    
    Parameters
    ----------
    results : BacktestResults
        回测结果
    market_regimes : Dict[str, pd.Series]
        市场环境名称和标识序列的字典（1表示该环境，0表示非该环境）
    risk_free_rate : float, optional
        无风险利率，默认为0.0
        
    Returns
    -------
    pd.DataFrame
        不同市场环境下的比较结果
    """
    # 默认比较指标
    metrics_to_compare = [
        'total_return', 'annualized_return', 'daily_sharpe', 'max_drawdown', 'win_rate'
    ]
    
    # 获取收益率序列
    get_returns = results.get_returns
    returns = get_returns() if callable(get_returns) else get_returns
    
    # 计算不同市场环境下的指标
    regime_metrics = {}
    
    for regime_name, regime_series in market_regimes.items():
        try:
            # 提取该市场环境下的收益率
            regime_returns = returns.loc[regime_series.index]
            regime_returns = regime_returns[regime_series > 0]
            
            if len(regime_returns) == 0:
                raise ValueError(f"No data for regime {regime_name}")
                
            # 创建子区间回测结果
            from copy import deepcopy
            sub_results = deepcopy(results)
            sub_results.returns = regime_returns
            
            # 计算指标
            metrics_dict = calculate_metrics(sub_results, risk_free_rate=risk_free_rate)
            
            # 提取要比较的指标
            regime_metrics[regime_name] = {
                metric: metrics_dict.get(metric) 
                for metric in metrics_to_compare 
                if metric in metrics_dict
            }
            
        except Exception as e:
            warnings.warn(f"Error calculating metrics for regime {regime_name}: {str(e)}")
            regime_metrics[regime_name] = {metric: None for metric in metrics_to_compare}
    
    # 创建比较表格
    comparison_df = pd.DataFrame(regime_metrics).T
    
    # 格式化指标
    format_dict = {
        'total_return': '{:.2%}',
        'annualized_return': '{:.2%}',
        'max_drawdown': '{:.2%}',
        'win_rate': '{:.2%}',
        'daily_sharpe': '{:.2f}'
    }
    
    for col in comparison_df.columns:
        if col in format_dict:
            comparison_df[col] = comparison_df[col].map(
                lambda x: format_dict[col].format(x) if pd.notnull(x) else 'N/A'
            )
    
    return comparison_df


def plot_strategy_comparison(strategies: Dict[str, BacktestResults],
                           benchmark: Optional[pd.Series] = None,
                           normalize: bool = True,
                           include_drawdowns: bool = True,
                           figsize: tuple = (15, 10),
                           **kwargs) -> Dict[str, plt.Figure]:
    """
    绘制策略比较图表
    
    Parameters
    ----------
    strategies : Dict[str, BacktestResults]
        策略名称和回测结果的字典
    benchmark : pd.Series, optional
        基准指数，默认为None
    normalize : bool, optional
        是否规范化收益，默认为True
    include_drawdowns : bool, optional
        是否包含回撤对比图，默认为True
    figsize : tuple, optional
        图表大小，默认为(15, 10)
    **kwargs : dict
        其他绘图参数
        
    Returns
    -------
    Dict[str, plt.Figure]
        包含各类比较图表的字典
    """
    # 创建比较器
    comparator = StrategyComparison(strategies, benchmark)
    
    # 初始化结果字典
    figures = {}
    
    # 绘制收益曲线对比图
    figures['returns'] = comparator.compare_returns(
        normalize=normalize, 
        figsize=figsize,
        title='Strategy Returns Comparison'
    )
    
    # 绘制回撤对比图
    if include_drawdowns:
        figures['drawdowns'] = comparator.compare_drawdowns(
            figsize=figsize,
            title='Strategy Drawdowns Comparison'
        )
    
    # 绘制指标对比图
    figures['metrics_bar'] = comparator.plot_metrics_comparison(
        kind='bar',
        figsize=figsize
    )
    
    # 绘制雷达图对比
    figures['metrics_radar'] = comparator.plot_metrics_comparison(
        kind='radar',
        figsize=(10, 10)
    )
    
    # 如果只要求返回一个图表，则返回收益对比图
    if kwargs.get('return_first_only', False):
        return figures['returns']
    
    return figures


def compare_with_benchmark(strategy: BacktestResults, 
                        benchmark: pd.Series,
                        risk_free_rate: float = 0.0) -> Dict[str, float]:
    """
    将策略与基准进行比较分析
    
    Parameters
    ----------
    strategy : BacktestResults
        策略回测结果
    benchmark : pd.Series
        基准指数收益率序列
    risk_free_rate : float, optional
        无风险利率，默认为0.0
        
    Returns
    -------
    Dict[str, float]
        包含比较结果的字典
    """
    # 获取策略收益率
    get_returns = strategy.get_returns
    strategy_returns = get_returns() if callable(get_returns) else get_returns
    
    # 确保日期对齐
    common_index = strategy_returns.index.intersection(benchmark.index)
    if len(common_index) == 0:
        raise ValueError("Strategy and benchmark have no common dates")
    
    strategy_returns = strategy_returns.loc[common_index]
    benchmark_returns = benchmark.loc[common_index]
    
    # 计算基准和策略指标
    from .metrics import (
        calculate_alpha_beta, calculate_sharpe_ratio, 
        calculate_information_ratio, calculate_tracking_error
    )
    
    # 计算 Alpha 和 Beta
    alpha, beta = calculate_alpha_beta(strategy_returns, benchmark_returns)
    
    # 计算信息比率
    information_ratio = calculate_information_ratio(strategy_returns, benchmark_returns)
    
    # 计算跟踪误差
    tracking_error = calculate_tracking_error(strategy_returns, benchmark_returns)
    
    # 策略 Sharpe 比率
    strategy_sharpe = calculate_sharpe_ratio(strategy_returns, risk_free_rate)
    
    # 基准 Sharpe 比率
    benchmark_sharpe = calculate_sharpe_ratio(benchmark_returns, risk_free_rate)
    
    # 相关性
    correlation = strategy_returns.corr(benchmark_returns)
    
    # 上行捕获率和下行捕获率
    up_returns = benchmark_returns[benchmark_returns > 0]
    down_returns = benchmark_returns[benchmark_returns < 0]
    
    strategy_up = strategy_returns.loc[up_returns.index]
    strategy_down = strategy_returns.loc[down_returns.index]
    
    up_capture = (1 + strategy_up).prod() / (1 + up_returns).prod() if len(up_returns) > 0 else 1
    down_capture = (1 + strategy_down).prod() / (1 + down_returns).prod() if len(down_returns) > 0 else 1
    
    # 结果字典
    result = {
        'alpha': alpha,
        'beta': beta,
        'correlation': correlation,
        'information_ratio': information_ratio,
        'tracking_error': tracking_error,
        'strategy_sharpe': strategy_sharpe,
        'benchmark_sharpe': benchmark_sharpe,
        'up_capture_ratio': up_capture,
        'down_capture_ratio': down_capture,
    }
    
    return result


def plot_benchmark_comparison(strategy: BacktestResults, 
                           benchmark: Union[BacktestResults, pd.Series],
                           figsize: tuple = (18, 12)) -> plt.Figure:
    """
    绘制策略与基准比较图表
    
    Parameters
    ----------
    strategy : BacktestResults
        策略回测结果
    benchmark : Union[BacktestResults, pd.Series]
        基准回测结果或收益率序列
    figsize : tuple, optional
        图表大小，默认为(18, 12)
        
    Returns
    -------
    plt.Figure
        图表对象
    """
    # 获取策略数据
    get_returns = strategy.get_returns
    strategy_returns = get_returns() if callable(get_returns) else get_returns
    
    strategy_equity = strategy.equity() if callable(strategy.equity) else strategy.equity
    
    # 获取基准数据
    if isinstance(benchmark, pd.Series):
        benchmark_returns = benchmark
        benchmark_equity = (1 + benchmark_returns).cumprod()
    else:
        get_returns = benchmark.get_returns
        benchmark_returns = get_returns() if callable(get_returns) else get_returns
        
        benchmark_equity = benchmark.equity() if callable(benchmark.equity) else benchmark.equity
    
    # 确保日期对齐
    common_index = strategy_returns.index.intersection(benchmark_returns.index)
    if len(common_index) == 0:
        raise ValueError("Strategy and benchmark have no common dates")
    
    strategy_returns = strategy_returns.loc[common_index]
    benchmark_returns = benchmark_returns.loc[common_index]
    
    strategy_equity = strategy_equity.loc[common_index]
    benchmark_equity = benchmark_equity.loc[common_index]
    
    # 规范化净值曲线
    strategy_equity_norm = strategy_equity / strategy_equity.iloc[0]
    benchmark_equity_norm = benchmark_equity / benchmark_equity.iloc[0]
    
    # 计算回撤
    from .metrics import calculate_drawdowns
    strategy_drawdowns = calculate_drawdowns(strategy_returns)
    benchmark_drawdowns = calculate_drawdowns(benchmark_returns)
    
    # 计算滚动 beta 和相关性
    window = min(252, len(strategy_returns) // 4) 
    rolling_correlation = strategy_returns.rolling(window=window).corr(benchmark_returns)
    
    # 计算相对表现
    relative_performance = strategy_equity_norm / benchmark_equity_norm
    
    # 创建图表
    fig = plt.figure(figsize=figsize)
    gs = fig.add_gridspec(3, 2)
    
    # 1. 净值曲线对比
    ax1 = fig.add_subplot(gs[0, :])
    ax1.plot(strategy_equity_norm.index, strategy_equity_norm, label='Strategy', linewidth=2)
    ax1.plot(benchmark_equity_norm.index, benchmark_equity_norm, label='Benchmark', 
            linewidth=2, alpha=0.7, linestyle='--')
    ax1.set_title('Normalized Performance Comparison', fontsize=14)
    ax1.set_ylabel('Value', fontsize=12)
    ax1.legend(loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    # 2. 回撤对比
    ax2 = fig.add_subplot(gs[1, :])
    ax2.fill_between(strategy_drawdowns.index, strategy_drawdowns, 0, 
                    color='red', alpha=0.3, label='Strategy Drawdown')
    ax2.fill_between(benchmark_drawdowns.index, benchmark_drawdowns, 0, 
                    color='blue', alpha=0.3, label='Benchmark Drawdown')
    ax2.set_title('Drawdown Comparison', fontsize=14)
    ax2.set_ylabel('Drawdown', fontsize=12)
    ax2.legend(loc='lower left')
    ax2.grid(True, alpha=0.3)
    
    # 3. 相对表现
    ax3 = fig.add_subplot(gs[2, 0])
    ax3.plot(relative_performance.index, relative_performance, color='green')
    ax3.axhline(y=1, color='black', linestyle='-', alpha=0.3)
    ax3.set_title('Relative Performance (Strategy / Benchmark)', fontsize=14)
    ax3.set_ylabel('Ratio', fontsize=12)
    ax3.grid(True, alpha=0.3)
    
    # 4. 滚动相关性
    ax4 = fig.add_subplot(gs[2, 1])
    ax4.plot(rolling_correlation.index, rolling_correlation, color='purple')
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax4.set_title(f'{window}-day Rolling Correlation', fontsize=14)
    ax4.set_ylabel('Correlation', fontsize=12)
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    return fig


def plot_parameter_comparison(param_results: Dict[Any, BacktestResults],
                          param_name: str = 'Parameter',
                          metrics: List[str] = None,
                          figsize: tuple = (15, 10)) -> plt.Figure:
    """
    绘制参数比较图表
    
    Parameters
    ----------
    param_results : Dict[Any, BacktestResults]
        参数值和对应回测结果的字典
    param_name : str, optional
        参数名称，默认为'Parameter'
    metrics : List[str], optional
        要比较的指标列表，默认为None（使用默认指标集）
    figsize : tuple, optional
        图表大小，默认为(15, 10)
        
    Returns
    -------
    plt.Figure
        图表对象
    """
    if not param_results:
        raise ValueError("No parameter results to compare")
        
    # 默认比较指标
    default_metrics = [
        'total_return', 'annualized_return', 'max_drawdown', 
        'daily_sharpe', 'volatility', 'calmar_ratio'
    ]
    
    metrics_to_compare = metrics or default_metrics
    
    # 收集各参数的指标数据
    param_metrics = {}
    param_values = []
    equity_curves = {}
    
    for param_value, results in param_results.items():
        param_values.append(param_value)
        
        # 获取净值曲线
        equity = results.equity() if callable(results.equity) else results.equity
        equity_norm = equity / equity.iloc[0]
        equity_curves[param_value] = equity_norm
        
        # 计算指标
        metrics_dict = calculate_metrics(results)
        
        # 提取要比较的指标
        param_metrics[param_value] = {
            metric: metrics_dict.get(metric) 
            for metric in metrics_to_compare 
            if metric in metrics_dict
        }
    
    # 将指标转换为DataFrame
    metrics_df = pd.DataFrame(param_metrics).T
    
    # 创建图表
    fig = plt.figure(figsize=figsize)
    gs = fig.add_gridspec(len(metrics_to_compare) + 1, 1, height_ratios=[3] + [1] * len(metrics_to_compare))
    
    # 1. 净值曲线比较
    ax0 = fig.add_subplot(gs[0])
    
    colors = plt.cm.viridis(np.linspace(0, 1, len(param_values)))
    
    for i, (param_value, equity) in enumerate(equity_curves.items()):
        ax0.plot(equity.index, equity, 
               label=f'{param_name}={param_value}', 
               color=colors[i], linewidth=2)
    
    ax0.set_title('Equity Curves by Parameter Value', fontsize=14)
    ax0.set_ylabel('Normalized Value', fontsize=12)
    ax0.legend(loc='upper left')
    ax0.grid(True, alpha=0.3)
    
    # 2. 各指标比较
    for i, metric in enumerate(metrics_to_compare):
        ax = fig.add_subplot(gs[i+1])
        
        if metric in metrics_df.columns:
            metric_values = metrics_df[metric].values
            
            # 调整指标方向（对于某些指标，越小越好）
            if metric in ['max_drawdown', 'volatility']:
                bar_colors = plt.cm.RdYlGn_r(np.linspace(0, 1, len(param_values)))
            else:
                bar_colors = plt.cm.RdYlGn(np.linspace(0, 1, len(param_values)))
            
            ax.bar(metrics_df.index, metric_values, color=bar_colors)
            
            # 设置副标题
            ax.set_title(metric.replace('_', ' ').title(), fontsize=12)
            ax.set_ylabel('Value', fontsize=10)
            ax.grid(True, alpha=0.3)
            
            # 将大数值格式化为百分比
            if metric in ['total_return', 'annualized_return', 'max_drawdown', 'volatility']:
                from matplotlib.ticker import FuncFormatter
                ax.yaxis.set_major_formatter(FuncFormatter(lambda y, _: '{:.1%}'.format(y)))
    
    plt.tight_layout()
    return fig


def plot_period_comparison(results: BacktestResults,
                       periods: Dict[str, Tuple[datetime, datetime]],
                       figsize: tuple = (15, 12)) -> plt.Figure:
    """
    绘制不同时间段比较图表
    
    Parameters
    ----------
    results : BacktestResults
        回测结果
    periods : Dict[str, Tuple[datetime, datetime]]
        时间段名称和起止日期元组的字典
    figsize : tuple, optional
        图表大小，默认为(15, 12)
        
    Returns
    -------
    plt.Figure
        图表对象
    """
    if not periods:
        raise ValueError("No periods to compare")
        
    # 获取收益率和净值序列
    get_returns = results.get_returns
    returns = get_returns() if callable(get_returns) else get_returns
    
    equity = results.equity() if callable(results.equity) else results.equity
    
    # 创建各时间段的收益和回撤数据
    period_returns = {}
    period_equity = {}
    period_drawdowns = {}
    period_metrics = {}
    
    from .metrics import calculate_drawdowns, calculate_metrics_from_returns, calculate_metrics
    
    for period_name, (start_date, end_date) in periods.items():
        try:
            # 提取时间段数据
            period_rets = returns.loc[start_date:end_date].copy()
            
            if len(period_rets) == 0:
                warnings.warn(f"No data for period {period_name}")
                continue
                
            period_eq = equity.loc[start_date:end_date].copy()
            
            # 规范化净值
            period_eq_norm = period_eq / period_eq.iloc[0]
            
            # 计算回撤
            period_dd = calculate_drawdowns(period_rets)
            
            # 存储数据
            period_returns[period_name] = period_rets
            period_equity[period_name] = period_eq_norm
            period_drawdowns[period_name] = period_dd
            
            # 计算指标 - 使用calculate_metrics_from_returns而不是calculate_metrics
            metrics = calculate_metrics_from_returns(period_rets)
            period_metrics[period_name] = metrics
            
        except Exception as e:
            warnings.warn(f"Error processing period {period_name}: {str(e)}")
    
    # 如果没有有效的时间段数据，则创建一个简单的图表并返回
    if not period_metrics:
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.text(0.5, 0.5, "No valid period data available", 
               horizontalalignment='center', verticalalignment='center',
               fontsize=14)
        ax.set_axis_off()
        return fig
    
    # 创建图表
    fig = plt.figure(figsize=figsize)
    gs = fig.add_gridspec(3, 2, height_ratios=[2, 1, 1])
    
    # 1. 各时间段的净值曲线
    ax1 = fig.add_subplot(gs[0, :])
    
    colors = plt.cm.tab10(np.linspace(0, 1, len(periods)))
    
    for i, (period_name, eq) in enumerate(period_equity.items()):
        ax1.plot(eq.index, eq, label=period_name, color=colors[i], linewidth=2)
    
    ax1.set_title('Normalized Equity by Period', fontsize=14)
    ax1.set_ylabel('Value', fontsize=12)
    ax1.legend(loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    # 2. 各时间段的回撤
    ax2 = fig.add_subplot(gs[1, 0])
    
    for i, (period_name, dd) in enumerate(period_drawdowns.items()):
        ax2.fill_between(dd.index, dd, 0, label=period_name, 
                        color=colors[i], alpha=0.5)
    
    ax2.set_title('Drawdowns by Period', fontsize=14)
    ax2.set_ylabel('Drawdown', fontsize=12)
    ax2.legend(loc='lower left')
    ax2.grid(True, alpha=0.3)
    
    # 3. 收益率对比
    ax3 = fig.add_subplot(gs[1, 1])
    
    # 收集各时间段的总收益率
    total_returns = [period_metrics[p].get('total_return', 0) for p in period_metrics]
    period_names = list(period_metrics.keys())
    
    ax3.bar(period_names, total_returns, color=colors[:len(period_names)])
    ax3.set_title('Total Return by Period', fontsize=14)
    ax3.set_ylabel('Return', fontsize=12)
    ax3.grid(True, alpha=0.3)
    
    # 将大数值格式化为百分比
    from matplotlib.ticker import FuncFormatter
    ax3.yaxis.set_major_formatter(FuncFormatter(lambda y, _: '{:.1%}'.format(y)))
    
    # 旋转x轴标签
    plt.setp(ax3.get_xticklabels(), rotation=45, ha='right')
    
    # 4. 主要指标比较
    ax4 = fig.add_subplot(gs[2, :])
    
    # 要显示的指标
    metrics_to_show = ['annualized_return', 'volatility', 'daily_sharpe', 'max_drawdown']
    metrics_labels = ['Ann. Return', 'Volatility', 'Sharpe', 'Max DD']
    
    # 准备数据
    metrics_data = []
    for period in period_metrics:
        metrics_data.append([
            period_metrics[period].get(m, 0) for m in metrics_to_show
        ])
    
    x = np.arange(len(metrics_labels))
    width = 0.8 / len(period_metrics) if period_metrics else 0.4
    
    # 绘制分组柱状图
    for i, (period, data) in enumerate(zip(period_metrics.keys(), metrics_data)):
        ax4.bar(x + i * width - width * (len(period_metrics) - 1) / 2, 
              data, width, label=period, color=colors[i])
    
    ax4.set_title('Key Metrics by Period', fontsize=14)
    ax4.set_ylabel('Value', fontsize=12)
    ax4.set_xticks(x)
    ax4.set_xticklabels(metrics_labels)
    ax4.legend(loc='upper right')
    ax4.grid(True, alpha=0.3)
    
    # 将大数值格式化为百分比
    ax4.yaxis.set_major_formatter(FuncFormatter(lambda y, _: '{:.1%}'.format(y)))
    
    plt.tight_layout()
    return fig


def create_comparison_table(strategies: Dict[str, BacktestResults],
                         benchmark: Optional[pd.Series] = None,
                         risk_free_rate: float = 0.0,
                         metrics: Optional[List[str]] = None) -> pd.DataFrame:
    """
    创建策略比较表格
    
    Parameters
    ----------
    strategies : Dict[str, BacktestResults]
        策略名称和回测结果的字典
    benchmark : pd.Series, optional
        基准指数，默认为None
    risk_free_rate : float, optional
        无风险利率，默认为0.0
    metrics : List[str], optional
        要比较的指标列表，默认为None（使用默认指标集）
        
    Returns
    -------
    pd.DataFrame
        比较表格
    """
    # 默认比较指标
    default_metrics = [
        'total_return', 'annualized_return', 'volatility',
        'daily_sharpe', 'max_drawdown', 'calmar_ratio',
        'win_rate', 'profit_factor', 'avg_trade', 'best_trade', 'worst_trade'
    ]
    
    metrics_to_compare = metrics or default_metrics
    
    # 使用 compare_strategies 函数计算指标
    comparison_results = compare_strategies(
        strategies, 
        benchmark=benchmark,
        risk_free_rate=risk_free_rate,
        metrics=metrics_to_compare,
        output_format='dataframe'
    )
    
    # 如果存在基准，添加基准指标
    if benchmark is not None:
        # 计算基准指标
        from .metrics import calculate_metrics_from_returns
        benchmark_metrics = calculate_metrics_from_returns(
            benchmark, risk_free_rate=risk_free_rate
        )
        
        # 提取要比较的指标
        benchmark_data = {
            metric: benchmark_metrics.get(metric) 
            for metric in metrics_to_compare 
            if metric in benchmark_metrics
        }
        
        # 将基准数据添加到比较结果中
        comparison_results.loc['Benchmark'] = pd.Series(benchmark_data)
    
    return comparison_results


def plot_correlation_matrix(returns_dict: Dict[str, pd.Series],
                         figsize: tuple = (10, 8)) -> plt.Figure:
    """
    绘制收益率相关性矩阵
    
    Parameters
    ----------
    returns_dict : Dict[str, pd.Series]
        策略名称和收益率序列的字典
    figsize : tuple, optional
        图表大小，默认为(10, 8)
        
    Returns
    -------
    plt.Figure
        图表对象
    """
    if not returns_dict:
        raise ValueError("No returns data to analyze")
        
    # 合并收益率数据
    returns_df = pd.DataFrame(returns_dict)
    
    # 计算相关性矩阵
    corr_matrix = returns_df.corr()
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    # 使用热力图可视化相关性
    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
    cmap = sns.diverging_palette(230, 20, as_cmap=True)
    
    sns.heatmap(corr_matrix, mask=mask, cmap=cmap, vmax=1, vmin=-1, center=0,
               annot=True, fmt='.2f', square=True, linewidths=.5, cbar_kws={"shrink": .8})
    
    ax.set_title('Correlation Matrix', fontsize=16)
    plt.tight_layout()
    
    return fig


__all__ = [
    'StrategyComparison',
    'compare_strategies',
    'compare_time_periods',
    'compare_with_market_regimes',
    'plot_strategy_comparison',
    'compare_with_benchmark',
    'plot_benchmark_comparison',
    'plot_parameter_comparison',
    'plot_period_comparison',
    'create_comparison_table',
    'plot_correlation_matrix'
] 