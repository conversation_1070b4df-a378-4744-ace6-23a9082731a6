"""
风险指标计算模块

实现各种常用风险指标的计算，包括波动率、回撤、夏普比率等。
"""

from typing import Dict, Any, List, Optional, Union, Tuple
import pandas as pd
import numpy as np
from scipy import stats
from risk.assessment.base import RiskMetric


class VolatilityMetric(RiskMetric):
    """
    波动率指标
    
    计算收益率的标准差(波动率)，是衡量风险的基础指标。
    """
    
    def __init__(self, name: str = "volatility", description: str = "收益率波动率", period: int = 252, window: int = None, **params):
        """
        初始化波动率指标
        
        Parameters
        ----------
        name : str, optional
            指标名称，默认为"volatility"
        description : str, optional
            指标描述，默认为"收益率波动率"
        period : int, optional
            年化周期，默认为252（交易日）
        window : int, optional
            计算窗口，默认为None表示使用全部数据
        **params : dict
            其他参数
        """
        self.period = period
        self.window = window
        super().__init__(name, description, period=period, window=window, **params)
    
    def validate_params(self) -> None:
        """验证参数有效性"""
        if not isinstance(self.period, int) or self.period <= 0:
            raise ValueError("period必须是正整数")
        
        if self.window is not None and (not isinstance(self.window, int) or self.window <= 0):
            raise ValueError("window必须是正整数或None")
    
    def calculate(self, data: pd.DataFrame, context: Dict[str, Any] = None) -> float:
        """
        计算波动率
        
        Parameters
        ----------
        data : pd.DataFrame
            包含'close'或'returns'列的DataFrame
        context : Dict[str, Any], optional
            计算上下文，默认为None
            
        Returns
        -------
        float
            波动率值，年化
        """
        # 检查数据
        if data.empty:
            return 0.0
        
        # 获取或计算收益率
        if 'returns' in data.columns:
            returns = data['returns']
        elif 'close' in data.columns:
            returns = data['close'].pct_change().dropna()
        else:
            raise ValueError("数据必须包含'returns'或'close'列")
        
        # 应用窗口（如果指定）
        if self.window is not None and len(returns) > self.window:
            returns = returns.iloc[-self.window:]
        
        # 计算波动率
        daily_vol = returns.std()
        annualized_vol = daily_vol * np.sqrt(self.period)
        
        return annualized_vol
    
    def get_normalized_score(self, value: float) -> float:
        """
        将波动率转换为标准化风险分数
        
        Parameters
        ----------
        value : float
            波动率值
            
        Returns
        -------
        float
            标准化分数 (0-100)
        """
        # 基于经验阈值的评分函数
        # 波动率越大，风险越高
        if value <= 0.05:  # 5%以下波动率，低风险
            return 10
        elif value <= 0.10:  # 5-10%波动率，适中风险
            return 25 + (value - 0.05) * 1000
        elif value <= 0.20:  # 10-20%波动率，较高风险
            return 50 + (value - 0.10) * 250
        elif value <= 0.40:  # 20-40%波动率，高风险
            return 75 + (value - 0.20) * 75
        else:  # 40%以上波动率，极高风险
            return 90 + min(10, (value - 0.40) * 25)
    
    def get_risk_direction(self) -> str:
        """波动率越高风险越大"""
        return "higher"


class DrawdownMetric(RiskMetric):
    """
    最大回撤指标
    
    计算历史最大回撤，是衡量下行风险的重要指标。
    """
    
    def __init__(self, name: str = "max_drawdown", description: str = "最大回撤", window: int = None, **params):
        """
        初始化最大回撤指标
        
        Parameters
        ----------
        name : str, optional
            指标名称，默认为"max_drawdown"
        description : str, optional
            指标描述，默认为"最大回撤"
        window : int, optional
            计算窗口，默认为None表示使用全部数据
        **params : dict
            其他参数
        """
        self.window = window
        super().__init__(name, description, window=window, **params)
    
    def validate_params(self) -> None:
        """验证参数有效性"""
        if self.window is not None and (not isinstance(self.window, int) or self.window <= 0):
            raise ValueError("window必须是正整数或None")
    
    def calculate(self, data: pd.DataFrame, context: Dict[str, Any] = None) -> float:
        """
        计算最大回撤
        
        Parameters
        ----------
        data : pd.DataFrame
            包含'equity'或'close'列的DataFrame
        context : Dict[str, Any], optional
            计算上下文，默认为None
            
        Returns
        -------
        float
            最大回撤值，以小数形式表示
        """
        # 检查数据
        if data.empty:
            return 0.0
        
        # 获取权益曲线或价格
        if 'equity' in data.columns:
            equity = data['equity']
        elif 'close' in data.columns:
            equity = data['close']
        else:
            raise ValueError("数据必须包含'equity'或'close'列")
        
        # 应用窗口（如果指定）
        if self.window is not None and len(equity) > self.window:
            equity = equity.iloc[-self.window:]
        
        # 计算累积最大值
        running_max = equity.cummax()
        
        # 计算回撤
        drawdown = (equity / running_max - 1)
        
        # 返回最大回撤（负值取绝对值）
        max_drawdown = abs(drawdown.min())
        
        return max_drawdown
    
    def get_normalized_score(self, value: float) -> float:
        """
        将最大回撤转换为标准化风险分数
        
        Parameters
        ----------
        value : float
            最大回撤值
            
        Returns
        -------
        float
            标准化分数 (0-100)
        """
        # 基于经验阈值的评分函数
        # 回撤越大，风险越高
        if value <= 0.05:  # 5%以下回撤，低风险
            return 10 + value * 300
        elif value <= 0.10:  # 5-10%回撤，适中风险
            return 25 + (value - 0.05) * 500
        elif value <= 0.20:  # 10-20%回撤，较高风险
            return 50 + (value - 0.10) * 250
        elif value <= 0.30:  # 20-30%回撤，高风险
            return 75 + (value - 0.20) * 150
        else:  # 30%以上回撤，极高风险
            return 90 + min(10, (value - 0.30) * 100)
    
    def get_risk_direction(self) -> str:
        """回撤越大风险越高"""
        return "higher"


class SharpeRatioMetric(RiskMetric):
    """
    夏普比率指标
    
    计算风险调整后收益，衡量单位风险下的超额回报。
    """
    
    def __init__(self, name: str = "sharpe_ratio", description: str = "夏普比率", 
                 risk_free_rate: float = 0.02, period: int = 252, window: int = None, **params):
        """
        初始化夏普比率指标
        
        Parameters
        ----------
        name : str, optional
            指标名称，默认为"sharpe_ratio"
        description : str, optional
            指标描述，默认为"夏普比率"
        risk_free_rate : float, optional
            无风险收益率，默认为0.02 (2%)
        period : int, optional
            年化周期，默认为252（交易日）
        window : int, optional
            计算窗口，默认为None表示使用全部数据
        **params : dict
            其他参数
        """
        self.risk_free_rate = risk_free_rate
        self.period = period
        self.window = window
        super().__init__(name, description, risk_free_rate=risk_free_rate, 
                         period=period, window=window, **params)
    
    def validate_params(self) -> None:
        """验证参数有效性"""
        if not isinstance(self.risk_free_rate, (int, float)):
            raise ValueError("risk_free_rate必须是数值")
        
        if not isinstance(self.period, int) or self.period <= 0:
            raise ValueError("period必须是正整数")
        
        if self.window is not None and (not isinstance(self.window, int) or self.window <= 0):
            raise ValueError("window必须是正整数或None")
    
    def calculate(self, data: pd.DataFrame, context: Dict[str, Any] = None) -> float:
        """
        计算夏普比率
        
        Parameters
        ----------
        data : pd.DataFrame
            包含'returns'或'close'列的DataFrame
        context : Dict[str, Any], optional
            计算上下文，默认为None
            
        Returns
        -------
        float
            夏普比率
        """
        # 检查数据
        if data.empty:
            return 0.0
        
        # 获取或计算收益率
        if 'returns' in data.columns:
            returns = data['returns']
        elif 'close' in data.columns:
            returns = data['close'].pct_change().dropna()
        else:
            raise ValueError("数据必须包含'returns'或'close'列")
        
        # 应用窗口（如果指定）
        if self.window is not None and len(returns) > self.window:
            returns = returns.iloc[-self.window:]
        
        # 计算夏普比率
        daily_rf = (1 + self.risk_free_rate) ** (1 / self.period) - 1
        excess_returns = returns - daily_rf
        
        if excess_returns.std() == 0:
            return 0.0  # 避免除以零
        
        sharpe = (excess_returns.mean() / excess_returns.std()) * np.sqrt(self.period)
        
        return sharpe
    
    def get_normalized_score(self, value: float) -> float:
        """
        将夏普比率转换为标准化风险分数
        
        Parameters
        ----------
        value : float
            夏普比率
            
        Returns
        -------
        float
            标准化分数 (0-100)，注意夏普比率越低风险越高
        """
        # 基于经验阈值的评分函数
        # 夏普比率越低，风险越高
        if value <= 0:
            return 90 - min(40, value * 10)  # 负夏普比率，高风险
        elif value < 0.5:
            return 90 - (value / 0.5) * 30  # 0-0.5，高中风险
        elif value < 1.0:
            return 60 - ((value - 0.5) / 0.5) * 30  # 0.5-1.0，中风险
        elif value < 2.0:
            return 30 - ((value - 1.0) / 1.0) * 20  # 1.0-2.0，低中风险
        else:
            return max(5, 10 - (value - 2.0) * 2.5)  # >2.0，低风险
    
    def get_risk_direction(self) -> str:
        """夏普比率越低风险越高"""
        return "lower"


class ValueAtRiskMetric(RiskMetric):
    """
    风险价值(VaR)指标
    
    计算在给定置信水平下可能的最大损失。
    """
    
    def __init__(self, name: str = "value_at_risk", description: str = "风险价值(VaR)", 
                 confidence: float = 0.95, method: str = "historical", window: int = None, **params):
        """
        初始化VaR指标
        
        Parameters
        ----------
        name : str, optional
            指标名称，默认为"value_at_risk"
        description : str, optional
            指标描述，默认为"风险价值(VaR)"
        confidence : float, optional
            置信水平，默认为0.95 (95%)
        method : str, optional
            计算方法，可选 "historical"、"parametric"，默认为"historical"
        window : int, optional
            计算窗口，默认为None表示使用全部数据
        **params : dict
            其他参数
        """
        self.confidence = confidence
        self.method = method
        self.window = window
        super().__init__(name, description, confidence=confidence, 
                         method=method, window=window, **params)
    
    def validate_params(self) -> None:
        """验证参数有效性"""
        if not isinstance(self.confidence, float) or self.confidence <= 0 or self.confidence >= 1:
            raise ValueError("confidence必须是0到1之间的浮点数")
        
        if self.method not in ["historical", "parametric"]:
            raise ValueError("method必须是'historical'或'parametric'")
        
        if self.window is not None and (not isinstance(self.window, int) or self.window <= 0):
            raise ValueError("window必须是正整数或None")
    
    def calculate(self, data: pd.DataFrame, context: Dict[str, Any] = None) -> float:
        """
        计算风险价值(VaR)
        
        Parameters
        ----------
        data : pd.DataFrame
            包含'returns'或'close'列的DataFrame
        context : Dict[str, Any], optional
            计算上下文，默认为None
            
        Returns
        -------
        float
            VaR值，以正数表示
        """
        # 检查数据
        if data.empty:
            return 0.0
        
        # 获取或计算收益率
        if 'returns' in data.columns:
            returns = data['returns']
        elif 'close' in data.columns:
            returns = data['close'].pct_change().dropna()
        else:
            raise ValueError("数据必须包含'returns'或'close'列")
        
        # 应用窗口（如果指定）
        if self.window is not None and len(returns) > self.window:
            returns = returns.iloc[-self.window:]
        
        # 计算VaR
        if self.method == "historical":
            # 历史模拟法
            var = abs(np.percentile(returns, 100 * (1 - self.confidence)))
        else:
            # 参数法（假设正态分布）
            mean = returns.mean()
            std = returns.std()
            var = abs(mean + std * stats.norm.ppf(1 - self.confidence))
        
        return var
    
    def get_normalized_score(self, value: float) -> float:
        """
        将VaR转换为标准化风险分数
        
        Parameters
        ----------
        value : float
            VaR值
            
        Returns
        -------
        float
            标准化分数 (0-100)
        """
        # 基于经验阈值的评分函数
        # VaR越大，风险越高
        if value <= 0.01:  # 1%以下VaR，低风险
            return 10 + value * 1000
        elif value <= 0.02:  # 1-2%VaR，低中风险
            return 20 + (value - 0.01) * 1000
        elif value <= 0.03:  # 2-3%VaR，中风险
            return 30 + (value - 0.02) * 1000
        elif value <= 0.05:  # 3-5%VaR，中高风险
            return 40 + (value - 0.03) * 500
        elif value <= 0.10:  # 5-10%VaR，高风险
            return 50 + (value - 0.05) * 400
        else:  # >10%VaR，极高风险
            return 70 + min(30, (value - 0.10) * 150)
    
    def get_risk_direction(self) -> str:
        """VaR越大风险越高"""
        return "higher"


class SortinoRatioMetric(RiskMetric):
    """
    索提诺比率指标
    
    计算下行风险调整后收益，只考虑负收益的波动。
    """
    
    def __init__(self, name: str = "sortino_ratio", description: str = "索提诺比率", 
                 risk_free_rate: float = 0.02, period: int = 252, window: int = None, **params):
        """
        初始化索提诺比率指标
        
        Parameters
        ----------
        name : str, optional
            指标名称，默认为"sortino_ratio"
        description : str, optional
            指标描述，默认为"索提诺比率"
        risk_free_rate : float, optional
            无风险收益率，默认为0.02 (2%)
        period : int, optional
            年化周期，默认为252（交易日）
        window : int, optional
            计算窗口，默认为None表示使用全部数据
        **params : dict
            其他参数
        """
        self.risk_free_rate = risk_free_rate
        self.period = period
        self.window = window
        super().__init__(name, description, risk_free_rate=risk_free_rate, 
                         period=period, window=window, **params)
    
    def validate_params(self) -> None:
        """验证参数有效性"""
        if not isinstance(self.risk_free_rate, (int, float)):
            raise ValueError("risk_free_rate必须是数值")
        
        if not isinstance(self.period, int) or self.period <= 0:
            raise ValueError("period必须是正整数")
        
        if self.window is not None and (not isinstance(self.window, int) or self.window <= 0):
            raise ValueError("window必须是正整数或None")
    
    def calculate(self, data: pd.DataFrame, context: Dict[str, Any] = None) -> float:
        """
        计算索提诺比率
        
        Parameters
        ----------
        data : pd.DataFrame
            包含'returns'或'close'列的DataFrame
        context : Dict[str, Any], optional
            计算上下文，默认为None
            
        Returns
        -------
        float
            索提诺比率
        """
        # 检查数据
        if data.empty:
            return 0.0
        
        # 获取或计算收益率
        if 'returns' in data.columns:
            returns = data['returns']
        elif 'close' in data.columns:
            returns = data['close'].pct_change().dropna()
        else:
            raise ValueError("数据必须包含'returns'或'close'列")
        
        # 应用窗口（如果指定）
        if self.window is not None and len(returns) > self.window:
            returns = returns.iloc[-self.window:]
        
        # 计算索提诺比率
        daily_rf = (1 + self.risk_free_rate) ** (1 / self.period) - 1
        excess_returns = returns - daily_rf
        
        # 计算下行偏差（只考虑负收益）
        downside_returns = excess_returns[excess_returns < 0]
        
        if len(downside_returns) == 0 or downside_returns.std() == 0:
            return excess_returns.mean() * np.sqrt(self.period) * 10  # 如果没有下行波动，返回放大的平均超额收益
        
        downside_deviation = downside_returns.std()
        sortino = (excess_returns.mean() / downside_deviation) * np.sqrt(self.period)
        
        return sortino
    
    def get_normalized_score(self, value: float) -> float:
        """
        将索提诺比率转换为标准化风险分数
        
        Parameters
        ----------
        value : float
            索提诺比率
            
        Returns
        -------
        float
            标准化分数 (0-100)，注意索提诺比率越低风险越高
        """
        # 类似夏普比率的评分函数，但阈值稍有调整
        # 索提诺比率越低，风险越高
        if value <= 0:
            return 90 - min(40, value * 10)  # 负值，高风险
        elif value < 0.7:
            return 90 - (value / 0.7) * 30  # 0-0.7，高中风险
        elif value < 1.5:
            return 60 - ((value - 0.7) / 0.8) * 30  # 0.7-1.5，中风险
        elif value < 3.0:
            return 30 - ((value - 1.5) / 1.5) * 20  # 1.5-3.0，低中风险
        else:
            return max(5, 10 - (value - 3.0) * 2.5)  # >3.0，低风险
    
    def get_risk_direction(self) -> str:
        """索提诺比率越低风险越高"""
        return "lower"


class CalmarRatioMetric(RiskMetric):
    """
    卡玛比率指标
    
    计算年化收益率与最大回撤的比值，衡量承担回撤风险所获得的回报。
    """
    
    def __init__(self, name: str = "calmar_ratio", description: str = "卡玛比率", 
                 period: int = 252, window: int = None, **params):
        """
        初始化卡玛比率指标
        
        Parameters
        ----------
        name : str, optional
            指标名称，默认为"calmar_ratio"
        description : str, optional
            指标描述，默认为"卡玛比率"
        period : int, optional
            年化周期，默认为252（交易日）
        window : int, optional
            计算窗口，默认为None表示使用全部数据
        **params : dict
            其他参数
        """
        self.period = period
        self.window = window
        super().__init__(name, description, period=period, window=window, **params)
    
    def validate_params(self) -> None:
        """验证参数有效性"""
        if not isinstance(self.period, int) or self.period <= 0:
            raise ValueError("period必须是正整数")
        
        if self.window is not None and (not isinstance(self.window, int) or self.window <= 0):
            raise ValueError("window必须是正整数或None")
    
    def calculate(self, data: pd.DataFrame, context: Dict[str, Any] = None) -> float:
        """
        计算卡玛比率
        
        Parameters
        ----------
        data : pd.DataFrame
            包含'returns'或'close'列和'equity'列的DataFrame
        context : Dict[str, Any], optional
            计算上下文，默认为None
            
        Returns
        -------
        float
            卡玛比率
        """
        # 检查数据
        if data.empty:
            return 0.0
        
        # 应用窗口（如果指定）
        if self.window is not None and len(data) > self.window:
            data = data.iloc[-self.window:]
        
        # 计算年化收益率
        if 'returns' in data.columns:
            returns = data['returns']
            annual_return = (1 + returns.mean()) ** self.period - 1
        elif 'close' in data.columns:
            returns = data['close'].pct_change().dropna()
            annual_return = (1 + returns.mean()) ** self.period - 1
        else:
            raise ValueError("数据必须包含'returns'或'close'列")
        
        # 计算最大回撤
        if 'equity' in data.columns:
            equity = data['equity']
        elif 'close' in data.columns:
            equity = data['close']
        else:
            raise ValueError("数据必须包含'equity'或'close'列")
        
        running_max = equity.cummax()
        drawdown = (equity / running_max - 1)
        max_drawdown = abs(drawdown.min())
        
        # 计算卡玛比率
        if max_drawdown == 0:
            return annual_return * 10  # 避免除以零，放大年化收益
        
        calmar = annual_return / max_drawdown
        
        return calmar
    
    def get_normalized_score(self, value: float) -> float:
        """
        将卡玛比率转换为标准化风险分数
        
        Parameters
        ----------
        value : float
            卡玛比率
            
        Returns
        -------
        float
            标准化分数 (0-100)，注意卡玛比率越低风险越高
        """
        # 卡玛比率越低，风险越高
        if value <= 0:
            return 90 - min(40, value * 15)  # 负值，高风险
        elif value < 0.5:
            return 90 - (value / 0.5) * 30  # 0-0.5，高中风险
        elif value < 1.0:
            return 60 - ((value - 0.5) / 0.5) * 30  # 0.5-1.0，中风险
        elif value < 2.0:
            return 30 - ((value - 1.0) / 1.0) * 20  # 1.0-2.0，低中风险
        else:
            return max(5, 10 - (value - 2.0) * 2.5)  # >2.0，低风险
    
    def get_risk_direction(self) -> str:
        """卡玛比率越低风险越高"""
        return "lower"


class ConcentrationMetric(RiskMetric):
    """
    仓位集中度指标
    
    计算投资组合中最大单一仓位占比，衡量集中风险。
    """
    
    def __init__(self, name: str = "concentration", description: str = "仓位集中度", **params):
        """
        初始化集中度指标
        
        Parameters
        ----------
        name : str, optional
            指标名称，默认为"concentration"
        description : str, optional
            指标描述，默认为"仓位集中度"
        **params : dict
            其他参数
        """
        super().__init__(name, description, **params)
    
    def validate_params(self) -> None:
        """验证参数有效性"""
        pass  # 无参数需要验证
    
    def calculate(self, data: pd.DataFrame, context: Dict[str, Any] = None) -> float:
        """
        计算仓位集中度
        
        Parameters
        ----------
        data : pd.DataFrame
            不使用，仓位信息从context中获取
        context : Dict[str, Any], optional
            计算上下文，必须包含'positions'键，值为Dict[str, float]类型
            表示各个资产的持仓价值
            
        Returns
        -------
        float
            最大单一仓位占比，范围0-1
        """
        if context is None or 'positions' not in context:
            return 0.0
        
        positions = context['positions']
        
        if not positions:
            return 0.0
        
        # 计算总持仓价值
        total_value = sum(abs(value) for value in positions.values())
        
        if total_value == 0:
            return 0.0
        
        # 计算最大单一仓位占比
        max_position = max(abs(value) for value in positions.values())
        concentration = max_position / total_value
        
        return concentration
    
    def get_normalized_score(self, value: float) -> float:
        """
        将集中度转换为标准化风险分数
        
        Parameters
        ----------
        value : float
            集中度值，范围0-1
            
        Returns
        -------
        float
            标准化分数 (0-100)
        """
        # 集中度越高，风险越大
        if value <= 0.20:  # 分散投资，低风险
            return 10 + value * 100
        elif value <= 0.40:  # 适度集中，中低风险
            return 30 + (value - 0.20) * 150
        elif value <= 0.60:  # 较高集中，中风险
            return 60 + (value - 0.40) * 100
        elif value <= 0.80:  # 高度集中，高风险
            return 80 + (value - 0.60) * 50
        else:  # 极度集中，极高风险
            return 90 + (value - 0.80) * 100
    
    def get_risk_direction(self) -> str:
        """集中度越高风险越大"""
        return "higher"


class LeverageMetric(RiskMetric):
    """
    杠杆率指标
    
    计算投资组合的杠杆使用率，衡量杠杆风险。
    """
    
    def __init__(self, name: str = "leverage", description: str = "杠杆率", **params):
        """
        初始化杠杆率指标
        
        Parameters
        ----------
        name : str, optional
            指标名称，默认为"leverage"
        description : str, optional
            指标描述，默认为"杠杆率"
        **params : dict
            其他参数
        """
        super().__init__(name, description, **params)
    
    def validate_params(self) -> None:
        """验证参数有效性"""
        pass  # 无参数需要验证
    
    def calculate(self, data: pd.DataFrame, context: Dict[str, Any] = None) -> float:
        """
        计算杠杆率
        
        Parameters
        ----------
        data : pd.DataFrame
            不使用，杠杆信息从context中获取
        context : Dict[str, Any], optional
            计算上下文，必须包含'total_position_value'和'net_worth'键
            
        Returns
        -------
        float
            杠杆率，总仓位价值/净值
        """
        if context is None:
            return 1.0
        
        # 如果提供了杠杆率，直接使用
        if 'leverage' in context:
            return context['leverage']
        
        # 否则尝试从仓位和净值计算
        total_position_value = context.get('total_position_value', None)
        net_worth = context.get('net_worth', None)
        
        if total_position_value is None or net_worth is None or net_worth == 0:
            return 1.0
        
        leverage = abs(total_position_value) / net_worth
        
        return leverage
    
    def get_normalized_score(self, value: float) -> float:
        """
        将杠杆率转换为标准化风险分数
        
        Parameters
        ----------
        value : float
            杠杆率
            
        Returns
        -------
        float
            标准化分数 (0-100)
        """
        # 杠杆率越高，风险越大
        if value <= 1.0:  # 无杠杆，低风险
            return 10
        elif value <= 2.0:  # 低杠杆，中低风险
            return 10 + (value - 1.0) * 40
        elif value <= 3.0:  # 中等杠杆，中风险
            return 50 + (value - 2.0) * 20
        elif value <= 5.0:  # 高杠杆，高风险
            return 70 + (value - 3.0) * 7.5
        else:  # 极高杠杆，极高风险
            return max(100, 100 - (value - 5.0) * 0)
    
    def get_risk_direction(self) -> str:
        """杠杆率越高风险越大"""
        return "higher"

# 直接调用函数 - 方便快速使用
def calculate_volatility(returns_or_prices, period=252, is_returns=False):
    """
    计算波动率
    
    Parameters
    ----------
    returns_or_prices : pd.Series or np.array
        收益率或价格序列
    period : int, optional
        年化周期，默认为252（交易日）
    is_returns : bool, optional
        输入是否为收益率序列，默认为False
        
    Returns
    -------
    float
        波动率值，年化
    """
    if isinstance(returns_or_prices, pd.DataFrame):
        if 'returns' in returns_or_prices.columns:
            returns = returns_or_prices['returns']
        elif 'close' in returns_or_prices.columns:
            returns = returns_or_prices['close'].pct_change().dropna()
        else:
            raise ValueError("DataFrame必须包含'returns'或'close'列")
    else:
        # 处理Series或numpy数组
        if not is_returns:
            # 如果是价格序列，转换为收益率
            if isinstance(returns_or_prices, pd.Series):
                returns = returns_or_prices.pct_change().dropna()
            else:
                returns = np.diff(returns_or_prices) / returns_or_prices[:-1]
        else:
            returns = returns_or_prices
    
    # 计算波动率
    daily_vol = np.std(returns)
    annualized_vol = daily_vol * np.sqrt(period)
    
    return annualized_vol

def calculate_drawdown(prices_or_equity):
    """
    计算最大回撤及相关信息
    
    Parameters
    ----------
    prices_or_equity : pd.Series or np.array
        价格或权益曲线
        
    Returns
    -------
    dict
        包含最大回撤及相关信息的字典
    """
    if isinstance(prices_or_equity, pd.DataFrame):
        if 'equity' in prices_or_equity.columns:
            equity = prices_or_equity['equity']
        elif 'close' in prices_or_equity.columns:
            equity = prices_or_equity['close']
        else:
            raise ValueError("DataFrame必须包含'equity'或'close'列")
    else:
        equity = prices_or_equity
    
    # 确保数据是Series类型，便于使用pandas方法
    if not isinstance(equity, pd.Series):
        equity = pd.Series(equity)
    
    # 计算累积最大值
    running_max = equity.cummax()
    
    # 计算回撤
    drawdown = (equity / running_max - 1)
    
    # 获取最大回撤相关信息
    max_drawdown = abs(drawdown.min())
    max_drawdown_idx = drawdown.idxmin()
    
    # 寻找最大回撤的起点
    if max_drawdown > 0:
        peak_idx = running_max.loc[:max_drawdown_idx].idxmax()
        recovery_idx = None
        
        # 寻找恢复点（如果有）
        if max_drawdown_idx < equity.index[-1]:
            recovery_series = equity.loc[max_drawdown_idx:]
            recovery_mask = recovery_series >= equity.loc[peak_idx]
            if recovery_mask.any():
                recovery_idx = recovery_series[recovery_mask].index[0]
        
        result = {
            'max_drawdown': max_drawdown,
            'peak_date': peak_idx,
            'valley_date': max_drawdown_idx,
            'recovery_date': recovery_idx,
            'drawdown_length': (max_drawdown_idx - peak_idx).days if hasattr(peak_idx, 'days') else None
        }
    else:
        result = {
            'max_drawdown': 0.0,
            'peak_date': None,
            'valley_date': None,
            'recovery_date': None,
            'drawdown_length': None
        }
    
    return result

def calculate_sharpe_ratio(returns, risk_free_rate=0.0, period=252):
    """
    计算夏普比率
    
    Parameters
    ----------
    returns : pd.Series or np.array
        收益率序列
    risk_free_rate : float, optional
        无风险收益率，默认为0.0
    period : int, optional
        年化周期，默认为252（交易日）
        
    Returns
    -------
    float
        夏普比率
    """
    if isinstance(returns, pd.DataFrame):
        if 'returns' in returns.columns:
            returns = returns['returns']
        else:
            raise ValueError("DataFrame必须包含'returns'列")
    
    # 确保数据是numpy数组
    if isinstance(returns, pd.Series):
        returns = returns.values
    
    # 计算年化收益率和波动率
    annual_return = np.mean(returns) * period
    annual_volatility = np.std(returns) * np.sqrt(period)
    
    # 防止除以零
    if annual_volatility == 0:
        return 0.0
    
    # 计算夏普比率
    sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility
    
    return sharpe_ratio

def calculate_cvar(returns, confidence_level=0.95, method='historical', portfolio_value=1.0):
    """
    计算条件风险价值(CVaR)，也称为期望亏损(Expected Shortfall)
    
    Parameters
    ----------
    returns : pd.Series
        收益率序列
    confidence_level : float, optional
        置信水平，默认为0.95 (95%)
    method : str, optional
        计算方法，可选 'historical', 'parametric'，默认为'historical'
    portfolio_value : float, optional
        投资组合价值，默认为1.0
        
    Returns
    -------
    float
        条件风险价值(CVaR)，表示为金额损失
    """
    from risk.assessment.var_models import HistoricalVaRModel, ParametricVaRModel
    
    if method == 'historical':
        model = HistoricalVaRModel(confidence_level=confidence_level)
    elif method == 'parametric':
        model = ParametricVaRModel(confidence_level=confidence_level)
    else:
        raise ValueError(f"不支持的方法: {method}，可用选项: 'historical', 'parametric'")
    
    return model.calculate_conditional_var(returns, portfolio_value)

def calculate_sortino_ratio(returns, risk_free_rate=0.0, period=252):
    """
    计算索提诺比率
    
    Parameters
    ----------
    returns : pd.Series or np.array
        收益率序列
    risk_free_rate : float, optional
        无风险收益率，默认为0.0
    period : int, optional
        年化周期，默认为252（交易日）
        
    Returns
    -------
    float
        索提诺比率
    """
    if isinstance(returns, pd.DataFrame):
        if 'returns' in returns.columns:
            returns = returns['returns']
        else:
            raise ValueError("DataFrame必须包含'returns'列")
    
    # 确保数据是numpy数组
    if isinstance(returns, pd.Series):
        returns = returns.values
    
    # 计算年化收益率
    annual_return = np.mean(returns) * period
    
    # 计算下行风险
    downside_returns = returns.copy()
    downside_returns[returns > 0] = 0  # 只保留负收益率
    downside_std = np.std(downside_returns) * np.sqrt(period)
    
    # 防止除以零
    if downside_std == 0:
        return 0.0
    
    # 计算索提诺比率
    sortino_ratio = (annual_return - risk_free_rate) / downside_std
    
    return sortino_ratio

def calculate_calmar_ratio(returns=None, prices=None, period=252, max_drawdown_value=None):
    """
    计算卡玛比率
    
    Parameters
    ----------
    returns : pd.Series or np.array, optional
        收益率序列，如果提供则使用此序列计算年化收益率
    prices : pd.Series or np.array, optional
        价格序列，如果提供则使用此序列计算最大回撤
    period : int, optional
        年化周期，默认为252（交易日）
    max_drawdown_value : float, optional
        预先计算的最大回撤值，如果提供则直接使用
        
    Returns
    -------
    float
        卡玛比率
    """
    # 检查输入
    if returns is None and prices is None and max_drawdown_value is None:
        raise ValueError("必须提供returns或prices或max_drawdown_value中的至少一个")
    
    # 计算年化收益率
    if returns is not None:
        if isinstance(returns, pd.DataFrame):
            if 'returns' in returns.columns:
                returns = returns['returns']
            else:
                raise ValueError("DataFrame必须包含'returns'列")
        
        # 确保数据是numpy数组
        if isinstance(returns, pd.Series):
            returns = returns.values
        
        annual_return = np.mean(returns) * period
    elif prices is not None:
        if isinstance(prices, pd.DataFrame):
            if 'close' in prices.columns:
                prices = prices['close']
            else:
                raise ValueError("DataFrame必须包含'close'列")
        
        # 确保数据是Series
        if not isinstance(prices, pd.Series):
            prices = pd.Series(prices)
        
        # 计算收益率
        returns = prices.pct_change().dropna()
        annual_return = np.mean(returns) * period
    else:
        # 如果只给了最大回撤值但没有给收益率，无法计算
        raise ValueError("必须提供returns或prices以计算年化收益率")
    
    # 计算最大回撤
    if max_drawdown_value is not None:
        max_drawdown = max_drawdown_value
    elif prices is not None:
        drawdown_info = calculate_drawdown(prices)
        max_drawdown = drawdown_info['max_drawdown']
    else:
        # 如果只有收益率没有价格，通过累计收益率计算价格
        cum_returns = (1 + pd.Series(returns)).cumprod()
        drawdown_info = calculate_drawdown(cum_returns)
        max_drawdown = drawdown_info['max_drawdown']
    
    # 防止除以零
    if max_drawdown == 0:
        return float('inf')
    
    # 计算卡玛比率
    calmar_ratio = annual_return / max_drawdown
    
    return calmar_ratio

def calculate_var(returns, confidence_level=0.95, method='historical', portfolio_value=1.0):
    """
    计算风险价值(VaR)
    
    Parameters
    ----------
    returns : pd.Series
        收益率序列
    confidence_level : float, optional
        置信水平，默认为0.95 (95%)
    method : str, optional
        计算方法，可选 'historical', 'parametric', 'monte_carlo'，默认为'historical'
    portfolio_value : float, optional
        投资组合价值，默认为1.0
        
    Returns
    -------
    float
        风险价值(VaR)，表示为金额损失
    """
    from risk.assessment.var_models import calculate_var as var_calc
    return var_calc(returns, confidence_level, method, portfolio_value)