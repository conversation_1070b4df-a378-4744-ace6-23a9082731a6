"""
风险管理模块

提供风险评估、监控和可视化相关的功能。
"""

# 导入风险评估相关组件
from risk.assessment.base import RiskMetric, RiskReport, RiskAssessor
from risk.assessment.metrics import (
    calculate_volatility, calculate_sharpe_ratio, calculate_sortino_ratio,
    calculate_drawdown, calculate_calmar_ratio, calculate_var, calculate_cvar
)
# 已移除scoring模块，使用freqtrade保护系统替代
from risk.assessment.var_models import (
    VaRModel, HistoricalVaRModel, ParametricVaRModel, MonteCarloVaRModel
)

# 导入风险监控相关组件
from risk.monitoring.base import MonitorBase, MonitoringEvent, EventSeverity, EventHandler
from risk.monitoring.real_time import TradingSignalMonitor, CapitalChangeMonitor, PriceVolatilityMonitor
from risk.monitoring.alerts import (
    AlertMonitor, AlertRule, SeverityBasedAlertRule, 
    ThresholdAlertRule, FrequencyAlertRule, PatternMatchAlertRule
)
from risk.monitoring.notification import (
    LoggingHandler, FileStorageHandler, EmailNotificationHandler, 
    WebhookNotificationHandler, StrategyActionHandler
)

# 导入风险可视化相关组件
from risk.visualization.dashboard import MetricsPanel, StrategyRiskPanel, RiskDashboard

# 导入freqtrade保护系统
from risk.freqtrade_protections import (
    FreqtradeProtectionBase, CooldownPeriodProtection, MaxDrawdownProtection,
    StoplossGuardProtection, LowProfitPairsProtection, FreqtradeProtectionManager
)

# 外部框架适配器已移除，使用freqtrade保护系统

__all__ = [
    # 风险评估
    'RiskMetric', 'RiskReport', 'RiskAssessor',
    'calculate_volatility', 'calculate_sharpe_ratio', 'calculate_sortino_ratio',
    'calculate_drawdown', 'calculate_calmar_ratio', 'calculate_var', 'calculate_cvar',
# 已移除老的评分系统，使用freqtrade保护系统替代
    'VaRModel', 'HistoricalVaRModel', 'ParametricVaRModel', 'MonteCarloVaRModel',
    
    # 风险监控
    'MonitorBase', 'MonitoringEvent', 'EventSeverity', 'EventHandler',
    'TradingSignalMonitor', 'CapitalChangeMonitor', 'PriceVolatilityMonitor',
    'AlertMonitor', 'AlertRule', 'SeverityBasedAlertRule',
    'ThresholdAlertRule', 'FrequencyAlertRule', 'PatternMatchAlertRule',
    'LoggingHandler', 'FileStorageHandler', 'EmailNotificationHandler',
    'WebhookNotificationHandler', 'StrategyActionHandler',
    
    # 风险可视化
    'MetricsPanel', 'StrategyRiskPanel', 'RiskDashboard',
    
    # freqtrade保护系统
    'FreqtradeProtectionBase', 'CooldownPeriodProtection', 'MaxDrawdownProtection',
    'StoplossGuardProtection', 'LowProfitPairsProtection', 'FreqtradeProtectionManager'
]

# 版本信息
__version__ = '0.1.0' 