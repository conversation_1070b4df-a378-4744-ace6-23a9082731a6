#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
市场数据API - CCXT标准化版本

提供符合CCXT标准的市场数据获取接口。
支持多数据源、缓存管理和数据转换功能。
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Union, Tuple

import pandas as pd

from data.base import DataSource, DataStorage
from data.structures import OHLCVColumns, clean_ohlcv_dataframe
from data.utils import resample_ohlcv, merge_dataframes
from data.storage.optimized_storage import OptimizedStorage


class MarketDataAPI:
    """
    市场数据API - CCXT标准化版本
    
    提供符合CCXT标准的市场数据获取接口。
    支持多数据源、缓存管理和数据转换功能。
    
    CCXT标准接口:
    - get_ohlcv(symbol, timeframe, since=None, limit=None) -> pd.DataFrame
    - fetch_ohlcv(symbol, timeframe, since=None, limit=None) -> List[List]
    """
    
    def __init__(self, storage: DataStorage):
        """
        初始化市场数据API
        
        Args:
            storage: 数据存储对象
        """
        self.storage = storage
        self.data_sources: Dict[str, DataSource] = {}
        self.default_data_source: Optional[str] = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def register_data_source(self, name: str, data_source: DataSource, 
                           is_default: bool = False) -> None:
        """
        注册数据源
        
        Args:
            name: 数据源名称
            data_source: 数据源对象
            is_default: 是否设为默认数据源
        """
        self.data_sources[name] = data_source
        self.logger.info(f"注册数据源: {name}")
        
        if is_default or self.default_data_source is None:
            self.default_data_source = name
            self.logger.info(f"设置默认数据源: {name}")
    
    def get_ohlcv(self, symbol: str, timeframe: str, 
                  since: Optional[int] = None, limit: Optional[int] = None,
                  data_source: Optional[str] = None) -> pd.DataFrame:
        """
        获取OHLCV数据 - CCXT标准接口
        
        Args:
            symbol: 交易对符号 (如 'BTC/USDT')
            timeframe: 时间周期 (如 '1m', '5m', '1h', '1d')
            since: 开始时间戳(毫秒) (可选)
            limit: 获取的最大数据点数量 (可选，默认100)
            data_source: 数据源名称 (可选)
            
        Returns:
            包含OHLCV数据的DataFrame，索引为时间戳
        """
        # 处理数据源参数
        source_name = data_source or self.default_data_source
        if not source_name or source_name not in self.data_sources:
            raise ValueError(f"未指定有效的数据源: {source_name}")
        
        source = self.data_sources[source_name]
        self.logger.debug(f"使用数据源: {source_name}")
        
        # 设置默认limit
        if limit is None:
            limit = 100
        
        # 转换时间戳为datetime
        if since is not None:
            start_time = datetime.fromtimestamp(since / 1000)
        else:
            # 使用当前时间减去估算的时间范围
            end_time = datetime.now()
            timeframe_to_hours = {
                "1m": 1/60, "5m": 5/60, "15m": 15/60, "30m": 30/60,
                "1h": 1, "2h": 2, "4h": 4, "6h": 6, "8h": 8, "12h": 12,
                "1d": 24, "3d": 72, "1w": 168, "1M": 720
            }
            hours_back = limit * timeframe_to_hours.get(timeframe, 1)
            start_time = end_time - timedelta(hours=hours_back)
        
        # 计算结束时间
        end_time = datetime.now()
        
        # 调用数据源获取数据
        try:
            data = source.fetch_data(symbol, timeframe, start_time, end_time)
            
            if not data.empty and limit:
                # 限制数据点数量，保留最新的数据
                if len(data) > limit:
                    data = data.iloc[-limit:]
            
            self.logger.debug(f"获取到{len(data)}条{symbol} {timeframe}数据")
            return data
            
        except Exception as e:
            self.logger.error(f"获取数据失败: {symbol}, {timeframe}, 错误: {e}")
            return pd.DataFrame()
    
    def fetch_ohlcv(self, symbol: str, timeframe: str, 
                    since: Optional[int] = None, limit: Optional[int] = None,
                    data_source: Optional[str] = None) -> List[List]:
        """
        获取OHLCV数据 - CCXT原生格式
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期
            since: 开始时间戳(毫秒) (可选)
            limit: 获取的最大数据点数量 (可选)
            data_source: 数据源名称 (可选)
            
        Returns:
            CCXT标准格式的OHLCV数据: [[timestamp, open, high, low, close, volume], ...]
        """
        df = self.get_ohlcv(symbol, timeframe, since, limit, data_source)
        
        if df.empty:
            return []
        
        # 转换为CCXT标准格式
        result = []
        for timestamp, row in df.iterrows():
            result.append([
                int(timestamp.timestamp() * 1000),  # 转换为毫秒时间戳
                float(row.get('open', 0)),
                float(row.get('high', 0)),
                float(row.get('low', 0)),
                float(row.get('close', 0)),
                float(row.get('volume', 0))
            ])
        
        return result
    
    def get_symbols(self, data_source: Optional[str] = None) -> List[str]:
        """
        获取支持的交易对列表
        
        Args:
            data_source: 数据源名称 (可选)
            
        Returns:
            交易对代码列表
        """
        if data_source:
            if data_source not in self.data_sources:
                raise ValueError(f"未知的数据源: {data_source}")
            return self.data_sources[data_source].get_symbols()
        elif self.default_data_source:
            return self.data_sources[self.default_data_source].get_symbols()
        else:
            return []
    
    def get_timeframes(self, data_source: Optional[str] = None) -> List[str]:
        """
        获取支持的时间周期列表
        
        Args:
            data_source: 数据源名称 (可选)
            
        Returns:
            时间周期列表
        """
        if data_source:
            if data_source not in self.data_sources:
                raise ValueError(f"未知的数据源: {data_source}")
            return self.data_sources[data_source].get_timeframes()
        elif self.default_data_source:
            return self.data_sources[self.default_data_source].get_timeframes()
        else:
            return []
    
    def resample_data(self, data: pd.DataFrame, target_timeframe: str) -> pd.DataFrame:
        """
        重采样数据到指定的时间周期
        
        Args:
            data: 包含OHLCV数据的DataFrame
            target_timeframe: 目标时间周期
            
        Returns:
            重采样后的DataFrame
        """
        if data.empty:
            return data
        
        return resample_ohlcv(data, target_timeframe)
    
    def filter_data(self, data: pd.DataFrame, 
                   start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None,
                   columns: Optional[List[str]] = None) -> pd.DataFrame:
        """
        筛选数据
        
        Args:
            data: 包含OHLCV数据的DataFrame
            start_time: 开始时间 (可选)
            end_time: 结束时间 (可选)
            columns: 需要的列 (可选)
            
        Returns:
            筛选后的DataFrame
        """
        if data.empty:
            return data
        
        result = data.copy()
        
        # 按时间筛选
        if start_time:
            result = result[result.index >= start_time]
        
        if end_time:
            result = result[result.index <= end_time]
        
        # 按列筛选
        if columns:
            # 确保所有请求的列都存在
            available_columns = [col for col in columns if col in result.columns]
            if available_columns:
                result = result[available_columns]
        
        return result 