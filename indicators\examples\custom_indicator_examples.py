#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自定义指标示例

展示如何使用自定义指标框架创建各种组合指标和信号。
"""

# 添加项目根目录到Python路径中
import sys
import os

# 获取当前文件所在的目录
current_dir = os.path.dirname(os.path.abspath(__file__))

# 获取项目的根目录(向上两级)
project_root = os.path.abspath(os.path.join(current_dir, "../.."))

# 将项目根目录添加到Python路径
if project_root not in sys.path:
    sys.path.insert(0, project_root)
    print(f"已将项目根目录添加到Python路径: {project_root}")

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta

# 导入指标模块
from indicators.base import Indicator
from indicators.trend import SMA, EMA, MACD, BollingerBands
from indicators.oscillators import RSI
from indicators.volatility import AverageTrueRange as ATR
from indicators.custom import (
    create_custom_indicator, 
    create_operation,
    create_transform,
    create_signal_generator,
    create_from_template
)
from indicators.custom.builder import IndicatorBuilder


def generate_sample_data(days=200):
    """
    生成示例OHLCV数据
    
    Parameters
    ----------
    days : int, optional
        数据天数，默认为200
        
    Returns
    -------
    pd.DataFrame
        示例OHLCV数据
    """
    np.random.seed(42)
    today = datetime.now().date()
    dates = [today - timedelta(days=i) for i in range(days)]
    dates.reverse()
    
    # 生成价格序列（带有趋势和波动）
    close = np.zeros(days)
    close[0] = 100
    
    # 添加长期趋势
    trend = np.linspace(0, 20, days)
    
    # 添加周期性波动
    cycles = 10 * np.sin(np.linspace(0, 5 * np.pi, days))
    
    # 添加随机波动
    noise = np.random.normal(0, 1, days)
    
    # 组合各成分
    for i in range(1, days):
        close[i] = close[i-1] + 0.1 * trend[i] + cycles[i] - cycles[i-1] + noise[i]
    
    # 确保价格为正值
    close = np.maximum(10, close)
    
    # 生成其他OHLCV数据
    high = close + np.random.uniform(0, 5, days)
    low = close - np.random.uniform(0, 5, days)
    open_price = low + np.random.uniform(0, high - low, days)
    volume = np.random.uniform(1000, 5000, days) * (1 + 0.1 * np.sign(close - np.roll(close, 1)))
    volume[0] = 2000
    
    # 创建DataFrame
    df = pd.DataFrame({
        'date': dates,
        'open': open_price,
        'high': high,
        'low': low,
        'close': close,
        'volume': volume
    })
    
    df['date'] = pd.to_datetime(df['date'])
    df.set_index('date', inplace=True)
    
    return df


def example_1_basic_custom_indicator():
    """
    基本自定义指标示例
    
    创建一个结合SMA和EMA的指标。
    """
    print("示例1: 基本自定义指标")
    
    # 生成示例数据
    df = generate_sample_data()
    
    # 创建一个简单的自定义指标
    custom_ind = (create_custom_indicator("SMA_EMA_Combo")
                 .add(SMA(window=10, column='close'))
                 .add(EMA(window=20, column='close'))
                 .build())
    
    # 计算指标
    result = custom_ind.calculate(df)
    
    # 绘制结果
    plt.figure(figsize=(12, 6))
    plt.plot(result.index, result['close'], label='Close Price')
    plt.plot(result.index, result['SMA_10'], label='SMA(10)')
    plt.plot(result.index, result['EMA_20'], label='EMA(20)')
    
    plt.title('SMA-EMA combination indicator')
    plt.xlabel('date')
    plt.ylabel('price')
    plt.grid(True)
    plt.legend()
    plt.tight_layout()
    plt.show()
    
    print("SMA(10)与EMA(20)的组合指标已创建")
    return result


def example_2_operations():
    """
    指标运算示例
    
    展示如何进行指标之间的算术运算。
    """
    print("示例2: 指标运算")
    
    # 生成示例数据
    df = generate_sample_data()
    
    # 创建两个移动平均指标
    sma_fast = SMA(window=10, column='close')
    sma_slow = SMA(window=30, column='close')
    
    # 计算两者的差值（作为指标发散度）
    diff_operation = create_operation('subtract', sma_fast, sma_slow)
    
    # 创建自定义指标
    custom_ind = (create_custom_indicator("MA_Divergence")
                 .add(sma_fast)
                 .add(sma_slow)
                 .build())
    
    # 计算指标
    result = custom_ind.calculate(df)
    
    # 应用运算
    result = diff_operation.apply(result)
    
    # 绘制结果
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), gridspec_kw={'height_ratios': [3, 1]})
    
    # 价格和均线
    ax1.plot(result.index, result['close'], label='Close Price')
    ax1.plot(result.index, result['SMA_10'], label='SMA(10)')
    ax1.plot(result.index, result['SMA_30'], label='SMA(30)')
    ax1.set_title('moving average line')
    ax1.set_ylabel('price')
    ax1.grid(True)
    ax1.legend()
    
    # 差值
    ax2.plot(result.index, result['SMA_10_minus_SMA_30'], color='green')
    ax2.axhline(y=0, color='r', linestyle='-')
    ax2.set_title('SMA difference (SMA10 - SMA30)')
    ax2.set_xlabel('date')
    ax2.set_ylabel('difference')
    ax2.grid(True)
    
    plt.tight_layout()
    plt.show()
    
    print("moving average line difference indicator created")
    return result


def example_3_transforms():
    """
    数据转换示例
    
    展示如何应用各种时间序列转换。
    """
    print("示例3: 数据转换")
    
    # 生成示例数据
    df = generate_sample_data()
    
    # 创建几个转换
    pct_change = create_transform('pct_change', 'close', periods=1)
    rolling_mean = create_transform('rolling', 'close', window=10, function='mean')
    rolling_std = create_transform('rolling', 'close', window=10, function='std')
    
    # 应用转换
    result = df.copy()
    result = pct_change.apply(result)
    result = rolling_mean.apply(result)
    result = rolling_std.apply(result)
    
    # 绘制结果
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10))
    
    # 原始价格和滚动均值
    ax1.plot(result.index, result['close'], label='Close')
    ax1.plot(result.index, result['close_mean_10'], label='Rolling Mean(10)')
    ax1.set_title('price and rolling mean')
    ax1.grid(True)
    ax1.legend()
    
    # 价格变化率
    ax2.plot(result.index, result['close_pct_1'], label='Price % Change')
    ax2.axhline(y=0, color='r', linestyle='-')
    ax2.set_title('price change rate')
    ax2.grid(True)
    ax2.legend()
    
    # 波动率
    ax3.plot(result.index, result['close_std_10'], label='Rolling Std(10)', color='purple')
    ax3.set_title('price volatility (10 day standard deviation)')
    ax3.set_xlabel('date')
    ax3.grid(True)
    ax3.legend()
    
    plt.tight_layout()
    plt.show()
    
    print("数据转换示例完成")
    return result


def example_4_signals():
    """
    信号生成示例
    
    展示如何生成交易信号。
    """
    print("示例4: 信号生成")
    
    # 生成示例数据
    df = generate_sample_data()
    
    # 计算移动平均线
    sma_fast = SMA(window=10, column='close')
    sma_slow = SMA(window=30, column='close')
    
    result = df.copy()
    result = sma_fast.calculate(result)
    result = sma_slow.calculate(result)
    
    # 创建交叉信号生成器
    cross_signal = create_signal_generator('cross', 
                                          fast_col='SMA_10', 
                                          slow_col='SMA_30',
                                          signal_name='sma_cross_signal')
    
    # 生成交叉信号
    result = cross_signal.generate(result)
    
    # 计算RSI并生成阈值信号
    rsi = RSI(window=14, column='close')
    result = rsi.calculate(result)
    
    threshold_signal = create_signal_generator('threshold',
                                              column='RSI_14',
                                              upper_threshold=70.0,
                                              lower_threshold=30.0,
                                              signal_name='rsi_signal')
    
    result = threshold_signal.generate(result)
    
    # 绘制结果
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 12), gridspec_kw={'height_ratios': [3, 1, 1]})
    
    # 价格和均线
    ax1.plot(result.index, result['close'], label='Close')
    ax1.plot(result.index, result['SMA_10'], label='SMA(10)')
    ax1.plot(result.index, result['SMA_30'], label='SMA(30)')
    
    # 在价格图上标记交叉信号
    buy_signals = result[result['sma_cross_signal'] == 1]
    sell_signals = result[result['sma_cross_signal'] == -1]
    
    ax1.scatter(buy_signals.index, buy_signals['close'], marker='^', color='green', s=100, label='Buy Signal')
    ax1.scatter(sell_signals.index, sell_signals['close'], marker='v', color='red', s=100, label='Sell Signal')
    
    ax1.set_title('moving average line cross signal')
    ax1.grid(True)
    ax1.legend()
    
    # RSI
    ax2.plot(result.index, result['RSI_14'], color='purple')
    ax2.axhline(y=30, color='g', linestyle='--')
    ax2.axhline(y=70, color='r', linestyle='--')
    ax2.set_title('RSI(14)')
    ax2.set_ylim(0, 100)
    ax2.grid(True)
    
    # RSI信号
    buy_signals_rsi = result[result['rsi_signal'] == 1]
    sell_signals_rsi = result[result['rsi_signal'] == -1]
    
    ax2.scatter(buy_signals_rsi.index, buy_signals_rsi['RSI_14'], marker='^', color='green', s=100)
    ax2.scatter(sell_signals_rsi.index, sell_signals_rsi['RSI_14'], marker='v', color='red', s=100)
    
    # 信号图
    ax3.plot(result.index, result['sma_cross_signal'], label='SMA Cross Signal', color='blue')
    ax3.plot(result.index, result['rsi_signal'], label='RSI Signal', color='orange')
    ax3.set_title('signal comparison')
    ax3.set_xlabel('date')
    ax3.set_yticks([-1, 0, 1])
    ax3.set_yticklabels(['sell', 'hold', 'buy'])
    ax3.grid(True)
    ax3.legend()
    
    plt.tight_layout()
    plt.show()
    
    print("信号生成示例完成")
    return result


def example_5_templates():
    """
    模板指标示例
    
    展示如何使用预定义的指标模板。
    """
    print("示例5: 模板指标")
    
    # 生成示例数据
    df = generate_sample_data()
    
    # 使用双均线交叉模板
    dual_ma = create_from_template('dual_ma_crossover', 
                                  fast_period=5,
                                  slow_period=20,
                                  column='close',
                                  build=True)
    
    # 计算指标
    result = dual_ma.calculate(df)
    
    # 打印列名以便调试
    print("双均线模板的列名:", result.columns.tolist())
    
    # 绘制双均线系统
    fig1, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), gridspec_kw={'height_ratios': [3, 1]})
    
    ax1.plot(result.index, result['close'], label='Close')
    ax1.plot(result.index, result['SMA_5'], label='SMA(5)')
    ax1.plot(result.index, result['SMA_20'], label='SMA(20)')
    
    # 在价格图上标记交叉信号
    buy_signals = result[result['MA_crossover_signal'] == 1]
    sell_signals = result[result['MA_crossover_signal'] == -1]
    
    ax1.scatter(buy_signals.index, buy_signals['close'], marker='^', color='green', s=100, label='Buy')
    ax1.scatter(sell_signals.index, sell_signals['close'], marker='v', color='red', s=100, label='Sell')
    
    ax1.set_title('dual moving average line crossover system')
    ax1.grid(True)
    ax1.legend()
    
    ax2.plot(result.index, result['MA_crossover_signal'], color='blue')
    ax2.set_title('cross signal')
    ax2.set_xlabel('date')
    ax2.set_yticks([-1, 0, 1])
    ax2.set_yticklabels(['sell', 'hold', 'buy'])
    ax2.grid(True)
    
    plt.tight_layout()
    plt.show()
    
    print("模板指标示例完成")
    return result


def example_6_custom_complex_indicator():
    """
    复杂自定义指标示例
    
    创建一个组合多个指标并生成交易信号的复杂指标。
    """
    print("示例6: 复杂自定义指标")
    
    # 生成示例数据
    df = generate_sample_data()
    
    # 创建一个组合指标
    # 将RSI、布林带和成交量指标组合起来
    builder = IndicatorBuilder().set_name("VolumeRSIStrategy")
    
    # 添加基础指标组件
    builder.add(RSI(window=14, column='close'))
    builder.add(BollingerBands(window=20, num_std=2.0, column='close'))
    builder.add(SMA(window=20, column='volume', name='VolumeSMA'))
    
    # 保存成交量SMA列名
    volume_sma_column = 'SMA_20'  # 默认SMA列名
    
    # 添加成交量比例计算
    def calculate_volume_ratio(data, comp_results):
        result = data.copy()
        
        # 查找SMA_20列，这里需要注意的是SMA指标生成的列名可能与指定的名称不同
        nonlocal volume_sma_column
        for cr in comp_results:
            cols = cr.columns.tolist()
            for col in cols:
                if 'SMA_20' in col and col != 'BB_Middle_20':
                    volume_sma_column = col
                    break
        
        # 使用找到的SMA列名计算成交量比例
        result['volume_ratio'] = result['volume'] / result[volume_sma_column]
        
        return result
    
    builder.apply(calculate_volume_ratio)
    
    # 添加信号生成逻辑
    def generate_custom_signal(data, comp_results):
        result = data.copy()
        
        # 初始化信号列
        result['custom_signal'] = 0
        
        # 布林带触及
        price_gt_upper = result['close'] > result['BB_Upper_20_2.0']
        price_lt_lower = result['close'] < result['BB_Lower_20_2.0']
        
        # RSI超买超卖
        rsi_overbought = result['RSI_14'] > 70
        rsi_oversold = result['RSI_14'] < 30
        
        # 成交量确认
        volume_high = result['volume_ratio'] > 1.5
        
        # 生成买入信号：价格触及下轨 + RSI超卖 + 成交量放大
        buy_condition = price_lt_lower & rsi_oversold & volume_high
        result.loc[buy_condition, 'custom_signal'] = 1
        
        # 生成卖出信号：价格触及上轨 + RSI超买 + 成交量放大
        sell_condition = price_gt_upper & rsi_overbought & volume_high
        result.loc[sell_condition, 'custom_signal'] = -1
        
        return result
    
    builder.apply(generate_custom_signal)
    
    # 构建复杂指标
    complex_indicator = builder.build()
    
    # 计算指标
    result = complex_indicator.calculate(df)
    
    # 绘制结果
    fig, (ax1, ax2, ax3, ax4) = plt.subplots(4, 1, figsize=(12, 14), gridspec_kw={'height_ratios': [3, 1, 1, 1]})
    
    # 价格和布林带
    ax1.plot(result.index, result['close'], label='Close')
    ax1.plot(result.index, result['BB_Middle_20'], label='SMA(20)')
    ax1.plot(result.index, result['BB_Upper_20_2.0'], label='Upper Band')
    ax1.plot(result.index, result['BB_Lower_20_2.0'], label='Lower Band')
    ax1.fill_between(result.index, result['BB_Upper_20_2.0'], result['BB_Lower_20_2.0'], alpha=0.1, color='gray')
    
    # 标记自定义信号
    buy_signals = result[result['custom_signal'] == 1]
    sell_signals = result[result['custom_signal'] == -1]
    
    ax1.scatter(buy_signals.index, buy_signals['close'], marker='^', color='green', s=100, label='Buy')
    ax1.scatter(sell_signals.index, sell_signals['close'], marker='v', color='red', s=100, label='Sell')
    
    ax1.set_title('complex indicator strategy')
    ax1.grid(True)
    ax1.legend()
    
    # RSI
    ax2.plot(result.index, result['RSI_14'], color='purple')
    ax2.axhline(y=30, color='g', linestyle='--')
    ax2.axhline(y=70, color='r', linestyle='--')
    ax2.set_title('RSI(14)')
    ax2.set_ylim(0, 100)
    ax2.grid(True)
    
    # 成交量和均值
    ax3.bar(result.index, result['volume'], alpha=0.3, label='Volume')
    ax3.plot(result.index, result[volume_sma_column], color='orange', label=f'Volume {volume_sma_column}')
    ax3.set_title('volume')
    ax3.grid(True)
    ax3.legend()
    
    # 成交量比率
    ax4.plot(result.index, result['volume_ratio'], color='blue')
    ax4.axhline(y=1.5, color='r', linestyle='--')
    ax4.axhline(y=1.0, color='g', linestyle='--')
    ax4.set_title('volume ratio (Volume/SMA)')
    ax4.set_xlabel('date')
    ax4.grid(True)
    
    plt.tight_layout()
    plt.show()
    
    print("复杂自定义指标示例完成")
    return result


def run_all_examples():
    """
    运行所有示例
    
    包括基本自定义指标、指标运算、数据转换、信号生成、模板指标和复杂指标。
    所有示例现在可以正常运行，修复了之前的几个问题：
    1. 修复了自定义指标中组件结果的列名传递问题
    2. 修复了当使用多个apply函数时可能丢失中间结果的问题
    3. 解决了实际列名与预期列名不同的兼容性问题
    """
    print("\n=== 运行自定义指标示例 ===\n")
    
    example_1_basic_custom_indicator()
    print("\n" + "-" * 50 + "\n")
    
    example_2_operations()
    print("\n" + "-" * 50 + "\n")
    
    example_3_transforms()
    print("\n" + "-" * 50 + "\n")
    
    example_4_signals()
    print("\n" + "-" * 50 + "\n")
    
    example_5_templates()
    print("\n" + "-" * 50 + "\n")
    
    example_6_custom_complex_indicator()
    
    print("\n=== 所有示例已成功运行 ===")
    
    
def test_imports():
    """测试所有需要的模块是否已正确导入"""
    print("测试导入...")
    print(f"IndicatorBuilder 已导入: {IndicatorBuilder.__name__}")
    print(f"SMA 已导入: {SMA.__name__}")
    print(f"RSI 已导入: {RSI.__name__}")
    print(f"BollingerBands 已导入: {BollingerBands.__name__}")
    print(f"创建自定义指标函数已导入: {create_custom_indicator.__name__}")
    print("所有模块导入正常!")
    
    
if __name__ == "__main__":
    # 测试导入
    test_imports()
    
    # 运行所有示例
    run_all_examples() 