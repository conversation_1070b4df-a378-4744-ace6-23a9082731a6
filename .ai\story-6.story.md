# Epic-2: 指标模块开发
# Story-2: 自定义指标创建框架

## Story

**作为** 量化交易系统开发者和用户
**我想要** 一个灵活的自定义指标创建框架
**以便于** 能够设计和实现自己的技术指标，而不仅限于系统已有的指标库

## 状态

已完成

## 上下文

在完成基础技术指标库的实现后，我们需要提供一个灵活的框架，让用户能够轻松创建自定义指标。这个Story将实现一个自定义指标创建框架，使用户能够组合现有指标、定义新的计算逻辑、添加条件信号生成等功能，从而满足不同交易策略的特殊需求。

## 估算

Story Points: 4

## 任务

1. - [x] 设计自定义指标创建框架
   1. - [x] 定义自定义指标接口和抽象类
   2. - [x] 设计组件化架构，支持指标组合
   3. - [x] 规划指标参数和信号生成机制
   4. - [x] 设计指标注册和管理系统

2. - [x] 实现通用指标组合工具
   1. - [x] 实现指标加、减、乘、除等运算
   2. - [x] 创建逻辑比较和条件判断功能
   3. - [x] 实现时间序列转换和变换工具
   4. - [x] 开发数据窗口和聚合功能

3. - [x] 开发指标创建器类
   1. - [x] 实现IndicatorBuilder构建器模式
   2. - [x] 添加链式API支持
   3. - [x] 创建指标模板和工厂方法
   4. - [x] 添加自定义函数支持

4. - [x] 实现信号生成框架
   1. - [x] 创建信号定义和配置接口
   2. - [x] 实现多条件信号生成器
   3. - [x] 添加信号过滤和确认机制
   4. - [x] 开发信号可视化工具

5. - [x] 创建自定义指标实例和示例
   1. - [x] 开发复合趋势指标示例
   2. - [x] 创建自定义振荡器示例
   3. - [x] 实现多指标交叉信号示例
   4. - [x] 构建条件信号生成示例

6. - [x] 编写测试和文档
   1. - [x] 开发单元测试用例
   2. - [x] 创建集成测试

## 约束

- 自定义指标框架应与现有指标系统无缝集成
- 应提供直观的API，降低创建自定义指标的难度
- 需要支持高效的向量化操作，避免循环
- 指标组合不应显著影响性能
- 应提供完整的错误处理和参数验证
- 框架应足够灵活，支持各种类型的指标创建

## 数据模型

```python
# 自定义指标框架的核心接口和类
class CustomIndicator(Indicator):
    """自定义指标基类"""
    
    def __init__(self, name: str, **kwargs):
        super().__init__(name, "custom", **kwargs)
        self.components = []
        
    def add_component(self, component: Indicator) -> 'CustomIndicator':
        """添加指标组件"""
        self.components.append(component)
        return self
        
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算自定义指标值"""
        # 实现自定义计算逻辑
        pass

# 指标构建器
class IndicatorBuilder:
    """指标构建器，使用链式API创建自定义指标"""
    
    def __init__(self):
        self.steps = []
        
    def add(self, indicator: Indicator) -> 'IndicatorBuilder':
        """添加指标"""
        self.steps.append(('add', indicator))
        return self
        
    def apply(self, func, **params) -> 'IndicatorBuilder':
        """应用自定义函数"""
        self.steps.append(('apply', (func, params)))
        return self
        
    def build(self, name: str = "CustomIndicator") -> CustomIndicator:
        """构建自定义指标"""
        # 实现构建逻辑
        pass
```

## 结构

```
/indicators
├── custom/
│   ├── __init__.py
│   ├── builder.py           # 指标构建器
│   ├── operations.py        # 指标运算和操作
│   ├── signals.py           # 信号生成框架
│   ├── templates.py         # 指标模板
│   └── transforms.py        # 数据变换工具
├── examples/
│   ├── custom_indicator_examples.py
│   └── signal_examples.py
```

## 开发注意事项

- 确保自定义指标框架足够灵活，能满足各种需求
- 优化组合指标的性能，特别是处理大数据量时
- 提供详细的文档和示例，帮助用户理解如何创建自定义指标
- 确保自定义指标与基础指标库保持一致的接口和行为
- 考虑添加指标注册和发现机制，便于管理和复用

## 完成情况

已完成：
1. 设计了自定义指标创建框架的整体架构，包括：
   - 定义了CustomIndicator类，继承自基础的Indicator类
   - 创建了IndicatorBuilder构建器，支持链式API
   - 设计了模块化的组件结构，包括operations、transforms、signals和templates
   - 建立了自定义指标与基础指标的无缝集成机制

2. 实现了通用指标组合工具：
   - 在operations.py模块中实现了Add、Subtract、Multiply、Divide等算术操作类
   - 添加了Compare类支持各种比较操作（大于、小于、等于等）
   - 在transforms.py模块中实现了多种数据转换工具，如Shift、RollingTransform、DiffTransform和PctChangeTransform
   - 实现了灵活的窗口计算和数据聚合功能

3. 开发了指标创建器类：
   - 在builder.py模块中实现了IndicatorBuilder类，使用构建器模式
   - 添加了链式API支持，使创建复杂指标变得简单直观
   - 在templates.py模块中创建了多个指标模板和工厂方法
   - 实现了自定义函数支持，可以灵活定义计算逻辑

4. 实现了信号生成框架：
   - 在signals.py模块中创建了SignalGenerator基类和多种信号生成器
   - 实现了CrossSignal（交叉信号）、ThresholdSignal（阈值信号）和PatternSignal（模式信号）
   - 添加了信号过滤和确认机制，提高信号质量
   - 在示例中展示了信号的可视化方法

5. 创建了丰富的自定义指标示例：
   - 在custom_indicator_examples.py中实现了多种示例
   - 包括基本示例、指标运算示例、数据转换示例、信号生成示例、模板示例和复杂指标示例
   - 每个示例都包含了完整的计算和可视化展示

6. 编写了测试和文档：
   - 添加了测试脚本和解决了导入问题
   - 修复了在直接运行Python脚本时的包导入错误
   - 创建了README.md提供使用说明和文档
   - 添加了测试运行脚本，使测试更加便捷

## 聊天命令日志

在本次开发中，我们设计并实现了自定义指标创建框架。我们创建了以下主要模块：

1. `custom/__init__.py`：定义了模块的导入接口和版本管理
2. `custom/builder.py`：实现了CustomIndicator类和IndicatorBuilder构建器，支持组件化构建指标
3. `custom/operations.py`：实现了指标之间的算术和逻辑操作，包括Add、Subtract、Multiply、Divide和Compare等类
4. `custom/transforms.py`：实现了数据转换工具，如Shift、RollingTransform、DiffTransform和PctChangeTransform
5. `custom/signals.py`：实现了信号生成框架，包括CrossSignal、ThresholdSignal和PatternSignal等类
6. `custom/templates.py`：实现了几种常用的指标模板和工厂方法，提供快速构建复杂指标的能力
7. `examples/custom_indicator_examples.py`：提供了详细的示例展示如何使用框架创建各种自定义指标

为解决测试中的导入问题，我们进行了以下修复：

1. 将示例文件中的相对导入改为绝对导入，解决在直接运行脚本时的导入问题
2. 添加了pytest.ini配置文件，确保测试时能正确识别项目包路径
3. 创建了测试运行脚本tests/run_tests.py，帮助用户在命令行直接运行测试
4. 添加了在运行单个脚本时自动将项目根目录添加到Python路径的代码
5. 创建了项目README.md文件，提供了详细的使用说明和示例运行指南

这些修改共同解决了测试过程中遇到的导入问题，使项目的示例和测试脚本能够正常运行。所有任务已全部完成。 