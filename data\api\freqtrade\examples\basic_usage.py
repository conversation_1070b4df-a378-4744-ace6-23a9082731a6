#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Freqtrade接口基本使用示例

展示如何使用Freqtrade接口来发送交易信号、查询状态等基本操作。
"""

import sys
import os
import logging
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../..')))

from data.api.freqtrade import FreqtradeClient, TradeSignal, OrderType
from data.api.freqtrade.service import FreqtradeService
from data.api.freqtrade.converter import SignalConverter


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def client_basic_usage():
    """
    FreqtradeClient基本用法示例
    """
    # 初始化客户端
    # 注意：需要替换为实际的Freqtrade服务器地址、用户名和密码
    client = FreqtradeClient(
        server_url="http://localhost:8080",
        username="your_username",
        password="your_password"
    )
    
    # 测试连接
    if client.ping():
        logger.info("成功连接到Freqtrade服务器")
    else:
        logger.error("无法连接到Freqtrade服务器")
        return
    
    # 获取机器人状态
    try:
        bot_status = client.get_bot_status()
        logger.info(f"机器人状态: {bot_status.status}")
        logger.info(f"开放交易数量: {bot_status.trade_count}")
    except Exception as e:
        logger.error(f"获取机器人状态失败: {str(e)}")
    
    # 获取账户余额
    try:
        balances = client.get_account_balance()
        logger.info("账户余额:")
        for balance in balances:
            logger.info(f"  {balance.currency}: 可用 {balance.free}, 已用 {balance.used}, 总计 {balance.total}")
    except Exception as e:
        logger.error(f"获取账户余额失败: {str(e)}")
    
    # 创建交易信号
    signal = TradeSignal(
        pair="BTC/USDT",
        side="long",
        order_type=OrderType.MARKET,
        enter_tag="SuperAwesomeStrategy"
    )
    
    # 发送交易信号
    logger.info(f"发送交易信号: {signal.pair} {signal.side}")
    result = client.force_enter_trade(signal)
    
    if result.success:
        logger.info("交易信号发送成功")
        if result.tradeid:
            logger.info(f"交易ID: {result.tradeid}")
            
            # 等待5秒后关闭交易（仅作示例）
            logger.info("等待5秒后关闭交易...")
            time.sleep(5)
            
            # 关闭交易
            exit_result = client.force_exit_trade(result.tradeid)
            if exit_result.success:
                logger.info("交易已成功关闭")
            else:
                logger.error(f"关闭交易失败: {exit_result.error}")
    else:
        logger.error(f"交易信号发送失败: {result.error}")
    
    # 获取开放的交易
    try:
        open_trades = client.get_open_trades()
        logger.info(f"当前开放交易数量: {len(open_trades)}")
        for trade in open_trades:
            logger.info(f"  交易ID: {trade.get('tradeid')}, 交易对: {trade.get('pair')}, 状态: {trade.get('status')}")
    except Exception as e:
        logger.error(f"获取开放交易失败: {str(e)}")


def service_basic_usage():
    """
    FreqtradeService基本用法示例
    """
    # 初始化服务
    # 注意：需要替换为实际的Freqtrade服务器地址、用户名和密码
    service = FreqtradeService(
        server_url="http://localhost:8080",
        username="your_username",
        password="your_password",
        enable_async_processing=True  # 启用异步处理
    )
    
    # 创建交易信号（字典形式）
    signal_dict = {
        "pair": "ETH/USDT",
        "side": "long",
        "order_type": "market",
        "enter_tag": "ServiceExample",
        "timestamp": datetime.now().isoformat()
    }
    
    # 提交交易信号
    logger.info(f"提交交易信号: {signal_dict['pair']} {signal_dict['side']}")
    if service.submit_signal(signal_dict):
        logger.info("交易信号已提交到队列")
    else:
        logger.error("交易信号提交失败")
    
    # 等待处理完成
    logger.info("等待信号处理...")
    time.sleep(2)
    
    # 获取机器人状态
    bot_status = service.get_bot_status()
    if bot_status:
        logger.info(f"机器人状态: {bot_status.status}")
        logger.info(f"开放交易数量: {bot_status.trade_count}")
    
    # 获取账户余额
    balances = service.get_account_balance()
    logger.info("账户余额:")
    for balance in balances:
        logger.info(f"  {balance.currency}: 可用 {balance.free}, 总计 {balance.total}")
    
    # 获取开放的交易
    open_trades = service.get_open_trades()
    logger.info(f"当前开放交易数量: {len(open_trades)}")
    
    # 关闭服务（停止处理线程）
    service.stop_processing()
    logger.info("服务已关闭")


def signal_converter_example():
    """
    SignalConverter用法示例
    """
    # 从策略信号创建交易信号
    strategy_signal = {
        "pair": "ADA/USDT",
        "side": "long",
        "order_type": "limit",
        "rate": 1.25,
        "enter_tag": "ConverterExample",
        "stop_loss": 1.20,
        "take_profit": 1.35,
        "timeframe": "5m",
        "indicator_value": 0.75,  # 自定义元数据
        "signal_strength": "high"  # 自定义元数据
    }
    
    signal = SignalConverter.from_strategy_signal(strategy_signal)
    
    if signal:
        logger.info(f"成功转换信号: {signal.pair} {signal.side}")
        logger.info(f"订单类型: {signal.order_type}")
        logger.info(f"限价: {signal.rate}")
        logger.info(f"元数据: {signal.metadata}")
        
        # 转换为JSON
        json_str = SignalConverter.to_json(signal)
        logger.info(f"JSON: {json_str}")
        
        # 从JSON解析回交易信号
        parsed_signal = SignalConverter.from_json(json_str)
        if parsed_signal:
            logger.info("成功从JSON解析信号")
            logger.info(f"解析后的信号: {parsed_signal.pair} {parsed_signal.side}")
    else:
        logger.error("信号转换失败")


if __name__ == "__main__":
    logger.info("=== FreqtradeClient基本用法示例 ===")
    # 注释掉实际调用，防止意外连接到真实服务器
    # client_basic_usage()
    
    logger.info("\n=== FreqtradeService基本用法示例 ===")
    # service_basic_usage()
    
    logger.info("\n=== SignalConverter用法示例 ===")
    signal_converter_example()
    
    logger.info("\n完成所有示例") 