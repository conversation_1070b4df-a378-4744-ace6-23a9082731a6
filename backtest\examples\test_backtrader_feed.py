#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试Backtrader数据源

简单测试Backtrader的PandasData类的用法。
"""

import backtrader as bt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def create_test_data(n_days=200):
    """创建测试用的价格数据"""
    # 生成日期序列
    end_date = datetime.now()
    start_date = end_date - timedelta(days=n_days)
    dates = pd.date_range(start=start_date, end=end_date, freq='B')
    
    # 生成价格数据
    np.random.seed(42)
    price = 100
    prices = [price]
    
    for i in range(1, len(dates)):
        change_percent = np.random.normal(0.0005, 0.01)
        price = price * (1 + change_percent)
        prices.append(price)
    
    # 基于收盘价生成OHLCV数据
    closes = np.array(prices)
    highs = closes * (1 + np.random.uniform(0, 0.015, len(dates)))
    lows = closes * (1 - np.random.uniform(0, 0.015, len(dates)))
    opens = lows + np.random.uniform(0, 1, len(dates)) * (highs - lows)
    volumes = np.random.uniform(1000, 10000, len(dates)) * closes
    
    # 创建DataFrame
    df = pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    }, index=dates)
    
    return df

# 测试直接使用backtrader的PandasData
def test_original_pandas_data():
    print("测试原生PandasData类")
    
    # 创建测试数据
    data = create_test_data()
    print(f"数据示例：\n{data.head()}")
    
    # 创建cerebro实例
    cerebro = bt.Cerebro()
    
    # 将数据添加到cerebro
    pandas_data = bt.feeds.PandasData(dataname=data)
    cerebro.adddata(pandas_data)
    
    # 设置初始资金
    cerebro.broker.setcash(100000.0)
    
    # 运行回测
    print("\n运行回测...")
    result = cerebro.run()
    
    print("回测完成!")
    
    return result

if __name__ == "__main__":
    # 测试原生PandasData
    test_original_pandas_data() 