"""
信号转换模块

提供将内部系统交易信号转换为Freqtrade可识别格式的功能。
"""

from typing import Dict, Any, Optional
import datetime
import json
import logging

from .models import TradeSignal, OrderType

logger = logging.getLogger(__name__)


class SignalConverter:
    """
    交易信号转换器
    
    用于将内部交易信号格式转换为Freqtrade可识别的格式，并执行必要的验证。
    """
    
    @staticmethod
    def from_strategy_signal(strategy_signal: Dict[str, Any]) -> Optional[TradeSignal]:
        """
        将策略生成的信号转换为Freqtrade交易信号
        
        Parameters
        ----------
        strategy_signal : Dict[str, Any]
            策略生成的原始信号
            
        Returns
        -------
        Optional[TradeSignal]
            转换后的交易信号，如果转换失败则返回None
        """
        try:
            # 验证必须的字段
            if 'pair' not in strategy_signal:
                logger.error("信号缺少必需的'pair'字段")
                return None
                
            if 'side' not in strategy_signal:
                logger.error("信号缺少必需的'side'字段")
                return None
            
            # 标准化交易方向
            side = strategy_signal['side'].lower()
            if side not in ['long', 'short']:
                logger.error(f"无效的交易方向: {side}，必须是'long'或'short'")
                return None
            
            # 提取基本参数
            pair = strategy_signal['pair']
            
            # 创建信号对象
            signal = TradeSignal(
                pair=pair,
                side=side
            )
            
            # 设置可选参数
            if 'order_type' in strategy_signal:
                try:
                    order_type_str = strategy_signal['order_type'].lower()
                    signal.order_type = OrderType(order_type_str)
                except ValueError:
                    logger.warning(f"无效的订单类型: {strategy_signal['order_type']}，使用默认的'market'类型")
                    signal.order_type = OrderType.MARKET
            
            if 'amount' in strategy_signal:
                signal.amount = float(strategy_signal['amount'])
                
            if 'rate' in strategy_signal:
                signal.rate = float(strategy_signal['rate'])
                
            if 'enter_tag' in strategy_signal:
                signal.enter_tag = str(strategy_signal['enter_tag'])
                
            if 'leverage' in strategy_signal:
                signal.leverage = float(strategy_signal['leverage'])
                
            if 'stop_loss' in strategy_signal:
                signal.stop_loss = float(strategy_signal['stop_loss'])
                
            if 'take_profit' in strategy_signal:
                signal.take_profit = float(strategy_signal['take_profit'])
                
            if 'timeframe' in strategy_signal:
                signal.timeframe = str(strategy_signal['timeframe'])
                
            # 处理元数据
            metadata = {}
            for key, value in strategy_signal.items():
                if key not in ['pair', 'side', 'order_type', 'amount', 'rate', 
                              'enter_tag', 'leverage', 'stop_loss', 'take_profit', 
                              'timeframe', 'timestamp']:
                    metadata[key] = value
            
            if metadata:
                signal.metadata = metadata
                
            # 处理时间戳
            if 'timestamp' in strategy_signal:
                timestamp = strategy_signal['timestamp']
                if isinstance(timestamp, str):
                    try:
                        signal.timestamp = datetime.datetime.fromisoformat(timestamp)
                    except ValueError:
                        logger.warning(f"无法解析时间戳: {timestamp}，使用当前时间")
                elif isinstance(timestamp, (int, float)):
                    signal.timestamp = datetime.datetime.fromtimestamp(timestamp)
                elif isinstance(timestamp, datetime.datetime):
                    signal.timestamp = timestamp
            
            return signal
            
        except Exception as e:
            logger.error(f"转换交易信号时出错: {str(e)}")
            return None
    
    @staticmethod
    def to_json(signal: TradeSignal) -> str:
        """
        将交易信号转换为JSON字符串
        
        Parameters
        ----------
        signal : TradeSignal
            交易信号对象
            
        Returns
        -------
        str
            JSON字符串表示
        """
        signal_dict = {
            'pair': signal.pair,
            'side': signal.side,
            'order_type': signal.order_type.value,
            'timestamp': signal.timestamp.isoformat()
        }
        
        # 添加可选字段
        if signal.amount is not None:
            signal_dict['amount'] = signal.amount
            
        if signal.rate is not None:
            signal_dict['rate'] = signal.rate
            
        if signal.enter_tag is not None:
            signal_dict['enter_tag'] = signal.enter_tag
            
        if signal.leverage is not None:
            signal_dict['leverage'] = signal.leverage
            
        if signal.stop_loss is not None:
            signal_dict['stop_loss'] = signal.stop_loss
            
        if signal.take_profit is not None:
            signal_dict['take_profit'] = signal.take_profit
            
        if signal.timeframe is not None:
            signal_dict['timeframe'] = signal.timeframe
            
        # 添加元数据
        if signal.metadata:
            signal_dict['metadata'] = signal.metadata
            
        return json.dumps(signal_dict)
    
    @staticmethod
    def from_json(json_str: str) -> Optional[TradeSignal]:
        """
        从JSON字符串解析交易信号
        
        Parameters
        ----------
        json_str : str
            JSON字符串
            
        Returns
        -------
        Optional[TradeSignal]
            解析后的交易信号对象，如果解析失败则返回None
        """
        try:
            data = json.loads(json_str)
            return SignalConverter.from_strategy_signal(data)
        except json.JSONDecodeError as e:
            logger.error(f"解析JSON时出错: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"从JSON创建信号时出错: {str(e)}")
            return None 