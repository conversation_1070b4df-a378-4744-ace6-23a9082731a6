#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
策略示例脚本

展示如何使用VectorBT回测引擎进行多种策略的回测和比较。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import sys
from datetime import datetime, timedelta
import yfinance as yf

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入项目模块
from backtest.vectorbt import VectorBTEngine
from backtest.strategies import (
    MovingAverageCrossover,
    RSIStrategy,
    MACDStrategy,
    BollingerBandsStrategy
)
from backtest.analysis import generate_report, plot_performance
from data.sources.utils import download_crypto_data  # 导入加密货币数据下载函数


def download_data(symbol='BTC/USDT', start_date='2018-01-01', end_date='2023-01-01', use_crypto=True):
    """
    下载市场数据，优先使用加密货币数据
    
    Parameters
    ----------
    symbol : str
        交易对符号，例如'BTC/USDT'或股票代码'SPY'
    start_date : str
        起始日期
    end_date : str
        结束日期
    use_crypto : bool
        是否优先使用加密货币数据，默认为True
        
    Returns
    -------
    pd.DataFrame
        市场数据
    """
    data = None
    
    # 将日期字符串转换为datetime对象
    start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
    end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
    
    # 尝试从Binance获取加密货币数据
    if use_crypto and ('/' in symbol):
        print(f"尝试从Binance下载{symbol}数据...")
        try:
            # 使用我们开发的download_crypto_data函数下载数据
            data = download_crypto_data(
                symbol=symbol, 
                timeframe='1d',  # 使用日线数据
                start_time=start_datetime,
                end_time=end_datetime
            )
            
            # 检查数据是否成功获取
            if data is not None and len(data) > 0:
                print(f"成功从Binance获取{symbol}数据，共{len(data)}条记录")
                return data
            else:
                print("未能从Binance获取数据，将尝试其他数据源")
        except Exception as e:
            print(f"从Binance获取数据失败: {e}")
    
    # 如果不是加密货币或Binance获取失败，尝试使用yfinance
    try:
        # 对于非加密货币符号，直接使用原符号
        yf_symbol = symbol.replace('/', '-') if '/' in symbol else symbol
        
        print(f"尝试使用yfinance下载{yf_symbol}数据...")
        data = yf.download(yf_symbol, start=start_date, end=end_date)
        
        # 将列名转换为小写
        data.columns = [str(col).lower() for col in data.columns]
        
        # 检查数据是否为空或缺少关键列
        if len(data) == 0 or not all(col in map(str.lower, data.columns) for col in ['open', 'high', 'low', 'close']):
            raise ValueError("下载的数据为空或缺少关键列")
            
        # 确保索引频率正确设置
        data.index = pd.DatetimeIndex(data.index)
        # 设置为商业日频率，因为股票数据通常是交易日
        data = data.asfreq('B')
            
        print(f"成功下载 {yf_symbol} 数据，从 {start_date} 到 {end_date}")
        print(f"数据形状: {data.shape}")
        
    except Exception as e:
        print(f"下载数据出错: {e}")
        data = None
        
    # 如果下载失败或数据有问题，创建模拟数据
    if data is None or len(data) == 0:
        print("创建模拟数据...")
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')  # 使用日频率，对齐加密货币数据
        
        np.random.seed(42)
        price = 100
        prices = [price]
        
        for _ in range(1, len(date_range)):
            change_percent = np.random.normal(0.0005, 0.01)
            price *= (1 + change_percent)
            prices.append(price)
        
        data = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
            'close': prices,
            'volume': [np.random.randint(100000, 1000000) for _ in prices],
            'adj close': prices
        }, index=date_range)
        
        # 确保频率正确设置
        data.index.freq = 'D'
        
    return data


def create_strategy(strategy_type, **params):
    """
    创建策略
    
    Parameters
    ----------
    strategy_type : str
        策略类型
    **params : dict
        策略参数
        
    Returns
    -------
    Strategy
        策略实例
    """
    strategies = {
        'ma_cross': MovingAverageCrossover,
        'rsi': RSIStrategy,
        'macd': MACDStrategy,
        'bollinger': BollingerBandsStrategy
    }
    
    if strategy_type not in strategies:
        raise ValueError(f"不支持的策略类型: {strategy_type}")
    
    return strategies[strategy_type](**params)


def run_strategy_backtest(data, strategy, params, output_dir, name=None):
    """
    运行策略回测
    
    Parameters
    ----------
    data : pd.DataFrame
        市场数据
    strategy : Strategy
        策略实例
    params : dict
        回测参数
    output_dir : str
        输出目录
    name : str, optional
        策略名称，默认为None
        
    Returns
    -------
    tuple
        (BacktestResults, equity_curve)
    """
    print(f"\n运行 {strategy} 策略回测...")
    
    # 创建回测引擎
    engine = VectorBTEngine(data, **params)
    
    # 运行回测
    results = engine.run(strategy)
    
    # 获取权益曲线
    equity = results.equity
    
    # 如果提供了输出目录，保存结果
    if output_dir:
        strategy_name = name or str(strategy).replace(" ", "_")
        output_file = os.path.join(output_dir, f"{strategy_name}_results.html")
        
        # 创建输出目录（如果不存在）
        os.makedirs(output_dir, exist_ok=True)
        
        # 绘制并保存结果
        engine.plot()
        
    return results, equity


def compare_strategies(equities, output_dir):
    """
    比较不同策略的性能
    
    Parameters
    ----------
    equities : dict
        策略名称和净值曲线的字典
    output_dir : str
        输出目录
    """
    print("\n比较不同策略的性能...")
    
    # 创建比较图
    fig, ax = plt.subplots(figsize=(12, 6))
    
    for name, equity in equities.items():
        # 获取净值数据，处理可能是函数的情况
        equity_data = equity() if callable(equity) else equity
        
        # 归一化净值曲线
        normalized = equity_data / equity_data.iloc[0]
        ax.plot(normalized.index, normalized, label=name)
    
    # 设置图表属性
    ax.set_title('Strategy Comparison', fontsize=14)
    ax.set_ylabel('Normalized Equity')
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    # 保存图表
    plt.savefig(os.path.join(output_dir, 'strategy_comparison.png'))
    plt.close(fig)


def main():
    """主函数"""
    print("VectorBT策略示例")
    print("-" * 50)
    
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'output', 'strategies')
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置日期范围 - 使用更近的一年数据
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
    
    # 下载BTC/USDT数据
    data = download_data(symbol='BTC/USDT', start_date=start_date, end_date=end_date, use_crypto=True)
    
    # 设置回测参数
    backtest_params = {
        'initial_capital': 10000,
        'commission_rate': 0.001,
        'slippage': 0.0005,
        'freq': 'D',  # 添加频率参数，避免自动推断错误
    }
    
    # 创建和运行不同的策略
    strategies = {
        'MA Crossover (20, 50)': create_strategy('ma_cross', short_window=20, long_window=50),
        'RSI (14, 30, 70)': create_strategy('rsi', window=14, oversold=30, overbought=70),
        'MACD (12, 26, 9)': create_strategy('macd', fast_period=12, slow_period=26, signal_period=9),
        'Bollinger Bands (20, 2.0)': create_strategy('bollinger', window=20, num_std=2.0)
    }
    
    # 存储每个策略的净值曲线
    equities = {}
    
    # 运行每个策略的回测
    for name, strategy in strategies.items():
        results, equity = run_strategy_backtest(data, strategy, backtest_params, output_dir, name)
        equities[name] = equity
    
    # 比较不同策略的性能
    compare_strategies(equities, output_dir)
    
    print(f"\n完成！所有报告已保存至 {output_dir}")


if __name__ == '__main__':
    main() 