"""
数据API基础用法示例

展示如何使用数据API获取、处理和分析市场数据。
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

import pandas as pd
import matplotlib.pyplot as plt
from data.api.market_data import MarketDataAPI
from data.api.operations import DataOperationsAPI
from data.api.management import DataManagementAPI
from data.storage.optimized_storage import OptimizedStorage
from data.sources.ccxt_source import CCXTDataSource


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def main():
    """主函数"""
    setup_logging()
    
    # 创建存储对象
    storage_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../data/storage/data'))
    os.makedirs(storage_path, exist_ok=True)
    storage = OptimizedStorage(storage_path)
    
    # 创建数据源
    exchange_id = 'binance'
    ccxt_source = CCXTDataSource(exchange_id)
    
    # 创建API对象
    market_api = MarketDataAPI(storage)
    operations_api = DataOperationsAPI()
    management_api = DataManagementAPI(storage)
    
    # 注册数据源
    market_api.register_data_source('binance', ccxt_source, is_default=True)
    management_api.register_data_source('binance', ccxt_source, is_default=True)
    
    # 示例1: 使用CCXT标准化接口获取数据
    print("\n===== 示例1: CCXT标准化接口 =====")
    symbol = 'BTC/USDT'
    timeframe = '1d'
    limit = 30  # 获取最近30个数据点
    
    print(f"获取 {symbol} {timeframe} 数据，最近 {limit} 个数据点")
    data = market_api.get_ohlcv(symbol, timeframe, limit=limit)
    
    if not data.empty:
        print(f"获取到 {len(data)} 条数据")
        print("数据预览:")
        print(data.head())
        print(f"数据时间范围: {data.index.min()} 到 {data.index.max()}")
    else:
        print("未获取到数据")
        return
    
    # 示例2: 向后兼容接口演示
    print("\n===== 示例2: 向后兼容接口 =====")
    end_time = datetime.now()
    start_time = end_time - timedelta(days=30)
    
    print(f"使用向后兼容接口获取 {symbol} {timeframe} 数据，从 {start_time} 到 {end_time}")
    legacy_data = market_api.get_data(symbol, timeframe, start_time, end_time)
    
    if not legacy_data.empty:
        print(f"获取到 {len(legacy_data)} 条数据")
        print("数据和标准接口是否一致:", data.equals(legacy_data.tail(len(data))))
    
    # 示例3: CCXT原生格式
    print("\n===== 示例3: CCXT原生格式 =====")
    ohlcv_list = market_api.fetch_ohlcv(symbol, timeframe, limit=5)
    
    if ohlcv_list:
        print("CCXT原生格式数据 (前5条):")
        for i, candle in enumerate(ohlcv_list):
            timestamp_ms, o, h, l, c, v = candle
            dt = datetime.fromtimestamp(timestamp_ms / 1000)
            print(f"  {i+1}. {dt}: O={o:.2f} H={h:.2f} L={l:.2f} C={c:.2f} V={v:.2f}")
    
    # 示例4: 数据清洗和转换
    print("\n===== 示例4: 数据清洗和转换 =====")
    # 清洗数据
    cleaned_data = operations_api.clean_data(
        data, 
        fill_method="ffill",
        remove_outliers_method="zscore",
        outlier_threshold=3.0
    )
    
    # 转换数据
    transformed_data = operations_api.transform_data(
        cleaned_data,
        calculate_returns_flag=True,
        returns_type="pct_change",
        extract_time_features_flag=True
    )
    
    print("转换后的数据列:")
    print(transformed_data.columns.tolist())
    print("数据预览:")
    print(transformed_data.head())
    
    # 示例5: 添加技术指标
    print("\n===== 示例5: 添加技术指标 =====")
    data_with_indicators = operations_api.add_technical_indicators(
        transformed_data,
        add_ma=True,
        ma_periods=[5, 10, 20],
        add_rsi=True,
        add_macd=True,
        add_bbands=True
    )
    
    print("添加技术指标后的数据列:")
    print(data_with_indicators.columns.tolist())
    
    # 示例6: 数据可视化
    print("\n===== 示例6: 数据可视化 =====")
    plt.figure(figsize=(12, 8))
    
    # 绘制价格和移动平均线
    ax1 = plt.subplot(2, 1, 1)
    ax1.set_title(f"{symbol} 价格和移动平均线")
    ax1.plot(data_with_indicators.index, data_with_indicators['close'], label='价格')
    ax1.plot(data_with_indicators.index, data_with_indicators['ma_5_close'], label='MA5')
    ax1.plot(data_with_indicators.index, data_with_indicators['ma_20_close'], label='MA20')
    ax1.legend()
    ax1.grid(True)
    
    # 绘制RSI
    ax2 = plt.subplot(2, 1, 2)
    ax2.set_title("RSI指标")
    ax2.plot(data_with_indicators.index, data_with_indicators['rsi_14_close'], label='RSI(14)')
    ax2.axhline(y=70, color='r', linestyle='--')
    ax2.axhline(y=30, color='g', linestyle='--')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    
    # 保存图表
    output_path = os.path.join(os.path.dirname(__file__), 'basic_usage_chart.png')
    plt.savefig(output_path)
    print(f"图表已保存到: {output_path}")
    
    # 示例7: 数据管理
    print("\n===== 示例7: 数据管理 =====")
    # 获取存储信息
    storage_info = management_api.get_storage_info()
    print("存储信息:")
    for key, value in storage_info.items():
        print(f"  {key}: {value}")
    
    # 获取可用的交易对和时间周期
    symbols = management_api.get_available_symbols()
    print(f"可用的交易对: {symbols}")
    
    if symbols:
        timeframes = management_api.get_available_timeframes(symbols[0])
        print(f"{symbols[0]} 可用的时间周期: {timeframes}")
        
        # 获取数据信息
        data_info = management_api.get_data_info(symbols[0], timeframes[0])
        print(f"{symbols[0]} {timeframes[0]} 数据信息:")
        for key, value in data_info.items():
            print(f"  {key}: {value}")


if __name__ == "__main__":
    main() 