#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Backtrader回测系统集成测试

对Backtrader回测系统的核心组件、数据源连接和策略实现进行单元测试。
"""

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import tempfile
import matplotlib
import sys

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

matplotlib.use('Agg')  # 设置非交互式后端，避免测试时显示图表

# 导入测试目标模块
from backtest.backtrader.core import BacktraderEngine, BacktraderStrategy
from backtest.backtrader.data_feeds import PandasDataFeed, DataSourceFeed
from backtest.backtrader.analyzers import PerformanceAnalyzer
from backtest.strategies.templates import MovingAverageCrossover
from backtest.base import Strategy, BacktestResults
from data.structures import OHLCV

class MockStrategy(Strategy):
    """模拟策略，用于测试"""
    
    def __init__(self, **params):
        super().__init__(**params)
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        signals = pd.DataFrame(index=data.index)
        
        # 简单策略：如果收盘价高于移动平均线，则买入
        signals['ma'] = data['close'].rolling(window=20).mean()
        signals['entries'] = (data['close'] > signals['ma']) & (signals['ma'].notnull())
        signals['exits'] = (data['close'] < signals['ma']) & (signals['ma'].notnull())
        
        return signals


def create_test_data(n_days=200):
    """创建测试用的价格数据"""
    # 生成日期序列
    end_date = datetime.now()
    start_date = end_date - timedelta(days=n_days)
    dates = pd.date_range(start=start_date, end=end_date, freq='B')
    
    # 生成价格数据
    np.random.seed(42)
    price = 100
    prices = [price]
    
    for i in range(1, len(dates)):
        change_percent = np.random.normal(0.0005, 0.01)
        price = price * (1 + change_percent)
        prices.append(price)
    
    # 基于收盘价生成OHLCV数据
    closes = np.array(prices)
    highs = closes * (1 + np.random.uniform(0, 0.015, len(dates)))
    lows = closes * (1 - np.random.uniform(0, 0.015, len(dates)))
    opens = lows + np.random.uniform(0, 1, len(dates)) * (highs - lows)
    volumes = np.random.uniform(1000, 10000, len(dates)) * closes
    
    # 创建DataFrame
    df = pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    }, index=dates)
    
    return df


class TestBacktraderCore(unittest.TestCase):
    """测试Backtrader核心组件"""
    
    def setUp(self):
        """准备测试环境"""
        self.data = create_test_data()
        self.engine = BacktraderEngine(self.data, initial_cash=100000)
        self.strategy = MockStrategy()
    
    def test_engine_initialization(self):
        """测试回测引擎初始化"""
        self.assertIsNotNone(self.engine)
        self.assertIsNotNone(self.engine.cerebro)
        self.assertEqual(self.engine.cerebro.broker.get_cash(), 100000)
    
    def test_strategy_run(self):
        """测试策略运行"""
        # 运行回测
        results = self.engine.run(self.strategy)
        
        # 验证结果
        self.assertIsInstance(results, BacktestResults)
        self.assertIsNotNone(results.equity)
        self.assertIsNotNone(results.returns_data)
        self.assertIsNotNone(results.trades)
        self.assertIsNotNone(results.metrics)
    
    def test_plot_method(self):
        """测试绘图方法"""
        # 运行回测
        self.engine.run(self.strategy)
        
        # 测试绘图功能(不显示图像)
        try:
            # 将输出重定向到临时文件
            with tempfile.TemporaryDirectory() as tmpdirname:
                temp_file = os.path.join(tmpdirname, 'plot.png')
                self.engine.plot(figsize=(8, 6), plot_volume=True, plot_equity=True)
                # 如果没有抛出异常，则测试通过
                self.assertTrue(True)
        except Exception as e:
            self.fail(f"绘图方法抛出异常: {e}")


class TestDataFeeds(unittest.TestCase):
    """测试数据源连接器"""
    
    def setUp(self):
        """准备测试环境"""
        self.data = create_test_data()
    
    def test_pandas_data_feed(self):
        """测试PandasDataFeed"""
        # 创建数据源
        data_feed = PandasDataFeed(dataname=self.data)
        
        # 初始化数据源
        # 注意：在实际使用时，这个步骤由Cerebro完成
        data_feed.start()  
        
        # 确认数据源有数据
        self.assertGreater(len(data_feed.p.dataname), 0)
        
        # 验证数据源参数
        self.assertEqual(data_feed.params.open, 'open')
        self.assertEqual(data_feed.params.high, 'high')
        self.assertEqual(data_feed.params.low, 'low')
        self.assertEqual(data_feed.params.close, 'close')
        self.assertEqual(data_feed.params.volume, 'volume')
    
    def test_data_feed_validation(self):
        """测试数据验证功能"""
        # 测试缺少日期索引的情况
        bad_data = self.data.reset_index()
        with self.assertRaises(ValueError):
            # 使用validate_dataframe静态方法进行验证
            PandasDataFeed.validate_dataframe(bad_data)
        
        # 测试不是DataFrame的情况
        with self.assertRaises(ValueError):
            # 使用validate_dataframe静态方法进行验证
            PandasDataFeed.validate_dataframe([1, 2, 3])
    
    def test_from_dataframe(self):
        """测试从DataFrame创建数据源"""
        data_feed = DataSourceFeed.from_dataframe(self.data)
        self.assertIsNotNone(data_feed)
    
    def test_from_ohlcv(self):
        """测试从OHLCV创建数据源"""
        # 创建OHLCV对象
        ohlcv = OHLCV(
            open=self.data['open'].values,
            high=self.data['high'].values,
            low=self.data['low'].values,
            close=self.data['close'].values,
            volume=self.data['volume'].values,
            dates=self.data.index.to_pydatetime()
        )
        
        data_feed = DataSourceFeed.from_ohlcv(ohlcv)
        self.assertIsNotNone(data_feed)


class TestMovingAverageStrategy(unittest.TestCase):
    """测试示例移动平均线策略"""
    
    def setUp(self):
        """准备测试环境"""
        self.data = create_test_data()
        self.engine = BacktraderEngine(self.data, initial_cash=100000)
        self.strategy = MovingAverageCrossover(short_window=10, long_window=30)
    
    def test_strategy_parameters(self):
        """测试策略参数"""
        self.assertEqual(self.strategy.short_window, 10)
        self.assertEqual(self.strategy.long_window, 30)
        self.assertEqual(self.strategy.params['name'], 'MovingAverageCrossover')
    
    def prepare_data(self):
        """测试数据准备"""
        processed_data = self.strategy.prepare_data(self.data)
        
        # 验证指标
        self.assertIsNotNone(self.strategy.get_indicator('MA10'))
        self.assertIsNotNone(self.strategy.get_indicator('MA30'))
    
    def test_generate_signals(self):
        """测试信号生成"""
        signals = self.strategy.generate_signals(self.data)
        
        # 验证信号
        self.assertIsInstance(signals, pd.DataFrame)
        self.assertIn('entries', signals.columns)
        self.assertIn('exits', signals.columns)
        self.assertEqual(len(signals), len(self.data))
    
    def test_strategy_backtest(self):
        """测试策略回测"""
        # 运行回测
        results = self.engine.run(self.strategy)
        
        # 验证结果
        self.assertIsInstance(results, BacktestResults)
        
        # 检查关键指标
        metrics = results.metrics
        self.assertIn('sharpe_ratio', metrics)
        self.assertIn('max_drawdown', metrics)
        self.assertIn('total_return', metrics)


if __name__ == '__main__':
    unittest.main()