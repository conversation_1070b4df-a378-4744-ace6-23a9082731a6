 #!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高级策略优化示例

展示如何实现和优化复杂交易策略，包括多指标组合和遗传算法优化。
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import matplotlib.pyplot as plt
import logging
import sys
from typing import Dict, Any, List, Tuple

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

# 导入相关模块
from backtest.backtrader.core import BacktraderEngine
from backtest.backtrader.optimization import ParameterOptimizer, WalkForwardAnalysis
from backtest.backtrader.genetic_optimizer import GeneticOptimizer
from backtest.base import Strategy, BacktestResults
from indicators.trend import moving_averages, macd
from indicators.oscillators import rsi
from indicators.volatility import bollinger
from data.sources.utils import download_crypto_data

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MultiFactorStrategy(Strategy):
    """
    多因子策略
    
    结合多种技术指标的复杂策略，使用指标权重和信号阈值进行优化。
    """
    
    def __init__(self, rsi_period: int = 14, rsi_upper: int = 70, rsi_lower: int = 30,
                 macd_fast: int = 12, macd_slow: int = 26, macd_signal: int = 9,
                 bb_period: int = 20, bb_std: float = 2.0,
                 ma_short: int = 10, ma_long: int = 50,
                 rsi_weight: float = 0.25, macd_weight: float = 0.25,
                 bollinger_weight: float = 0.25, ma_weight: float = 0.25,
                 entry_threshold: float = 0.5, exit_threshold: float = -0.3,
                 position_size: float = 1.0,
                 **kwargs):
        """
        初始化多因子策略
        
        Parameters
        ----------
        rsi_period : int
            RSI指标周期
        rsi_upper : int
            RSI超买阈值
        rsi_lower : int
            RSI超卖阈值
        macd_fast : int
            MACD快线周期
        macd_slow : int
            MACD慢线周期
        macd_signal : int
            MACD信号线周期
        bb_period : int
            布林带周期
        bb_std : float
            布林带标准差倍数
        ma_short : int
            短期移动平均线周期
        ma_long : int
            长期移动平均线周期
        rsi_weight : float
            RSI指标权重
        macd_weight : float
            MACD指标权重
        bollinger_weight : float
            布林带指标权重
        ma_weight : float
            均线指标权重
        entry_threshold : float
            入场信号阈值
        exit_threshold : float
            出场信号阈值
        position_size : float
            仓位大小(0-1)
        **kwargs : dict
            其他参数
        """
        params = {
            'name': 'MultiFactorStrategy',
            'rsi_period': rsi_period,
            'rsi_upper': rsi_upper,
            'rsi_lower': rsi_lower,
            'macd_fast': macd_fast,
            'macd_slow': macd_slow,
            'macd_signal': macd_signal,
            'bb_period': bb_period,
            'bb_std': bb_std,
            'ma_short': ma_short,
            'ma_long': ma_long,
            'rsi_weight': rsi_weight,
            'macd_weight': macd_weight,
            'bollinger_weight': bollinger_weight,
            'ma_weight': ma_weight,
            'entry_threshold': entry_threshold,
            'exit_threshold': exit_threshold,
            'position_size': position_size
        }
        params.update(kwargs)
        super().__init__(**params)
        
        # 设置策略参数
        self.rsi_period = rsi_period
        self.rsi_upper = rsi_upper
        self.rsi_lower = rsi_lower
        self.macd_fast = macd_fast
        self.macd_slow = macd_slow
        self.macd_signal = macd_signal
        self.bb_period = bb_period
        self.bb_std = bb_std
        self.ma_short = ma_short
        self.ma_long = ma_long
        self.rsi_weight = rsi_weight
        self.macd_weight = macd_weight
        self.bollinger_weight = bollinger_weight
        self.ma_weight = ma_weight
        self.entry_threshold = entry_threshold
        self.exit_threshold = exit_threshold
        self.position_size = position_size
        
        # 保存指标
        self.indicators = {}
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号
        
        Parameters
        ----------
        data : pd.DataFrame
            市场数据
            
        Returns
        -------
        pd.DataFrame
            包含交易信号的DataFrame
        """
        # 创建信号DataFrame
        signals = pd.DataFrame(index=data.index)
        
        # 计算RSI指标
        rsi_values = rsi.rsi(data['close'], period=self.rsi_period)
        signals['rsi'] = rsi_values
        self.indicators['RSI'] = rsi_values
        
        # 计算RSI信号（超卖买入，超买卖出）
        rsi_signal = pd.Series(0, index=data.index)
        rsi_signal[signals['rsi'] < self.rsi_lower] = 1  # 超卖信号
        rsi_signal[signals['rsi'] > self.rsi_upper] = -1  # 超买信号
        signals['rsi_signal'] = rsi_signal
        
        # 计算MACD指标
        macd_result = macd.macd(data['close'], fast_period=self.macd_fast, 
                                slow_period=self.macd_slow, signal_period=self.macd_signal)
        signals['macd'] = macd_result['macd']
        signals['macd_signal'] = macd_result['signal']
        signals['macd_histogram'] = macd_result['histogram']
        self.indicators['MACD'] = macd_result['macd']
        self.indicators['MACD_Signal'] = macd_result['signal']
        self.indicators['MACD_Histogram'] = macd_result['histogram']
        
        # 计算MACD信号
        macd_sig = pd.Series(0, index=data.index)
        macd_sig[(signals['macd'] > signals['macd_signal']) & 
                 (signals['macd_histogram'] > 0)] = 1  # 金叉信号
        macd_sig[(signals['macd'] < signals['macd_signal']) & 
                 (signals['macd_histogram'] < 0)] = -1  # 死叉信号
        signals['macd_sig'] = macd_sig
        
        # 计算布林带指标
        bb_result = bollinger.bollinger_bands(data['close'], period=self.bb_period, std=self.bb_std)
        signals['bb_upper'] = bb_result['upper']
        signals['bb_middle'] = bb_result['middle']
        signals['bb_lower'] = bb_result['lower']
        self.indicators['BB_Upper'] = bb_result['upper']
        self.indicators['BB_Middle'] = bb_result['middle']
        self.indicators['BB_Lower'] = bb_result['lower']
        
        # 计算布林带信号
        bb_signal = pd.Series(0, index=data.index)
        bb_signal[data['close'] < signals['bb_lower']] = 1  # 价格低于下轨，买入信号
        bb_signal[data['close'] > signals['bb_upper']] = -1  # 价格高于上轨，卖出信号
        signals['bb_signal'] = bb_signal
        
        # 计算移动平均线
        signals['ma_short'] = moving_averages.sma(data['close'], period=self.ma_short)
        signals['ma_long'] = moving_averages.sma(data['close'], period=self.ma_long)
        self.indicators['MA_Short'] = signals['ma_short']
        self.indicators['MA_Long'] = signals['ma_long']
        
        # 计算移动平均线信号
        ma_signal = pd.Series(0, index=data.index)
        ma_signal[signals['ma_short'] > signals['ma_long']] = 1  # 短期均线在长期均线上方，买入信号
        ma_signal[signals['ma_short'] < signals['ma_long']] = -1  # 短期均线在长期均线下方，卖出信号
        signals['ma_signal'] = ma_signal
        
        # 计算综合信号
        signals['composite_signal'] = (
            self.rsi_weight * signals['rsi_signal'] +
            self.macd_weight * signals['macd_sig'] +
            self.bollinger_weight * signals['bb_signal'] +
            self.ma_weight * signals['ma_signal']
        )
        
        # 生成最终交易信号
        signals['position'] = 0
        signals.loc[signals['composite_signal'] >= self.entry_threshold, 'position'] = 1
        signals.loc[signals['composite_signal'] <= self.exit_threshold, 'position'] = 0
        
        # 计算资金比例
        signals['position_size'] = signals['position'] * self.position_size
        
        # 返回包含信号和指标的DataFrame
        return signals
    
    def get_indicator(self, name: str) -> pd.Series:
        """获取指标数据"""
        return self.indicators.get(name)


def get_data(symbol: str = "BTC/USDT", timeframe: str = "1d", days: int = 365):
    """获取回测数据"""
    try:
        # 尝试下载数据
        logger.info(f"尝试下载{symbol}历史数据...")
        data = download_crypto_data(
            symbol, 
            timeframe, 
            start_time=(datetime.now() - timedelta(days=days)),
            end_time=datetime.now()
        )
        if data is not None and len(data) > 0:
            logger.info(f"成功下载数据，共 {len(data)} 条记录")
            return data
    except Exception as e:
        logger.warning(f"无法下载数据: {e}")
    
    # 如果下载失败，则生成模拟数据
    logger.info("生成模拟数据...")
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    dates = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # 生成价格数据
    np.random.seed(42)
    price = 100
    prices = [price]
    
    for i in range(1, len(dates)):
        change_percent = np.random.normal(0.0005, 0.01)
        price = price * (1 + change_percent)
        prices.append(price)
    
    # 基于收盘价生成OHLCV数据
    closes = np.array(prices)
    highs = closes * (1 + np.random.uniform(0, 0.015, len(dates)))
    lows = closes * (1 - np.random.uniform(0, 0.015, len(dates)))
    opens = lows + np.random.uniform(0, 1, len(dates)) * (highs - lows)
    volumes = np.random.uniform(1000, 10000, len(dates)) * closes
    
    # 创建DataFrame
    df = pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    }, index=dates)
    
    return df


def run_genetic_optimization():
    """运行遗传算法优化"""
    logger.info("开始遗传算法优化...")
    
    # 获取数据
    data = get_data(days=365)
    
    # 创建回测引擎
    engine = BacktraderEngine(data, initial_cash=100000)
    
    # 创建遗传算法优化器
    optimizer = GeneticOptimizer(
        engine=engine,
        strategy_class=MultiFactorStrategy,
        data=data,
        metric='sharpe_ratio',
        maximize=True,
        initial_cash=100000,
        commission=0.001
    )
    
    # 定义参数范围 (min, max, type)
    param_ranges = {
        'rsi_period': (7, 21, 'int'),
        'rsi_upper': (65, 85, 'int'),
        'rsi_lower': (15, 35, 'int'),
        'macd_fast': (8, 20, 'int'),
        'macd_slow': (20, 40, 'int'),
        'macd_signal': (5, 15, 'int'),
        'bb_period': (10, 30, 'int'),
        'bb_std': (1.5, 3.0, 'float'),
        'ma_short': (5, 20, 'int'),
        'ma_long': (30, 100, 'int'),
        'rsi_weight': (0.1, 0.5, 'float'),
        'macd_weight': (0.1, 0.5, 'float'),
        'bollinger_weight': (0.1, 0.5, 'float'),
        'ma_weight': (0.1, 0.5, 'float'),
        'entry_threshold': (0.2, 0.8, 'float'),
        'exit_threshold': (-0.8, -0.2, 'float'),
        'position_size': (0.1, 1.0, 'float')
    }
    
    # 运行遗传算法优化
    results = optimizer.optimize(
        param_ranges=param_ranges,
        population_size=30,
        generations=10,
        mutation_rate=0.1,
        crossover_rate=0.7,
        elite_size=2,
        n_jobs=4
    )
    
    # 打印结果
    print("\n遗传算法优化结果（前5）:")
    print(results[['generation', 'individual', 'sharpe_ratio', 'rsi_period', 'macd_fast', 'macd_slow', 'ma_short', 'ma_long']].head())
    
    # 获取最佳参数
    best_params = optimizer.get_best_params()
    print("\n最佳参数组合:")
    for param, value in best_params.items():
        print(f"{param}: {value}")
    
    # 可视化进化过程
    optimizer.plot_evolution()
    
    # 使用最佳参数运行策略
    best_strategy = MultiFactorStrategy(**best_params)
    results = engine.run(best_strategy)
    
    # 可视化回测结果
    engine.plot()
    
    return results, best_params


def run_multi_factor_backtest(params: Dict[str, Any] = None):
    """运行多因子策略回测"""
    logger.info("开始多因子策略回测...")
    
    # 获取数据
    data = get_data(days=365)
    
    # 创建回测引擎
    engine = BacktraderEngine(data, initial_cash=100000)
    
    # 创建策略
    if params is None:
        # 使用默认参数
        strategy = MultiFactorStrategy()
    else:
        # 使用指定参数
        strategy = MultiFactorStrategy(**params)
    
    # 运行回测
    results = engine.run(strategy)
    
    # 打印回测结果
    print("\n回测性能指标:")
    for metric, value in results.metrics.items():
        print(f"{metric}: {value}")
    
    # 可视化回测结果
    engine.plot()
    
    return results


def compare_strategies():
    """比较不同策略优化方法的性能"""
    logger.info("比较不同策略优化方法的性能...")
    
    # 获取数据
    data = get_data(days=730)  # 使用2年数据
    
    # 训练和测试数据分割
    split_idx = int(len(data) * 0.7)
    train_data = data.iloc[:split_idx].copy()
    test_data = data.iloc[split_idx:].copy()
    
    # 创建回测引擎
    train_engine = BacktraderEngine(train_data, initial_cash=100000)
    test_engine = BacktraderEngine(test_data, initial_cash=100000)
    
    # 1. 默认参数策略
    default_strategy = MultiFactorStrategy()
    default_results = test_engine.run(default_strategy)
    
    # 2. 网格搜索优化
    # 创建参数优化器
    optimizer = ParameterOptimizer(
        engine=train_engine,
        strategy_class=MultiFactorStrategy,
        data=train_data,
        metric='sharpe_ratio',
        maximize=True
    )
    
    # 简化的参数网格
    param_grid = {
        'rsi_period': [10, 14, 18],
        'macd_fast': [10, 12, 14],
        'macd_slow': [24, 26, 28],
        'bb_period': [18, 20, 22],
        'ma_short': [8, 10, 12],
        'ma_long': [40, 50, 60],
        'entry_threshold': [0.4, 0.5, 0.6],
        'exit_threshold': [-0.4, -0.3, -0.2]
    }
    
    # 运行网格搜索
    grid_results = optimizer.grid_search(param_grid, n_jobs=4)
    grid_best_params = optimizer.get_best_params()
    
    # 使用最佳参数在测试数据上验证
    grid_strategy = MultiFactorStrategy(**grid_best_params)
    grid_test_results = test_engine.run(grid_strategy)
    
    # 3. 遗传算法优化
    ga_optimizer = GeneticOptimizer(
        engine=train_engine,
        strategy_class=MultiFactorStrategy,
        data=train_data,
        metric='sharpe_ratio',
        maximize=True
    )
    
    # 简化的参数范围
    param_ranges = {
        'rsi_period': (7, 21, 'int'),
        'rsi_upper': (65, 85, 'int'),
        'rsi_lower': (15, 35, 'int'),
        'macd_fast': (8, 20, 'int'),
        'macd_slow': (20, 40, 'int'),
        'bb_period': (10, 30, 'int'),
        'ma_short': (5, 20, 'int'),
        'ma_long': (30, 100, 'int'),
        'entry_threshold': (0.2, 0.8, 'float'),
        'exit_threshold': (-0.8, -0.2, 'float')
    }
    
    # 运行遗传算法优化
    ga_optimizer.optimize(
        param_ranges=param_ranges,
        population_size=20,
        generations=5,
        n_jobs=4
    )
    ga_best_params = ga_optimizer.get_best_params()
    
    # 使用最佳参数在测试数据上验证
    ga_strategy = MultiFactorStrategy(**ga_best_params)
    ga_test_results = test_engine.run(ga_strategy)
    
    # 对比结果
    comparison = pd.DataFrame({
        '默认参数': [
            default_results.metrics.get('sharpe_ratio', 0),
            default_results.metrics.get('total_return', 0),
            default_results.metrics.get('max_drawdown', 0),
            default_results.metrics.get('win_rate', 0)
        ],
        '网格搜索': [
            grid_test_results.metrics.get('sharpe_ratio', 0),
            grid_test_results.metrics.get('total_return', 0),
            grid_test_results.metrics.get('max_drawdown', 0),
            grid_test_results.metrics.get('win_rate', 0)
        ],
        '遗传算法': [
            ga_test_results.metrics.get('sharpe_ratio', 0),
            ga_test_results.metrics.get('total_return', 0),
            ga_test_results.metrics.get('max_drawdown', 0),
            ga_test_results.metrics.get('win_rate', 0)
        ]
    }, index=['SharpeRatio', 'TotalReturn', 'MaxDrawdown', 'WinRate'])
    
    print("\n策略对比结果:")
    print(comparison)
    
    # 可视化对比
    plt.figure(figsize=(12, 8))
    
    # 权益曲线对比
    plt.subplot(2, 1, 1)
    plt.plot(default_results.equity.index, default_results.equity, label='Default Parameters')
    plt.plot(grid_test_results.equity.index, grid_test_results.equity, label='Grid Search')
    plt.plot(ga_test_results.equity.index, ga_test_results.equity, label='Genetic Algorithm')
    plt.title('Equity Curve Comparison')
    plt.xlabel('Date')
    plt.ylabel('Equity')
    plt.legend()
    plt.grid(True)
    
    # 绘制性能指标柱状图
    plt.subplot(2, 1, 2)
    comparison.plot(kind='bar', ax=plt.gca())
    plt.title('Performance Metrics Comparison')
    plt.ylabel('Value')
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()
    
    return comparison


def main():
    """主函数"""
    print("多因子策略优化示例")
    print("------------------")
    print("1. 运行基础多因子策略回测")
    print("2. 运行遗传算法优化")
    print("3. 比较不同优化方法")
    
    choice = input("请选择要运行的示例 (1/2/3): ")
    
    if choice == '1':
        run_multi_factor_backtest()
    elif choice == '2':
        run_genetic_optimization()
    elif choice == '3':
        compare_strategies()
    else:
        print("无效选择，退出")


if __name__ == "__main__":
    main()