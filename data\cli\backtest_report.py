#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
回测报告生成命令行工具

提供命令行接口，用于生成单个或批量回测的分析报告。
"""

import argparse
import os
import sys
import logging
import pandas as pd
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
import pickle

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    from backtest.base import BacktestResults
    from backtest.analysis import (
        BatchProcessor, 
        batch_analyze_results, 
        generate_report,
        calculate_metrics
    )
except ImportError:
    logger.error("无法导入回测相关模块。请确保在正确的环境中运行该脚本。")
    sys.exit(1)


def load_backtest_results(file_path: str) -> Union[BacktestResults, Dict[str, BacktestResults]]:
    """
    从文件加载回测结果
    
    Parameters
    ----------
    file_path : str
        回测结果文件路径，支持.pkl和.json格式
        
    Returns
    -------
    BacktestResults or Dict[str, BacktestResults]
        加载的回测结果对象或结果字典
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    if file_path.suffix.lower() == '.pkl':
        with open(file_path, 'rb') as f:
            results = pickle.load(f)
        return results
    
    elif file_path.suffix.lower() == '.json':
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 这里需要将JSON数据转换为BacktestResults对象
        # 具体实现取决于JSON结构和BacktestResults的实现
        logger.warning("JSON格式的回测结果需要额外处理，可能无法正确加载所有数据")
        
        # 这里应该有一个将JSON转换为BacktestResults的函数
        # 暂时返回None作为占位符
        return None
    
    else:
        raise ValueError(f"不支持的文件格式: {file_path.suffix}。请使用.pkl或.json文件。")


def load_benchmark(file_path: str) -> pd.Series:
    """
    加载基准数据
    
    Parameters
    ----------
    file_path : str
        基准数据文件路径，支持.csv、.pkl和.json格式
        
    Returns
    -------
    pd.Series
        基准收益率序列
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    if file_path.suffix.lower() == '.csv':
        # 假设CSV文件包含日期和收益率/价格列
        df = pd.read_csv(file_path, index_col=0, parse_dates=True)
        if len(df.columns) == 1:
            return df.iloc[:, 0]
        else:
            logger.warning(f"CSV文件包含多列，使用第一列作为基准: {df.columns[0]}")
            return df.iloc[:, 0]
    
    elif file_path.suffix.lower() == '.pkl':
        with open(file_path, 'rb') as f:
            data = pickle.load(f)
        if isinstance(data, pd.Series):
            return data
        elif isinstance(data, pd.DataFrame):
            if len(data.columns) == 1:
                return data.iloc[:, 0]
            else:
                logger.warning(f"DataFrame包含多列，使用第一列作为基准: {data.columns[0]}")
                return data.iloc[:, 0]
        else:
            raise TypeError(f"不支持的数据类型: {type(data)}。请提供pd.Series或pd.DataFrame。")
    
    elif file_path.suffix.lower() == '.json':
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 将JSON数据转换为Series
        if isinstance(data, dict):
            # 假设格式为 {"date1": value1, "date2": value2, ...}
            return pd.Series(data)
        elif isinstance(data, list):
            # 假设格式为 [{"date": date1, "value": value1}, ...]
            dates = [item.get('date') for item in data]
            values = [item.get('value') for item in data]
            return pd.Series(values, index=pd.DatetimeIndex(dates))
        else:
            raise ValueError(f"不支持的JSON格式: {type(data)}")
    
    else:
        raise ValueError(f"不支持的文件格式: {file_path.suffix}。请使用.csv、.pkl或.json文件。")


def generate_single_report(args):
    """生成单个回测报告"""
    try:
        # 加载回测结果
        results = load_backtest_results(args.input)
        
        # 加载基准数据（如果提供）
        benchmark = None
        if args.benchmark:
            benchmark = load_benchmark(args.benchmark)
        
        # 检查输出目录
        output_dir = os.path.dirname(args.output)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        
        # 确定输出格式
        output_format = args.format
        if not output_format:
            # 如果未指定格式，尝试从输出文件名推断
            output_ext = os.path.splitext(args.output)[1].lower()
            if output_ext in ['.html', '.pdf', '.txt']:
                output_format = output_ext[1:]
            else:
                output_format = 'html'  # 默认格式
        
        # 生成报告
        logger.info(f"正在生成{output_format}格式的回测报告...")
        generate_report(
            results=results,
            benchmark=benchmark,
            output_format=output_format,
            output_path=args.output,
            strategy_name=args.name
        )
        
        logger.info(f"报告已成功生成: {args.output}")
    
    except Exception as e:
        logger.error(f"生成报告时出错: {str(e)}")
        sys.exit(1)


def generate_batch_reports(args):
    """生成批量回测报告"""
    try:
        # 加载回测结果
        results_data = load_backtest_results(args.input)
        
        # 检查结果是否为字典（多个回测结果）
        if not isinstance(results_data, dict):
            logger.error("批量报告生成需要多个回测结果（字典格式）")
            sys.exit(1)
        
        # 加载基准数据（如果提供）
        benchmark = None
        if args.benchmark:
            benchmark = load_benchmark(args.benchmark)
        
        # 创建输出目录
        if not os.path.exists(args.output_dir):
            os.makedirs(args.output_dir, exist_ok=True)
        
        # 生成批量报告
        logger.info(f"正在为{len(results_data)}个回测结果生成批量报告...")
        
        processor = BatchProcessor(parallel=args.parallel)
        processor.add_results(results_data)
        
        # 生成个别报告
        if args.individual:
            individual_dir = os.path.join(args.output_dir, 'individual_reports')
            logger.info(f"生成个别报告到: {individual_dir}")
            processor.generate_batch_reports(
                output_dir=individual_dir,
                output_format=args.format,
                benchmark=benchmark,
                risk_free_rate=args.risk_free_rate
            )
        
        # 生成汇总报告
        if args.summary:
            summary_path = os.path.join(args.output_dir, f"summary.{args.format}")
            logger.info(f"生成汇总报告: {summary_path}")
            processor.generate_summary_report(
                output_path=summary_path,
                benchmark=benchmark,
                risk_free_rate=args.risk_free_rate,
                include_correlation=True,
                include_returns_chart=True,
                include_drawdowns_chart=True
            )
        
        # 导出汇总指标到CSV
        if args.export_csv:
            csv_path = os.path.join(args.output_dir, "summary_metrics.csv")
            logger.info(f"导出汇总指标到CSV: {csv_path}")
            processor.export_summary_to_csv(csv_path)
        
        # 导出汇总指标到Excel
        if args.export_excel:
            excel_path = os.path.join(args.output_dir, "summary_metrics.xlsx")
            logger.info(f"导出汇总指标到Excel: {excel_path}")
            processor.export_summary_to_excel(excel_path)
        
        logger.info(f"批量报告已成功生成到: {args.output_dir}")
    
    except Exception as e:
        logger.error(f"生成批量报告时出错: {str(e)}")
        sys.exit(1)


def optimize_integration(args):
    """基于优化结果生成报告"""
    try:
        # 加载优化结果
        with open(args.input, 'rb') as f:
            optimization_results = pickle.load(f)
        
        logger.info(f"已加载优化结果，包含{len(optimization_results)}条记录")
        
        # 创建输出目录
        if not os.path.exists(args.output_dir):
            os.makedirs(args.output_dir, exist_ok=True)
        
        # 加载基准数据（如果提供）
        benchmark = None
        if args.benchmark:
            benchmark = load_benchmark(args.benchmark)
        
        # 根据最优参数生成报告
        if not hasattr(optimization_results, 'sort_values'):
            logger.error("优化结果不是DataFrame格式")
            sys.exit(1)
        
        # 按照指定的指标排序，获取最优参数
        metric = args.metric
        if metric not in optimization_results.columns:
            available_metrics = [col for col in optimization_results.columns 
                              if isinstance(optimization_results[col].iloc[0], (int, float))]
            logger.warning(f"指定的指标'{metric}'不存在。可用的指标有: {available_metrics}")
            
            if 'sharpe_ratio' in available_metrics:
                metric = 'sharpe_ratio'
            else:
                metric = available_metrics[0]
            
            logger.info(f"使用'{metric}'作为排序指标")
        
        # 排序优化结果
        sorted_results = optimization_results.sort_values(
            by=metric, 
            ascending=not args.maximize
        )
        
        # 获取前N个结果
        top_n = min(args.top_n, len(sorted_results))
        top_results = sorted_results.head(top_n)
        
        logger.info(f"选取了前{top_n}个优化结果进行分析")
        
        # 执行后续分析（这里需要具体项目的实现细节）
        # 例如，可以根据最优参数重新运行回测，然后生成报告
        
        logger.info(f"优化结果分析报告已生成到: {args.output_dir}")
    
    except Exception as e:
        logger.error(f"处理优化结果时出错: {str(e)}")
        sys.exit(1)


def main():
    """主函数，解析命令行参数并执行相应功能"""
    parser = argparse.ArgumentParser(description="回测报告生成工具")
    subparsers = parser.add_subparsers(dest="command", help="子命令")
    
    # 单个报告命令
    single_parser = subparsers.add_parser("single", help="生成单个回测报告")
    single_parser.add_argument("-i", "--input", required=True, help="回测结果文件路径(.pkl或.json)")
    single_parser.add_argument("-o", "--output", required=True, help="输出报告路径")
    single_parser.add_argument("-b", "--benchmark", help="基准数据文件路径(.csv、.pkl或.json)")
    single_parser.add_argument("-f", "--format", choices=["html", "pdf", "text"], help="报告格式")
    single_parser.add_argument("-n", "--name", default="Strategy", help="策略名称")
    
    # 批量报告命令
    batch_parser = subparsers.add_parser("batch", help="生成批量回测报告")
    batch_parser.add_argument("-i", "--input", required=True, help="包含多个回测结果的文件路径(.pkl)")
    batch_parser.add_argument("-o", "--output-dir", required=True, help="输出目录")
    batch_parser.add_argument("-b", "--benchmark", help="基准数据文件路径(.csv、.pkl或.json)")
    batch_parser.add_argument("-f", "--format", default="html", choices=["html", "pdf", "text"], help="报告格式")
    batch_parser.add_argument("--individual", action="store_true", help="生成个别报告")
    batch_parser.add_argument("--summary", action="store_true", help="生成汇总报告")
    batch_parser.add_argument("--export-csv", action="store_true", help="导出汇总指标到CSV")
    batch_parser.add_argument("--export-excel", action="store_true", help="导出汇总指标到Excel")
    batch_parser.add_argument("--parallel", action="store_true", help="使用并行处理")
    batch_parser.add_argument("--risk-free-rate", type=float, default=0.0, help="无风险利率")
    
    # 优化集成命令
    optimize_parser = subparsers.add_parser("optimize", help="基于优化结果生成报告")
    optimize_parser.add_argument("-i", "--input", required=True, help="优化结果文件路径(.pkl)")
    optimize_parser.add_argument("-o", "--output-dir", required=True, help="输出目录")
    optimize_parser.add_argument("-b", "--benchmark", help="基准数据文件路径(.csv、.pkl或.json)")
    optimize_parser.add_argument("-m", "--metric", default="sharpe_ratio", help="排序指标")
    optimize_parser.add_argument("--maximize", action="store_true", help="最大化指标")
    optimize_parser.add_argument("--top-n", type=int, default=5, help="选取前N个结果")
    
    # 解析命令行参数
    args = parser.parse_args()
    
    if args.command is None:
        parser.print_help()
        return
    
    # 执行相应的命令
    if args.command == "single":
        generate_single_report(args)
    elif args.command == "batch":
        generate_batch_reports(args)
    elif args.command == "optimize":
        optimize_integration(args)


if __name__ == "__main__":
    main() 