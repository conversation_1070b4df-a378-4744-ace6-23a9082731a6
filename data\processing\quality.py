"""
数据质量检查模块

提供工具检查数据质量问题，包括缺失值、异常值检测、数据完整性验证和报告生成等功能。
"""

import pandas as pd
import numpy as np
import json
import re
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass

from data.structures import OHLCVColumns


@dataclass
class QualityCheckResult:
    """数据质量检查结果"""
    missing_count: Dict[str, int]  # 每列的缺失值数量
    missing_percent: Dict[str, float]  # 每列的缺失值百分比
    outlier_count: Dict[str, int]  # 每列的异常值数量
    outlier_percent: Dict[str, float]  # 每列的异常值百分比
    duplicate_count: int  # 重复行数量
    invalid_rows: List[int]  # 无效行的索引
    has_gaps: bool  # 是否存在时间间隔问题
    gaps: List[Tuple[pd.Timestamp, pd.Timestamp]]  # 时间间隔列表
    
    def to_dict(self) -> Dict[str, Any]:
        """将结果转换为字典"""
        result = {
            'missing_count': self.missing_count,
            'missing_percent': self.missing_percent,
            'outlier_count': self.outlier_count,
            'outlier_percent': self.outlier_percent,
            'duplicate_count': self.duplicate_count,
            'invalid_rows_count': len(self.invalid_rows),
            'has_gaps': self.has_gaps,
            'gaps_count': len(self.gaps)
        }
        
        # 转换时间戳为字符串
        if self.gaps:
            result['gaps'] = [
                (start.strftime('%Y-%m-%d %H:%M:%S'), end.strftime('%Y-%m-%d %H:%M:%S'))
                for start, end in self.gaps
            ]
        
        return result
    
    def to_json(self, indent: int = 2) -> str:
        """将结果转换为JSON字符串"""
        return json.dumps(self.to_dict(), indent=indent)


def check_missing_values(df: pd.DataFrame) -> Tuple[Dict[str, int], Dict[str, float]]:
    """
    检查DataFrame中的缺失值
    
    Args:
        df: 要检查的DataFrame
        
    Returns:
        包含每列缺失值数量和百分比的元组
    """
    # 计算每列的缺失值数量
    missing_count = df.isna().sum().to_dict()
    
    # 计算每列的缺失值百分比
    total_rows = len(df)
    missing_percent = {col: count / total_rows * 100 for col, count in missing_count.items()}
    
    return missing_count, missing_percent


def check_outliers(df: pd.DataFrame, method: str = 'zscore', threshold: float = 3.0) -> Dict[str, pd.Series]:
    """
    检测DataFrame中的异常值
    
    Args:
        df: 要检查的DataFrame
        method: 检测方法，'zscore' 或 'iqr'
        threshold: 异常值阈值，对于zscore通常为3.0，对于iqr通常为1.5
        
    Returns:
        每列的异常值掩码的字典
    """
    # 只检查数值列
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    
    # 初始化结果字典
    outlier_masks = {}
    
    for col in numeric_cols:
        if method == 'zscore':
            # Z-score方法
            z_scores = np.abs((df[col] - df[col].mean()) / df[col].std())
            outlier_masks[col] = z_scores > threshold
        elif method == 'iqr':
            # IQR方法
            q1 = df[col].quantile(0.25)
            q3 = df[col].quantile(0.75)
            iqr = q3 - q1
            lower_bound = q1 - threshold * iqr
            upper_bound = q3 + threshold * iqr
            outlier_masks[col] = (df[col] < lower_bound) | (df[col] > upper_bound)
        else:
            raise ValueError(f"不支持的异常值检测方法: {method}")
    
    return outlier_masks


def check_data_integrity(df: pd.DataFrame, timeframe: Optional[str] = None) -> Tuple[bool, List[Tuple[pd.Timestamp, pd.Timestamp]]]:
    """
    检查数据完整性，包括时间间隔问题
    
    Args:
        df: 要检查的DataFrame
        timeframe: 时间周期，如"1m", "1h"，用于检查时间间隔
        
    Returns:
        包含是否存在间隔问题和间隔列表的元组
    """
    # 确保索引已排序
    df = df.sort_index()
    
    # 如果没有提供timeframe，则尝试从索引中推断
    if timeframe is None:
        # 计算最常见的时间差
        if len(df) > 1:
            time_diffs = df.index.to_series().diff().dropna()
            if not time_diffs.empty:
                most_common_diff = time_diffs.mode()[0]
                # 使用最常见的时间差作为预期间隔
                expected_diff = most_common_diff
            else:
                # 如果无法推断，则默认为1小时
                expected_diff = pd.Timedelta(hours=1)
        else:
            # 默认为1小时
            expected_diff = pd.Timedelta(hours=1)
    else:
        # 从timeframe字符串解析时间间隔
        match = re.match(r"^(\d+)([mhdwM])$", timeframe)
        if not match:
            raise ValueError(f"无效的时间周期格式: {timeframe}")
        
        value = int(match.group(1))
        unit = match.group(2)
        
        if unit == "m":
            expected_diff = pd.Timedelta(minutes=value)
        elif unit == "h":
            expected_diff = pd.Timedelta(hours=value)
        elif unit == "d":
            expected_diff = pd.Timedelta(days=value)
        elif unit == "w":
            expected_diff = pd.Timedelta(weeks=value)
        elif unit == "M":
            # 月份比较特殊，这里简化处理
            expected_diff = pd.Timedelta(days=30 * value)
    
    # 计算时间差
    time_diffs = df.index.to_series().diff().dropna()
    
    # 找出大于预期间隔的差值
    # 确保expected_diff是一个timedelta对象
    if isinstance(expected_diff, pd.Timedelta):
        multiplier = 1.5
        threshold = pd.Timedelta(seconds=expected_diff.total_seconds() * multiplier)
        gaps_mask = time_diffs > threshold
    else:
        # 如果不是timedelta，使用简单倍数比较
        gaps_mask = time_diffs > expected_diff * 1.5
    
    if not gaps_mask.any():
        return False, []
    
    # 收集间隔信息
    gaps = []
    gap_indices = time_diffs[gaps_mask].index
    
    for idx in gap_indices:
        try:
            # 尝试使用get_indexer获取位置
            idx_loc = df.index.get_indexer([idx])[0]
            if idx_loc > 0:
                start_time = df.index[idx_loc - 1]
                end_time = idx
                gaps.append((start_time, end_time))
        except Exception:
            # 如果遇到索引错误，尝试替代方法
            try:
                # 使用索引值直接查找前一个时间点
                idx_pos = list(df.index).index(idx)
                if idx_pos > 0:
                    start_time = df.index[idx_pos - 1]
                    end_time = idx
                    gaps.append((start_time, end_time))
            except Exception:
                # 如果还是失败，则跳过此间隔
                continue
    
    return True, gaps


def generate_quality_report(df: pd.DataFrame, 
                           outlier_method: str = 'zscore', 
                           outlier_threshold: float = 3.0,
                           timeframe: Optional[str] = None) -> QualityCheckResult:
    """
    生成完整的数据质量报告
    
    Args:
        df: 要检查的DataFrame
        outlier_method: 异常值检测方法
        outlier_threshold: 异常值阈值
        timeframe: 时间周期
        
    Returns:
        QualityCheckResult对象，包含所有质量检查结果
    """
    # 检查缺失值
    missing_count, missing_percent = check_missing_values(df)
    
    # 检查异常值
    outlier_masks = check_outliers(df, method=outlier_method, threshold=outlier_threshold)
    outlier_count = {col: mask.sum() for col, mask in outlier_masks.items()}
    outlier_percent = {col: count / len(df) * 100 for col, count in outlier_count.items()}
    
    # 检查数据完整性
    has_gaps, gaps = check_data_integrity(df, timeframe)
    
    # 检查重复行
    duplicates = df.index.duplicated()
    duplicate_count = duplicates.sum()
    
    # 检查OHLCV逻辑错误
    invalid_rows = []
    if all(col in df.columns for col in [OHLCVColumns.HIGH, OHLCVColumns.LOW, OHLCVColumns.OPEN, OHLCVColumns.CLOSE]):
        # 检查HIGH必须大于等于其他价格列
        high_invalid = (df[OHLCVColumns.HIGH] < df[OHLCVColumns.LOW]) | \
                      (df[OHLCVColumns.HIGH] < df[OHLCVColumns.OPEN]) | \
                      (df[OHLCVColumns.HIGH] < df[OHLCVColumns.CLOSE])
        
        # 检查LOW必须小于等于其他价格列
        low_invalid = (df[OHLCVColumns.LOW] > df[OHLCVColumns.HIGH]) | \
                     (df[OHLCVColumns.LOW] > df[OHLCVColumns.OPEN]) | \
                     (df[OHLCVColumns.LOW] > df[OHLCVColumns.CLOSE])
        
        invalid_price_mask = high_invalid | low_invalid
        invalid_rows = df.index[invalid_price_mask].tolist()
        
        # 检查成交量是否为负数
        if OHLCVColumns.VOLUME in df.columns:
            volume_invalid = df[OHLCVColumns.VOLUME] < 0
            invalid_volume_indices = df.index[volume_invalid].tolist()
            invalid_rows.extend(invalid_volume_indices)
            
            # 去除重复
            invalid_rows = list(set(invalid_rows))
    
    # 创建结果对象
    result = QualityCheckResult(
        missing_count=missing_count,
        missing_percent=missing_percent,
        outlier_count=outlier_count,
        outlier_percent=outlier_percent,
        duplicate_count=duplicate_count,
        invalid_rows=invalid_rows,
        has_gaps=has_gaps,
        gaps=gaps
    )
    
    return result 