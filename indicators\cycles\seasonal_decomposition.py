#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
季节性分解指标模块

实现季节性分解指标，用于将时间序列分解为趋势、季节性和残差成分。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Union, List, Tuple

from ..base import Indicator
from ..utils import validate_data


class SeasonalDecomposition(Indicator):
    """
    季节性分解指标
    
    将时间序列分解为趋势、季节性和残差成分。
    有助于识别市场的季节性模式和周期性行为。
    """
    
    def __init__(
        self, 
        period: int = 30, 
        column: str = 'close', 
        model: str = 'additive',
        extrapolate_trend: int = 0,
        **kwargs
    ):
        """
        初始化季节性分解指标
        
        Parameters
        ----------
        period : int, optional
            季节性周期长度，默认为30（适用于月度季节性）
        column : str, optional
            用于计算的列名，默认为'close'
        model : str, optional
            分解模型，'additive'(加法模型)或'multiplicative'(乘法模型)，默认为'additive'
        extrapolate_trend : int, optional
            趋势外推的点数，默认为0（不外推）
        **kwargs : dict
            其他参数
        """
        if period <= 1:
            raise ValueError("季节性周期长度必须大于1")
        
        valid_models = ['additive', 'multiplicative']
        if model not in valid_models:
            raise ValueError(f"分解模型必须是以下之一: {', '.join(valid_models)}")
        
        super().__init__(
            name="SeasonalDecomposition", 
            category="cycle", 
            period=period,
            column=column,
            model=model,
            extrapolate_trend=extrapolate_trend,
            **kwargs
        )
    
    def _moving_average(self, series: pd.Series, window: int, center: bool = True) -> pd.Series:
        """
        计算中心化移动平均
        
        Parameters
        ----------
        series : pd.Series
            输入时间序列
        window : int
            窗口大小
        center : bool, optional
            是否居中窗口，默认为True
            
        Returns
        -------
        pd.Series
            移动平均结果
        """
        if window % 2 == 0:
            # 对于偶数窗口，先计算简单移动平均，再对结果进行二次平均
            s1 = series.rolling(window=window, center=center).mean()
            s2 = s1.rolling(window=2, center=center).mean()
            return s2
        else:
            # 对于奇数窗口，直接计算中心化移动平均
            return series.rolling(window=window, center=center).mean()
    
    def _decompose(
        self, 
        series: pd.Series, 
        period: int, 
        model: str = 'additive',
        extrapolate_trend: int = 0
    ) -> Tuple[pd.Series, pd.Series, pd.Series, pd.Series]:
        """
        分解时间序列
        
        Parameters
        ----------
        series : pd.Series
            输入时间序列
        period : int
            季节性周期长度
        model : str, optional
            分解模型，'additive'或'multiplicative'，默认为'additive'
        extrapolate_trend : int, optional
            趋势外推的点数，默认为0
            
        Returns
        -------
        Tuple[pd.Series, pd.Series, pd.Series, pd.Series]
            原始数据、趋势成分、季节性成分和残差成分
        """
        # 确保period不大于数据长度的一半
        if period > len(series) // 2:
            period = len(series) // 2
            if period < 2:
                raise ValueError("数据长度不足以进行季节性分解")
        
        # 计算趋势成分（使用移动平均）
        trend = self._moving_average(series, period, center=True)
        
        # 可选地外推趋势开始和结束的缺失值
        if extrapolate_trend > 0 and len(trend) > 2 * extrapolate_trend:
            valid_indices = trend.dropna().index
            start_idx, end_idx = valid_indices[0], valid_indices[-1]
            
            if extrapolate_trend > 0:
                # 外推开头
                start_values = trend.loc[start_idx:].head(extrapolate_trend).values
                start_slope = (start_values[-1] - start_values[0]) / (extrapolate_trend - 1) if extrapolate_trend > 1 else 0
                for i in range(extrapolate_trend):
                    if series.index[i] < start_idx:
                        trend.loc[series.index[i]] = trend.loc[start_idx] - start_slope * (extrapolate_trend - i)
                
                # 外推结尾
                end_values = trend.loc[:end_idx].tail(extrapolate_trend).values
                end_slope = (end_values[-1] - end_values[0]) / (extrapolate_trend - 1) if extrapolate_trend > 1 else 0
                for i in range(extrapolate_trend):
                    if i < len(series) and series.index[-i-1] > end_idx:
                        trend.loc[series.index[-i-1]] = trend.loc[end_idx] + end_slope * (extrapolate_trend - i)
        
        # 去除趋势获取周期+残差
        if model == 'additive':
            detrended = series - trend
        else:  # multiplicative
            detrended = series / trend
            # 处理可能的除零问题
            detrended.replace([np.inf, -np.inf], np.nan, inplace=True)
        
        # 计算平均季节性模式
        periods = []
        for i in range(period):
            seasonal_slice = detrended.iloc[i::period].dropna()
            if len(seasonal_slice) > 0:
                periods.append(seasonal_slice.mean())
            else:
                periods.append(np.nan)
        
        seasonal_pattern = pd.Series(periods)
        
        # 使季节性模式均值为0（加法模型）或1（乘法模型）
        if model == 'additive':
            seasonal_pattern = seasonal_pattern - seasonal_pattern.mean()
        else:
            seasonal_pattern = seasonal_pattern / seasonal_pattern.mean()
        
        # 构建完整的季节性成分
        seasonal = pd.Series(index=series.index, dtype=float)
        for i, idx in enumerate(series.index):
            seasonal.loc[idx] = seasonal_pattern[i % period]
        
        # 计算残差
        if model == 'additive':
            resid = series - trend - seasonal
        else:
            resid = series / (trend * seasonal)
            # 处理可能的除零问题
            resid.replace([np.inf, -np.inf], np.nan, inplace=True)
        
        return series, trend, seasonal, resid
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算季节性分解指标
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据，需包含用于计算的列
            
        Returns
        -------
        pd.DataFrame
            包含季节性分解结果的DataFrame
        """
        column = self.params['column']
        validate_data(data, [column])
        
        df = data.copy()
        period = self.params['period']
        model = self.params['model']
        extrapolate_trend = self.params['extrapolate_trend']
        
        # 获取数据序列
        series = df[column]
        
        # 执行季节性分解
        original, trend, seasonal, resid = self._decompose(
            series, period, model, extrapolate_trend
        )
        
        # 存储结果
        result = df.copy()
        result['seasonal_trend'] = trend
        result['seasonal_seasonal'] = seasonal
        result['seasonal_resid'] = resid
        
        # 存储额外信息
        self._decomposition = {
            'original': original,
            'trend': trend,
            'seasonal': seasonal,
            'resid': resid
        }
        self._result = result
        
        return result
    
    def get_seasonal_pattern(self) -> pd.Series:
        """
        获取季节性模式
        
        Returns
        -------
        pd.Series
            季节性模式，按周期索引
        """
        if not hasattr(self, '_decomposition'):
            return pd.Series()
        
        period = self.params['period']
        seasonal = self._decomposition['seasonal']
        
        # 提取一个完整的季节性周期
        pattern = []
        for i in range(period):
            if i < len(seasonal):
                pattern.append(seasonal.iloc[i])
            else:
                pattern.append(np.nan)
        
        return pd.Series(pattern, index=range(1, period + 1))
    
    def plot(self, ax=None, **kwargs):
        """
        绘制季节性分解结果
        
        Parameters
        ----------
        ax : matplotlib.axes.Axes, optional
            用于绘图的Axes对象
        **kwargs : dict
            传递给绘图函数的其他参数
            
        Returns
        -------
        List[matplotlib.axes.Axes]
            绘图结果
        """
        if not hasattr(self, '_decomposition'):
            raise ValueError("没有计算结果可供绘制，请先调用calculate方法")
        
        import matplotlib.pyplot as plt
        
        if ax is None:
            fig, (ax1, ax2, ax3, ax4) = plt.subplots(4, 1, figsize=kwargs.get('figsize', (12, 10)))
        else:
            # 如果提供了ax，假设它是一个4x1的子图
            if isinstance(ax, (list, tuple)) and len(ax) >= 4:
                ax1, ax2, ax3, ax4 = ax[0], ax[1], ax[2], ax[3]
            else:
                fig, (ax1, ax2, ax3, ax4) = plt.subplots(4, 1, figsize=kwargs.get('figsize', (12, 10)))
        
        column = self.params['column']
        period = self.params['period']
        model = self.params['model']
        
        # 获取分解结果
        original = self._decomposition['original']
        trend = self._decomposition['trend']
        seasonal = self._decomposition['seasonal']
        resid = self._decomposition['resid']
        
        # 绘制原始数据
        ax1.plot(original.index, original, label='原始数据')
        ax1.set_title(f"{column.capitalize()}原始数据")
        ax1.grid(True)
        ax1.legend()
        
        # 绘制趋势成分
        ax2.plot(trend.index, trend, label='趋势', color='red')
        ax2.set_title("趋势成分")
        ax2.grid(True)
        ax2.legend()
        
        # 绘制季节性成分
        ax3.plot(seasonal.index, seasonal, label='季节性', color='green')
        ax3.set_title(f"季节性成分 (周期={period})")
        ax3.grid(True)
        ax3.legend()
        
        # 绘制残差成分
        ax4.plot(resid.index, resid, label='残差', color='purple')
        ax4.set_title("残差成分")
        ax4.grid(True)
        ax4.legend()
        
        plt.tight_layout()
        
        return [ax1, ax2, ax3, ax4] 