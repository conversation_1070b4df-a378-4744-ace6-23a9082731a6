 #!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Backtrader回测系统使用文档示例

本文件提供了Backtrader回测系统的使用示例和API参考，展示了如何使用系统功能。
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import matplotlib.pyplot as plt
import logging
import sys

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

# 导入相关模块
from backtest.backtrader.core import BacktraderEngine
from backtest.backtrader.optimization import ParameterOptimizer, WalkForwardAnalysis
from backtest.backtrader.genetic_optimizer import GeneticOptimizer
from backtest.base import Strategy, BacktestResults
from indicators.trend import moving_averages
from indicators.oscillators import rsi
from data.sources.utils import download_crypto_data

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

"""
========================================
Backtrader回测系统使用指南
========================================

本示例详细展示了如何使用Backtrader回测系统进行策略开发、回测和优化。
"""

def create_sample_data(n_days=200):
    """创建示例数据"""
    # 生成日期序列
    end_date = datetime.now()
    start_date = end_date - timedelta(days=n_days)
    dates = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # 生成价格数据
    np.random.seed(42)
    price = 100
    prices = [price]
    
    for i in range(1, len(dates)):
        change_percent = np.random.normal(0.0005, 0.01)
        price = price * (1 + change_percent)
        prices.append(price)
    
    # 基于收盘价生成OHLCV数据
    closes = np.array(prices)
    highs = closes * (1 + np.random.uniform(0, 0.015, len(dates)))
    lows = closes * (1 - np.random.uniform(0, 0.015, len(dates)))
    opens = lows + np.random.uniform(0, 1, len(dates)) * (highs - lows)
    volumes = np.random.uniform(1000, 10000, len(dates)) * closes
    
    # 创建DataFrame
    df = pd.DataFrame({
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    }, index=dates)
    
    return df


class SimpleRSIStrategy(Strategy):
    """
    简单RSI策略示例
    """
    
    def __init__(self, rsi_period: int = 14, overbought: int = 70, oversold: int = 30, **kwargs):
        """初始化策略"""
        params = {
            'name': 'SimpleRSIStrategy',
            'rsi_period': rsi_period,
            'overbought': overbought,
            'oversold': oversold
        }
        params.update(kwargs)
        super().__init__(**params)
        
        self.rsi_period = rsi_period
        self.overbought = overbought
        self.oversold = oversold
        self.indicators = {}
        
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        signals = pd.DataFrame(index=data.index)
        
        # 计算RSI
        signals['rsi'] = rsi.rsi(data['close'], period=self.rsi_period)
        self.indicators['RSI'] = signals['rsi']
        
        # 生成交易信号 (True = 做多, False = 不做多/平仓)
        signals['position'] = 0
        signals.loc[signals['rsi'] < self.oversold, 'position'] = 1  # 超卖买入
        signals.loc[signals['rsi'] > self.overbought, 'position'] = 0  # 超买卖出
        
        # 填充缺失值
        signals['position'] = signals['position'].ffill().fillna(0)
        
        return signals
    
    def get_indicator(self, name: str) -> pd.Series:
        """获取指标数据"""
        return self.indicators.get(name)


def basic_backtest_example():
    """
    基础回测示例
    
    展示如何进行基本的策略回测。
    """
    print("1. 基础回测示例")
    print("====================")
    
    # 创建示例数据
    data = create_sample_data(n_days=365)
    print(f"数据时间范围: {data.index[0]} 到 {data.index[-1]}")
    print(f"数据形状: {data.shape}")
    
    # 创建回测引擎
    engine = BacktraderEngine(
        data=data,
        initial_cash=100000,
        commission=0.001
    )
    
    # 创建策略并添加参数
    strategy = SimpleRSIStrategy(
        rsi_period=14,
        overbought=70,
        oversold=30
    )
    
    # 运行回测
    results = engine.run(strategy)
    
    # 打印回测结果
    print("\n回测结果:")
    print(f"初始资金: $100,000")
    print(f"最终资金: ${results.equity.iloc[-1]:.2f}")
    print(f"总收益率: {results.metrics['total_return']:.2%}")
    print(f"年化收益率: {results.metrics['annual_return']:.2%}")
    print(f"夏普比率: {results.metrics['sharpe_ratio']:.2f}")
    print(f"最大回撤: {results.metrics['max_drawdown']:.2%}")
    print(f"胜率: {results.metrics['win_rate']:.2%}")
    print(f"交易次数: {len(results.trades) if not results.trades.empty else 0}")
    
    # 可视化回测结果
    engine.plot(figsize=(12, 8), plot_volume=True, plot_equity=True)
    
    return results


def parameter_optimization_example():
    """
    参数优化示例
    
    展示如何对策略参数进行优化。
    """
    print("\n2. 参数优化示例")
    print("====================")
    
    # 创建示例数据
    data = create_sample_data(n_days=365)
    
    # 创建回测引擎
    engine = BacktraderEngine(
        data=data,
        initial_cash=100000,
        commission=0.001
    )
    
    # 创建参数优化器
    optimizer = ParameterOptimizer(
        engine=engine,
        strategy_class=SimpleRSIStrategy,
        data=data,
        metric='sharpe_ratio',
        maximize=True
    )
    
    # 定义参数网格
    param_grid = {
        'rsi_period': [5, 10, 14, 20, 25],
        'overbought': [65, 70, 75, 80],
        'oversold': [20, 25, 30, 35]
    }
    
    # 运行网格搜索
    print("\n开始参数网格搜索...")
    results = optimizer.grid_search(param_grid, n_jobs=4)
    
    # 打印优化结果
    print("\n优化结果 (前5):")
    print(results[['rsi_period', 'overbought', 'oversold', 'sharpe_ratio', 'total_return', 'max_drawdown', 'win_rate']].head())
    
    # 获取最佳参数
    best_params = optimizer.get_best_params()
    print("\n最佳参数组合:")
    for param, value in best_params.items():
        print(f"{param}: {value}")
    
    # 可视化优化结果
    optimizer.plot_optimization_results()
    
    # 使用最佳参数运行回测
    best_strategy = SimpleRSIStrategy(**best_params)
    final_results = engine.run(best_strategy)
    
    # 打印最终回测结果
    print("\n使用最佳参数的回测结果:")
    print(f"初始资金: $100,000")
    print(f"最终资金: ${final_results.equity.iloc[-1]:.2f}")
    print(f"总收益率: {final_results.metrics['total_return']:.2%}")
    print(f"夏普比率: {final_results.metrics['sharpe_ratio']:.2f}")
    
    # 可视化最终回测结果
    engine.plot(figsize=(12, 8))
    
    return final_results, best_params


def walk_forward_analysis_example():
    """
    Walk Forward Analysis示例
    
    展示如何使用Walk Forward Analysis防止过拟合。
    """
    print("\n3. Walk Forward Analysis示例")
    print("====================")
    
    # 创建示例数据
    data = create_sample_data(n_days=730)  # 使用两年数据
    
    # 创建回测引擎
    engine = BacktraderEngine(
        data=data,
        initial_cash=100000,
        commission=0.001
    )
    
    # 创建Walk Forward Analysis
    wfa = WalkForwardAnalysis(
        engine=engine,
        strategy_class=SimpleRSIStrategy,
        data=data,
        train_size=0.7,
        test_size=0.3,
        n_windows=5,
        metric='sharpe_ratio',
        maximize=True
    )
    
    # 定义参数网格
    param_grid = {
        'rsi_period': [5, 10, 14, 20, 25],
        'overbought': [65, 70, 75, 80],
        'oversold': [20, 25, 30, 35]
    }
    
    # 运行Walk Forward Analysis
    print("\n开始Walk Forward Analysis...")
    results = wfa.run(param_grid, n_jobs=4)
    
    # 打印WFA结果
    print("\nWalk Forward Analysis结果:")
    print(results[['window', 'train_start', 'train_end', 'test_start', 'test_end', 'sharpe_ratio', 'total_return', 'max_drawdown', 'win_rate']])
    
    # 获取稳健参数
    robust_params = wfa.get_robust_params()
    print("\n稳健参数组合:")
    for param, value in robust_params.items():
        print(f"{param}: {value}")
    
    # 可视化WFA结果
    wfa.plot_results()
    
    # 使用稳健参数运行回测
    robust_strategy = SimpleRSIStrategy(**robust_params)
    final_results = engine.run(robust_strategy)
    
    # 打印最终回测结果
    print("\n使用稳健参数的回测结果:")
    print(f"初始资金: $100,000")
    print(f"最终资金: ${final_results.equity.iloc[-1]:.2f}")
    print(f"总收益率: {final_results.metrics['total_return']:.2%}")
    print(f"夏普比率: {final_results.metrics['sharpe_ratio']:.2f}")
    
    # 可视化最终回测结果
    engine.plot(figsize=(12, 8))
    
    return final_results, robust_params


def genetic_algorithm_example():
    """
    遗传算法优化示例
    
    展示如何使用遗传算法优化策略参数。
    """
    print("\n4. 遗传算法优化示例")
    print("====================")
    
    # 创建示例数据
    data = create_sample_data(n_days=365)
    
    # 创建回测引擎
    engine = BacktraderEngine(
        data=data,
        initial_cash=100000,
        commission=0.001
    )
    
    # 创建遗传算法优化器
    ga_optimizer = GeneticOptimizer(
        engine=engine,
        strategy_class=SimpleRSIStrategy,
        data=data,
        metric='sharpe_ratio',
        maximize=True
    )
    
    # 定义参数范围
    param_ranges = {
        'rsi_period': (5, 30, 'int'),
        'overbought': (60, 90, 'int'),
        'oversold': (10, 40, 'int')
    }
    
    # 运行遗传算法优化
    print("\n开始遗传算法优化...")
    results = ga_optimizer.optimize(
        param_ranges=param_ranges,
        population_size=20,
        generations=8,
        mutation_rate=0.2,
        crossover_rate=0.7,
        elite_size=2,
        n_jobs=4
    )
    
    # 打印优化结果
    print("\n遗传算法优化结果 (前5):")
    print(results[['generation', 'individual', 'sharpe_ratio', 'rsi_period', 'overbought', 'oversold']].head())
    
    # 获取最佳参数
    best_params = ga_optimizer.get_best_params()
    print("\n最佳参数组合:")
    for param, value in best_params.items():
        print(f"{param}: {value}")
    
    # 可视化优化过程
    ga_optimizer.plot_evolution()
    
    # 使用最佳参数运行回测
    best_strategy = SimpleRSIStrategy(**best_params)
    final_results = engine.run(best_strategy)
    
    # 打印最终回测结果
    print("\n使用最佳参数的回测结果:")
    print(f"初始资金: $100,000")
    print(f"最终资金: ${final_results.equity.iloc[-1]:.2f}")
    print(f"总收益率: {final_results.metrics['total_return']:.2%}")
    print(f"夏普比率: {final_results.metrics['sharpe_ratio']:.2f}")
    
    # 可视化最终回测结果
    engine.plot(figsize=(12, 8))
    
    return final_results, best_params


def api_reference():
    """
    打印API参考
    """
    print("\n5. API参考")
    print("====================")
    
    # BacktestEngine API
    print("\nBacktestEngine API:")
    print("-------------------")
    print("BacktraderEngine(data, **kwargs):")
    print("  - data: pandas DataFrame，包含OHLCV数据")
    print("  - initial_cash: 初始资金，默认为100000")
    print("  - commission: 佣金率，默认为0.001")
    print("\n  方法:")
    print("  - run(strategy): 运行回测，返回BacktestResults")
    print("  - plot(**kwargs): 可视化回测结果")
    
    # Strategy API
    print("\nStrategy API:")
    print("-------------------")
    print("Strategy(**params):")
    print("  - 策略基类，所有策略必须继承此类")
    print("\n  方法:")
    print("  - generate_signals(data): 生成交易信号，返回DataFrame")
    print("  - get_indicator(name): 获取指标数据")
    
    # Optimization API
    print("\n优化API:")
    print("-------------------")
    print("ParameterOptimizer(engine, strategy_class, data, metric, maximize):")
    print("  - grid_search(param_grid, n_jobs): 网格搜索优化")
    print("  - get_best_params(): 获取最佳参数")
    print("  - plot_optimization_results(): 可视化优化结果")
    
    print("\nWalkForwardAnalysis(engine, strategy_class, data, train_size, test_size, n_windows):")
    print("  - run(param_grid, n_jobs): 运行Walk Forward Analysis")
    print("  - get_robust_params(): 获取稳健参数")
    print("  - plot_results(): 可视化WFA结果")
    
    print("\nGeneticOptimizer(engine, strategy_class, data, metric, maximize):")
    print("  - optimize(param_ranges, population_size, generations, ...): 遗传算法优化")
    print("  - get_best_params(): 获取最佳参数")
    print("  - plot_evolution(): 可视化进化过程")
    
    # BacktestResults API
    print("\nBacktestResults API:")
    print("-------------------")
    print("BacktestResults属性:")
    print("  - equity: 权益曲线")
    print("  - positions: 持仓数据")
    print("  - trades: 交易记录")
    print("  - metrics: 性能指标")
    print("  - returns_data: 收益率序列")
    
    print("\n方法:")
    print("  - get_returns(): 获取收益率序列")
    print("  - get_drawdowns(): 获取回撤序列")
    print("  - get_metric(name): 获取指定指标")
    print("  - summary(): 获取性能指标摘要")


def usage_patterns():
    """
    展示常见使用模式
    """
    print("\n6. 常见使用模式")
    print("====================")
    
    # 1. 基本回测流程
    print("\n基本回测流程:")
    print("```python")
    print("from backtest.backtrader.core import BacktraderEngine")
    print("from backtest.base import Strategy")
    print("")
    print("# 创建策略")
    print("class MyStrategy(Strategy):")
    print("    def __init__(self, param1=value1, param2=value2, **kwargs):")
    print("        super().__init__(**kwargs)")
    print("        self.param1 = param1")
    print("        self.param2 = param2")
    print("")
    print("    def generate_signals(self, data):")
    print("        signals = pd.DataFrame(index=data.index)")
    print("        # 计算指标和生成信号")
    print("        signals['position'] = ...  # 1表示做多，0表示平仓")
    print("        return signals")
    print("")
    print("# 获取数据")
    print("data = pd.DataFrame(...)")
    print("")
    print("# 创建回测引擎")
    print("engine = BacktraderEngine(data, initial_cash=100000)")
    print("")
    print("# 创建策略实例")
    print("strategy = MyStrategy(param1=value1, param2=value2)")
    print("")
    print("# 运行回测")
    print("results = engine.run(strategy)")
    print("")
    print("# 分析结果")
    print("print(results.metrics)")
    print("")
    print("# 可视化结果")
    print("engine.plot()")
    print("```")
    
    # 2. 参数优化流程
    print("\n参数优化流程:")
    print("```python")
    print("from backtest.backtrader.optimization import ParameterOptimizer")
    print("")
    print("# 创建优化器")
    print("optimizer = ParameterOptimizer(")
    print("    engine=engine,")
    print("    strategy_class=MyStrategy,")
    print("    data=data,")
    print("    metric='sharpe_ratio',")
    print("    maximize=True")
    print(")")
    print("")
    print("# 定义参数网格")
    print("param_grid = {")
    print("    'param1': [value1, value2, value3],")
    print("    'param2': [value1, value2, value3]")
    print("}")
    print("")
    print("# 运行网格搜索")
    print("results = optimizer.grid_search(param_grid, n_jobs=4)")
    print("")
    print("# 获取最佳参数")
    print("best_params = optimizer.get_best_params()")
    print("")
    print("# 使用最佳参数运行回测")
    print("best_strategy = MyStrategy(**best_params)")
    print("final_results = engine.run(best_strategy)")
    print("```")


def main():
    """主函数"""
    print("Backtrader回测系统使用文档示例")
    print("==============================")
    print("本示例展示了如何使用Backtrader回测系统进行回测和优化。")
    print("\n请选择要查看的示例:")
    print("1. 基础回测示例")
    print("2. 参数优化示例")
    print("3. Walk Forward Analysis示例")
    print("4. 遗传算法优化示例")
    print("5. API参考")
    print("6. 常见使用模式")
    print("7. 运行所有示例")
    
    choice = input("\n请输入选项 (1-7): ")
    
    if choice == '1':
        basic_backtest_example()
    elif choice == '2':
        parameter_optimization_example()
    elif choice == '3':
        walk_forward_analysis_example()
    elif choice == '4':
        genetic_algorithm_example()
    elif choice == '5':
        api_reference()
    elif choice == '6':
        usage_patterns()
    elif choice == '7':
        basic_backtest_example()
        parameter_optimization_example()
        walk_forward_analysis_example()
        genetic_algorithm_example()
        api_reference()
        usage_patterns()
    else:
        print("无效选择，退出")


if __name__ == "__main__":
    main()