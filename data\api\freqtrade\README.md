# Freqtrade 接口集成

本模块提供与 Freqtrade 交易机器人的接口集成，支持交易信号发送、状态查询和交易管理等功能。

## 概述

Freqtrade 是一个基于 Python 的加密货币交易机器人，提供 REST API 接口用于远程控制和监控。本模块实现了对 Freqtrade API 的封装，提供简洁易用的接口，并集成了风控系统，确保交易安全。

## 主要功能

- 与 Freqtrade REST API 通信
- 交易信号格式转换
- 风控规则集成和验证
- 交易执行和监控
- 异步信号处理队列

## 模块结构

- **client.py**: 基础 API 客户端实现
- **models.py**: 数据模型定义
- **converter.py**: 信号转换工具
- **risk_integration.py**: 风控系统集成
- **service.py**: 高级服务封装
- **examples/**: 使用示例

## 使用示例

### 基本用法

```python
from data.api.freqtrade import FreqtradeClient, TradeSignal, OrderType

# 初始化客户端
client = FreqtradeClient(
    server_url="http://localhost:8080",
    username="your_username",
    password="your_password"
)

# 测试连接
if client.ping():
    print("成功连接到 Freqtrade 服务器")
else:
    print("无法连接到 Freqtrade 服务器")
    exit(1)

# 获取机器人状态
bot_status = client.get_bot_status()
print(f"机器人状态: {bot_status.status}")
print(f"开放交易数量: {bot_status.trade_count}")

# 创建交易信号
signal = TradeSignal(
    pair="BTC/USDT",
    side="long",
    order_type=OrderType.MARKET,
    enter_tag="MyStrategy"
)

# 发送交易信号
result = client.force_enter_trade(signal)

if result.success:
    print("交易信号发送成功")
    print(f"交易ID: {result.tradeid}")
else:
    print(f"交易信号发送失败: {result.error}")
```

### 使用服务管理器

```python
from data.api.freqtrade.service import FreqtradeService

# 初始化服务
service = FreqtradeService(
    server_url="http://localhost:8080",
    username="your_username",
    password="your_password",
    enable_async_processing=True  # 启用异步处理
)

# 创建交易信号（字典形式）
signal_dict = {
    "pair": "ETH/USDT",
    "side": "long",
    "order_type": "market",
    "enter_tag": "ServiceExample"
}

# 提交交易信号
if service.submit_signal(signal_dict):
    print("交易信号已提交到队列")
else:
    print("交易信号提交失败")

# 获取开放的交易
open_trades = service.get_open_trades()
print(f"当前开放交易数量: {len(open_trades)}")

# 关闭服务
service.stop_processing()
```

更多详细示例请参考 `examples/` 目录下的示例文件。

## 配置

在使用 Freqtrade 接口前，需要确保 Freqtrade 实例已正确配置 REST API。在 Freqtrade 的配置文件中，应包含以下设置：

```json
"api_server": {
    "enabled": true,
    "listen_ip_address": "127.0.0.1",
    "listen_port": 8080,
    "username": "your_username",
    "password": "your_password",
    "jwt_secret_key": "your_jwt_secret_key"
}
```

## 风控集成

本模块与风控系统集成，确保交易信号在发送前通过风控规则验证：

```python
from risk.rules.presets import create_crypto_risk_manager
from data.api.freqtrade.risk_integration import RiskValidator

# 创建风控管理器
risk_manager = create_crypto_risk_manager()

# 创建风控验证器
validator = RiskValidator(risk_manager)

# 验证交易信号
validation_result = validator.validate_signal(signal, market_data)

if validation_result.get('allow_trade', False):
    print("信号通过风控验证")
else:
    print(f"信号被风控系统拒绝: {validation_result.get('reason')}")
```

## 异步处理

服务管理器支持异步处理交易信号，通过内部队列和专用线程处理信号，避免阻塞主线程：

```python
service = FreqtradeService(
    server_url="http://localhost:8080",
    username="your_username",
    password="your_password",
    enable_async_processing=True,
    signal_queue_size=100
)

# 提交多个信号，无需等待处理完成
for i in range(10):
    signal = {...}  # 创建信号
    service.submit_signal(signal)

# 程序可以继续执行其他任务
# ...

# 停止处理线程
service.stop_processing()
```

## 错误处理

所有 API 调用都包含错误处理机制，会自动重试连接并记录错误信息：

```python
try:
    result = client.force_enter_trade(signal)
    # 处理结果
except FreqtradeError as e:
    print(f"API 错误: {str(e)}")
```

## 依赖项

- Python 3.8+
- requests
- pandas
- 风控系统模块 (risk/) 