#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Freqtrade接口单元测试

测试Freqtrade接口功能，使用模拟数据和请求。
"""

import unittest
from unittest.mock import patch, MagicMock
import json
import datetime
import pandas as pd
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(__file__))))

from data.api.freqtrade import FreqtradeClient, TradeSignal, OrderType, TradeResult
from data.api.freqtrade.converter import SignalConverter
from data.api.freqtrade.service import FreqtradeService


class TestFreqtradeClient(unittest.TestCase):
    """测试FreqtradeClient类"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建带有模拟功能的客户端
        self.client = FreqtradeClient(
            server_url="http://localhost:8080",
            username="test_user",
            password="test_password"
        )
        
        # 模拟token信息
        self.client._token = "fake_token"
        self.client._token_expires = datetime.datetime.now() + datetime.timedelta(minutes=10)
    
    @patch('requests.get')
    def test_ping(self, mock_get):
        """测试ping功能"""
        # 设置模拟响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "pong"}
        mock_get.return_value = mock_response
        
        # 执行测试
        result = self.client.ping()
        
        # 验证结果
        self.assertTrue(result)
        mock_get.assert_called_once_with(
            "http://localhost:8080/api/v1/ping",
            verify=True,
            timeout=10
        )
    
    @patch('data.api.freqtrade.client.FreqtradeClient._request')
    def test_force_enter_trade(self, mock_request):
        """测试发送交易信号"""
        # 设置模拟响应
        mock_request.return_value = {"tradeid": 123}
        
        # 创建交易信号
        signal = TradeSignal(
            pair="BTC/USDT",
            side="long",
            order_type=OrderType.MARKET,
            enter_tag="test_tag"
        )
        
        # 执行测试
        result = self.client.force_enter_trade(signal)
        
        # 验证结果
        self.assertTrue(result.success)
        self.assertEqual(result.tradeid, 123)
        mock_request.assert_called_once_with(
            'POST', 
            'forceenter', 
            data={'pair': 'BTC/USDT', 'side': 'long', 'enter_tag': 'test_tag'}
        )
    
    @patch('data.api.freqtrade.client.FreqtradeClient._request')
    def test_force_exit_trade(self, mock_request):
        """测试关闭交易"""
        # 设置模拟响应
        mock_request.return_value = {"result": "success"}
        
        # 执行测试
        result = self.client.force_exit_trade(123)
        
        # 验证结果
        self.assertTrue(result.success)
        mock_request.assert_called_once_with(
            'POST', 
            'forceexit', 
            data={'tradeid': '123', 'ordertype': 'market'}
        )


class TestSignalConverter(unittest.TestCase):
    """测试SignalConverter类"""
    
    def test_from_strategy_signal(self):
        """测试从策略信号转换"""
        # 创建测试信号
        strategy_signal = {
            "pair": "ETH/USDT",
            "side": "long",
            "order_type": "limit",
            "rate": 1500.0,
            "enter_tag": "test_conversion",
            "custom_field": "custom_value"
        }
        
        # 执行测试
        signal = SignalConverter.from_strategy_signal(strategy_signal)
        
        # 验证结果
        self.assertIsNotNone(signal)
        self.assertEqual(signal.pair, "ETH/USDT")
        self.assertEqual(signal.side, "long")
        self.assertEqual(signal.order_type, OrderType.LIMIT)
        self.assertEqual(signal.rate, 1500.0)
        self.assertEqual(signal.enter_tag, "test_conversion")
        self.assertEqual(signal.metadata, {"custom_field": "custom_value"})
    
    def test_to_from_json(self):
        """测试JSON序列化和反序列化"""
        # 创建交易信号
        signal = TradeSignal(
            pair="BTC/USDT",
            side="short",
            order_type=OrderType.LIMIT,
            rate=40000.0,
            enter_tag="json_test"
        )
        
        # 转换为JSON
        json_str = SignalConverter.to_json(signal)
        
        # 从JSON解析回信号
        parsed_signal = SignalConverter.from_json(json_str)
        
        # 验证结果
        self.assertIsNotNone(parsed_signal)
        self.assertEqual(parsed_signal.pair, signal.pair)
        self.assertEqual(parsed_signal.side, signal.side)
        self.assertEqual(parsed_signal.order_type, signal.order_type)
        self.assertEqual(parsed_signal.rate, signal.rate)
        self.assertEqual(parsed_signal.enter_tag, signal.enter_tag)


class TestFreqtradeService(unittest.TestCase):
    """测试FreqtradeService类"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建模拟的客户端和风控验证器
        self.mock_client = MagicMock()
        self.mock_risk_validator = MagicMock()
        
        # 使用补丁创建服务
        with patch('data.api.freqtrade.service.FreqtradeClient', return_value=self.mock_client), \
             patch('data.api.freqtrade.service.RiskValidator', return_value=self.mock_risk_validator):
            
            self.service = FreqtradeService(
                server_url="http://localhost:8080",
                username="test_user",
                password="test_password",
                enable_async_processing=False  # 禁用异步处理以简化测试
            )
    
    def test_submit_signal_successful(self):
        """测试成功提交信号"""
        # 设置模拟响应
        self.mock_client.ping.return_value = True
        self.mock_risk_validator.validate_signal.return_value = {"allow_trade": True}
        self.mock_client.force_enter_trade.return_value = TradeResult(success=True, tradeid=456)
        
        # 创建交易信号
        signal = TradeSignal(
            pair="ETH/USDT",
            side="long",
            order_type=OrderType.MARKET
        )
        
        # 执行测试
        result = self.service.submit_signal(signal)
        
        # 验证结果
        self.assertTrue(result)
        self.mock_risk_validator.validate_signal.assert_called_once()
        self.mock_client.force_enter_trade.assert_called_once_with(signal)
    
    def test_submit_signal_rejected_by_risk(self):
        """测试被风控系统拒绝的信号"""
        # 设置模拟响应
        self.mock_client.ping.return_value = True
        self.mock_risk_validator.validate_signal.return_value = {
            "allow_trade": False, 
            "reason": "Risk too high"
        }
        
        # 创建交易信号
        signal = TradeSignal(
            pair="BTC/USDT",
            side="long",
            order_type=OrderType.MARKET
        )
        
        # 执行测试
        result = self.service.submit_signal(signal)
        
        # 验证结果
        self.assertFalse(result)
        self.mock_risk_validator.validate_signal.assert_called_once()
        self.mock_client.force_enter_trade.assert_not_called()
    
    def test_get_open_trades(self):
        """测试获取开放交易"""
        # 设置模拟响应
        self.mock_client.ping.return_value = True
        self.mock_client.get_open_trades.return_value = [
            {"tradeid": 1, "pair": "BTC/USDT", "amount": 0.1},
            {"tradeid": 2, "pair": "ETH/USDT", "amount": 1.0}
        ]
        
        # 执行测试
        result = self.service.get_open_trades()
        
        # 验证结果
        self.assertEqual(len(result), 2)
        self.mock_client.get_open_trades.assert_called_once()


if __name__ == '__main__':
    unittest.main() 