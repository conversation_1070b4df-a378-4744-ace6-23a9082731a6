"""
实时数据目录结构初始化脚本

为FreqTrade创建实时数据目录并生成示例文件
"""

import os
import json
from datetime import datetime, timezone
import time

class LiveDataDirCreator:
    """实时数据目录创建器"""
    
    def __init__(self):
        """初始化目录创建器"""
        self.base_dir = "user_data/data"
        self.live_dir = os.path.join(self.base_dir, "live")
        self.price_dir = os.path.join(self.live_dir, "price_data")
        self.ticker_dir = os.path.join(self.live_dir, "ticker_updates")
        
        self.test_pairs = [
            "BTC/USDT:USDT",
            "ETH/USDT:USDT", 
            "XRP/USDT:USDT",
            "ADA/USDT:USDT",
            "BNB/USDT:USDT"
        ]
    
    def create_directory_structure(self):
        """创建目录结构"""
        print("📁 创建实时数据目录结构...")
        
        directories = [
            self.live_dir,
            self.price_dir,
            self.ticker_dir,
            os.path.join(self.live_dir, "market_data"),
            os.path.join(self.live_dir, "ohlcv_cache"),
            os.path.join(self.live_dir, "logs")
        ]
        
        for directory in directories:
            try:
                os.makedirs(directory, exist_ok=True)
                print(f"   ✅ 创建目录: {directory}")
            except Exception as e:
                print(f"   ❌ 创建目录失败 {directory}: {e}")
                return False
        
        return True
    
    def generate_sample_price_files(self):
        """生成示例价格数据文件"""
        print("💰 生成示例价格数据文件...")
        
        current_time = datetime.now(timezone.utc)
        
        for pair in self.test_pairs:
            try:
                # 生成示例价格数据
                price_data = {
                    "symbol": pair,
                    "timestamp": current_time.isoformat(),
                    "price": self._generate_mock_price(pair),
                    "bid": 0,
                    "ask": 0,
                    "high": 0,
                    "low": 0,
                    "volume": 0,
                    "change_24h": 0,
                    "change_percent_24h": 0,
                    "last_updated": current_time.isoformat(),
                    "source": "FreqTrade_Live_Data",
                    "data_quality": "simulated"
                }
                
                # 生成文件名
                safe_pair = pair.replace("/", "_").replace(":", "_")
                filename = f"{safe_pair.lower()}_price.json"
                filepath = os.path.join(self.price_dir, filename)
                
                # 写入文件
                with open(filepath, 'w') as f:
                    json.dump(price_data, f, indent=2)
                
                print(f"   ✅ 生成价格文件: {filename}")
                
            except Exception as e:
                print(f"   ❌ 生成价格文件失败 {pair}: {e}")
    
    def _generate_mock_price(self, pair):
        """生成模拟价格数据"""
        # 根据交易对生成合理的模拟价格
        prices = {
            "BTC/USDT:USDT": 43000 + (time.time() % 1000),
            "ETH/USDT:USDT": 2500 + (time.time() % 100),
            "XRP/USDT:USDT": 0.6 + (time.time() % 0.1),
            "ADA/USDT:USDT": 0.45 + (time.time() % 0.05),
            "BNB/USDT:USDT": 320 + (time.time() % 20)
        }
        return prices.get(pair, 100 + (time.time() % 10))
    
    def create_ticker_update_file(self):
        """创建实时ticker更新文件"""
        print("📊 创建实时ticker更新文件...")
        
        try:
            current_time = datetime.now(timezone.utc)
            
            ticker_data = {
                "last_updated": current_time.isoformat(),
                "update_frequency_seconds": 30,
                "total_pairs": len(self.test_pairs),
                "tickers": {}
            }
            
            for pair in self.test_pairs:
                ticker_data["tickers"][pair] = {
                    "last_price": self._generate_mock_price(pair),
                    "bid": 0,
                    "ask": 0,
                    "volume_24h": 1000000 + (time.time() % 100000),
                    "change_24h": (time.time() % 10) - 5,  # -5 到 +5 的变化
                    "last_update": current_time.isoformat(),
                    "status": "active"
                }
            
            filepath = os.path.join(self.ticker_dir, "latest_tickers.json")
            with open(filepath, 'w') as f:
                json.dump(ticker_data, f, indent=2)
            
            print(f"   ✅ 创建ticker文件: latest_tickers.json")
            
        except Exception as e:
            print(f"   ❌ 创建ticker文件失败: {e}")
    
    def create_data_info_file(self):
        """创建数据信息文件"""
        print("📋 创建数据信息文件...")
        
        try:
            info_data = {
                "created_at": datetime.now(timezone.utc).isoformat(),
                "purpose": "FreqTrade实时数据目录",
                "structure": {
                    "price_data/": "各交易对的实时价格文件",
                    "ticker_updates/": "市场ticker数据",
                    "market_data/": "市场深度和订单簿数据",
                    "ohlcv_cache/": "OHLCV数据缓存",
                    "logs/": "数据更新日志"
                },
                "update_mechanism": "由FreqTrade WebSocket和API定期更新",
                "data_retention": "实时数据，定期清理旧文件",
                "pairs_monitored": self.test_pairs,
                "notes": [
                    "这些是示例文件，实际数据将由FreqTrade实时更新",
                    "请确保FreqTrade有写入权限",
                    "文件格式遵循FreqTrade标准"
                ]
            }
            
            filepath = os.path.join(self.live_dir, "README.json")
            with open(filepath, 'w') as f:
                json.dump(info_data, f, indent=2)
            
            print(f"   ✅ 创建信息文件: README.json")
            
        except Exception as e:
            print(f"   ❌ 创建信息文件失败: {e}")
    
    def create_update_script(self):
        """创建数据更新脚本"""
        print("🔄 创建数据更新脚本...")
        
        script_content = '''#!/usr/bin/env python3
"""
实时数据更新脚本

定期更新live目录中的价格数据
"""

import json
import os
from datetime import datetime, timezone
import time

def update_live_data():
    """更新实时数据"""
    live_dir = "user_data/data/live"
    
    if not os.path.exists(live_dir):
        print("❌ 实时数据目录不存在")
        return
    
    print(f"🔄 更新实时数据 - {datetime.now()}")
    
    # 这里可以添加实际的数据获取逻辑
    # 例如：从交易所API获取最新价格
    
    print("✅ 数据更新完成")

if __name__ == "__main__":
    update_live_data()
'''
        
        try:
            script_path = os.path.join(self.live_dir, "update_live_data.py")
            with open(script_path, 'w') as f:
                f.write(script_content)
            
            print(f"   ✅ 创建更新脚本: update_live_data.py")
            
        except Exception as e:
            print(f"   ❌ 创建更新脚本失败: {e}")
    
    def verify_directory_structure(self):
        """验证目录结构"""
        print("\n🔍 验证目录结构...")
        
        expected_paths = [
            self.live_dir,
            self.price_dir,
            self.ticker_dir,
            os.path.join(self.price_dir, "btc_usdt_usdt_price.json"),
            os.path.join(self.ticker_dir, "latest_tickers.json"),
            os.path.join(self.live_dir, "README.json")
        ]
        
        all_good = True
        for path in expected_paths:
            if os.path.exists(path):
                print(f"   ✅ 验证通过: {path}")
            else:
                print(f"   ❌ 缺失: {path}")
                all_good = False
        
        return all_good
    
    def run_initialization(self):
        """运行完整的初始化过程"""
        print("🚀 开始实时数据目录初始化\n")
        
        # 1. 创建目录结构
        if not self.create_directory_structure():
            print("❌ 目录创建失败，终止初始化")
            return False
        
        # 2. 生成示例文件
        self.generate_sample_price_files()
        self.create_ticker_update_file()
        self.create_data_info_file()
        self.create_update_script()
        
        # 3. 验证结果
        success = self.verify_directory_structure()
        
        # 4. 生成报告
        self.generate_initialization_report(success)
        
        return success
    
    def generate_initialization_report(self, success):
        """生成初始化报告"""
        print("\n📋 实时数据目录初始化报告:")
        print("=" * 60)
        
        if success:
            print("✅ 状态: 初始化成功")
            print(f"✅ 基础目录: {self.live_dir}")
            print(f"✅ 价格数据目录: {self.price_dir}")
            print(f"✅ Ticker数据目录: {self.ticker_dir}")
            print(f"✅ 监控交易对: {len(self.test_pairs)} 个")
            
            print("\n📁 目录结构:")
            print(f"   {self.live_dir}/")
            print(f"   ├── price_data/")
            print(f"   │   ├── btc_usdt_usdt_price.json")
            print(f"   │   ├── eth_usdt_usdt_price.json")
            print(f"   │   └── ...")
            print(f"   ├── ticker_updates/")
            print(f"   │   └── latest_tickers.json") 
            print(f"   ├── market_data/")
            print(f"   ├── ohlcv_cache/")
            print(f"   ├── logs/")
            print(f"   ├── README.json")
            print(f"   └── update_live_data.py")
            
            print("\n🎯 结论: 实时数据目录已准备就绪")
            print("📝 下一步: 重启FreqTrade以应用新配置并测试API")
        else:
            print("❌ 状态: 初始化失败")
            print("🔧 请检查文件权限和磁盘空间")
        
        print("=" * 60)

if __name__ == "__main__":
    creator = LiveDataDirCreator()
    creator.run_initialization() 