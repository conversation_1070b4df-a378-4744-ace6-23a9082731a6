#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
freqtrade风控保护系统集成模块

整合freqtrade的专业风控保护功能，提供CooldownPeriod、MaxDrawdown、StoplossGuard、
LowProfitPairs等风控保护方法。这些都是经过市场验证的专业风控策略。
"""

from typing import Dict, Any, List, Optional, Union, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from abc import ABC, abstractmethod
import warnings


class FreqtradeProtectionBase(ABC):
    """freqtrade保护方法基类"""
    
    def __init__(self, method_name: str, **kwargs):
        """
        初始化保护方法
        
        Parameters
        ----------
        method_name : str
            保护方法名称
        **kwargs
            保护方法参数
        """
        self.method_name = method_name
        self.parameters = kwargs
        self.is_enabled = True
        self.global_stop = datetime.min
        self.pair_stops = {}
    
    @abstractmethod
    def should_stop(self, trade_context: Dict[str, Any]) -> Tuple[bool, str]:
        """
        判断是否应该停止交易
        
        Parameters
        ----------
        trade_context : Dict[str, Any]
            交易上下文信息
            
        Returns
        -------
        Tuple[bool, str]
            (是否停止, 停止原因)
        """
        pass
    
    def enable(self) -> None:
        """启用保护"""
        self.is_enabled = True
    
    def disable(self) -> None:
        """禁用保护"""
        self.is_enabled = False
    
    def reset(self) -> None:
        """重置保护状态"""
        self.global_stop = datetime.min
        self.pair_stops = {}


class CooldownPeriodProtection(FreqtradeProtectionBase):
    """
    冷却期保护
    
    在退出交易后，阻止在指定时间内重新进入同一交易对。
    """
    
    def __init__(self, stop_duration_candles: int = 2, **kwargs):
        """
        初始化冷却期保护
        
        Parameters
        ----------
        stop_duration_candles : int
            冷却期长度（蜡烛图数量）
        """
        super().__init__("CooldownPeriod", stop_duration_candles=stop_duration_candles, **kwargs)
        self.stop_duration_candles = stop_duration_candles
    
    def should_stop(self, trade_context: Dict[str, Any]) -> Tuple[bool, str]:
        """判断是否在冷却期内"""
        if not self.is_enabled:
            return False, ""
        
        pair = trade_context.get('pair', '')
        current_time = trade_context.get('current_time', datetime.now())
        candle_duration = trade_context.get('candle_duration', timedelta(minutes=15))
        
        # 检查该交易对是否在冷却期
        if pair in self.pair_stops:
            cooldown_end = self.pair_stops[pair] + (candle_duration * self.stop_duration_candles)
            if current_time < cooldown_end:
                return True, f"CooldownPeriod: {pair} in cooldown until {cooldown_end}"
        
        return False, ""
    
    def register_exit(self, pair: str, exit_time: datetime) -> None:
        """注册交易退出，开始冷却期"""
        self.pair_stops[pair] = exit_time


class MaxDrawdownProtection(FreqtradeProtectionBase):
    """
    最大回撤保护
    
    当回撤超过指定阈值时停止交易。
    """
    
    def __init__(
        self, 
        lookback_period_candles: int = 48,
        trade_limit: int = 20,
        stop_duration_candles: int = 12,
        max_allowed_drawdown: float = 0.2,
        **kwargs
    ):
        """
        初始化最大回撤保护
        
        Parameters
        ----------
        lookback_period_candles : int
            回看期长度（蜡烛图数量）
        trade_limit : int
            最小交易数量要求
        stop_duration_candles : int
            停止交易时长（蜡烛图数量）
        max_allowed_drawdown : float
            最大允许回撤比例
        """
        super().__init__(
            "MaxDrawdown",
            lookback_period_candles=lookback_period_candles,
            trade_limit=trade_limit,
            stop_duration_candles=stop_duration_candles,
            max_allowed_drawdown=max_allowed_drawdown,
            **kwargs
        )
        self.lookback_period_candles = lookback_period_candles
        self.trade_limit = trade_limit
        self.stop_duration_candles = stop_duration_candles
        self.max_allowed_drawdown = max_allowed_drawdown
    
    def should_stop(self, trade_context: Dict[str, Any]) -> Tuple[bool, str]:
        """判断是否触发最大回撤保护"""
        if not self.is_enabled:
            return False, ""
        
        current_time = trade_context.get('current_time', datetime.now())
        candle_duration = trade_context.get('candle_duration', timedelta(minutes=15))
        
        # 检查全局停止状态
        if current_time < self.global_stop:
            return True, f"MaxDrawdown: Global stop until {self.global_stop}"
        
        trades_history = trade_context.get('trades_history', [])
        equity_curve = trade_context.get('equity_curve', pd.Series())
        
        if len(trades_history) < self.trade_limit:
            return False, ""
        
        # 计算回看期内的回撤
        lookback_start = current_time - (candle_duration * self.lookback_period_candles)
        recent_equity = equity_curve[equity_curve.index >= lookback_start]
        
        if len(recent_equity) > 0:
            peak = recent_equity.max()
            current_value = recent_equity.iloc[-1]
            drawdown = (peak - current_value) / peak
            
            if drawdown > self.max_allowed_drawdown:
                # 设置全局停止时间
                self.global_stop = current_time + (candle_duration * self.stop_duration_candles)
                return True, f"MaxDrawdown: Drawdown {drawdown:.2%} > {self.max_allowed_drawdown:.2%}"
        
        return False, ""


class StoplossGuardProtection(FreqtradeProtectionBase):
    """
    止损保护
    
    当止损交易过多时停止交易。
    """
    
    def __init__(
        self,
        lookback_period_candles: int = 24,
        trade_limit: int = 4,
        stop_duration_candles: int = 4,
        required_profit: float = 0.0,
        only_per_pair: bool = False,
        only_per_side: bool = False,
        **kwargs
    ):
        """
        初始化止损保护
        
        Parameters
        ----------
        lookback_period_candles : int
            回看期长度（蜡烛图数量）
        trade_limit : int
            触发保护的止损交易数量
        stop_duration_candles : int
            停止交易时长（蜡烛图数量）
        required_profit : float
            触发保护的最小利润要求
        only_per_pair : bool
            是否仅对单个交易对应用
        only_per_side : bool
            是否仅对单个方向应用
        """
        super().__init__(
            "StoplossGuard",
            lookback_period_candles=lookback_period_candles,
            trade_limit=trade_limit,
            stop_duration_candles=stop_duration_candles,
            required_profit=required_profit,
            only_per_pair=only_per_pair,
            only_per_side=only_per_side,
            **kwargs
        )
        self.lookback_period_candles = lookback_period_candles
        self.trade_limit = trade_limit
        self.stop_duration_candles = stop_duration_candles
        self.required_profit = required_profit
        self.only_per_pair = only_per_pair
        self.only_per_side = only_per_side
    
    def should_stop(self, trade_context: Dict[str, Any]) -> Tuple[bool, str]:
        """判断是否触发止损保护"""
        if not self.is_enabled:
            return False, ""
        
        current_time = trade_context.get('current_time', datetime.now())
        candle_duration = trade_context.get('candle_duration', timedelta(minutes=15))
        pair = trade_context.get('pair', '')
        side = trade_context.get('side', 'long')
        
        # 检查保护状态
        protection_key = self._get_protection_key(pair, side)
        if protection_key in self.pair_stops:
            if current_time < self.pair_stops[protection_key]:
                return True, f"StoplossGuard: {protection_key} protected until {self.pair_stops[protection_key]}"
        
        # 检查全局保护状态
        if not self.only_per_pair and current_time < self.global_stop:
            return True, f"StoplossGuard: Global protection until {self.global_stop}"
        
        trades_history = trade_context.get('trades_history', [])
        
        # 计算回看期内的止损交易数量
        lookback_start = current_time - (candle_duration * self.lookback_period_candles)
        
        stoploss_trades = []
        for trade in trades_history:
            trade_time = trade.get('close_time', trade.get('exit_time'))
            if not trade_time or trade_time < lookback_start:
                continue
            
            # 检查是否为止损交易
            exit_reason = trade.get('exit_reason', '')
            profit = trade.get('profit_ratio', 0)
            
            if 'stop_loss' in exit_reason.lower() and profit <= self.required_profit:
                # 检查过滤条件
                if self.only_per_pair and trade.get('pair') != pair:
                    continue
                if self.only_per_side and trade.get('side', 'long') != side:
                    continue
                
                stoploss_trades.append(trade)
        
        # 判断是否触发保护
        if len(stoploss_trades) >= self.trade_limit:
            stop_until = current_time + (candle_duration * self.stop_duration_candles)
            
            if self.only_per_pair:
                self.pair_stops[protection_key] = stop_until
            else:
                self.global_stop = stop_until
            
            return True, f"StoplossGuard: {len(stoploss_trades)} stoploss trades >= {self.trade_limit}"
        
        return False, ""
    
    def _get_protection_key(self, pair: str, side: str) -> str:
        """获取保护键值"""
        if self.only_per_pair and self.only_per_side:
            return f"{pair}_{side}"
        elif self.only_per_pair:
            return pair
        elif self.only_per_side:
            return side
        else:
            return "global"


class LowProfitPairsProtection(FreqtradeProtectionBase):
    """
    低利润交易对保护
    
    当交易对利润过低时停止该交易对的交易。
    """
    
    def __init__(
        self,
        lookback_period_candles: int = 6,
        trade_limit: int = 2,
        stop_duration_candles: int = 60,
        required_profit: float = 0.02,
        only_per_pair: bool = True,
        **kwargs
    ):
        """
        初始化低利润交易对保护
        
        Parameters
        ----------
        lookback_period_candles : int
            回看期长度（蜡烛图数量）
        trade_limit : int
            最小交易数量
        stop_duration_candles : int
            停止交易时长（蜡烛图数量）
        required_profit : float
            要求的最小利润率
        only_per_pair : bool
            是否仅对单个交易对应用
        """
        super().__init__(
            "LowProfitPairs",
            lookback_period_candles=lookback_period_candles,
            trade_limit=trade_limit,
            stop_duration_candles=stop_duration_candles,
            required_profit=required_profit,
            only_per_pair=only_per_pair,
            **kwargs
        )
        self.lookback_period_candles = lookback_period_candles
        self.trade_limit = trade_limit
        self.stop_duration_candles = stop_duration_candles
        self.required_profit = required_profit
        self.only_per_pair = only_per_pair
    
    def should_stop(self, trade_context: Dict[str, Any]) -> Tuple[bool, str]:
        """判断是否触发低利润保护"""
        if not self.is_enabled:
            return False, ""
        
        current_time = trade_context.get('current_time', datetime.now())
        candle_duration = trade_context.get('candle_duration', timedelta(minutes=15))
        pair = trade_context.get('pair', '')
        
        # 检查保护状态
        protection_key = pair if self.only_per_pair else "global"
        if protection_key in self.pair_stops:
            if current_time < self.pair_stops[protection_key]:
                return True, f"LowProfitPairs: {protection_key} protected until {self.pair_stops[protection_key]}"
        
        trades_history = trade_context.get('trades_history', [])
        
        # 计算回看期内的交易利润
        lookback_start = current_time - (candle_duration * self.lookback_period_candles)
        
        relevant_trades = []
        for trade in trades_history:
            trade_time = trade.get('close_time', trade.get('exit_time'))
            if not trade_time or trade_time < lookback_start:
                continue
            
            if self.only_per_pair and trade.get('pair') != pair:
                continue
            
            relevant_trades.append(trade)
        
        # 判断是否触发保护
        if len(relevant_trades) >= self.trade_limit:
            total_profit = sum(trade.get('profit_ratio', 0) for trade in relevant_trades)
            avg_profit = total_profit / len(relevant_trades)
            
            if avg_profit < self.required_profit:
                stop_until = current_time + (candle_duration * self.stop_duration_candles)
                self.pair_stops[protection_key] = stop_until
                
                return True, f"LowProfitPairs: Avg profit {avg_profit:.2%} < {self.required_profit:.2%}"
        
        return False, ""


class FreqtradeProtectionManager:
    """freqtrade保护管理器"""
    
    def __init__(self, protections_config: Optional[Dict[str, Dict[str, Any]]] = None):
        """
        初始化保护管理器
        
        Parameters
        ----------
        protections_config : Optional[Dict[str, Dict[str, Any]]]
            保护配置字典，格式为 {method_name: params}
        """
        self.protections: List[FreqtradeProtectionBase] = []
        self.enabled = True
        
        # 如果提供了配置，自动创建保护实例
        if protections_config:
            self._create_protections_from_config(protections_config)
    
    def _create_protections_from_config(self, config: Dict[str, Dict[str, Any]]) -> None:
        """
        从配置字典创建保护实例
        
        Parameters
        ----------
        config : Dict[str, Dict[str, Any]]
            保护配置字典
        """
        for method_name, params in config.items():
            if method_name == "CooldownPeriod":
                protection = CooldownPeriodProtection(**params)
            elif method_name == "MaxDrawdown":
                protection = MaxDrawdownProtection(**params)
            elif method_name == "StoplossGuard":
                protection = StoplossGuardProtection(**params)
            elif method_name == "LowProfitPairs":
                protection = LowProfitPairsProtection(**params)
            else:
                warnings.warn(f"未知的保护方法: {method_name}")
                continue
            
            self.add_protection(protection)
    
    def add_protection(self, protection: FreqtradeProtectionBase) -> None:
        """
        添加保护方法
        
        Parameters
        ----------
        protection : FreqtradeProtectionBase
            保护方法实例
        """
        self.protections.append(protection)
    
    def remove_protection(self, method_name: str) -> None:
        """
        移除保护方法
        
        Parameters
        ----------
        method_name : str
            保护方法名称
        """
        self.protections = [p for p in self.protections if p.method_name != method_name]
    
    def check_protections(self, trade_context: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        检查所有保护方法
        
        Parameters
        ----------
        trade_context : Dict[str, Any]
            交易上下文
            
        Returns
        -------
        Tuple[bool, List[str]]
            (是否被保护, 保护原因列表)
        """
        if not self.enabled:
            return False, []
        
        protection_reasons = []
        
        for protection in self.protections:
            should_stop, reason = protection.should_stop(trade_context)
            if should_stop:
                protection_reasons.append(reason)
        
        return len(protection_reasons) > 0, protection_reasons
    
    def enable(self) -> None:
        """启用保护管理器"""
        self.enabled = True
    
    def disable(self) -> None:
        """禁用保护管理器"""
        self.enabled = False
    
    def reset_all(self) -> None:
        """重置所有保护状态"""
        for protection in self.protections:
            protection.reset()
    
    @classmethod
    def from_freqtrade_config(cls, protections_config: List[Dict[str, Any]]) -> 'FreqtradeProtectionManager':
        """
        从freqtrade配置创建保护管理器
        
        Parameters
        ----------
        protections_config : List[Dict[str, Any]]
            freqtrade保护配置列表
            
        Returns
        -------
        FreqtradeProtectionManager
            配置好的保护管理器
        """
        manager = cls()
        
        for config in protections_config:
            method = config.get('method', '')
            params = {k: v for k, v in config.items() if k != 'method'}
            
            if method == "CooldownPeriod":
                protection = CooldownPeriodProtection(**params)
            elif method == "MaxDrawdown":
                protection = MaxDrawdownProtection(**params)
            elif method == "StoplossGuard":
                protection = StoplossGuardProtection(**params)
            elif method == "LowProfitPairs":
                protection = LowProfitPairsProtection(**params)
            else:
                warnings.warn(f"未知的保护方法: {method}")
                continue
            
            manager.add_protection(protection)
        
        return manager


# 导出的类和函数
__all__ = [
    'FreqtradeProtectionBase',
    'CooldownPeriodProtection',
    'MaxDrawdownProtection', 
    'StoplossGuardProtection',
    'LowProfitPairsProtection',
    'FreqtradeProtectionManager',
] 