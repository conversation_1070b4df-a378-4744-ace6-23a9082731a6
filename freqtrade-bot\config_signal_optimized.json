{"$schema": "https://schema.freqtrade.io/schema.json", "max_open_trades": 8, "stake_currency": "USDT", "stake_amount": "unlimited", "tradable_balance_ratio": 0.99, "dry_run": true, "dry_run_wallet": 10000, "cancel_open_orders_on_exit": false, "trading_mode": "futures", "margin_mode": "isolated", "liquidation_buffer": 0.05, "strategy": "SMCStrategy", "strategy_path": "E:/newADM/gitRepository/AriQuantification/backtest/strategies/", "unfilledtimeout": {"entry": 300, "exit": 180, "unit": "seconds"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": true, "bids_to_ask_delta": 0.03}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0}, "exchange": {"name": "binance", "key": "", "secret": "", "ccxt_config": {"enableRateLimit": true, "rateLimit": 200, "timeout": 30000, "options": {"defaultType": "future"}}, "ccxt_async_config": {"enableRateLimit": true, "rateLimit": 200, "timeout": 30000, "options": {"defaultType": "future"}}, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "BNB/USDT:USDT", "ADA/USDT:USDT", "XRP/USDT:USDT", "SOL/USDT:USDT", "DOGE/USDT:USDT", "MATIC/USDT:USDT", "LTC/USDT:USDT", "AVAX/USDT:USDT"], "pair_blacklist": ["BNB/.*", ".*/BTC", ".*/ETH"]}, "pairlists": [{"method": "StaticPairList"}], "edge": {"enabled": false}, "telegram": {"enabled": false}, "api_server": {"enabled": true, "listen_ip_address": "127.0.0.1", "listen_port": 8080, "verbosity": "error", "enable_openapi": false, "jwt_secret_key": "somethingrandom", "CORS_origins": [], "username": "freqtrader", "password": "SuperSecretPassword"}, "bot_name": "freqtrade", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 1, "heartbeat_interval": 60}, "logging": {"level": "INFO", "disable_warnings": ["urllib3.connectionpool:InsecureRequestWarning", "urllib3.exceptions.InsecureRequestWarning"]}, "dataformat_ohlcv": "json", "dataformat_trades": "jsongz", "process_only_new_candles": false}