"""
资金管理系统教程

详细介绍资金管理系统的概念和使用方法。
"""

import pandas as pd
import numpy as np
import sys
import os
import matplotlib.pyplot as plt
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from risk.money_management.base import MoneyManager, PositionSizer
from risk.money_management.position_sizing import (
    FixedAmountSizer, 
    FixedPercentSizer,
    KellyCriterionSizer, 
    OptimalFSizer,
    VolatilityAdjustedSizer, 
    MaxDrawdownSizer
)
from risk.money_management.capital_allocation import (
    EqualAllocationStrategy,
    VolatilityAdjustedAllocation,
    PerformanceBasedAllocation
)

print("=" * 80)
print("资金管理系统教程")
print("=" * 80)

# =========================================================================
print("\n1. 资金管理系统概述")
print("-" * 50)
# =========================================================================

print("""
资金管理是量化交易中至关重要的一部分，它决定了如何分配资金和控制风险。良好的资金管理可以:
- 保护交易资本免受过度损失
- 优化资本使用效率，最大化回报
- 在多个交易品种之间合理分配资金

在本教程中，我们将介绍资金管理系统的两个主要组成部分:
1. 仓位规模计算 - 决定每次交易投入多少资金
2. 资金分配策略 - 如何在多个交易品种之间分配资金

我们的资金管理系统提供了多种仓位规模计算器和资金分配策略，可以灵活组合使用。
""")

# =========================================================================
print("\n2. 仓位规模计算方法")
print("-" * 50)
# =========================================================================

print("""
仓位规模计算决定了每次交易应该投入多少资金。这是风险管理的核心部分，因为过大的仓位会带来过高的风险，
而过小的仓位则可能无法充分利用交易机会。

我们实现了以下几种常用的仓位规模计算方法:
""")

print("\n2.1 固定金额策略 (FixedAmountSizer)")
print("    每次交易使用固定金额，如$1000。简单但不考虑账户大小和风险。")

print("\n2.2 固定百分比策略 (FixedPercentSizer)")
print("    每次交易使用账户资金的固定百分比，如2%。随账户大小自动调整。")

print("\n2.3 凯利准则策略 (KellyCriterionSizer)")
print("""    基于凯利公式计算最优仓位：f* = (bp - q) / b
    其中b是赔率，p是胜率，q是1-p。理论上最优但波动较大，实践中常用"半凯利"降低风险。""")

print("\n2.4 最优F值策略 (OptimalFSizer)")
print("    基于Ralph Vince的研究，使用历史交易数据找到能最大化资金增长的比例。")

print("\n2.5 波动率调整策略 (VolatilityAdjustedSizer)")
print("    根据市场波动率动态调整仓位大小，波动率高时减小仓位，波动率低时增加仓位。")

print("\n2.6 最大回撤调整策略 (MaxDrawdownSizer)")
print("    根据历史最大回撤调整仓位，回撤大时减小仓位，以控制风险。")

# =========================================================================
print("\n3. 资金分配策略")
print("-" * 50)
# =========================================================================

print("""
资金分配策略决定了如何在多个交易品种之间分配资金。在多品种交易系统中，合理的资金分配可以分散风险并优化整体收益。

我们实现了以下几种资金分配策略:
""")

print("\n3.1 等比例分配策略 (EqualAllocationStrategy)")
print("    将资金平均分配给每个交易品种。最简单的方法，不考虑各品种特性差异。")

print("\n3.2 波动率调整分配策略 (VolatilityAdjustedAllocation)")
print("    根据各品种的波动率调整分配比例，一般是波动率越低分配越多，以平衡风险。")

print("\n3.3 绩效基础分配策略 (PerformanceBasedAllocation)")
print("    根据各品种的历史表现（如胜率、平均收益、夏普比率等）分配资金，表现越好分配越多。")

# =========================================================================
print("\n4. 使用示例")
print("-" * 50)
# =========================================================================

print("下面通过几个具体例子演示如何使用资金管理系统:")

# 示例数据生成
def generate_sample_data():
    # 生成BTC的示例数据
    dates = pd.date_range('2022-01-01', periods=100)
    np.random.seed(42)
    returns = np.random.normal(0.001, 0.03, len(dates))
    prices = 100 * (1 + returns).cumprod()
    
    data = pd.DataFrame({
        'open': prices * 0.99,
        'high': prices * 1.02,
        'low': prices * 0.98,
        'close': prices,
        'volume': np.random.lognormal(10, 1, len(dates))
    }, index=dates)
    
    return data

# 示例交易记录生成
def generate_sample_trades():
    np.random.seed(42)
    trades = []
    
    for i in range(30):
        is_win = np.random.random() < 0.6  # 60%胜率
        pnl = np.random.normal(50, 20) if is_win else -np.random.normal(30, 15)
        entry_price = 50000 + np.random.normal(0, 1000)
        exit_price = entry_price + pnl
        stop_loss_price = entry_price - np.random.uniform(500, 1500)
        
        trades.append({
            'entry_price': entry_price,
            'exit_price': exit_price,
            'stop_loss_price': stop_loss_price,
            'pnl': pnl,
            'initial_risk': entry_price - stop_loss_price
        })
    
    return trades

# 生成示例数据
market_data = generate_sample_data()
trades = generate_sample_trades()
equity_curve = [10000]
for trade in trades:
    equity_curve.append(equity_curve[-1] + trade['pnl'])

# 4.1 固定金额策略示例
print("\n4.1 固定金额策略示例")
context = {'capital': 10000}
fixed_amount = FixedAmountSizer(amount=1000)
size = fixed_amount.calculate_position_size(context)
print(f"使用固定金额策略，账户资金${context['capital']}，计算出的仓位大小为: ${size}")

# 4.2 固定百分比策略示例
print("\n4.2 固定百分比策略示例")
fixed_percent = FixedPercentSizer(percent=2)
size = fixed_percent.calculate_position_size(context)
print(f"使用固定百分比策略(2%)，账户资金${context['capital']}，计算出的仓位大小为: ${size}")

# 4.3 凯利准则策略示例
print("\n4.3 凯利准则策略示例")
context_with_trades = {'capital': 10000, 'trades': trades}
kelly = KellyCriterionSizer(fraction=0.5)  # 半凯利
size = kelly.calculate_position_size(context_with_trades)
print(f"使用凯利准则策略(半凯利)，账户资金${context['capital']}，计算出的仓位大小为: ${size}")

# 自动计算胜率和盈亏比
wins = [t for t in trades if t['pnl'] > 0]
win_rate = len(wins) / len(trades)
avg_win = sum(t['pnl'] for t in wins) / len(wins)
avg_loss = sum(abs(t['pnl']) for t in trades if t['pnl'] < 0) / len([t for t in trades if t['pnl'] < 0])
win_loss_ratio = avg_win / avg_loss
print(f"基于历史交易数据: 胜率={win_rate:.2f}, 平均盈利=${avg_win:.2f}, 平均亏损=${avg_loss:.2f}, 盈亏比={win_loss_ratio:.2f}")
kelly_formula = (win_loss_ratio * win_rate - (1 - win_rate)) / win_loss_ratio
print(f"理论凯利比例: {kelly_formula:.4f} (半凯利: {kelly_formula*0.5:.4f})")

# 4.4 波动率调整策略示例
print("\n4.4 波动率调整策略示例")
volatility_sizer = VolatilityAdjustedSizer(target_risk_pct=0.5)
size = volatility_sizer.calculate_position_size(context, market_data)
print(f"使用波动率调整策略，目标风险0.5%，计算出的仓位大小为: ${size}")
vol = market_data['close'].pct_change().std() * 100
print(f"市场波动率: {vol:.2f}%")

# 4.5 资金分配示例
print("\n4.5 资金分配示例")
symbols = ['BTC', 'ETH', 'SOL', 'USDT']
capital = 50000
context = {'capital': capital}

# 生成各品种的模拟波动率数据
volatilities = {'BTC': 3.0, 'ETH': 2.5, 'SOL': 2.0, 'USDT': 0.1}

# 创建模拟市场数据
market_data_dict = {}
np.random.seed(42)
for symbol, vol in volatilities.items():
    dates = pd.date_range('2022-01-01', periods=100)
    returns = np.random.normal(0.001, vol/100, len(dates))
    prices = 100 * (1 + returns).cumprod()
    
    data = pd.DataFrame({
        'open': prices * 0.99,
        'high': prices * 1.02,
        'low': prices * 0.98,
        'close': prices,
        'volume': np.random.lognormal(10, 1, len(dates))
    }, index=dates)
    
    market_data_dict[symbol] = data

# 等比例分配
equal_strategy = EqualAllocationStrategy()
equal_allocation = equal_strategy.allocate(symbols, capital, context)
print("\n等比例分配策略:")
for symbol, amount in equal_allocation.items():
    print(f"  {symbol}: ${amount:.2f} ({amount/capital*100:.1f}%)")

# 波动率调整分配
volatility_strategy = VolatilityAdjustedAllocation(
    volatility_lookback=20,
    max_allocation_pct=95,
    min_allocation_pct=5,
    inverse_weight=True  # 波动率越低，分配越多
)
volatility_allocation = volatility_strategy.allocate(symbols, capital, context, market_data_dict)
print("\n波动率调整分配策略 (低波动率优先):")
for symbol, amount in volatility_allocation.items():
    vol = volatilities.get(symbol, "N/A")
    vol_str = f"{vol:.2f}%" if isinstance(vol, (int, float)) else vol
    print(f"  {symbol} (波动率: {vol_str}): ${amount:.2f} ({amount/capital*100:.1f}%)")

# =========================================================================
print("\n5. 将仓位规模计算与资金分配结合使用")
print("-" * 50)
# =========================================================================

print("""
在实际交易中，资金分配和仓位规模计算通常结合使用:
1. 首先使用资金分配策略决定各品种可用资金
2. 然后为每个品种单独计算仓位规模

MoneyManager类提供了这两种功能的统一接口:
""")

# 初始化资金管理器
money_manager = MoneyManager(100000)

# 添加仓位规模计算器
money_manager.add_position_sizer(FixedPercentSizer(percent=2), is_default=True)
money_manager.add_position_sizer(KellyCriterionSizer(fraction=0.3))
money_manager.add_position_sizer(VolatilityAdjustedSizer(target_risk_pct=0.5))

# 设置资金分配策略
money_manager.set_allocation_strategy(
    VolatilityAdjustedAllocation(
        volatility_lookback=20,
        max_allocation_pct=90
    )
)

# 分配资金
symbols = ['BTC', 'ETH', 'SOL', 'USDT']
allocations = money_manager.allocate_capital(symbols, {'capital': 100000}, market_data_dict)

print("资金分配结果:")
for symbol, amount in allocations.items():
    print(f"  {symbol}: ${amount:.2f}")

# 计算BTC的仓位规模
btc_size = money_manager.calculate_position_size(
    'BTC', 
    {'capital': allocations['BTC']}
)
print(f"\nBTC分配资金: ${allocations['BTC']:.2f}, 仓位规模: ${btc_size:.2f}")

# =========================================================================
print("\n6. 资金管理系统的整合应用")
print("-" * 50)
# =========================================================================

print("""
资金管理系统可以与回测引擎、策略和风控规则整合，以下是一些常见的使用场景:

1. 回测时动态调整仓位规模
   - 可以根据策略表现、市场波动率等动态调整仓位
   - 模拟真实交易中的资金管理决策

2. 多策略资金分配
   - 根据各策略的表现和特性分配资金
   - 可以使用绩效基础分配策略

3. 风险管理整合
   - 与风控规则协同工作，在满足风控要求的前提下计算仓位
   - 例如，结合最大回撤规则和波动率调整仓位

4. 实盘交易应用
   - 自动化计算下单规模
   - 定期重新平衡投资组合
""")

# =========================================================================
print("\n7. 案例分析：市场波动率变化时的仓位调整")
print("-" * 50)
# =========================================================================

# 准备不同波动率的数据
volatilities = np.linspace(0.5, 5, 20)  # 从0.5%到5%
capital = 10000

# 不同的仓位规模计算器
fixed_amount = FixedAmountSizer(amount=1000)
fixed_percent = FixedPercentSizer(percent=5)
volatility_adjusted = VolatilityAdjustedSizer(target_risk_pct=1.0)

# 计算不同波动率下的仓位规模
sizes = {
    "固定金额": [],
    "固定百分比": [],
    "波动率调整": []
}

for vol in volatilities:
    # 创建模拟的市场数据
    dates = pd.date_range('2022-01-01', periods=30)
    returns = np.random.normal(0, vol/100, len(dates))
    prices = 100 * (1 + returns).cumprod()
    market_data = pd.DataFrame({
        'open': prices * 0.99,
        'high': prices * 1.02,
        'low': prices * 0.98,
        'close': prices,
        'volume': np.random.lognormal(10, 1, len(dates))
    }, index=dates)
    
    # 计算各策略的仓位规模
    context = {'capital': capital}
    sizes["固定金额"].append(fixed_amount.calculate_position_size(context))
    sizes["固定百分比"].append(fixed_percent.calculate_position_size(context))
    sizes["波动率调整"].append(volatility_adjusted.calculate_position_size(context, market_data))

# 绘制比较图
plt.figure(figsize=(10, 6))
for name, values in sizes.items():
    plt.plot(volatilities, values, label=name, marker='o' if name == "固定金额" else ('s' if name == "固定百分比" else '^'))

plt.xlabel('波动率 (%)')
plt.ylabel('仓位规模 ($)')
plt.title('不同波动率下的仓位规模比较')
plt.grid(True)
plt.legend()
plt.tight_layout()

# 保存图表
output_dir = os.path.join(os.path.dirname(__file__), 'output')
os.makedirs(output_dir, exist_ok=True)
plt.savefig(os.path.join(output_dir, 'position_size_vs_volatility.png'))
print(f"图表已保存到: {os.path.join(output_dir, 'position_size_vs_volatility.png')}")

print("\n波动率变化对仓位规模的影响表:")
print("-" * 50)
print("波动率(%)   固定金额($)   固定百分比($)   波动率调整($)")
print("-" * 50)
for i in range(0, len(volatilities), 4):  # 只打印部分数据
    vol = volatilities[i]
    fixed_amt = sizes["固定金额"][i]
    fixed_pct = sizes["固定百分比"][i]
    vol_adj = sizes["波动率调整"][i]
    print(f"{vol:5.2f}%      ${fixed_amt:7.2f}      ${fixed_pct:7.2f}      ${vol_adj:7.2f}")

# =========================================================================
print("\n8. 总结与最佳实践")
print("-" * 50)
# =========================================================================

print("""
资金管理系统的要点与最佳实践:

1. 仓位规模的选择
   - 初学者最好从固定百分比开始，简单且有效
   - 进阶可考虑波动率调整或凯利准则，但需要更多历史数据支持
   - 建议仓位规模不超过总资金的5%，除非有充分理由

2. 资金分配策略
   - 等比例分配适合初始阶段或各品种表现相近时
   - 波动率调整分配有助于平衡风险
   - 绩效基础分配可以随时间自动向表现好的品种倾斜

3. 风险管理整合
   - 资金管理是风险控制的一部分，应与止损策略配合使用
   - 考虑市场状态，在高波动环境降低整体风险敞口

4. 灵活调整
   - 定期评估资金管理策略的表现
   - 根据市场环境和策略表现调整参数

5. 常见错误
   - 仓位过大：即使策略很好，过大仓位也可能导致灾难性损失
   - 忽略相关性：高度相关的品种不应同时持有过大仓位
   - 过度交易：频繁调整仓位可能增加交易成本
   - 忽略波动率：市场环境变化时应相应调整仓位
""")

print("\n感谢使用本教程。资金管理系统是量化交易成功的关键组成部分，希望本教程能帮助您更好地管理资金和控制风险。") 