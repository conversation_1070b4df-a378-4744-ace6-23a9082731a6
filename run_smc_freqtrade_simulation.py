#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SMC FreqTrade 监控系统

整合SMC策略的FreqTrade监控和风险管理系统。
不再使用自定义TradingEngine，完全基于FreqTrade原生架构。
"""

import os
import sys
import json
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 导入风险监控组件
from risk.monitoring.real_time import TradingSignalMonitor, CapitalChangeMonitor, PriceVolatilityMonitor
from risk.monitoring.alerts import AlertMonitor, SeverityBasedAlertRule, ThresholdAlertRule
from risk.monitoring.notification import LoggingHandler, FileStorageHandler

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SMCFreqtradeMonitor:
    """
    SMC FreqTrade 监控系统
    
    专门用于监控FreqTrade的SMC策略表现，不包含交易执行功能。
    """
    
    def __init__(self, config_path: str = "config/smc_freqtrade_config.json"):
        """
        初始化监控系统
        
        Parameters
        ----------
        config_path : str
            配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        
        # 监控组件
        self.signal_monitor = None
        self.capital_monitor = None
        self.price_monitor = None
        self.alert_monitor = None
        
        # 运行状态
        self.is_running = False
        self.monitor_thread = None
        
        logger.info("✅ SMC FreqTrade 监控系统初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"📄 已加载配置文件: {self.config_path}")
                return config
            else:
                logger.warning(f"⚠️ 配置文件不存在: {self.config_path}")
                return self._get_default_config()
        except Exception as e:
            logger.error(f"❌ 加载配置文件失败: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "monitoring": { 
                "signal_monitor": {
                    "enabled": True,
                    "max_signals_per_interval": 10,
                    "min_signal_interval": 5,
                    "check_interval": 1
                },
                "capital_monitor": {
                    "enabled": True,
                    "max_drawdown_pct": 5.0,
                    "max_hourly_drawdown_pct": 10.0,
                    "check_interval": 5
                },
                "price_monitor": {
                    "enabled": True,
                    "max_short_term_change_pct": 3.0,
                    "max_hourly_change_pct": 8.0,
                    "check_interval": 5
                }
            },
            "data": {
                "storage_dir": "data/storage/data",
                "cache_enabled": True
            },
            "alerts": {
                "log_file": "data/monitoring/smc_freqtrade/alerts.log",
                "storage_dir": "data/monitoring/smc_freqtrade/monitoring_data"
            }
        }

    def setup_monitoring(self):
        """设置监控组件"""
        try:
            # 不再创建外部数据API实例，避免网络依赖
            logger.info("📡 设置本地监控组件（无外部API依赖）")
            
            # 创建监控器
            monitor_config = self.config.get("monitoring", {})
            
            # 信号监控器
            if monitor_config.get("signal_monitor", {}).get("enabled", True):
                signal_config = monitor_config.get("signal_monitor", {})
                self.signal_monitor = TradingSignalMonitor(
                    max_signals_per_interval=signal_config.get("max_signals_per_interval", 10),
                    min_signal_interval=signal_config.get("min_signal_interval", 5),
                    check_interval=signal_config.get("check_interval", 1)
                )
                logger.info("📡 信号监控器已启用")

            # 资金监控器
            if monitor_config.get("capital_monitor", {}).get("enabled", True):
                capital_config = monitor_config.get("capital_monitor", {})
                self.capital_monitor = CapitalChangeMonitor(
                    max_drawdown_pct=capital_config.get("max_drawdown_pct", 5.0),
                    max_hourly_drawdown_pct=capital_config.get("max_hourly_drawdown_pct", 10.0),
                    check_interval=capital_config.get("check_interval", 5)
                )
                logger.info("💰 资金监控器已启用")

            # 价格监控器
            if monitor_config.get("price_monitor", {}).get("enabled", True):
                price_config = monitor_config.get("price_monitor", {})
                self.price_monitor = PriceVolatilityMonitor(
                    max_short_term_change_pct=price_config.get("max_short_term_change_pct", 3.0),
                    max_hourly_change_pct=price_config.get("max_hourly_change_pct", 8.0),
                    check_interval=price_config.get("check_interval", 5)
                )
                logger.info("📈 价格监控器已启用")

            # 创建预警监控器
            self.alert_monitor = AlertMonitor()
            
            # 添加预警规则
            severity_rule = SeverityBasedAlertRule(
                name="high_severity_rule",
                min_severity="WARNING"
            )
            self.alert_monitor.add_rule(severity_rule)
            
            # 添加处理器
            alerts_config = self.config.get("alerts", {})
            
            # 日志处理器
            log_file = alerts_config.get("log_file", "data/monitoring/smc_freqtrade/alerts.log")
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            logging_handler = LoggingHandler(
                name="smc_logging_handler",
                log_file=log_file
            )
            self.alert_monitor.event_handlers.append(logging_handler)
            
            # 文件存储处理器
            storage_dir = alerts_config.get("storage_dir", "data/monitoring/smc_freqtrade/monitoring_data")
            os.makedirs(storage_dir, exist_ok=True)
            file_handler = FileStorageHandler(
                name="smc_file_handler",
                storage_dir=storage_dir
        )
            self.alert_monitor.event_handlers.append(file_handler)
        
            logger.info("🚨 预警系统已设置完成")
            
        except Exception as e:
            logger.error(f"❌ 设置监控组件失败: {e}")
            raise

    def start_monitoring(self):
        """启动监控"""
        if self.is_running:
            logger.warning("⚠️ 监控系统已在运行")
            return
        
        try:
            # 设置监控组件
            self.setup_monitoring()
            
            # 启动监控线程
            self.is_running = True
            self.monitor_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True,
                name="SMCFreqtradeMonitor"
            )
            self.monitor_thread.start()
            
            logger.info("🚀 SMC FreqTrade 监控系统已启动")
            
            # 检查并打开FreqTrade UI
            self._open_freqtrade_ui()
            
        except Exception as e:
            logger.error(f"❌ 启动监控失败: {e}")
            self.is_running = False
            raise
    
    def _open_freqtrade_ui(self):
        """打开FreqTrade UI网页"""
        try:
            import webbrowser
            import requests
            import time
            
            # FreqTrade UI地址
            ui_url = "http://127.0.0.1:8080"
            
            # 等待API服务器启动
            logger.info("🔍 检查FreqTrade API服务器...")
            max_retries = 30
            for retry in range(max_retries):
                try:
                    response = requests.get(f"{ui_url}/api/v1/ping", timeout=3)
                    if response.status_code == 200:
                        logger.info("✅ FreqTrade API服务器已就绪")
                        break
                except:
                    if retry < max_retries - 1:
                        logger.debug(f"⏳ 等待API服务器启动 ({retry+1}/{max_retries})")
                        time.sleep(2)
                    else:
                        logger.warning("⚠️ FreqTrade API服务器未响应，但仍会尝试打开UI")
            
            # 打开浏览器
            logger.info(f"🌐 正在打开FreqTrade UI: {ui_url}")
            webbrowser.open(ui_url)
            
            # 显示UI信息
            self._show_ui_info(ui_url)
            
        except Exception as e:
            logger.error(f"❌ 打开FreqTrade UI失败: {e}")

    def _show_ui_info(self, ui_url: str):
        """显示UI信息"""
        print("\n" + "="*80)
        print("🎉 SMC FreqTrade 监控系统启动成功")
        print("="*80)
        print("🌐 FreqTrade UI 监控界面:")
        print("📈 功能说明:")
        print("   • Trade页面: 查看实时交易状态和开放订单")
        print("   • Performance页面: 查看策略性能和利润分析")
        print("   • Backtest页面: 进行策略回测")
        print("   • Logs页面: 查看详细日志")
        print("   • Config页面: 修改配置参数")
        print()
        print("🎯 SMC策略监控:")
        print("   • 智能资金概念(SMC)策略实时监控")
        print("   • 1分钟高频交易优化")
        print("   • 自动信号生成和执行跟踪")
        print("   • 风险管理和异常检测")
        print()
        print("💡 使用提示:")
        print("   • 在Trade页面可以看到当前开放的交易")
        print("   • Performance页面显示策略盈亏统计")
        print("   • 监控系统在后台持续运行")
        print("   • 按Ctrl+C停止监控系统")
        print("="*80)
        print()

    def stop_monitoring(self):
        """停止监控"""
        if not self.is_running:
            logger.warning("⚠️ 监控系统未在运行")
            return
        
        logger.info("🛑 正在停止监控系统...")
        self.is_running = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5.0)
        
        logger.info("✅ 监控系统已停止")

    def _monitoring_loop(self):
        """监控主循环"""
        logger.info("🔄 监控主循环开始")
        
        while self.is_running:
            try:
                # 模拟监控FreqTrade数据
                # 实际实现中，这里会从FreqTrade API或数据库读取实际数据
                self._check_freqtrade_status()
                
                # 等待下一次检查
                time.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                logger.error(f"❌ 监控循环中出现错误: {e}")
                time.sleep(5)
    
        logger.info("✅ 监控主循环结束")

    def _check_freqtrade_status(self):
        """检查FreqTrade状态"""
        try:
            current_time = datetime.now()
            logger.debug(f"🔍 检查FreqTrade状态: {current_time}")
            
            freqtrade_config = self.config.get("freqtrade", {})
            
            # 1. 检查FreqTrade进程是否运行
            try:
                import subprocess
                result = subprocess.run(['pgrep', '-f', 'freqtrade'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    logger.debug("✅ FreqTrade进程正在运行")
                else:
                    logger.warning("⚠️ FreqTrade进程未检测到")
            except (subprocess.TimeoutExpired, FileNotFoundError):
                # Windows系统或超时，跳过进程检查
                logger.debug("⏭️ 跳过进程检查")
            
            # 2. 检查FreqTrade配置文件
            config_path = freqtrade_config.get("config_path", "freqtrade-bot/config.json")
            if os.path.exists(config_path):
                logger.debug(f"✅ FreqTrade配置文件存在: {config_path}")
            else:
                logger.warning(f"⚠️ FreqTrade配置文件不存在: {config_path}")
            
            # 3. 检查FreqTrade数据库
            db_path = freqtrade_config.get("database_path", "freqtrade-bot/tradesv3.dryrun.sqlite")
            if os.path.exists(db_path):
                logger.debug(f"✅ FreqTrade数据库存在: {db_path}")
                # 可以在这里添加数据库查询逻辑
                self._check_freqtrade_database(db_path)
            else:
                logger.warning(f"⚠️ FreqTrade数据库不存在: {db_path}")
            
            # 4. 检查FreqTrade日志文件
            log_path = freqtrade_config.get("log_path", "freqtrade-bot/logs/freqtrade.log")
            if os.path.exists(log_path):
                logger.debug(f"✅ FreqTrade日志文件存在: {log_path}")
                # 可以在这里添加日志分析逻辑
                self._check_freqtrade_logs(log_path)
            else:
                logger.warning(f"⚠️ FreqTrade日志文件不存在: {log_path}")
            
            # 5. 检查API端点（如果可用）
            self._check_freqtrade_api()
            
        except Exception as e:
            logger.error(f"❌ 检查FreqTrade状态失败: {e}")
    
    def _check_freqtrade_database(self, db_path: str):
        """检查FreqTrade数据库"""
        try:
            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 查询开放交易数量
            cursor.execute("SELECT COUNT(*) FROM trades WHERE is_open = 1")
            open_trades = cursor.fetchone()[0]
            
            # 查询总交易数量
            cursor.execute("SELECT COUNT(*) FROM trades")
            total_trades = cursor.fetchone()[0]
            
            logger.debug(f"📊 数据库状态: 开放交易 {open_trades}, 总交易 {total_trades}")
            
            conn.close()
            
        except Exception as e:
            logger.debug(f"数据库检查失败: {e}")
    
    def _check_freqtrade_logs(self, log_path: str):
        """检查FreqTrade日志文件"""
        try:
            # 读取最后几行日志
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                if lines:
                    last_line = lines[-1].strip()
                    logger.debug(f"📝 最新日志: {last_line[:100]}...")
                    
        except Exception as e:
            logger.debug(f"日志检查失败: {e}")

    def _check_freqtrade_api(self):
        """检查FreqTrade API端点"""
        try:
            import requests
            response = requests.get("http://127.0.0.1:8080/api/v1/ping", timeout=3)
            if response.status_code == 200:
                logger.debug("✅ FreqTrade API响应正常")
            else:
                logger.debug(f"⚠️ FreqTrade API异常响应: {response.status_code}")
        except Exception as e:
            logger.debug(f"API检查失败: {e}")

    def get_monitoring_stats(self) -> Dict[str, Any]:
        """获取监控统计信息"""
        stats = {
            "is_running": self.is_running,
            "start_time": getattr(self, 'start_time', None),
            "monitors": {
                "signal_monitor": self.signal_monitor is not None,
                "capital_monitor": self.capital_monitor is not None,
                "price_monitor": self.price_monitor is not None
            },
            "alert_monitor": self.alert_monitor is not None
        }
        
        return stats


def main():
    """主函数"""
    logger.info("🚀 启动 SMC FreqTrade 监控系统")
    
    # 创建监控系统
    monitor = SMCFreqtradeMonitor()
    
    try:
        # 启动监控
        monitor.start_monitoring()
        
        # 显示状态信息
        logger.info("📊 监控系统状态:")
        stats = monitor.get_monitoring_stats()
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
        
        logger.info("💡 监控系统正在运行，按 Ctrl+C 停止")
        
        # 保持运行
        while True:
            time.sleep(1)
        
    except KeyboardInterrupt:
        logger.info("⏹️ 接收到停止信号")
    except Exception as e:
        logger.error(f"❌ 运行时出现错误: {e}")
    finally:
        # 停止监控
        monitor.stop_monitoring()
        logger.info("👋 SMC FreqTrade 监控系统已退出")


if __name__ == "__main__":
    main()