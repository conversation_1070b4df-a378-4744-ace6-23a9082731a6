#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自定义指标单元测试

测试自定义指标创建框架的各个组件和功能。
"""

# 添加项目根目录到Python路径中
import sys
import os

# 获取当前文件所在的目录
current_dir = os.path.dirname(os.path.abspath(__file__))

# 获取项目的根目录(向上一级)
project_root = os.path.abspath(os.path.join(current_dir, ".."))

# 将项目根目录添加到Python路径
if project_root not in sys.path:
    sys.path.insert(0, project_root)
    print(f"已将项目根目录添加到Python路径: {project_root}")

import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 导入要测试的模块
from indicators.base import Indicator
from indicators.trend import SMA, EMA, BollingerBands
from indicators.oscillators import RSI
from indicators.volatility import StandardDeviation, AverageTrueRange
from indicators.custom import (
    CustomIndicator,
    IndicatorBuilder,
    create_custom_indicator,
    create_operation,
    create_transform,
    create_signal_generator,
    create_from_template
)


def generate_test_data(days=100):
    """
    生成测试用的OHLCV数据
    """
    np.random.seed(42)
    today = datetime.now().date()
    dates = [today - timedelta(days=i) for i in range(days)]
    dates.reverse()
    
    close = 100 + np.cumsum(np.random.normal(0, 1, days))
    high = close + np.random.uniform(0, 3, days)
    low = close - np.random.uniform(0, 3, days)
    open_price = low + np.random.uniform(0, high - low, days)
    volume = np.random.uniform(1000, 5000, days)
    
    df = pd.DataFrame({
        'date': dates,
        'open': open_price,
        'high': high,
        'low': low,
        'close': close,
        'volume': volume
    })
    
    df['date'] = pd.to_datetime(df['date'])
    df.set_index('date', inplace=True)
    
    return df


class TestCustomIndicator(unittest.TestCase):
    """测试CustomIndicator类"""
    
    def setUp(self):
        """初始化测试环境"""
        self.data = generate_test_data()
        self.sma = SMA(window=10, column='close')
        self.ema = EMA(window=20, column='close')
    
    def test_create_custom_indicator(self):
        """测试创建自定义指标"""
        # 创建自定义指标
        custom_ind = CustomIndicator("TestIndicator")
        self.assertEqual(custom_ind.name, "TestIndicator")
        self.assertEqual(custom_ind.category, "custom")
        
        # 测试添加组件
        custom_ind.add_component(self.sma)
        custom_ind.add_component(self.ema)
        self.assertEqual(len(custom_ind.components), 2)
        self.assertEqual(custom_ind.components[0], self.sma)
        self.assertEqual(custom_ind.components[1], self.ema)
    
    def test_calculate_custom_indicator(self):
        """测试计算自定义指标"""
        # 创建并计算自定义指标
        custom_ind = CustomIndicator("TestIndicator")
        custom_ind.add_component(self.sma)
        custom_ind.add_component(self.ema)
        
        result = custom_ind.calculate(self.data)
        
        # 验证结果
        self.assertIn('SMA_10', result.columns)
        self.assertIn('EMA_20', result.columns)
        self.assertEqual(len(result), len(self.data))
        
        # 检查前10行的SMA值是否为NaN (因为窗口大小为10)
        self.assertTrue(result['SMA_10'].iloc[:9].isna().all())
        # 检查第11行开始是否有有效值
        self.assertFalse(result['SMA_10'].iloc[10:].isna().all())


class TestIndicatorBuilder(unittest.TestCase):
    """测试IndicatorBuilder类"""
    
    def setUp(self):
        """初始化测试环境"""
        self.data = generate_test_data()
        self.builder = IndicatorBuilder()
    
    def test_builder_initialization(self):
        """测试构建器初始化"""
        self.assertEqual(len(self.builder.steps), 0)
        self.assertEqual(self.builder.name, "CustomIndicator")
    
    def test_builder_add_indicator(self):
        """测试添加指标"""
        sma = SMA(window=10, column='close')
        self.builder.add(sma)
        self.assertEqual(len(self.builder.steps), 1)
        self.assertEqual(self.builder.steps[0][0], 'add')
        self.assertEqual(self.builder.steps[0][1], sma)
    
    def test_builder_build(self):
        """测试构建自定义指标"""
        sma = SMA(window=10, column='close')
        ema = EMA(window=20, column='close')
        
        # 添加指标并构建
        self.builder.add(sma).add(ema)
        custom_ind = self.builder.build()
        
        # 验证构建的指标
        self.assertEqual(custom_ind.name, "CustomIndicator")
        self.assertEqual(len(custom_ind.components), 2)
        
        # 计算并验证结果
        result = custom_ind.calculate(self.data)
        self.assertIn('SMA_10', result.columns)
        self.assertIn('EMA_20', result.columns)


class TestOperations(unittest.TestCase):
    """测试操作模块"""
    
    def setUp(self):
        """初始化测试环境"""
        self.data = generate_test_data()
        self.sma10 = SMA(window=10, column='close')
        self.sma20 = SMA(window=20, column='close')
        
        # 预计算指标
        self.result = self.data.copy()
        self.result = self.sma10.calculate(self.result)
        self.result = self.sma20.calculate(self.result)
    
    def test_add_operation(self):
        """测试加法操作"""
        add_op = create_operation('add', 'SMA_10', 'SMA_20')
        result = add_op.apply(self.result)
        
        self.assertIn('SMA_10_plus_SMA_20', result.columns)
        
        # 验证计算结果
        for i in range(len(result)):
            if pd.notna(result['SMA_10'].iloc[i]) and pd.notna(result['SMA_20'].iloc[i]):
                expected = result['SMA_10'].iloc[i] + result['SMA_20'].iloc[i]
                self.assertAlmostEqual(result['SMA_10_plus_SMA_20'].iloc[i], expected)
    
    def test_subtract_operation(self):
        """测试减法操作"""
        sub_op = create_operation('subtract', 'SMA_10', 'SMA_20')
        result = sub_op.apply(self.result)
        
        self.assertIn('SMA_10_minus_SMA_20', result.columns)
        
        # 验证计算结果
        for i in range(len(result)):
            if pd.notna(result['SMA_10'].iloc[i]) and pd.notna(result['SMA_20'].iloc[i]):
                expected = result['SMA_10'].iloc[i] - result['SMA_20'].iloc[i]
                self.assertAlmostEqual(result['SMA_10_minus_SMA_20'].iloc[i], expected)
    
    def test_compare_operation(self):
        """测试比较操作"""
        compare_op = create_operation('compare', 'SMA_10', 'SMA_20', operator='gt')
        result = compare_op.apply(self.result)
        
        self.assertIn('SMA_10_gt_SMA_20', result.columns)
        
        # 验证计算结果
        for i in range(len(result)):
            if pd.notna(result['SMA_10'].iloc[i]) and pd.notna(result['SMA_20'].iloc[i]):
                expected = int(result['SMA_10'].iloc[i] > result['SMA_20'].iloc[i])
                self.assertEqual(result['SMA_10_gt_SMA_20'].iloc[i], expected)


class TestTransforms(unittest.TestCase):
    """测试转换模块"""
    
    def setUp(self):
        """初始化测试环境"""
        self.data = generate_test_data()
    
    def test_shift_transform(self):
        """测试移位转换"""
        shift_transform = create_transform('shift', 'close', periods=1)
        result = shift_transform.apply(self.data)
        
        self.assertIn('close_shift_1', result.columns)
        
        # 验证计算结果
        for i in range(1, len(result)):
            self.assertEqual(result['close_shift_1'].iloc[i], self.data['close'].iloc[i-1])
        
        # 第一个值应该是NaN
        self.assertTrue(np.isnan(result['close_shift_1'].iloc[0]))
    
    def test_rolling_transform(self):
        """测试滚动计算转换"""
        rolling_transform = create_transform('rolling', 'close', window=5, function='mean')
        result = rolling_transform.apply(self.data)
        
        self.assertIn('close_mean_5', result.columns)
        
        # 验证前5行结果
        for i in range(4):
            self.assertTrue(np.isnan(result['close_mean_5'].iloc[i]))
        
        # 验证计算结果
        for i in range(4, len(result)):
            expected = self.data['close'].iloc[i-4:i+1].mean()
            self.assertAlmostEqual(result['close_mean_5'].iloc[i], expected)
    
    def test_pct_change_transform(self):
        """测试百分比变化转换"""
        pct_transform = create_transform('pct_change', 'close', periods=1)
        result = pct_transform.apply(self.data)
        
        self.assertIn('close_pct_1', result.columns)
        
        # 验证计算结果
        for i in range(1, len(result)):
            expected = (self.data['close'].iloc[i] - self.data['close'].iloc[i-1]) / self.data['close'].iloc[i-1]
            self.assertAlmostEqual(result['close_pct_1'].iloc[i], expected)


class TestSignals(unittest.TestCase):
    """测试信号模块"""
    
    def setUp(self):
        """初始化测试环境"""
        self.data = generate_test_data()
        
        # 计算SMA
        sma_fast = SMA(window=10, column='close')
        sma_slow = SMA(window=20, column='close')
        self.result = self.data.copy()
        self.result = sma_fast.calculate(self.result)
        self.result = sma_slow.calculate(self.result)
    
    def test_cross_signal(self):
        """测试交叉信号"""
        cross_signal = create_signal_generator('cross', 
                                            fast_col='SMA_10',
                                            slow_col='SMA_20',
                                            signal_name='cross_signal')
        result = cross_signal.generate(self.result)
        
        self.assertIn('cross_signal', result.columns)
        
        # 验证信号值只有-1、0、1
        unique_values = result['cross_signal'].unique()
        for val in unique_values:
            self.assertIn(val, [-1, 0, 1])
    
    def test_threshold_signal(self):
        """测试阈值信号"""
        # 计算RSI
        rsi = RSI(window=14, column='close')
        result = rsi.calculate(self.result)
        
        threshold_signal = create_signal_generator('threshold',
                                                column='RSI_14',
                                                upper_threshold=70.0,
                                                lower_threshold=30.0,
                                                signal_name='rsi_signal')
        result = threshold_signal.generate(result)
        
        self.assertIn('rsi_signal', result.columns)
        
        # 验证信号值
        for i in range(len(result)):
            if not np.isnan(result['RSI_14'].iloc[i]):
                if result['RSI_14'].iloc[i] > 70.0 and not np.isnan(result['RSI_14'].iloc[i-1]):
                    # 检查是否是从下方穿越上阈值
                    if result['RSI_14'].iloc[i-1] <= 70.0:
                        self.assertEqual(result['rsi_signal'].iloc[i], 1)
                elif result['RSI_14'].iloc[i] < 30.0 and not np.isnan(result['RSI_14'].iloc[i-1]):
                    # 检查是否是从上方穿越下阈值
                    if result['RSI_14'].iloc[i-1] >= 30.0:
                        self.assertEqual(result['rsi_signal'].iloc[i], -1)


class TestTemplates(unittest.TestCase):
    """测试模板模块"""
    
    def setUp(self):
        """初始化测试环境"""
        self.data = generate_test_data()
    
    def test_dual_ma_crossover_template(self):
        """测试双均线交叉模板"""
        dual_ma = create_from_template('dual_ma_crossover',
                                      fast_period=5,
                                      slow_period=20,
                                      build=True)
        result = dual_ma.calculate(self.data)
        
        # 检查生成的列
        self.assertIn('SMA_5', result.columns)
        self.assertIn('SMA_20', result.columns)
        self.assertIn('MA_crossover_signal', result.columns)
    
    def test_rsi_bollinger_template(self):
        """测试RSI布林带模板"""
        # 使用更长的测试数据，确保有足够的数据计算RSI
        data = generate_test_data(days=200)
        
        rsi_bb = create_from_template('rsi_bollinger_system',
                                     rsi_period=14,
                                     bb_period=20,
                                     bb_std=2.0,
                                     build=True)
        result = rsi_bb.calculate(data)
        
        # 检查生成的列
        self.assertIn('RSI_14', result.columns)
        self.assertIn('BB_Upper_20_2.0', result.columns)
        self.assertIn('BB_Middle_20', result.columns)
        self.assertIn('BB_Lower_20_2.0', result.columns)
        self.assertIn('RSI_signal', result.columns)
        self.assertIn('BB_signal', result.columns)


if __name__ == '__main__':
    unittest.main() 