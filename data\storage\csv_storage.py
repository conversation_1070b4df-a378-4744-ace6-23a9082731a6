"""
CSV数据存储

使用CSV文件存储市场数据，每个交易对和时间周期对应一个CSV文件。
"""

import os
import glob
import logging
from datetime import datetime
from typing import List, Dict, Optional, Any, Set

import pandas as pd

from data.storage.base import BaseDataStorage
from data.structures import OHLCVColumns
from data.utils import get_timeframe_file_path


class CSVDataStorage(BaseDataStorage):
    """
    CSV文件数据存储
    
    将市场数据存储为CSV文件，每个交易对和时间周期对应一个文件。
    CSV格式具有良好的可读性和兼容性，适合中小规模数据存储。
    """
    
    def __init__(self, data_dir: str):
        """
        初始化CSV数据存储
        
        Args:
            data_dir: 数据存储的根目录
        """
        super().__init__(data_dir)
    
    def save_data(self, data: pd.DataFrame, symbol: str, timeframe: str) -> None:
        """
        保存市场数据到CSV文件
        
        Args:
            data: 包含OHLCV数据的DataFrame
            symbol: 交易对或资产代码
            timeframe: 时间周期
        """
        if data.empty:
            self.logger.warning(f"尝试保存空数据: {symbol} {timeframe}")
            return
        
        # 确保数据索引是datetime类型
        if not isinstance(data.index, pd.DatetimeIndex):
            if OHLCVColumns.TIMESTAMP in data.columns:
                data = data.set_index(OHLCVColumns.TIMESTAMP)
            else:
                raise ValueError("数据必须包含时间戳列或以时间戳为索引")
        
        # 获取文件路径
        file_path = get_timeframe_file_path(self.data_dir, symbol, timeframe)
        
        # 保存数据
        data.to_csv(file_path)
        self.logger.info(f"数据已保存到: {file_path}, {len(data)}行")
    
    def load_data(self, symbol: str, timeframe: str, 
                 start_time: Optional[datetime] = None, 
                 end_time: Optional[datetime] = None) -> pd.DataFrame:
        """
        从CSV文件加载市场数据
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            start_time: 开始时间 (可选)
            end_time: 结束时间 (可选)
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        file_path = get_timeframe_file_path(self.data_dir, symbol, timeframe)
        
        if not os.path.exists(file_path):
            self.logger.warning(f"数据文件不存在: {file_path}")
            return pd.DataFrame()
        
        try:
            # 加载数据，将第一列解析为索引
            df = pd.read_csv(file_path, index_col=0, parse_dates=True)
            
            # 筛选时间范围
            if start_time is not None:
                df = df[df.index >= start_time]
            if end_time is not None:
                df = df[df.index <= end_time]
            
            self.logger.info(f"从{file_path}加载了{len(df)}行数据")
            return df
            
        except Exception as e:
            self.logger.error(f"加载数据失败: {file_path}, 错误: {str(e)}")
            return pd.DataFrame()
    
    def has_data(self, symbol: str, timeframe: str) -> bool:
        """
        检查是否有指定的市场数据
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            
        Returns:
            如果存在数据返回True，否则返回False
        """
        file_path = get_timeframe_file_path(self.data_dir, symbol, timeframe)
        exists = os.path.exists(file_path)
        
        # 检查文件是否为空
        if exists:
            try:
                with open(file_path, 'r') as f:
                    # 检查文件是否只有头行
                    first_line = f.readline().strip()
                    second_line = f.readline().strip()
                    return bool(second_line)  # 如果有第二行数据，则返回True
            except Exception:
                return False
        
        return False
    
    def get_timeframes(self, symbol: Optional[str] = None) -> List[str]:
        """
        获取存储中的所有时间周期
        
        Args:
            symbol: 交易对或资产代码 (可选)
            
        Returns:
            时间周期列表
        """
        timeframes = set()
        
        if symbol:
            # 获取特定交易对的时间周期
            symbol_dir = os.path.join(self.data_dir, symbol)
            if os.path.exists(symbol_dir):
                # 查找所有CSV文件，文件名即为时间周期
                csv_files = glob.glob(os.path.join(symbol_dir, "*.csv"))
                for file_path in csv_files:
                    timeframe = os.path.splitext(os.path.basename(file_path))[0]
                    timeframes.add(timeframe)
        else:
            # 获取所有交易对的所有时间周期
            for symbol in self.get_symbols():
                symbol_timeframes = self.get_timeframes(symbol)
                timeframes.update(symbol_timeframes)
        
        return sorted(list(timeframes))
    
    def get_data_info(self, symbol: str, timeframe: str) -> Dict[str, Any]:
        """
        获取数据信息
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            
        Returns:
            包含数据信息的字典，如开始时间、结束时间、数据点数量等
        """
        if not self.has_data(symbol, timeframe):
            return {
                "exists": False,
                "rows": 0,
                "start_time": None,
                "end_time": None,
                "timeframe": timeframe,
                "symbol": symbol
            }
        
        try:
            # 读取文件的第一行和最后一行，避免加载整个文件
            file_path = get_timeframe_file_path(self.data_dir, symbol, timeframe)
            
            # 获取行数
            with open(file_path, 'r') as f:
                lines = sum(1 for _ in f) - 1  # 减去标题行
            
            if lines <= 0:
                return {
                    "exists": True,
                    "rows": 0,
                    "start_time": None,
                    "end_time": None,
                    "timeframe": timeframe,
                    "symbol": symbol
                }
            
            # 读取第一行和最后一行以获取时间范围
            df_first = pd.read_csv(file_path, index_col=0, parse_dates=True, nrows=1)
            
            # 使用pandas的tail方法获取最后一行，更可靠但可能会比较慢
            df_last = pd.read_csv(file_path, index_col=0, parse_dates=True, skiprows=range(1, lines))
            
            # 获取文件大小
            file_size = os.path.getsize(file_path)
            
            return {
                "exists": True,
                "rows": lines,
                "start_time": df_first.index[0] if not df_first.empty else None,
                "end_time": df_last.index[0] if not df_last.empty else None,
                "timeframe": timeframe,
                "symbol": symbol,
                "file_size": file_size,
                "file_path": file_path
            }
            
        except Exception as e:
            self.logger.error(f"获取数据信息失败: {symbol} {timeframe}, 错误: {str(e)}")
            return {
                "exists": True,
                "rows": 0,
                "start_time": None,
                "end_time": None,
                "timeframe": timeframe,
                "symbol": symbol,
                "error": str(e)
            }
    
    def delete_data(self, symbol: str, timeframe: Optional[str] = None) -> bool:
        """
        删除指定的数据
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期 (可选，如果不指定则删除所有时间周期)
            
        Returns:
            如果删除成功返回True，否则返回False
        """
        try:
            if timeframe:
                # 删除特定时间周期的数据
                file_path = get_timeframe_file_path(self.data_dir, symbol, timeframe)
                if os.path.exists(file_path):
                    os.remove(file_path)
                    self.logger.info(f"已删除数据: {symbol} {timeframe}")
                    return True
                else:
                    self.logger.warning(f"数据不存在，无法删除: {symbol} {timeframe}")
                    return False
            else:
                # 删除交易对的所有数据
                symbol_dir = os.path.join(self.data_dir, symbol)
                if os.path.exists(symbol_dir):
                    for file_name in os.listdir(symbol_dir):
                        file_path = os.path.join(symbol_dir, file_name)
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                    os.rmdir(symbol_dir)
                    self.logger.info(f"已删除交易对所有数据: {symbol}")
                    return True
                else:
                    self.logger.warning(f"交易对目录不存在，无法删除: {symbol}")
                    return False
                
        except Exception as e:
            self.logger.error(f"删除数据失败: {symbol} {timeframe if timeframe else '所有'}, 错误: {str(e)}")
            return False 