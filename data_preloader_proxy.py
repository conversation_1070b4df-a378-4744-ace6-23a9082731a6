#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
带代理支持的数据预热脚本
使用项目自带的代理检测系统下载回测数据
"""

import os
import sys
import warnings
import requests
from datetime import datetime, timedelta
warnings.filterwarnings('ignore')

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.proxy_detector import get_proxy_detector
from data.storage.optimized_storage import OptimizedStorage
import ccxt

def preload_backtest_data_with_proxy():
    """使用代理下载回测数据"""
    
    print("=== 带代理支持的数据预热系统 ===")
    
    # 1. 检测代理配置
    print("🔍 检测代理配置...")
    proxy_detector = get_proxy_detector()
    proxy_url = proxy_detector.detect_proxy()
    
    if proxy_url:
        print(f"✅ 检测到可用代理: {proxy_url}")
    else:
        print("⚠️  未检测到代理，尝试直连...")
    
    # 2. 创建存储系统
    storage_dir = "data/storage/data"
    os.makedirs(storage_dir, exist_ok=True)
    storage = OptimizedStorage(storage_dir)
    
    # 3. 配置CCXT交易所（支持代理）
    print("\n📡 配置交易所连接...")
    
    binance_config = {
        'enableRateLimit': True,
        'timeout': 30000,
    }
    
    # 如果有代理，配置代理
    if proxy_url:
        if proxy_url.startswith('http'):
            binance_config['proxies'] = {
                'http': proxy_url,
                'https': proxy_url
            }
        elif proxy_url.startswith('socks5'):
            # CCXT原生不支持SOCKS5，需要转换为HTTP代理
            print("⚠️  CCXT不支持SOCKS5代理，尝试转换为HTTP代理...")
            # 尝试使用HTTP端口
            http_proxy = proxy_url.replace('socks5://', 'http://').replace(':10808', ':10809')
            binance_config['proxies'] = {
                'http': http_proxy,
                'https': http_proxy
            }
            print(f"🔄 转换为HTTP代理: {http_proxy}")
    
    try:
        exchange = ccxt.binance(binance_config)
        
        # 测试连接
        print("🧪 测试交易所连接...")
        ticker = exchange.fetch_ticker('BTC/USDT')
        print(f"✅ 连接成功！当前BTC价格: ${ticker['last']:,.2f}")
        
    except Exception as e:
        print(f"❌ 交易所连接失败: {e}")
        print("请检查网络和代理配置")
        return False
    
    # 4. 下载数据
    symbols = [
        "BTC/USDT",   # 比特币
        "ETH/USDT",   # 以太坊  
        "XRP/USDT",   # 瑞波币
        "ADA/USDT"    # 艾达币
    ]
    
    timeframe = '1m'
    
    # 设置时间范围 - 最近30天的数据
    end_time = datetime.now()
    start_time = end_time - timedelta(days=30)
    
    print(f"\n📊 开始下载数据...")
    print(f"时间范围: {start_time.strftime('%Y-%m-%d')} 到 {end_time.strftime('%Y-%m-%d')}")
    print(f"数据周期: {timeframe}")
    print(f"交易对数量: {len(symbols)}")
    print()
    
    # 统计信息
    total_symbols = len(symbols)
    completed_symbols = 0
    failed_symbols = []
    
    for i, symbol in enumerate(symbols, 1):
        print(f"[{i}/{total_symbols}] 下载 {symbol} 数据...")
        
        try:
            # 计算需要的数据点数（30天 * 24小时 * 60分钟）
            total_minutes = 30 * 24 * 60
            limit = 1000  # CCXT每次最多1000条
            
            all_ohlcv = []
            since = int(start_time.timestamp() * 1000)  # 转换为毫秒时间戳
            
            print(f"  📥 分批下载 {symbol} 数据...")
            
            while since < int(end_time.timestamp() * 1000):
                try:
                    # 获取OHLCV数据
                    ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since, limit)
                    
                    if not ohlcv:
                        break
                    
                    all_ohlcv.extend(ohlcv)
                    
                    # 更新since时间戳为最后一条数据的时间
                    since = ohlcv[-1][0] + 60000  # 加1分钟（毫秒）
                    
                    print(f"    ⬇ 已下载 {len(all_ohlcv)} 条数据...")
                    
                    # 避免请求过快
                    import time
                    time.sleep(0.1)
                    
                except Exception as e:
                    print(f"    ❌ 批次下载失败: {e}")
                    break
            
            if all_ohlcv:
                # 转换为DataFrame
                import pandas as pd
                
                df = pd.DataFrame(all_ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df = df.set_index('timestamp')
                
                # 保存到存储系统
                symbol_clean = symbol.replace('/', '_')
                storage.save_data(df, symbol_clean, timeframe)
                completed_symbols += 1
                
                print(f"  ✅ {symbol} 下载完成: {len(df):,} 条数据")
                print(f"  📅 时间范围: {df.index[0]} 到 {df.index[-1]}")
                
            else:
                failed_symbols.append(symbol)
                print(f"  ❌ {symbol} 下载失败 - 无数据")
                
        except Exception as e:
            failed_symbols.append(symbol)
            print(f"  ❌ {symbol} 下载出错: {e}")
        
        print()
    
    # 输出统计结果
    print("=" * 60)
    print("数据下载完成统计")
    print("=" * 60)
    print(f"目标交易对数量: {total_symbols}")
    print(f"成功下载数量: {completed_symbols}")
    print(f"失败数量: {len(failed_symbols)}")
    
    if failed_symbols:
        print(f"失败的交易对: {', '.join(failed_symbols)}")
    
    # 验证存储数据
    print("\n=== 数据验证 ===")
    total_records = 0
    
    for symbol in symbols:
        if symbol not in failed_symbols:
            symbol_clean = symbol.replace('/', '_')
            try:
                if storage.has_data(symbol_clean, timeframe):
                    data_info = storage.get_data_info(symbol_clean, timeframe)
                    rows = data_info.get('rows', 0)
                    file_size = data_info.get('file_size', 0)
                    total_records += rows
                    
                    print(f"{symbol}: {rows:,} 条记录, {file_size/1024/1024:.2f}MB")
                    
                    if data_info.get('start_time') and data_info.get('end_time'):
                        print(f"  时间范围: {data_info['start_time']} 到 {data_info['end_time']}")
                else:
                    print(f"{symbol}: 存储验证失败")
            except Exception as e:
                print(f"{symbol}: 验证出错 - {e}")
    
    print(f"\n📊 总记录数: {total_records:,}")
    
    if completed_symbols == total_symbols:
        print("\n🎉 所有数据下载完成！")
        return True
    elif completed_symbols > 0:
        print(f"\n⚠️  部分数据下载成功，成功率: {completed_symbols/total_symbols*100:.1f}%")
        return True
    else:
        print("\n❌ 所有数据下载失败")
        return False

if __name__ == "__main__":
    try:
        success = preload_backtest_data_with_proxy()
        if success:
            print("\n✅ 数据预热完成！现在可以运行回测脚本了：")
            print("   python test_storage/run_smc_backtest_extended.py")
        else:
            print("\n❌ 数据预热失败，请检查网络和代理配置")
    except Exception as e:
        print(f"\n💥 数据预热过程中出现错误: {e}")
        import traceback
        traceback.print_exc() 