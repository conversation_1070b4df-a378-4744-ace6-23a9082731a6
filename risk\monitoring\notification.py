"""
通知系统实现

提供事件通知功能，包括日志记录、邮件通知、短信通知等。
"""

import logging
import json
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import datetime
from typing import Dict, Any, List, Optional, Union, Callable, Set
from risk.monitoring.base import EventHandler, MonitoringEvent, EventSeverity


class LoggingHandler(EventHandler):
    """
    日志记录处理器
    
    将事件记录到日志系统。
    """
    
    def __init__(self, name: str = "logging_handler", description: str = "记录事件到日志系统", 
                enabled: bool = True, log_file: str = None, log_level: str = "INFO",
                formatter: str = "%(asctime)s - %(levelname)s - %(message)s"):
        """
        初始化日志记录处理器
        
        Parameters
        ----------
        name : str, optional
            处理器名称，默认为"logging_handler"
        description : str, optional
            处理器描述，默认为"记录事件到日志系统"
        enabled : bool, optional
            处理器是否启用，默认为True
        log_file : str, optional
            日志文件路径，默认为None表示输出到控制台
        log_level : str, optional
            日志级别，可选 "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"，默认为"INFO"
        formatter : str, optional
            日志格式，默认为"%(asctime)s - %(levelname)s - %(message)s"
        """
        super().__init__(name, description, enabled)
        
        # 设置日志级别
        log_level_map = {
            "DEBUG": logging.DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR,
            "CRITICAL": logging.CRITICAL
        }
        self.log_level = log_level_map.get(log_level.upper(), logging.INFO)
        
        # 创建日志器
        self.logger = logging.getLogger(f"risk_monitor.{name}")
        self.logger.setLevel(self.log_level)
        
        # 清除现有处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # 创建处理器
        if log_file:
            handler = logging.FileHandler(log_file)
        else:
            handler = logging.StreamHandler()
        
        # 设置格式
        formatter = logging.Formatter(formatter)
        handler.setFormatter(formatter)
        
        # 添加处理器
        self.logger.addHandler(handler)
    
    def handle_event(self, event: MonitoringEvent) -> bool:
        """
        处理事件并记录到日志
        
        Parameters
        ----------
        event : MonitoringEvent
            要处理的事件
        
        Returns
        -------
        bool
            处理是否成功
        """
        # 转换事件严重性到日志级别
        severity_to_level = {
            EventSeverity.INFO: logging.INFO,
            EventSeverity.WARNING: logging.WARNING,
            EventSeverity.ERROR: logging.ERROR,
            EventSeverity.CRITICAL: logging.CRITICAL
        }
        log_level = severity_to_level.get(event.severity, logging.INFO)
        
        # 格式化消息
        message = f"[{event.event_type}] {event.message}"
        
        # 记录日志
        self.logger.log(log_level, message)
        
        return True


class FileStorageHandler(EventHandler):
    """
    文件存储处理器
    
    将事件保存到JSON文件。
    """
    
    def __init__(self, name: str = "file_storage_handler", description: str = "保存事件到文件", 
                enabled: bool = True, storage_dir: str = "./logs", 
                filename_pattern: str = "events_%Y%m%d.json"):
        """
        初始化文件存储处理器
        
        Parameters
        ----------
        name : str, optional
            处理器名称，默认为"file_storage_handler"
        description : str, optional
            处理器描述，默认为"保存事件到文件"
        enabled : bool, optional
            处理器是否启用，默认为True
        storage_dir : str, optional
            存储目录路径，默认为"./logs"
        filename_pattern : str, optional
            文件名模式，支持日期格式化，默认为"events_%Y%m%d.json"
        """
        super().__init__(name, description, enabled)
        self.storage_dir = storage_dir
        self.filename_pattern = filename_pattern
        
        # 确保存储目录存在
        if not os.path.exists(storage_dir):
            os.makedirs(storage_dir)
    
    def _get_filename(self) -> str:
        """
        获取当前事件文件名
        
        Returns
        -------
        str
            文件路径
        """
        filename = datetime.datetime.now().strftime(self.filename_pattern)
        return os.path.join(self.storage_dir, filename)
    
    def handle_event(self, event: MonitoringEvent) -> bool:
        """
        处理事件并保存到文件
        
        Parameters
        ----------
        event : MonitoringEvent
            要处理的事件
        
        Returns
        -------
        bool
            处理是否成功
        """
        filename = self._get_filename()
        
        # 转换事件为字典
        event_dict = event.to_dict()
        
        # 读取现有事件（如果有）
        events = []
        if os.path.exists(filename):
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    events = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                events = []
        
        # 添加新事件
        events.append(event_dict)
        
        # 保存到文件
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(events, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            logging.error(f"保存事件到文件失败: {str(e)}")
            return False


class EmailNotificationHandler(EventHandler):
    """
    邮件通知处理器
    
    通过邮件发送事件通知。
    """
    
    def __init__(self, name: str = "email_notification_handler", 
                description: str = "发送事件邮件通知", 
                enabled: bool = True, smtp_server: str = "smtp.example.com", 
                smtp_port: int = 587, username: str = None, password: str = None,
                use_tls: bool = True, from_addr: str = None, to_addrs: List[str] = None,
                min_severity: EventSeverity = EventSeverity.WARNING):
        """
        初始化邮件通知处理器
        
        Parameters
        ----------
        name : str, optional
            处理器名称，默认为"email_notification_handler"
        description : str, optional
            处理器描述，默认为"发送事件邮件通知"
        enabled : bool, optional
            处理器是否启用，默认为True
        smtp_server : str, optional
            SMTP服务器地址，默认为"smtp.example.com"
        smtp_port : int, optional
            SMTP服务器端口，默认为587
        username : str, optional
            SMTP认证用户名，默认为None
        password : str, optional
            SMTP认证密码，默认为None
        use_tls : bool, optional
            是否使用TLS加密，默认为True
        from_addr : str, optional
            发件人地址，默认为None
        to_addrs : List[str], optional
            收件人地址列表，默认为None
        min_severity : EventSeverity, optional
            最小通知严重性，默认为WARNING
        """
        super().__init__(name, description, enabled)
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.use_tls = use_tls
        self.from_addr = from_addr or username
        self.to_addrs = to_addrs or []
        self.min_severity = min_severity
        
        # 验证配置
        if not self.from_addr:
            raise ValueError("必须提供发件人地址")
        
        if not self.to_addrs:
            raise ValueError("必须提供至少一个收件人地址")
    
    def handle_event(self, event: MonitoringEvent) -> bool:
        """
        处理事件并发送邮件通知
        
        Parameters
        ----------
        event : MonitoringEvent
            要处理的事件
        
        Returns
        -------
        bool
            处理是否成功
        """
        # 检查事件严重性是否达到通知级别
        severity_order = {
            EventSeverity.INFO: 0,
            EventSeverity.WARNING: 1,
            EventSeverity.ERROR: 2,
            EventSeverity.CRITICAL: 3
        }
        
        if severity_order[event.severity] < severity_order[self.min_severity]:
            return True  # 不需要通知，也视为处理成功
        
        # 创建消息
        msg = MIMEMultipart()
        msg['From'] = self.from_addr
        msg['To'] = ', '.join(self.to_addrs)
        msg['Subject'] = f"风险监控事件 [{event.severity.value}]: {event.event_type}"
        
        # 构建邮件正文
        body = f"""
        <html>
        <body>
            <h2>风险监控事件通知</h2>
            <p><strong>时间:</strong> {event.timestamp.strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>类型:</strong> {event.event_type}</p>
            <p><strong>严重性:</strong> {event.severity.value}</p>
            <p><strong>消息:</strong> {event.message}</p>
            
            <h3>相关数据:</h3>
            <pre>{json.dumps(event.related_data, indent=2, ensure_ascii=False)}</pre>
        </body>
        </html>
        """
        
        msg.attach(MIMEText(body, 'html'))
        
        # 发送邮件
        try:
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            if self.use_tls:
                server.starttls()
            
            if self.username and self.password:
                server.login(self.username, self.password)
            
            server.send_message(msg)
            server.quit()
            return True
        except Exception as e:
            logging.error(f"发送邮件通知失败: {str(e)}")
            return False


class WebhookNotificationHandler(EventHandler):
    """
    Webhook通知处理器
    
    通过HTTP POST请求发送事件通知到指定URL。
    """
    
    def __init__(self, name: str = "webhook_notification_handler", 
                description: str = "发送事件Webhook通知", 
                enabled: bool = True, webhook_url: str = None, 
                headers: Dict[str, str] = None, timeout: int = 10,
                min_severity: EventSeverity = EventSeverity.WARNING):
        """
        初始化Webhook通知处理器
        
        Parameters
        ----------
        name : str, optional
            处理器名称，默认为"webhook_notification_handler"
        description : str, optional
            处理器描述，默认为"发送事件Webhook通知"
        enabled : bool, optional
            处理器是否启用，默认为True
        webhook_url : str, optional
            Webhook URL，默认为None
        headers : Dict[str, str], optional
            请求头，默认为None
        timeout : int, optional
            请求超时时间（秒），默认为10
        min_severity : EventSeverity, optional
            最小通知严重性，默认为WARNING
        """
        super().__init__(name, description, enabled)
        self.webhook_url = webhook_url
        self.headers = headers or {'Content-Type': 'application/json'}
        self.timeout = timeout
        self.min_severity = min_severity
        
        # 验证配置
        if not self.webhook_url:
            raise ValueError("必须提供webhook_url")
    
    def handle_event(self, event: MonitoringEvent) -> bool:
        """
        处理事件并发送Webhook通知
        
        Parameters
        ----------
        event : MonitoringEvent
            要处理的事件
        
        Returns
        -------
        bool
            处理是否成功
        """
        # 检查事件严重性是否达到通知级别
        severity_order = {
            EventSeverity.INFO: 0,
            EventSeverity.WARNING: 1,
            EventSeverity.ERROR: 2,
            EventSeverity.CRITICAL: 3
        }
        
        if severity_order[event.severity] < severity_order[self.min_severity]:
            return True  # 不需要通知，也视为处理成功
        
        # 准备请求数据
        payload = event.to_dict()
        
        try:
            # 使用requests库发送POST请求
            # 注意：此处为了避免引入额外依赖，实际实现需要import requests
            import requests
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers=self.headers,
                timeout=self.timeout
            )
            
            # 检查响应状态
            if response.status_code >= 200 and response.status_code < 300:
                return True
            else:
                logging.error(f"Webhook通知失败, 状态码: {response.status_code}, 响应: {response.text}")
                return False
        except ImportError:
            logging.error("未安装requests库，无法发送Webhook通知")
            return False
        except Exception as e:
            logging.error(f"发送Webhook通知失败: {str(e)}")
            return False


class StrategyActionHandler(EventHandler):
    """
    策略动作处理器
    
    根据事件触发策略动作，如减仓、暂停交易等。
    """
    
    def __init__(self, name: str = "strategy_action_handler", 
                description: str = "根据事件触发策略动作", 
                enabled: bool = True, 
                action_callbacks: Dict[str, Callable[[MonitoringEvent], bool]] = None,
                min_severity: EventSeverity = EventSeverity.ERROR):
        """
        初始化策略动作处理器
        
        Parameters
        ----------
        name : str, optional
            处理器名称，默认为"strategy_action_handler"
        description : str, optional
            处理器描述，默认为"根据事件触发策略动作"
        enabled : bool, optional
            处理器是否启用，默认为True
        action_callbacks : Dict[str, Callable[[MonitoringEvent], bool]], optional
            动作回调函数字典，键为事件类型，值为处理函数，默认为None
        min_severity : EventSeverity, optional
            最小触发严重性，默认为ERROR
        """
        super().__init__(name, description, enabled)
        self.action_callbacks = action_callbacks or {}
        self.min_severity = min_severity
        
        # 默认动作函数
        if not self.action_callbacks:
            self._register_default_actions()
    
    def _register_default_actions(self) -> None:
        """注册默认动作处理函数"""
        self.action_callbacks = {
            "excessive_signals": self._handle_excessive_signals,
            "capital_daily_drawdown": self._handle_large_drawdown,
            "rapid_price_change": self._handle_price_volatility
        }
    
    def _handle_excessive_signals(self, event: MonitoringEvent) -> bool:
        """
        处理过多信号事件
        
        Parameters
        ----------
        event : MonitoringEvent
            事件对象
        
        Returns
        -------
        bool
            处理是否成功
        """
        logging.warning(f"检测到过多交易信号，建议暂停自动交易 ({event.message})")
        # 这里可以添加实际的交易暂停逻辑
        return True
    
    def _handle_large_drawdown(self, event: MonitoringEvent) -> bool:
        """
        处理大幅回撤事件
        
        Parameters
        ----------
        event : MonitoringEvent
            事件对象
        
        Returns
        -------
        bool
            处理是否成功
        """
        logging.warning(f"检测到大幅资金回撤，建议减仓 ({event.message})")
        # 这里可以添加实际的减仓逻辑
        return True
    
    def _handle_price_volatility(self, event: MonitoringEvent) -> bool:
        """
        处理价格波动事件
        
        Parameters
        ----------
        event : MonitoringEvent
            事件对象
        
        Returns
        -------
        bool
            处理是否成功
        """
        logging.warning(f"检测到价格异常波动，建议调整止损位 ({event.message})")
        # 这里可以添加实际的止损调整逻辑
        return True
    
    def register_action(self, event_type: str, callback: Callable[[MonitoringEvent], bool]) -> None:
        """
        注册动作处理函数
        
        Parameters
        ----------
        event_type : str
            事件类型
        callback : Callable[[MonitoringEvent], bool]
            处理函数，接受事件对象，返回处理是否成功
        """
        self.action_callbacks[event_type] = callback
    
    def handle_event(self, event: MonitoringEvent) -> bool:
        """
        处理事件并触发相应动作
        
        Parameters
        ----------
        event : MonitoringEvent
            要处理的事件
        
        Returns
        -------
        bool
            处理是否成功
        """
        # 检查事件严重性是否达到触发级别
        severity_order = {
            EventSeverity.INFO: 0,
            EventSeverity.WARNING: 1,
            EventSeverity.ERROR: 2,
            EventSeverity.CRITICAL: 3
        }
        
        if severity_order[event.severity] < severity_order[self.min_severity]:
            return True  # 不需要触发动作，也视为处理成功
        
        # 查找并执行相应的动作处理函数
        callback = self.action_callbacks.get(event.event_type)
        if callback:
            try:
                return callback(event)
            except Exception as e:
                logging.error(f"执行动作处理函数失败: {str(e)}")
                return False
        
        return True  # 没有对应的处理函数，视为处理成功 