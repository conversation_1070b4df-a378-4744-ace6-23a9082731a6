"""
风险仪表盘模块

提供风险数据可视化和交互式仪表盘功能。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any, List, Optional, Union, Callable, Set, Tuple
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import json
import os
from IPython.display import display, HTML


class MetricsPanel:
    """
    风险指标面板
    
    展示关键风险指标和统计数据。
    """
    
    def __init__(self, title: str = "Risk Metrics"):
        """
        初始化风险指标面板
        
        Parameters
        ----------
        title : str, optional
            面板标题，默认为"Risk Metrics"
        """
        self.title = title
        self.metrics = {}
        self.categories = {}
        self.thresholds = {}
    
    def add_metric(self, name: str, value: float, formatter: Callable[[float], str] = None,
                  category: str = "General", threshold: Dict[str, float] = None,
                  description: str = None):
        """
        添加风险指标
        
        Parameters
        ----------
        name : str
            指标名称
        value : float
            指标值
        formatter : Callable[[float], str], optional
            值格式化函数，默认为None
        category : str, optional
            指标类别，默认为"General"
        threshold : Dict[str, float], optional
            指标阈值，如{'warning': 10, 'danger': 20}，默认为None
        description : str, optional
            指标描述，默认为None
        """
        if formatter is None:
            formatter = lambda x: f"{x:.4f}"
        
        self.metrics[name] = {
            'value': value,
            'formatted_value': formatter(value),
            'category': category,
            'description': description
        }
        
        if category not in self.categories:
            self.categories[category] = []
        self.categories[category].append(name)
        
        if threshold:
            self.thresholds[name] = threshold
    
    def get_status_class(self, metric_name: str) -> str:
        """
        获取指标状态CSS类
        
        Parameters
        ----------
        metric_name : str
            指标名称
        
        Returns
        -------
        str
            状态CSS类
        """
        if metric_name not in self.metrics or metric_name not in self.thresholds:
            return "normal"
        
        value = self.metrics[metric_name]['value']
        threshold = self.thresholds[metric_name]
        
        if 'danger' in threshold and value >= threshold['danger']:
            return "danger"
        elif 'warning' in threshold and value >= threshold['warning']:
            return "warning"
        else:
            return "normal"
    
    def to_html(self) -> str:
        """
        生成HTML表示
        
        Returns
        -------
        str
            HTML内容
        """
        html = f"""
        <div class="metrics-panel">
            <style>
                .metrics-panel {{
                    font-family: Arial, sans-serif;
                    max-width: 1200px;
                    margin: 0 auto;
                }}
                .metrics-panel h2 {{
                    color: #2c3e50;
                    border-bottom: 1px solid #ecf0f1;
                    padding-bottom: 10px;
                }}
                .metrics-category {{
                    margin-bottom: 20px;
                }}
                .metrics-category h3 {{
                    color: #34495e;
                    font-size: 1.2em;
                    margin-bottom: 10px;
                }}
                .metrics-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                    gap: 15px;
                }}
                .metric-card {{
                    padding: 15px;
                    border-radius: 5px;
                    background-color: #f8f9fa;
                    border: 1px solid #e9ecef;
                }}
                .metric-name {{
                    font-weight: bold;
                    margin-bottom: 5px;
                }}
                .metric-value {{
                    font-size: 1.5em;
                    margin-bottom: 5px;
                }}
                .metric-description {{
                    font-size: 0.85em;
                    color: #6c757d;
                }}
                .status-normal {{
                    border-left: 4px solid #28a745;
                }}
                .status-warning {{
                    border-left: 4px solid #ffc107;
                }}
                .status-danger {{
                    border-left: 4px solid #dc3545;
                }}
            </style>
            <h2>{self.title}</h2>
        """
        
        for category, metric_names in self.categories.items():
            html += f"""
            <div class="metrics-category">
                <h3>{category}</h3>
                <div class="metrics-grid">
            """
            
            for name in metric_names:
                metric = self.metrics[name]
                status_class = self.get_status_class(name)
                description = metric.get('description', '')
                
                html += f"""
                <div class="metric-card status-{status_class}">
                    <div class="metric-name">{name}</div>
                    <div class="metric-value">{metric['formatted_value']}</div>
                    {f'<div class="metric-description">{description}</div>' if description else ''}
                </div>
                """
            
            html += """
                </div>
            </div>
            """
        
        html += "</div>"
        return html
    
    def display(self):
        """在Jupyter Notebook中显示面板"""
        display(HTML(self.to_html()))


class StrategyRiskPanel:
    """
    策略风险面板
    
    可视化展示策略风险指标和评估结果。
    """
    
    def __init__(self, title: str = "Strategy Risk Profile"):
        """
        初始化策略风险面板
        
        Parameters
        ----------
        title : str, optional
            面板标题，默认为"Strategy Risk Profile"
        """
        self.title = title
        self.risk_data = {}
        self.time_series_data = {}
        self.recommendations = []
    
    def set_risk_score(self, score: float, level: str) -> None:
        """
        设置风险评分
        
        Parameters
        ----------
        score : float
            风险评分(0-100)
        level : str
            风险等级
        """
        self.risk_data['score'] = score
        self.risk_data['level'] = level
    
    def add_metric(self, name: str, value: float, normalized_score: float = None) -> None:
        """
        添加风险指标
        
        Parameters
        ----------
        name : str
            指标名称
        value : float
            原始指标值
        normalized_score : float, optional
            标准化分数(0-100)，默认为None
        """
        if 'metrics' not in self.risk_data:
            self.risk_data['metrics'] = {}
        
        self.risk_data['metrics'][name] = {
            'value': value,
            'normalized_score': normalized_score
        }
    
    def add_time_series(self, name: str, data: pd.Series, color: str = None) -> None:
        """
        添加时间序列数据
        
        Parameters
        ----------
        name : str
            序列名称
        data : pd.Series
            时间序列数据
        color : str, optional
            线条颜色，默认为None表示自动选择
        """
        self.time_series_data[name] = {
            'data': data,
            'color': color
        }
    
    def add_recommendation(self, text: str) -> None:
        """
        添加风险缓解建议
        
        Parameters
        ----------
        text : str
            建议内容
        """
        self.recommendations.append(text)
    
    def plot_risk_radar(self, figsize: Tuple[int, int] = (8, 8)) -> plt.Figure:
        """
        绘制风险雷达图
        
        Parameters
        ----------
        figsize : Tuple[int, int], optional
            图表大小，默认为(8, 8)
        
        Returns
        -------
        plt.Figure
            图表对象
        """
        if 'metrics' not in self.risk_data or not self.risk_data['metrics']:
            return None
        
        # 提取指标和分数
        metrics = []
        scores = []
        
        for name, data in self.risk_data['metrics'].items():
            if 'normalized_score' in data and data['normalized_score'] is not None:
                metrics.append(name)
                scores.append(data['normalized_score'])
        
        if not metrics:
            return None
        
        # 创建雷达图
        fig = plt.figure(figsize=figsize)
        ax = fig.add_subplot(111, polar=True)
        
        # 计算角度
        angles = np.linspace(0, 2*np.pi, len(metrics), endpoint=False).tolist()
        scores.append(scores[0])
        angles.append(angles[0])
        metrics.append(metrics[0])
        
        # 绘制雷达图
        ax.plot(angles, scores, 'o-', linewidth=2, label='Risk Score')
        ax.fill(angles, scores, alpha=0.25)
        
        # 设置刻度和标签
        ax.set_thetagrids(np.degrees(angles[:-1]), metrics[:-1])
        ax.set_ylim(0, 100)
        ax.set_yticks([20, 40, 60, 80])
        ax.set_yticklabels(['20', '40', '60', '80'])
        ax.set_rlabel_position(0)
        
        plt.title(f"Risk Metrics Radar Chart\nOverall Risk Score: {self.risk_data.get('score', 'N/A')}", 
                 fontsize=12, pad=20)
        
        return fig
    
    def plot_time_series(self, figsize: Tuple[int, int] = (12, 6)) -> plt.Figure:
        """
        绘制时间序列图
        
        Parameters
        ----------
        figsize : Tuple[int, int], optional
            图表大小，默认为(12, 6)
        
        Returns
        -------
        plt.Figure
            图表对象
        """
        if not self.time_series_data:
            return None
        
        fig, ax = plt.subplots(figsize=figsize)
        
        for name, data in self.time_series_data.items():
            series = data['data']
            color = data['color']
            ax.plot(series.index, series.values, label=name, color=color)
        
        ax.set_xlabel('Date')
        ax.set_ylabel('Value')
        ax.set_title('Risk Metrics Time Series')
        ax.grid(True, linestyle='--', alpha=0.7)
        ax.legend()
        
        plt.tight_layout()
        return fig
    
    def create_plotly_dashboard(self) -> go.Figure:
        """
        创建Plotly交互式仪表盘
        
        Returns
        -------
        go.Figure
            Plotly图表对象
        """
        # 创建子图布局
        fig = make_subplots(
            rows=2, cols=2,
            specs=[
                [{"type": "polar"}, {"type": "indicator"}],
                [{"type": "scatter", "colspan": 2}, None]
            ],
            subplot_titles=["Risk Metrics Radar", "Overall Risk Score", "Risk Metrics Over Time"]
        )
        
        # 添加雷达图
        if 'metrics' in self.risk_data and self.risk_data['metrics']:
            metrics = []
            scores = []
            
            for name, data in self.risk_data['metrics'].items():
                if 'normalized_score' in data and data['normalized_score'] is not None:
                    metrics.append(name)
                    scores.append(data['normalized_score'])
            
            if metrics:
                fig.add_trace(
                    go.Scatterpolar(
                        r=scores,
                        theta=metrics,
                        fill='toself',
                        name='Risk Metrics'
                    ),
                    row=1, col=1
                )
        
        # 添加风险评分指示器
        risk_score = self.risk_data.get('score', 50)
        
        # 确定颜色
        if risk_score < 25:
            color = "green"
        elif risk_score < 50:
            color = "yellow"
        elif risk_score < 75:
            color = "orange"
        else:
            color = "red"
        
        fig.add_trace(
            go.Indicator(
                mode="gauge+number",
                value=risk_score,
                title={'text': "Risk Score"},
                gauge={
                    'axis': {'range': [0, 100]},
                    'bar': {'color': color},
                    'steps': [
                        {'range': [0, 25], 'color': 'rgba(0, 255, 0, 0.1)'},
                        {'range': [25, 50], 'color': 'rgba(255, 255, 0, 0.1)'},
                        {'range': [50, 75], 'color': 'rgba(255, 165, 0, 0.1)'},
                        {'range': [75, 100], 'color': 'rgba(255, 0, 0, 0.1)'}
                    ]
                }
            ),
            row=1, col=2
        )
        
        # 添加时间序列图
        if self.time_series_data:
            for name, data in self.time_series_data.items():
                series = data['data']
                fig.add_trace(
                    go.Scatter(
                        x=series.index,
                        y=series.values,
                        mode='lines',
                        name=name
                    ),
                    row=2, col=1
                )
        
        # 设置布局
        fig.update_layout(
            title_text="Strategy Risk Dashboard",
            height=800,
            showlegend=True
        )
        
        return fig
    
    def to_html(self) -> str:
        """
        生成HTML表示
        
        Returns
        -------
        str
            HTML内容
        """
        # 风险评分颜色
        risk_score = self.risk_data.get('score', 50)
        if risk_score < 25:
            score_class = "low-risk"
        elif risk_score < 50:
            score_class = "medium-risk"
        elif risk_score < 75:
            score_class = "high-risk"
        else:
            score_class = "critical-risk"
        
        html = f"""
        <div class="strategy-risk-panel">
            <style>
                .strategy-risk-panel {{
                    font-family: Arial, sans-serif;
                    max-width: 1200px;
                    margin: 0 auto;
                }}
                .strategy-risk-panel h2 {{
                    color: #2c3e50;
                    border-bottom: 1px solid #ecf0f1;
                    padding-bottom: 10px;
                }}
                .risk-score-section {{
                    display: flex;
                    align-items: center;
                    margin-bottom: 20px;
                }}
                .risk-score {{
                    font-size: 3em;
                    font-weight: bold;
                    padding: 15px 25px;
                    border-radius: 5px;
                    margin-right: 20px;
                }}
                .low-risk {{
                    background-color: #d5f5e3;
                    color: #27ae60;
                }}
                .medium-risk {{
                    background-color: #fcf3cf;
                    color: #f39c12;
                }}
                .high-risk {{
                    background-color: #f5b7b1;
                    color: #e74c3c;
                }}
                .critical-risk {{
                    background-color: #f2d7d5;
                    color: #922b21;
                }}
                .risk-level {{
                    font-size: 1.5em;
                }}
                .metrics-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                    gap: 15px;
                    margin-bottom: 20px;
                }}
                .metric-card {{
                    padding: 15px;
                    border-radius: 5px;
                    background-color: #f8f9fa;
                    border: 1px solid #e9ecef;
                }}
                .metric-name {{
                    font-weight: bold;
                    margin-bottom: 5px;
                }}
                .metric-value {{
                    font-size: 1.5em;
                    margin-bottom: 5px;
                }}
                .metric-score {{
                    height: 5px;
                    background-color: #e9ecef;
                    border-radius: 2px;
                    margin-top: 5px;
                }}
                .metric-score-fill {{
                    height: 100%;
                    border-radius: 2px;
                }}
                .recommendations {{
                    background-color: #eaf2f8;
                    border-left: 4px solid #3498db;
                    padding: 15px;
                    border-radius: 3px;
                }}
                .recommendations h3 {{
                    margin-top: 0;
                    color: #3498db;
                }}
                .recommendations ul {{
                    margin-bottom: 0;
                }}
            </style>
            <h2>{self.title}</h2>
            
            <div class="risk-score-section">
                <div class="risk-score {score_class}">{risk_score:.1f}</div>
                <div class="risk-level">Risk Level: {self.risk_data.get('level', 'Unknown')}</div>
            </div>
        """
        
        # 指标部分
        if 'metrics' in self.risk_data and self.risk_data['metrics']:
            html += '<div class="metrics-grid">'
            
            for name, data in self.risk_data['metrics'].items():
                value = data['value']
                score = data.get('normalized_score', 0)
                
                # 计算颜色
                if score < 25:
                    color = "#27ae60"  # 绿色
                elif score < 50:
                    color = "#f39c12"  # 黄色
                elif score < 75:
                    color = "#e74c3c"  # 橙色
                else:
                    color = "#922b21"  # 红色
                
                html += f"""
                <div class="metric-card">
                    <div class="metric-name">{name}</div>
                    <div class="metric-value">{value:.4f}</div>
                    <div class="metric-score">
                        <div class="metric-score-fill" style="width: {score}%; background-color: {color};"></div>
                    </div>
                </div>
                """
            
            html += '</div>'
        
        # 建议部分
        if self.recommendations:
            html += """
            <div class="recommendations">
                <h3>Risk Mitigation Recommendations</h3>
                <ul>
            """
            
            for rec in self.recommendations:
                html += f"<li>{rec}</li>"
            
            html += """
                </ul>
            </div>
            """
        
        html += "</div>"
        return html
    
    def display(self):
        """在Jupyter Notebook中显示面板"""
        display(HTML(self.to_html()))


class RiskDashboard:
    """
    风险仪表盘
    
    集成多个风险可视化面板的主仪表盘。
    """
    
    def __init__(self, title: str = "Risk Management Dashboard"):
        """
        初始化风险仪表盘
        
        Parameters
        ----------
        title : str, optional
            仪表盘标题，默认为"Risk Management Dashboard"
        """
        self.title = title
        self.metrics_panel = MetricsPanel()
        self.strategy_risk_panel = StrategyRiskPanel()
        self.last_update = datetime.now()
    
    def add_metric(self, name: str, value: float, formatter: Callable[[float], str] = None,
                  category: str = "General", threshold: Dict[str, float] = None,
                  description: str = None) -> None:
        """
        添加风险指标
        
        Parameters
        ----------
        name : str
            指标名称
        value : float
            指标值
        formatter : Callable[[float], str], optional
            值格式化函数，默认为None
        category : str, optional
            指标类别，默认为"General"
        threshold : Dict[str, float], optional
            指标阈值，如{'warning': 10, 'danger': 20}，默认为None
        description : str, optional
            指标描述，默认为None
        """
        self.metrics_panel.add_metric(
            name=name,
            value=value,
            formatter=formatter,
            category=category,
            threshold=threshold,
            description=description
        )
    
    def set_risk_score(self, score: float, level: str) -> None:
        """
        设置风险评分
        
        Parameters
        ----------
        score : float
            风险评分(0-100)
        level : str
            风险等级
        """
        self.strategy_risk_panel.set_risk_score(score, level)
    
    def add_risk_metric(self, name: str, value: float, normalized_score: float = None) -> None:
        """
        添加风险指标到策略风险面板
        
        Parameters
        ----------
        name : str
            指标名称
        value : float
            原始指标值
        normalized_score : float, optional
            标准化分数(0-100)，默认为None
        """
        self.strategy_risk_panel.add_metric(name, value, normalized_score)
    
    def add_time_series(self, name: str, data: pd.Series, color: str = None) -> None:
        """
        添加时间序列数据
        
        Parameters
        ----------
        name : str
            序列名称
        data : pd.Series
            时间序列数据
        color : str, optional
            线条颜色，默认为None表示自动选择
        """
        self.strategy_risk_panel.add_time_series(name, data, color)
    
    def add_recommendation(self, text: str) -> None:
        """
        添加风险缓解建议
        
        Parameters
        ----------
        text : str
            建议内容
        """
        self.strategy_risk_panel.add_recommendation(text)
    
    def update_timestamp(self) -> None:
        """更新仪表盘时间戳"""
        self.last_update = datetime.now()
    
    def to_html(self) -> str:
        """
        生成HTML表示
        
        Returns
        -------
        str
            HTML内容
        """
        html = f"""
        <div class="risk-dashboard">
            <style>
                .risk-dashboard {{
                    font-family: Arial, sans-serif;
                    max-width: 1200px;
                    margin: 0 auto;
                }}
                .dashboard-header {{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                    border-bottom: 2px solid #3498db;
                    padding-bottom: 10px;
                }}
                .dashboard-title {{
                    font-size: 1.8em;
                    color: #2c3e50;
                }}
                .dashboard-timestamp {{
                    color: #7f8c8d;
                }}
                .dashboard-section {{
                    margin-bottom: 30px;
                }}
            </style>
            
            <div class="dashboard-header">
                <div class="dashboard-title">{self.title}</div>
                <div class="dashboard-timestamp">Last updated: {self.last_update.strftime('%Y-%m-%d %H:%M:%S')}</div>
            </div>
            
            <div class="dashboard-section">
                {self.strategy_risk_panel.to_html()}
            </div>
            
            <div class="dashboard-section">
                {self.metrics_panel.to_html()}
            </div>
        </div>
        """
        return html
    
    def display(self):
        """在Jupyter Notebook中显示仪表盘"""
        self.update_timestamp()
        display(HTML(self.to_html()))
    
    def generate_plotly_dashboard(self) -> go.Figure:
        """
        生成Plotly交互式仪表盘
        
        Returns
        -------
        go.Figure
            Plotly图表对象
        """
        return self.strategy_risk_panel.create_plotly_dashboard()
    
    def save_html(self, filename: str) -> None:
        """
        保存仪表盘为HTML文件
        
        Parameters
        ----------
        filename : str
            输出文件名
        """
        self.update_timestamp()
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Risk Management Dashboard</title>
            </head>
            <body>
            """)
            f.write(self.to_html())
            f.write("""
            </body>
            </html>
            """)
    
    def load_from_risk_report(self, report_dict: Dict[str, Any]) -> None:
        """
        从风险报告字典加载数据
        
        Parameters
        ----------
        report_dict : Dict[str, Any]
            风险报告字典
        """
        # 设置风险评分
        if 'risk_score' in report_dict and 'risk_level' in report_dict:
            self.set_risk_score(report_dict['risk_score'], report_dict['risk_level'])
        
        # 加载指标
        if 'metrics' in report_dict:
            for name, data in report_dict['metrics'].items():
                value = data.get('value')
                score = data.get('normalized_score')
                
                if value is not None:
                    # 添加到策略风险面板
                    self.add_risk_metric(name, value, score)
                    
                    # 添加到指标面板
                    threshold = {}
                    if score is not None:
                        threshold = {'warning': 50, 'danger': 75}
                    
                    self.add_metric(
                        name=name,
                        value=value,
                        threshold=threshold
                    )
        
        # 加载建议
        if 'recommendations' in report_dict:
            for rec in report_dict['recommendations']:
                self.add_recommendation(rec)
        
        self.update_timestamp() 