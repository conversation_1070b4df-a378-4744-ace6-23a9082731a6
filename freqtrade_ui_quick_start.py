 #!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
FreqUI快速启动脚本

一键启动基于Freqtrade官方UI的监控系统
"""

import sys
import webbrowser
import subprocess
import time
import json
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def check_freqtrade_config(config_path: str = "freqtrade-bot/config.json") -> bool:
    """检查Freqtrade配置"""
    config_file = Path(config_path)
    
    if not config_file.exists():
        logger.error(f"配置文件不存在: {config_path}")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        api_config = config.get('api_server', {})
        if not api_config.get('enabled', False):
            logger.error("API服务器未启用")
            return False
        
        required_fields = ['username', 'password']
        for field in required_fields:
            if not api_config.get(field):
                logger.error(f"缺少配置: api_server.{field}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"配置检查失败: {e}")
        return False


def get_freqtrade_ui_url(config_path: str = "freqtrade-bot/config.json") -> str:
    """获取FreqUI访问URL"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        api_config = config.get('api_server', {})
        host = api_config.get('listen_ip_address', '127.0.0.1')
        port = api_config.get('listen_port', 8080)
        
        return f"http://{host}:{port}"
    except Exception:
        return "http://127.0.0.1:8080"


def check_freqtrade_running(ui_url: str) -> bool:
    """检查Freqtrade是否正在运行"""
    import requests
    
    try:
        response = requests.get(f"{ui_url}/api/v1/ping", timeout=3)
        return response.status_code == 200 and response.json().get('status') == 'pong'
    except Exception:
        return False


def start_freqtrade_if_needed(config_path: str) -> bool:
    """如果需要，启动Freqtrade"""
    print("🚀 启动Freqtrade...")
    
    # 检查freqtrade命令是否可用
    try:
        result = subprocess.run(['freqtrade', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode != 0:
            logger.error("freqtrade命令不可用，请安装freqtrade")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        logger.error("freqtrade命令不可用，请安装freqtrade")
        return False
    
    # 启动freqtrade
    cmd = ['freqtrade', 'trade', '--config', config_path]
    
    try:
        # 在后台启动freqtrade
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待几秒钟让服务启动
        print("⏳ 等待Freqtrade启动...")
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ Freqtrade已在后台启动")
            return True
        else:
            logger.error("Freqtrade启动失败")
            return False
            
    except Exception as e:
        logger.error(f"启动Freqtrade失败: {e}")
        return False


def main():
    """主函数"""
    print("🎯 FreqUI快速启动")
    print("基于Freqtrade官方UI的专业监控系统")
    print("=" * 50)
    
    config_path = "freqtrade-bot/config.json"
    
    # 1. 检查配置
    print("📋 检查配置...")
    if not check_freqtrade_config(config_path):
        print("❌ 配置检查失败")
        print("💡 运行验证脚本: python validate_freqtrade_ui_setup.py")
        return 1
    
    print("✅ 配置检查通过")
    
    # 2. 获取UI URL
    ui_url = get_freqtrade_ui_url(config_path)
    print(f"🌐 FreqUI地址: {ui_url}")
    
    # 3. 检查Freqtrade是否运行
    print("🔍 检查Freqtrade状态...")
    if not check_freqtrade_running(ui_url):
        print("⚠️ Freqtrade未运行，尝试启动...")
        
        # 询问用户是否要自动启动
        try:
            response = input("是否自动启动Freqtrade? (y/n): ").lower().strip()
        except KeyboardInterrupt:
            print("\n❌ 用户取消")
            return 1
        
        if response == 'y':
            if not start_freqtrade_if_needed(config_path):
                print("❌ 无法启动Freqtrade")
                print("💡 请手动启动: freqtrade trade --config freqtrade-bot/config.json")
                return 1
        else:
            print("💡 请手动启动Freqtrade后重新运行")
            return 1
    
    print("✅ Freqtrade运行正常")
    
    # 4. 打开FreqUI
    print("🌐 正在打开FreqUI...")
    try:
        webbrowser.open(ui_url)
        print(f"✅ FreqUI已打开: {ui_url}")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print(f"💡 请手动访问: {ui_url}")
    
    # 5. 显示使用说明
    print("\n" + "=" * 50)
    print("🎉 FreqUI监控系统已启动")
    print("=" * 50)
    print("📖 使用说明:")
    print(f"1. 访问地址: {ui_url}")
    print("2. 使用配置文件中的用户名密码登录")
    print("3. 在Trade页面查看实时交易数据")
    print("4. 在Backtesting页面进行策略回测")
    print("5. 使用Plot Configurator配置图表")
    print()
    print("🔧 高级功能:")
    print("- 实时WebSocket数据更新")
    print("- 专业的交易图表和指标")
    print("- 完整的交易历史和统计")
    print("- 策略性能分析")
    print()
    print("🛠️ 额外监控工具:")
    print("- 运行数据收集: python run_freqtrade_ui_monitoring.py")
    print("- 验证配置: python validate_freqtrade_ui_setup.py")
    print()
    print("按Ctrl+C退出...")
    
    try:
        # 保持脚本运行，直到用户按Ctrl+C
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 再见！")
        return 0


if __name__ == "__main__":
    sys.exit(main())