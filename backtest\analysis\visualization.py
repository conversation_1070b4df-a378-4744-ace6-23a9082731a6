#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
回测结果可视化模块

提供回测结果的可视化功能，包括净值曲线、回撤、月度收益等。
"""

from typing import Dict, Any, Union, Optional, List, Tuple
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.ticker import FuncFormatter
import seaborn as sns
import scipy.stats as stats

from ..base import BacktestResults


def plot_performance(results: BacktestResults,
                    benchmark: Optional[pd.Series] = None,
                    figsize: tuple = (15, 12),
                    **kwargs) -> plt.Figure:
    """
    绘制回测性能图表
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    benchmark : pd.Series, optional
        基准指数，默认为None
    figsize : tuple, optional
        图表大小，默认为(15, 12)
    **kwargs : dict
        其他绘图参数
        
    Returns
    -------
    plt.Figure
        图表对象
    """
    # 设置seaborn样式
    sns.set_style("whitegrid")
    
    # 创建图表
    fig = plt.figure(figsize=figsize)
    
    # 设置子图布局
    n_plots = 3
    if not results.trades.empty:
        n_plots = 4
    
    # 创建子图
    gs = fig.add_gridspec(n_plots, 1, height_ratios=[2] + [1] * (n_plots - 1))
    
    # 绘制净值曲线
    ax1 = fig.add_subplot(gs[0])
    plot_equity_curve(results, benchmark, ax=ax1)
    
    # 绘制回撤
    ax2 = fig.add_subplot(gs[1], sharex=ax1)
    plot_drawdowns(results, ax=ax2)
    
    # 绘制月度收益热图
    ax3 = fig.add_subplot(gs[2])
    plot_monthly_returns_heatmap(results, ax=ax3)
    
    # 如果有交易记录，绘制交易记录
    if not results.trades.empty and n_plots == 4:
        ax4 = fig.add_subplot(gs[3])
        plot_trades_distribution(results, ax=ax4)
    
    # 调整布局
    plt.tight_layout()
    
    return fig


def plot_equity_curve(results: BacktestResults,
                     benchmark: Optional[pd.Series] = None,
                     ax: Optional[plt.Axes] = None,
                     **kwargs) -> plt.Axes:
    """
    绘制净值曲线
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    benchmark : pd.Series, optional
        基准指数，默认为None
    ax : plt.Axes, optional
        绘图轴，默认为None
    **kwargs : dict
        其他绘图参数
        
    Returns
    -------
    plt.Axes
        绘图轴对象
    """
    # 创建轴（如果没有提供）
    if ax is None:
        _, ax = plt.subplots(figsize=(10, 6))
    
    # 获取净值曲线（处理函数或属性）
    equity = results.equity() if callable(results.equity) else results.equity
    
    # 规范化净值
    equity_norm = equity / equity.iloc[0]
    
    # 绘制净值曲线
    ax.plot(equity_norm.index, equity_norm, label='Strategy', linewidth=2)
    
    # 如果有基准指数，绘制基准指数
    if benchmark is not None:
        # 对齐索引
        benchmark = benchmark.reindex(equity.index, method='ffill')
        
        # 规范化基准指数
        benchmark_norm = benchmark / benchmark.iloc[0]
        
        # 绘制基准指数
        ax.plot(benchmark_norm.index, benchmark_norm, label='Benchmark', linewidth=1.5, alpha=0.7)
    
    # 添加买卖点（如果有交易记录）
    if not results.trades.empty:
        if 'Entry Time' in results.trades.columns and 'Exit Time' in results.trades.columns:
            try:
                # 尝试添加买卖点
                for _, trade in results.trades.iterrows():
                    entry_time = trade['Entry Time']
                    exit_time = trade['Exit Time']
                    
                    # 获取买卖点对应的净值
                    if entry_time in equity_norm.index:
                        entry_value = equity_norm.loc[entry_time]
                        ax.scatter(entry_time, entry_value, marker='^', color='green', s=50, alpha=0.7)
                    
                    if exit_time in equity_norm.index:
                        exit_value = equity_norm.loc[exit_time]
                        ax.scatter(exit_time, exit_value, marker='v', color='red', s=50, alpha=0.7)
            except Exception as e:
                print(f"无法添加买卖点标记: {e}")
    
    # 设置图表属性
    ax.set_title('Equity Curve', fontsize=14)
    ax.set_ylabel('Normalized Value')
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    # 格式化x轴日期
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    # 格式化y轴
    ax.yaxis.set_major_formatter(FuncFormatter(lambda y, _: f'{y:.2f}'))
    
    return ax


def plot_drawdowns(results: BacktestResults,
                  ax: Optional[plt.Axes] = None,
                  **kwargs) -> plt.Axes:
    """
    绘制回撤图
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    ax : plt.Axes, optional
        绘图轴，默认为None
    **kwargs : dict
        其他绘图参数
        
    Returns
    -------
    plt.Axes
        绘图轴对象
    """
    # 创建轴（如果没有提供）
    if ax is None:
        _, ax = plt.subplots(figsize=(10, 4))
    
    # 获取回撤序列（处理函数或属性）
    get_drawdowns = results.get_drawdowns
    drawdowns = get_drawdowns() if callable(get_drawdowns) else get_drawdowns
    
    # 确保drawdowns是一维的
    if isinstance(drawdowns, pd.DataFrame):
        if drawdowns.shape[1] == 1:
            drawdowns = drawdowns.iloc[:, 0]
        else:
            # 使用第一列或平均值
            drawdowns = drawdowns.iloc[:, 0]
            print("警告：多列回撤数据，使用第一列")
    
    # 绘制回撤
    ax.fill_between(drawdowns.index, drawdowns.values, 0, color='red', alpha=0.3, label='Drawdown')
    
    # 标记最大回撤
    max_dd_idx = drawdowns.idxmin()
    max_dd = drawdowns.min()
    
    if max_dd_idx is not None:
        ax.scatter(max_dd_idx, max_dd, color='darkred', s=50)
        ax.annotate(f'Max DD: {max_dd:.2%}', 
                   xy=(max_dd_idx, max_dd),
                   xytext=(30, -30),
                   textcoords='offset points',
                   arrowprops=dict(arrowstyle='->', color='black'))
    
    # 设置图表属性
    ax.set_title('Drawdowns', fontsize=14)
    ax.set_ylabel('Drawdown')
    ax.grid(True, alpha=0.3)
    
    # 格式化y轴为百分比
    ax.yaxis.set_major_formatter(FuncFormatter(lambda y, _: f'{y:.0%}'))
    
    return ax


def plot_monthly_returns_heatmap(results: BacktestResults,
                               ax: Optional[plt.Axes] = None,
                               **kwargs) -> plt.Axes:
    """
    绘制月度收益热图
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    ax : plt.Axes, optional
        绘图轴，默认为None
    **kwargs : dict
        其他绘图参数
        
    Returns
    -------
    plt.Axes
        绘图轴对象
    """
    # 创建轴（如果没有提供）
    if ax is None:
        _, ax = plt.subplots(figsize=(10, 4))
    
    try:
        # 获取收益率序列（处理函数或属性）
        get_returns = results.get_returns
        returns = get_returns() if callable(get_returns) else get_returns
        
        # 确保returns是一维的
        if isinstance(returns, pd.DataFrame):
            if returns.shape[1] == 1:
                returns = returns.iloc[:, 0]
            else:
                # 使用第一列
                returns = returns.iloc[:, 0]
                print("警告：多列收益率数据，使用第一列")
        
        if len(returns) < 30 or not isinstance(returns.index, pd.DatetimeIndex):
            ax.text(0.5, 0.5, 'Insufficient data for monthly returns heatmap',
                   ha='center', va='center', fontsize=12)
            ax.axis('off')
            return ax
        
        # 计算月度收益率
        monthly_returns = returns.resample('ME').apply(lambda x: (1 + x).prod() - 1)
        
        # 确保月度收益率不为空
        if monthly_returns.empty or monthly_returns.isna().all():
            ax.text(0.5, 0.5, 'No valid monthly returns data available',
                   ha='center', va='center', fontsize=12)
            ax.axis('off')
            return ax
        
        # 创建月度收益表格
        monthly_table = pd.DataFrame()
        monthly_table['Returns'] = monthly_returns
        monthly_table['Year'] = monthly_table.index.year
        monthly_table['Month'] = monthly_table.index.month
        
        # 透视表
        heatmap_data = monthly_table.pivot_table(
            values='Returns',
            index='Year',
            columns='Month'
        )
        
        # 确定最小和最大值以平衡颜色映射
        vmin = min(heatmap_data.min().min(), -heatmap_data.max().max())
        vmax = max(heatmap_data.max().max(), -heatmap_data.min().min())
        
        # 绘制热图
        sns.heatmap(
            heatmap_data,
            ax=ax,
            cmap='RdYlGn',
            center=0,
            annot=True,
            fmt='.1%',
            linewidths=0.5,
            vmin=vmin,
            vmax=vmax,
            cbar_kws={'label': 'Monthly Return'}
        )
        
        # 设置图表属性
        ax.set_title('Monthly Returns', fontsize=14)
        ax.set_ylabel('Year')
        
        # 设置月份名称
        month_labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        ax.set_xticklabels(month_labels, rotation=0)
    except Exception as e:
        print(f"无法创建月度收益热图: {e}")
        ax.text(0.5, 0.5, f'Error creating monthly returns heatmap: {e}',
               ha='center', va='center', fontsize=10)
        ax.axis('off')
    
    return ax


def plot_trades_distribution(results: BacktestResults,
                           ax: Optional[plt.Axes] = None,
                           **kwargs) -> plt.Axes:
    """
    绘制交易分布图
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    ax : plt.Axes, optional
        绘图轴，默认为None
    **kwargs : dict
        其他绘图参数
        
    Returns
    -------
    plt.Axes
        绘图轴对象
    """
    # 创建轴（如果没有提供）
    if ax is None:
        _, ax = plt.subplots(figsize=(10, 4))
    
    if results.trades.empty:
        ax.text(0.5, 0.5, 'No trade data available',
               ha='center', va='center', fontsize=12)
        ax.axis('off')
        return ax
    
    try:
        # 确定使用哪一列作为收益
        pnl_col = None
        if 'PnL' in results.trades.columns:
            pnl_col = 'PnL'
        elif 'Return' in results.trades.columns:
            pnl_col = 'Return'
        
        if pnl_col is None:
            ax.text(0.5, 0.5, 'No PnL or Return column in trades data',
                   ha='center', va='center', fontsize=12)
            ax.axis('off')
            return ax
        
        # 获取交易盈亏
        trade_pnls = results.trades[pnl_col]
        
        # 创建直方图
        sns.histplot(trade_pnls, ax=ax, kde=True, bins=30, color='skyblue')
        
        # 添加盈亏划分线
        ax.axvline(x=0, color='red', linestyle='--', alpha=0.7)
        
        # 计算统计信息
        winning_trades = trade_pnls[trade_pnls > 0]
        losing_trades = trade_pnls[trade_pnls < 0]
        
        win_count = len(winning_trades)
        loss_count = len(losing_trades)
        win_rate = win_count / len(trade_pnls) if len(trade_pnls) > 0 else 0
        
        avg_win = winning_trades.mean() if len(winning_trades) > 0 else 0
        avg_loss = losing_trades.mean() if len(losing_trades) > 0 else 0
        
        # 添加统计标注
        stats_text = (
            f"Win Rate: {win_rate:.1%}\n"
            f"Trades: {len(trade_pnls)} ({win_count} Win / {loss_count} Loss)\n"
            f"Avg Win: {avg_win:.2f} / Avg Loss: {avg_loss:.2f}"
        )
        
        ax.text(
            0.02, 0.95, stats_text,
            transform=ax.transAxes,
            verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.7)
        )
        
        # 设置图表属性
        ax.set_title('Trade PnL Distribution', fontsize=14)
        ax.set_xlabel('PnL')
        ax.set_ylabel('Frequency')
        
    except Exception as e:
        print(f"无法创建交易分布图: {e}")
        ax.text(0.5, 0.5, f'Error creating trade distribution: {e}',
               ha='center', va='center', fontsize=10)
        ax.axis('off')
    
    return ax


def plot_rolling_stats(results: BacktestResults,
                     window: int = 20,
                     figsize: tuple = (15, 12),
                     **kwargs) -> plt.Figure:
    """
    绘制滚动统计图表
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    window : int, optional
        滚动窗口大小，默认为20
    figsize : tuple, optional
        图表大小，默认为(15, 12)
    **kwargs : dict
        其他绘图参数
        
    Returns
    -------
    plt.Figure
        图表对象
    """
    # 设置seaborn样式
    sns.set_style("whitegrid")
    
    # 获取收益率序列（处理函数或属性）
    get_returns = results.get_returns
    returns = get_returns() if callable(get_returns) else get_returns
    
    if len(returns) < window * 2:
        _, ax = plt.subplots(figsize=(10, 6))
        ax.text(0.5, 0.5, f'Insufficient data for rolling stats (need at least {window * 2} data points)',
               ha='center', va='center', fontsize=12)
        ax.axis('off')
        return plt.gcf()
    
    # 计算滚动统计值
    rolling_mean = returns.rolling(window=window).mean()
    rolling_std = returns.rolling(window=window).std()
    rolling_sharpe = rolling_mean / rolling_std * np.sqrt(252)  # 年化Sharpe
    rolling_drawdown = returns.expanding().apply(lambda x: (1 + x).cumprod().min() / (1 + x).cumprod().max() - 1)
    
    # 创建图表
    fig, axes = plt.subplots(3, 1, figsize=figsize, sharex=True)
    
    # 绘制滚动收益率
    axes[0].plot(rolling_mean.index, rolling_mean * 100, color='blue')
    axes[0].set_title(f'Rolling {window}-day Mean Return', fontsize=14)
    axes[0].set_ylabel('Return (%)')
    axes[0].yaxis.set_major_formatter(FuncFormatter(lambda y, _: f'{y:.2f}'))
    axes[0].grid(True, alpha=0.3)
    
    # 绘制滚动波动率
    axes[1].plot(rolling_std.index, rolling_std * 100, color='red')
    axes[1].set_title(f'Rolling {window}-day Volatility', fontsize=14)
    axes[1].set_ylabel('Volatility (%)')
    axes[1].yaxis.set_major_formatter(FuncFormatter(lambda y, _: f'{y:.2f}'))
    axes[1].grid(True, alpha=0.3)
    
    # 绘制滚动Sharpe比率
    axes[2].plot(rolling_sharpe.index, rolling_sharpe, color='green')
    axes[2].set_title(f'Rolling {window}-day Sharpe Ratio', fontsize=14)
    axes[2].set_ylabel('Sharpe Ratio')
    axes[2].axhline(y=1, color='red', linestyle='--', alpha=0.7)
    axes[2].axhline(y=2, color='green', linestyle='--', alpha=0.7)
    axes[2].grid(True, alpha=0.3)
    
    # 格式化x轴日期
    axes[2].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    axes[2].xaxis.set_major_locator(mdates.MonthLocator(interval=3))
    plt.setp(axes[2].xaxis.get_majorticklabels(), rotation=45)
    
    # 调整布局
    plt.tight_layout()
    
    return fig


def plot_underwater(results: BacktestResults,
                  ax: Optional[plt.Axes] = None,
                  **kwargs) -> plt.Axes:
    """
    绘制水下图（回撤图的另一种形式）
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    ax : plt.Axes, optional
        绘图轴，默认为None
    **kwargs : dict
        其他绘图参数
        
    Returns
    -------
    plt.Axes
        绘图轴对象
    """
    # 创建轴（如果没有提供）
    if ax is None:
        _, ax = plt.subplots(figsize=(10, 4))
    
    # 获取回撤序列（处理函数或属性）
    get_drawdowns = results.get_drawdowns
    drawdowns = get_drawdowns() if callable(get_drawdowns) else get_drawdowns
    
    # 确保drawdowns是一维的
    if isinstance(drawdowns, pd.DataFrame):
        if drawdowns.shape[1] == 1:
            drawdowns = drawdowns.iloc[:, 0]
        else:
            # 使用第一列或平均值
            drawdowns = drawdowns.iloc[:, 0]
            print("警告：多列回撤数据，使用第一列")
    
    # 绘制水下图
    ax.fill_between(
        drawdowns.index,
        drawdowns.values,
        0,
        where=(drawdowns < 0),
        color='red',
        alpha=0.3,
        interpolate=True
    )
    ax.plot(drawdowns.index, drawdowns.values, color='red', alpha=0.5)
    
    # 设置图表属性
    ax.set_title('Underwater Chart', fontsize=14)
    ax.set_ylabel('Drawdown')
    ax.grid(True, alpha=0.3)
    
    # 格式化y轴为百分比
    ax.yaxis.set_major_formatter(FuncFormatter(lambda y, _: f'{y:.0%}'))
    
    # 限制y轴范围
    ax.set_ylim(min(drawdowns.min() * 1.1, -0.01), 0.01)
    
    return ax


def plot_returns_distribution(results: BacktestResults,
                            benchmark: Optional[pd.Series] = None,
                            ax: Optional[plt.Axes] = None,
                            bins: int = 50,
                            **kwargs) -> plt.Axes:
    """
    绘制回报分布图，包括正态分布拟合曲线
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    benchmark : pd.Series, optional
        基准指数，默认为None
    ax : plt.Axes, optional
        绘图轴，默认为None
    bins : int, optional
        直方图的箱数，默认为50
    **kwargs : dict
        其他绘图参数
        
    Returns
    -------
    plt.Axes
        绘图轴对象
    """
    # 创建轴（如果没有提供）
    if ax is None:
        _, ax = plt.subplots(figsize=(10, 6))
    
    # 获取收益率序列
    get_returns = results.get_returns
    returns = get_returns() if callable(get_returns) else get_returns
    
    # 绘制直方图
    sns.histplot(returns, bins=bins, kde=False, stat='density', 
                color='skyblue', alpha=0.6, ax=ax, label='Returns')
    
    # 添加核密度估计
    sns.kdeplot(returns, color='darkblue', ax=ax, linewidth=2)
    
    # 添加正态分布拟合曲线
    x = np.linspace(returns.min(), returns.max(), 100)
    mu, std = returns.mean(), returns.std()
    pdf = stats.norm.pdf(x, mu, std)
    ax.plot(x, pdf, 'r-', linewidth=2, label='Normal Fit')
    
    # 添加基准分布（如果有）
    if benchmark is not None:
        benchmark_returns = benchmark.pct_change().dropna()
        benchmark_returns = benchmark_returns.reindex(returns.index).dropna()
        if len(benchmark_returns) > 0:
            sns.kdeplot(benchmark_returns, color='green', ax=ax, 
                       linewidth=1.5, label='Benchmark')
    
    # 添加垂直线表示均值
    ax.axvline(returns.mean(), color='navy', linestyle='--', alpha=0.7, 
              label=f'Mean: {returns.mean():.2%}')
    
    # 添加垂直线表示中位数
    ax.axvline(returns.median(), color='purple', linestyle=':', alpha=0.7, 
              label=f'Median: {returns.median():.2%}')
    
    # 标记统计指标
    stats_text = (f"Mean: {returns.mean():.4f}\n"
                  f"Median: {returns.median():.4f}\n"
                  f"Std Dev: {returns.std():.4f}\n"
                  f"Skew: {returns.skew():.4f}\n"
                  f"Kurtosis: {returns.kurtosis():.4f}")
    
    # 放置文本框
    props = dict(boxstyle='round', facecolor='white', alpha=0.7)
    ax.text(0.05, 0.95, stats_text, transform=ax.transAxes, 
           verticalalignment='top', bbox=props, fontsize=9)
    
    # 设置图表属性
    ax.set_title('Returns Distribution', fontsize=14)
    ax.set_xlabel('Daily Returns')
    ax.set_ylabel('Density')
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    # 格式化x轴为百分比
    ax.xaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{x:.2%}'))
    
    return ax


def plot_drawdown_periods(results: BacktestResults,
                        top_n: int = 5,
                        ax: Optional[plt.Axes] = None,
                        **kwargs) -> plt.Axes:
    """
    绘制主要回撤期间分析图
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    top_n : int, optional
        显示的主要回撤数量，默认为5
    ax : plt.Axes, optional
        绘图轴，默认为None
    **kwargs : dict
        其他绘图参数
        
    Returns
    -------
    plt.Axes
        绘图轴对象
    """
    # 创建轴（如果没有提供）
    if ax is None:
        _, ax = plt.subplots(figsize=(12, 6))
    
    # 获取净值曲线和回撤
    equity = results.equity() if callable(results.equity) else results.equity
    get_drawdowns = results.get_drawdowns
    drawdowns = get_drawdowns() if callable(get_drawdowns) else get_drawdowns
    
    # 确保drawdowns是一维的
    if isinstance(drawdowns, pd.DataFrame):
        if drawdowns.shape[1] == 1:
            drawdowns = drawdowns.iloc[:, 0]
        else:
            drawdowns = drawdowns.iloc[:, 0]
    
    # 找出主要回撤期间
    dd_series = drawdowns.copy()
    dd_periods = []
    
    # 查找回撤开始、结束和恢复点
    in_drawdown = False
    start_idx = None
    bottom_idx = None
    
    for i, (date, value) in enumerate(dd_series.items()):
        if not in_drawdown and value < 0:
            # 回撤开始
            in_drawdown = True
            start_idx = i - 1 if i > 0 else i  # 前一个点为高点
            
        elif in_drawdown:
            if value == 0:
                # 回撤结束，已恢复
                if bottom_idx is not None:
                    recovery_idx = i
                    # 计算回撤的深度和长度
                    max_dd = dd_series.iloc[bottom_idx]
                    length = recovery_idx - start_idx
                    # 保存回撤期间
                    dd_periods.append({
                        'start': dd_series.index[start_idx],
                        'bottom': dd_series.index[bottom_idx],
                        'end': dd_series.index[recovery_idx],
                        'max_drawdown': max_dd,
                        'length': length
                    })
                
                # 重置状态
                in_drawdown = False
                start_idx = None
                bottom_idx = None
            
            elif bottom_idx is None or value < dd_series.iloc[bottom_idx]:
                # 更新回撤底部
                bottom_idx = i
    
    # 如果结束时仍在回撤中，使用最后一个点作为结束点
    if in_drawdown and bottom_idx is not None:
        recovery_idx = len(dd_series) - 1
        max_dd = dd_series.iloc[bottom_idx]
        length = recovery_idx - start_idx
        dd_periods.append({
            'start': dd_series.index[start_idx],
            'bottom': dd_series.index[bottom_idx],
            'end': dd_series.index[recovery_idx],
            'max_drawdown': max_dd,
            'length': length,
            'recovered': False
        })
    
    # 按回撤深度排序
    dd_periods.sort(key=lambda x: x['max_drawdown'])
    top_drawdowns = dd_periods[:top_n]
    
    # 绘制净值曲线
    equity_norm = equity / equity.iloc[0]
    ax.plot(equity_norm.index, equity_norm, color='blue', linewidth=1.5, label='Equity')
    
    # 使用不同颜色标记主要回撤期间
    colors = plt.cm.rainbow(np.linspace(0, 1, len(top_drawdowns)))
    
    # 绘制每个主要回撤期间
    for i, dd in enumerate(top_drawdowns):
        start_idx = equity_norm.index.get_loc(dd['start'])
        
        # 确保bottom_idx和end_idx有效
        try:
            bottom_idx = equity_norm.index.get_loc(dd['bottom'])
            end_idx = equity_norm.index.get_loc(dd['end'])
        except (KeyError, ValueError):
            # 如果日期不在索引中，则跳过
            continue
            
        # 绘制回撤期间
        dd_line = equity_norm.iloc[start_idx:end_idx+1]
        ax.plot(dd_line.index, dd_line, color=colors[i], linewidth=2.5, alpha=0.7,
               label=f"DD #{i+1}: {dd['max_drawdown']:.2%}")
        
        # 标记开始、底部和结束点
        ax.scatter(dd['start'], equity_norm.loc[dd['start']], color=colors[i], s=100, marker='^')
        ax.scatter(dd['bottom'], equity_norm.loc[dd['bottom']], color=colors[i], s=100, marker='o')
        ax.scatter(dd['end'], equity_norm.loc[dd['end']], color=colors[i], s=100, 
                 marker='v' if 'recovered' not in dd or dd['recovered'] else 's')
    
    # 设置图表属性
    ax.set_title('Major Drawdown Periods Analysis', fontsize=14)
    ax.set_ylabel('Normalized Equity')
    ax.grid(True, alpha=0.3)
    ax.legend(loc='upper left')
    
    # 格式化x轴日期
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    return ax


def plot_trade_analysis(results: BacktestResults,
                      ax: Optional[plt.Axes] = None,
                      figsize: tuple = (12, 10),
                      **kwargs) -> Union[plt.Axes, plt.Figure]:
    """
    绘制交易分析综合图，包括盈利/亏损分布、持仓周期分析和交易频率
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    ax : plt.Axes, optional
        绘图轴，默认为None
    figsize : tuple, optional
        图表大小，默认为(12, 10)
    **kwargs : dict
        其他绘图参数
        
    Returns
    -------
    Union[plt.Axes, plt.Figure]
        如果提供ax则返回plt.Axes，否则返回plt.Figure
    """
    if results.trades.empty:
        print("没有交易记录可供分析")
        return ax if ax is not None else plt.figure(figsize=(6, 4))
    
    # 如果提供了轴，则只绘制盈亏分布
    if ax is not None:
        return _plot_pnl_distribution(results, ax)
    
    # 创建图表和子图
    fig = plt.figure(figsize=figsize)
    gs = fig.add_gridspec(3, 2, height_ratios=[2, 1, 1])
    
    # 盈利/亏损分布
    ax1 = fig.add_subplot(gs[0, :])
    _plot_pnl_distribution(results, ax1)
    
    # 持仓周期分析
    ax2 = fig.add_subplot(gs[1, 0])
    _plot_holding_periods(results, ax2)
    
    # 交易频率分析
    ax3 = fig.add_subplot(gs[1, 1])
    _plot_trade_frequency(results, ax3)
    
    # 分位数分析
    ax4 = fig.add_subplot(gs[2, :])
    _plot_return_quantiles(results, ax4)
    
    plt.tight_layout()
    return fig


def _plot_pnl_distribution(results: BacktestResults, ax: plt.Axes) -> plt.Axes:
    """绘制交易盈亏分布图"""
    trades = results.trades
    
    # 确定使用哪个列作为盈亏列
    if 'PnL' in trades.columns:
        pnl_col = 'PnL'
    elif 'Return' in trades.columns:
        pnl_col = 'Return'
    else:
        ax.text(0.5, 0.5, 'No PnL or Return data found', 
               ha='center', va='center', transform=ax.transAxes)
        return ax
    
    # 分离盈利和亏损交易
    profits = trades[trades[pnl_col] > 0][pnl_col]
    losses = trades[trades[pnl_col] < 0][pnl_col]
    
    # 计算统计数据
    win_rate = len(profits) / len(trades) if len(trades) > 0 else 0
    profit_factor = abs(profits.sum() / losses.sum()) if losses.sum() < 0 else float('inf')
    avg_win = profits.mean() if len(profits) > 0 else 0
    avg_loss = losses.mean() if len(losses) > 0 else 0
    
    # 绘制分布图
    bins = min(50, max(10, len(trades) // 20))  # 动态调整bins数量
    
    # 绘制盈利交易
    if len(profits) > 0:
        sns.histplot(profits, bins=bins, color='green', alpha=0.6, ax=ax, label='Profits')
    
    # 绘制亏损交易
    if len(losses) > 0:
        sns.histplot(losses, bins=bins, color='red', alpha=0.6, ax=ax, label='Losses')
    
    # 添加垂直线表示平均盈利/亏损
    if len(profits) > 0:
        ax.axvline(avg_win, color='darkgreen', linestyle='--', alpha=0.7)
        ax.text(avg_win, 0.95*ax.get_ylim()[1], f'Avg Win: {avg_win:.2f}', 
               ha='center', va='top', color='darkgreen', fontsize=9)
    
    if len(losses) > 0:
        ax.axvline(avg_loss, color='darkred', linestyle='--', alpha=0.7)
        ax.text(avg_loss, 0.85*ax.get_ylim()[1], f'Avg Loss: {avg_loss:.2f}', 
               ha='center', va='top', color='darkred', fontsize=9)
    
    # 添加统计信息
    stats_text = (f"Win Rate: {win_rate:.2%}\n"
                  f"Profit Factor: {profit_factor:.2f}\n"
                  f"Avg Win: {avg_win:.4f}\n"
                  f"Avg Loss: {avg_loss:.4f}\n"
                  f"Win/Loss Ratio: {abs(avg_win/avg_loss):.2f}" if avg_loss != 0 else "Win/Loss Ratio: ∞")
    
    # 放置文本框
    props = dict(boxstyle='round', facecolor='white', alpha=0.7)
    ax.text(0.05, 0.95, stats_text, transform=ax.transAxes, 
           verticalalignment='top', bbox=props, fontsize=9)
    
    # 设置图表属性
    ax.set_title('Trade PnL Distribution', fontsize=14)
    ax.set_xlabel(pnl_col)
    ax.set_ylabel('Count')
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    return ax


def _plot_holding_periods(results: BacktestResults, ax: plt.Axes) -> plt.Axes:
    """绘制持仓周期分布图"""
    trades = results.trades
    
    # 检查是否有持仓周期数据
    if 'Entry Time' not in trades.columns or 'Exit Time' not in trades.columns:
        ax.text(0.5, 0.5, 'No Entry/Exit Time data found', 
               ha='center', va='center', transform=ax.transAxes)
        return ax
    
    # 计算持仓周期（以天为单位）
    holding_periods = [(exit_time - entry_time).days + 
                       (exit_time - entry_time).seconds / 86400 
                       for entry_time, exit_time in 
                       zip(trades['Entry Time'], trades['Exit Time'])]
    
    # 添加持仓周期列
    trades_with_periods = trades.copy()
    trades_with_periods['Holding Period'] = holding_periods
    
    # 绘制持仓周期分布
    if 'PnL' in trades.columns or 'Return' in trades.columns:
        pnl_col = 'PnL' if 'PnL' in trades.columns else 'Return'
        
        # 区分盈利和亏损交易
        profits = trades_with_periods[trades_with_periods[pnl_col] > 0]
        losses = trades_with_periods[trades_with_periods[pnl_col] < 0]
        
        # 绘制盈利交易的持仓周期
        if len(profits) > 0:
            sns.histplot(profits['Holding Period'], bins=20, color='green', 
                        alpha=0.6, ax=ax, label='Profitable Trades')
        
        # 绘制亏损交易的持仓周期
        if len(losses) > 0:
            sns.histplot(losses['Holding Period'], bins=20, color='red', 
                        alpha=0.6, ax=ax, label='Losing Trades')
    else:
        # 没有盈亏数据，只绘制整体持仓周期
        sns.histplot(holding_periods, bins=20, color='blue', alpha=0.6, ax=ax)
    
    # 添加平均持仓周期线
    avg_holding = np.mean(holding_periods)
    ax.axvline(avg_holding, color='navy', linestyle='--', alpha=0.7)
    ax.text(avg_holding, 0.95*ax.get_ylim()[1], f'Avg: {avg_holding:.1f} days', 
           ha='center', va='top', color='navy', fontsize=9)
    
    # 设置图表属性
    ax.set_title('Holding Period Distribution', fontsize=12)
    ax.set_xlabel('Holding Period (days)')
    ax.set_ylabel('Count')
    ax.grid(True, alpha=0.3)
    
    if 'PnL' in trades.columns or 'Return' in trades.columns:
        ax.legend()
    
    return ax


def _plot_trade_frequency(results: BacktestResults, ax: plt.Axes) -> plt.Axes:
    """绘制交易频率图"""
    trades = results.trades
    
    # 检查是否有入场时间数据
    if 'Entry Time' not in trades.columns:
        ax.text(0.5, 0.5, 'No Entry Time data found', 
               ha='center', va='center', transform=ax.transAxes)
        return ax
    
    # 按周重采样交易数量
    trades_ts = pd.Series(1, index=trades['Entry Time'])
    weekly_count = trades_ts.resample('W').count()
    
    # 绘制交易频率
    weekly_count.plot(kind='bar', color='skyblue', ax=ax)
    
    # 添加移动平均线
    if len(weekly_count) > 4:
        ma = weekly_count.rolling(window=4).mean()
        ax.plot(range(len(ma)), ma, 'r-', linewidth=2, label='4-week MA')
        ax.legend()
    
    # 设置图表属性
    ax.set_title('Weekly Trade Frequency', fontsize=12)
    ax.set_xlabel('')
    ax.set_ylabel('Number of Trades')
    ax.grid(True, alpha=0.3)
    
    # 如果数据点太多，则稀疏显示x轴标签
    if len(weekly_count) > 20:
        interval = max(1, len(weekly_count) // 20)
        for idx, label in enumerate(ax.xaxis.get_ticklabels()):
            if idx % interval != 0:
                label.set_visible(False)
    
    # 旋转x轴标签
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=90)
    
    return ax


def _plot_return_quantiles(results: BacktestResults, ax: plt.Axes) -> plt.Axes:
    """绘制收益分位数分析图"""
    trades = results.trades
    
    # 确定使用哪个列作为盈亏列
    if 'PnL' in trades.columns:
        pnl_col = 'PnL'
    elif 'Return' in trades.columns:
        pnl_col = 'Return'
    else:
        ax.text(0.5, 0.5, 'No PnL or Return data found', 
               ha='center', va='center', transform=ax.transAxes)
        return ax
    
    # 计算分位数
    quantiles = [0.01, 0.05, 0.1, 0.25, 0.5, 0.75, 0.9, 0.95, 0.99]
    q_values = trades[pnl_col].quantile(quantiles)
    
    # 绘制分位数图
    ax.plot(quantiles, q_values, 'bo-', markersize=8)
    
    # 添加水平线表示零点
    ax.axhline(0, color='gray', linestyle='-', alpha=0.5)
    
    # 设置图表属性
    ax.set_title('Return Quantile Analysis', fontsize=12)
    ax.set_xlabel('Quantile')
    ax.set_ylabel(pnl_col)
    ax.grid(True, alpha=0.3)
    
    # 设置x轴刻度
    ax.set_xticks(quantiles)
    ax.set_xticklabels([f'{q:.0%}' for q in quantiles])
    
    # 添加数据标签
    for i, (q, v) in enumerate(zip(quantiles, q_values)):
        ax.annotate(f'{v:.4f}', (q, v), xytext=(0, 10), 
                   textcoords='offset points', ha='center')
    
    return ax 