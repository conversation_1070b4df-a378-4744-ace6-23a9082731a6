"""
风险监控系统示例脚本

演示如何使用风险监控系统的各个组件。
"""

import os
import sys
import time
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import json

# 确保可以导入风险模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from risk.monitoring.base import MonitoringEvent, EventSeverity
from risk.monitoring.real_time import TradingSignalMonitor, CapitalChangeMonitor, PriceVolatilityMonitor
from risk.monitoring.alerts import (
    AlertMonitor, AlertRule, SeverityBasedAlertRule, ThresholdAlertRule, 
    FrequencyAlertRule, PatternMatchAlertRule
)
from risk.monitoring.notification import LoggingHandler, FileStorageHandler
from risk.assessment.metrics import calculate_volatility, calculate_drawdown, calculate_sharpe_ratio
from risk.assessment.var_models import HistoricalVaRModel
from risk.visualization.dashboard import RiskDashboard


def generate_sample_data(days=30, freq='1d'):
    """
    生成示例价格和交易数据
    
    Parameters
    ----------
    days : int, optional
        天数, 默认30
    freq : str, optional
        频率, 默认'1d'
        
    Returns
    -------
    dict
        示例数据字典
    """
    # 生成日期范围
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    dates = pd.date_range(start=start_date, end=end_date, freq=freq)
    
    # 生成价格数据
    np.random.seed(42)
    initial_price = 100
    returns = np.random.normal(0, 0.015, len(dates))
    prices = initial_price * (1 + returns).cumprod()
    
    # 生成波动率突增
    vol_spike_idx = int(len(dates) * 0.7)
    prices[vol_spike_idx:] = prices[vol_spike_idx:] * (1 + np.random.normal(0, 0.03, len(dates) - vol_spike_idx))
    
    # 生成交易信号
    signals = []
    for i in range(len(dates)):
        if np.random.random() < 0.3:  # 30%的概率生成信号
            signal_type = np.random.choice(['buy', 'sell'])
            price = prices[i] * (1 + np.random.normal(0, 0.005))
            volume = np.random.randint(10, 100)
            
            signals.append({
                'timestamp': dates[i],
                'type': signal_type,
                'price': price,
                'volume': volume
            })
    
    # 生成资金数据
    initial_capital = 10000
    capital = [initial_capital]
    
    for i in range(1, len(dates)):
        daily_return = returns[i]
        prev_capital = capital[-1]
        new_capital = prev_capital * (1 + daily_return)
        
        # 添加一些随机波动
        new_capital += np.random.normal(0, prev_capital * 0.005)
        
        capital.append(new_capital)
    
    # 创建数据字典
    data = {
        'dates': dates,
        'prices': pd.Series(prices, index=dates),
        'returns': pd.Series(returns, index=dates),
        'capital': pd.Series(capital, index=dates),
        'signals': signals
    }
    
    return data


def run_monitoring_demo():
    """运行风险监控系统演示"""
    print("风险监控系统演示开始...")
    
    # 生成示例数据
    print("生成示例数据...")
    sample_data = generate_sample_data(days=30)
    
    # 创建日志目录
    log_dir = os.path.join(os.path.dirname(__file__), 'output')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 设置日志处理器
    logging_handler = LoggingHandler(
        name="demo_logging_handler",
        log_file=os.path.join(log_dir, "risk_monitor.log"),
        log_level="INFO"
    )
    
    file_handler = FileStorageHandler(
        name="demo_file_handler",
        storage_dir=log_dir
    )
    
    # 创建监控器
    print("创建监控器...")
    trading_monitor = TradingSignalMonitor(
        max_signals_per_interval=5,
        min_signal_interval=2
    )
    
    capital_monitor = CapitalChangeMonitor(
        max_drawdown_pct=5.0,
        max_hourly_drawdown_pct=10.0,
        max_daily_drawdown_pct=20.0
    )
    
    price_monitor = PriceVolatilityMonitor(
        max_short_term_change_pct=3.0,
        max_hourly_change_pct=8.0
    )
    
    # 创建预警规则
    print("设置预警规则...")
    severity_rule = SeverityBasedAlertRule(
        name="high_severity_rule",
        min_severity=EventSeverity.WARNING
    )
    
    capital_loss_rule = ThresholdAlertRule(
        name="capital_loss_rule",
        field_path="related_data.percentage_change",
        threshold=-0.03,
        operator="<"
    )
    
    volatility_rule = ThresholdAlertRule(
        name="volatility_rule",
        field_path="related_data.volatility",
        threshold=0.02,
        operator=">"
    )
    
    signal_frequency_rule = FrequencyAlertRule(
        name="signal_frequency_rule",
        event_type="excessive_signals",
        max_occurrences=2,
        time_window=300  # 5分钟
    )
    
    # 创建预警监控器
    alert_monitor = AlertMonitor()
    
    # 添加规则
    alert_monitor.add_rule(severity_rule)
    alert_monitor.add_rule(capital_loss_rule)
    alert_monitor.add_rule(volatility_rule)
    alert_monitor.add_rule(signal_frequency_rule)
    
    # 添加处理器
    alert_monitor.event_handlers.append(logging_handler)
    alert_monitor.event_handlers.append(file_handler)
    
    # 运行监控
    print("开始监控数据...")
    events = []
    
    # 监控价格和波动率
    for i in range(1, len(sample_data['dates'])):
        current_date = sample_data['dates'][i]
        prev_date = sample_data['dates'][i-1]
        
        # 获取最近价格数据
        recent_prices = sample_data['prices'].iloc[max(0, i-10):i+1]
        
        # 计算波动率
        volatility = calculate_volatility(recent_prices)
        
        # 价格监控
        price_context = {
            'timestamp': current_date,
            'current_price': sample_data['prices'].iloc[i],
            'previous_price': sample_data['prices'].iloc[i-1],
            'returns': sample_data['returns'].iloc[i],
            'price_series': recent_prices,
            'volatility': volatility
        }
        
        price_events = price_monitor.check(price_context)
        events.extend(price_events)
        
        # 资金监控
        capital_context = {
            'timestamp': current_date,
            'current_capital': sample_data['capital'].iloc[i],
            'previous_capital': sample_data['capital'].iloc[i-1],
            'percentage_change': (sample_data['capital'].iloc[i] - sample_data['capital'].iloc[i-1]) / sample_data['capital'].iloc[i-1],
            'capital_series': sample_data['capital'].iloc[max(0, i-7):i+1]
        }
        
        capital_events = capital_monitor.check(capital_context)
        events.extend(capital_events)
    
    # 监控交易信号
    for signal in sample_data['signals']:
        # 交易信号监控
        signal_context = {
            'timestamp': signal['timestamp'],
            'signal_type': signal['type'],
            'signal_price': signal['price'],
            'signal_volume': signal['volume']
        }
        
        signal_events = trading_monitor.check(signal_context)
        events.extend(signal_events)
    
    # 处理所有监控事件并触发预警
    print(f"生成了 {len(events)} 个监控事件")
    alert_events = []
    
    for event in events:
        # 创建处理上下文
        context = {'events': event}
        
        # 触发预警处理
        alerts = alert_monitor.check(context)
        alert_events.extend(alerts)
    
    print(f"触发了 {len(alert_events)} 个预警")
    
    # 创建风险指标
    print("计算风险指标...")
    risk_metrics = {
        'volatility': calculate_volatility(sample_data['prices']),
        'daily_var_95': HistoricalVaRModel(confidence_level=0.95).calculate(sample_data['returns']),
        'max_drawdown': calculate_drawdown(sample_data['prices'])['max_drawdown'],
        'sharpe_ratio': calculate_sharpe_ratio(sample_data['returns'])
    }
    
    # 创建风险仪表盘
    print("创建风险仪表盘...")
    dashboard = RiskDashboard(title="风险监控演示仪表盘")
    
    # 设置风险评分
    risk_score = 65 if risk_metrics['volatility'] > 0.015 or risk_metrics['max_drawdown'] > 0.1 else 35
    risk_level = "High" if risk_score >= 50 else "Medium" if risk_score >= 25 else "Low"
    
    dashboard.set_risk_score(risk_score, risk_level)
    
    # 添加风险指标
    dashboard.add_risk_metric("Volatility", risk_metrics['volatility'], normalized_score=risk_metrics['volatility']*1000)
    dashboard.add_risk_metric("95% Daily VaR", risk_metrics['daily_var_95'], normalized_score=risk_metrics['daily_var_95']*500)
    dashboard.add_risk_metric("Max Drawdown", risk_metrics['max_drawdown'], normalized_score=risk_metrics['max_drawdown']*200)
    dashboard.add_risk_metric("Sharpe Ratio", risk_metrics['sharpe_ratio'], normalized_score=max(0, (3-risk_metrics['sharpe_ratio'])*33.3))
    
    # 添加时间序列数据
    dashboard.add_time_series("Prices", sample_data['prices'], color='blue')
    dashboard.add_time_series("Capital", sample_data['capital'], color='green')
    
    # 添加建议
    if risk_metrics['volatility'] > 0.015:
        dashboard.add_recommendation("降低投资组合波动率：增加低波动性资产占比")
    if risk_metrics['daily_var_95'] > 0.02:
        dashboard.add_recommendation("减少风险敞口：考虑使用对冲策略降低VaR")
    if risk_metrics['max_drawdown'] > 0.1:
        dashboard.add_recommendation("改进止损策略：实施更严格的止损规则减少最大回撤")
    if risk_metrics['sharpe_ratio'] < 1:
        dashboard.add_recommendation("优化风险收益比：重新评估交易策略以提高夏普比率")
    
    # 保存仪表盘到HTML文件
    dashboard_file = os.path.join(log_dir, "risk_dashboard.html")
    dashboard.save_html(dashboard_file)
    
    print(f"风险仪表盘已保存到 {dashboard_file}")
    print("风险监控系统演示完成！")
    
    # 返回生成的数据和事件
    return {
        'sample_data': sample_data,
        'events': events,
        'alert_events': alert_events,
        'risk_metrics': risk_metrics,
        'dashboard': dashboard
    }


if __name__ == "__main__":
    result = run_monitoring_demo()
    
    # 显示一些基本统计信息
    print("\n风险监控统计信息:")
    print("-" * 40)
    
    # 事件统计
    event_types = {}
    for event in result['events']:
        event_type = event.event_type
        if event_type not in event_types:
            event_types[event_type] = 0
        event_types[event_type] += 1
    
    print("监控事件类型统计:")
    for event_type, count in event_types.items():
        print(f"  - {event_type}: {count} 个事件")
    
    # 预警统计
    alert_severity = {}
    for alert in result['alert_events']:
        severity = alert.severity.value
        if severity not in alert_severity:
            alert_severity[severity] = 0
        alert_severity[severity] += 1
    
    print("\n预警严重性统计:")
    for severity, count in alert_severity.items():
        print(f"  - {severity}: {count} 个预警")
    
    # 风险指标
    print("\n风险指标:")
    for metric, value in result['risk_metrics'].items():
        print(f"  - {metric}: {value:.4f}") 