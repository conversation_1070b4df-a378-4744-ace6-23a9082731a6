#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
稳健性分析示例脚本

展示如何使用蒙特卡洛模拟、Bootstrap分析和参数敏感性分析来评估策略的稳健性。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from backtest.base import BacktestResults
from backtest.analysis.robustness import (
    monte_carlo_simulation,
    plot_monte_carlo_simulation,
    analyze_monte_carlo_results,
    bootstrap_analysis,
    plot_bootstrap_distributions,
    sensitivity_analysis,
    plot_sensitivity_analysis,
    maximum_adverse_excursion,
    plot_mae_analysis
)
from backtest.analysis.reporting import generate_report


def create_sample_backtest_results(start_date='2018-01-01', end_date='2022-12-31'):
    """创建示例回测结果数据"""
    # 创建日期范围
    dates = pd.date_range(start=start_date, end=end_date, freq='B')
    
    # 创建模拟净值曲线（带有趋势和随机性）
    np.random.seed(42)
    n = len(dates)
    
    # 基础趋势
    trend = np.linspace(0, 0.5, n)
    
    # 随机波动
    noise = 0.001 * np.random.randn(n).cumsum()
    
    # 组合成收益率
    returns = pd.Series(0.0005 + 0.002 * np.random.randn(n) + np.diff(np.concatenate([[0], trend + noise])), index=dates)
    
    # 创建净值曲线
    equity = (1 + returns).cumprod()
    
    # 创建回撤序列
    drawdowns = equity / equity.cummax() - 1
    
    # 创建模拟交易记录
    trades = pd.DataFrame({
        'Entry Time': pd.date_range(start=start_date, end=end_date, freq='W')[:-1],
        'Exit Time': pd.date_range(start=start_date, end=end_date, freq='W')[1:],
        'Entry Price': np.random.uniform(90, 110, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'Exit Price': np.random.uniform(90, 110, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'Size': np.random.randint(1, 10, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'PnL': np.random.uniform(-5, 5, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'Return': np.random.uniform(-0.05, 0.05, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'EntrySignal': np.random.choice(['MA Cross', 'RSI', 'Breakout'], len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'ExitSignal': np.random.choice(['Stop Loss', 'Take Profit', 'Signal Reversal'], len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'Max Adverse Excursion': np.random.uniform(-0.1, 0, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1)
    })
    
    # 创建模拟持仓记录
    positions = pd.DataFrame(index=dates)
    positions['position'] = np.random.randint(-2, 3, len(dates))
    
    # 创建回测结果对象
    class SampleBacktestResults(BacktestResults):
        @property
        def trades(self):
            return trades
        
        @property
        def positions(self):
            return positions
        
        def get_returns(self):
            return returns
        
        def get_drawdowns(self):
            return drawdowns
        
        def equity(self):
            return equity
        
        @property
        def metrics(self):
            return {}
    
    return SampleBacktestResults()


def sample_strategy(param1, param2, data):
    """样本策略函数，用于参数敏感性分析"""
    # 这里只是一个简化的模拟，实际应用中应该是一个完整的策略回测函数
    np.random.seed(42)
    n = len(data)
    
    # 基础表现
    base_performance = 0.5
    
    # 参数1影响 - 假设是移动平均线周期
    param1_effect = -0.1 * (param1 / 50) + 0.2  # 较小的值更好
    
    # 参数2影响 - 假设是止损比例
    param2_effect = 0.1 * np.exp(-((param2 - 0.05) ** 2) / 0.0004)  # 中等值更好
    
    # 模拟随机性
    randomness = 0.05 * np.random.randn()
    
    # 总体表现
    performance = base_performance + param1_effect + param2_effect + randomness
    
    # 创建模拟回测结果
    returns = pd.Series(0.0005 * performance + 0.001 * np.random.randn(n), index=data.index)
    equity = (1 + returns).cumprod()
    drawdowns = equity / equity.cummax() - 1
    
    # 创建简化的回测结果对象
    class SimpleBacktestResults(BacktestResults):
        @property
        def trades(self):
            return pd.DataFrame()
        
        @property
        def positions(self):
            return pd.DataFrame(index=data.index)
        
        def get_returns(self):
            return returns
        
        def get_drawdowns(self):
            return drawdowns
        
        def equity(self):
            return equity
        
        @property
        def metrics(self):
            return {
                'total_return': equity.iloc[-1] / equity.iloc[0] - 1,
                'annualized_return': (equity.iloc[-1] / equity.iloc[0]) ** (252 / n) - 1,
                'sharpe_ratio': returns.mean() / returns.std() * np.sqrt(252),
                'max_drawdown': drawdowns.min()
            }
    
    return SimpleBacktestResults()


def main():
    """主函数"""
    # 创建示例回测结果
    print("创建示例回测结果...")
    results = create_sample_backtest_results()
    
    # 设置输出目录
    output_dir = os.path.join(os.path.dirname(__file__), 'output', 'robustness')
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. 蒙特卡洛模拟
    print("\n1. 执行蒙特卡洛模拟...")
    returns = results.get_returns()
    mc_simulations = monte_carlo_simulation(returns, n_simulations=1000, seed=42)
    
    # 分析模拟结果
    mc_stats = analyze_monte_carlo_results(mc_simulations)
    print("蒙特卡洛模拟统计结果:")
    for key, value in mc_stats.items():
        print(f"  {key}: {value:.4f}")
    
    # 绘制蒙特卡洛模拟结果
    fig1, ax1 = plt.subplots(figsize=(12, 8))
    plot_monte_carlo_simulation(mc_simulations, original_equity=results.equity(), ax=ax1)
    fig1.savefig(os.path.join(output_dir, 'monte_carlo_simulation.png'))
    
    # 2. Bootstrap分析
    print("\n2. 执行Bootstrap分析...")
    bootstrap_results = bootstrap_analysis(returns, n_bootstraps=500, 
                                         metrics=['total_return', 'sharpe_ratio', 'max_drawdown'])
    
    print("Bootstrap统计结果:")
    for metric, stats in bootstrap_results.items():
        print(f"  {metric}:")
        for stat_name, value in stats.items():
            print(f"    {stat_name}: {value:.4f}")
    
    # 绘制Bootstrap分布图
    fig2 = plot_bootstrap_distributions(bootstrap_results)
    fig2.savefig(os.path.join(output_dir, 'bootstrap_distributions.png'))
    
    # 3. 最大不利偏移分析
    print("\n3. 执行最大不利偏移(MAE)分析...")
    # 注：在实际应用中，MAE数据应该从交易过程中提取
    # 这里我们使用trades中的模拟数据
    mae_data = maximum_adverse_excursion(results)
    
    # 绘制MAE分析图
    fig3, ax3 = plt.subplots(figsize=(10, 8))
    plot_mae_analysis(mae_data, ax=ax3)
    fig3.savefig(os.path.join(output_dir, 'mae_analysis.png'))
    
    # 4. 参数敏感性分析
    print("\n4. 执行参数敏感性分析...")
    # 创建一个用于测试的样本数据集
    sample_data = pd.DataFrame(index=pd.date_range(start='2018-01-01', end='2022-12-31', freq='B'))
    
    # 分析参数1的敏感性（例如：移动平均线周期）
    param1_values = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
    sensitivity_results1 = sensitivity_analysis(
        lambda p: sample_strategy(p, 0.05, sample_data),  # 固定参数2为0.05
        'MA Period',
        param1_values,
        metrics=['total_return', 'sharpe_ratio', 'max_drawdown']
    )
    
    # 绘制参数1敏感性分析图
    fig4 = plot_sensitivity_analysis(sensitivity_results1, 'MA Period')
    fig4.savefig(os.path.join(output_dir, 'param1_sensitivity.png'))
    
    # 分析参数2的敏感性（例如：止损比例）
    param2_values = [0.01, 0.02, 0.03, 0.04, 0.05, 0.06, 0.07, 0.08, 0.09, 0.10]
    sensitivity_results2 = sensitivity_analysis(
        lambda p: sample_strategy(50, p, sample_data),  # 固定参数1为50
        'Stop Loss',
        param2_values,
        metrics=['total_return', 'sharpe_ratio', 'max_drawdown']
    )
    
    # 绘制参数2敏感性分析图
    fig5 = plot_sensitivity_analysis(sensitivity_results2, 'Stop Loss')
    fig5.savefig(os.path.join(output_dir, 'param2_sensitivity.png'))
    
    # 5. 生成稳健性分析报告
    print("\n5. 生成HTML报告...")
    report_path = os.path.join(output_dir, 'robustness_analysis.html')
    generate_report(results, output_format='html', output_path=report_path, 
                   strategy_name='稳健性分析示例')
    print(f"报告已保存至: {report_path}")
    
    print("\n分析完成，结果已保存至:", output_dir)


if __name__ == "__main__":
    main() 