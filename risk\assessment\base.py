"""
风险评估基础类

定义风险评估系统的核心接口和基类，包括风险评估器和风险报告基类。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, Callable, Type, Set
from datetime import datetime
import pandas as pd
import numpy as np
import json
import uuid


class RiskMetric(ABC):
    """
    风险指标抽象基类
    
    所有具体风险指标必须继承此类并实现相应方法。
    """
    
    def __init__(self, name: str, description: str = "", **params):
        """
        初始化风险指标
        
        Parameters
        ----------
        name : str
            指标名称
        description : str, optional
            指标描述，默认为空字符串
        **params : dict
            指标计算参数
        """
        self.name = name
        self.description = description
        self.params = params
        self.validate_params()
    
    @abstractmethod
    def validate_params(self) -> None:
        """
        验证参数有效性
        
        每个具体指标类必须实现此方法来验证其特定参数。
        
        Raises
        ------
        ValueError
            如果参数无效
        """
        pass
    
    @abstractmethod
    def calculate(self, data: pd.DataFrame, context: Dict[str, Any] = None) -> float:
        """
        计算风险指标值
        
        Parameters
        ----------
        data : pd.DataFrame
            市场数据或绩效数据
        context : Dict[str, Any], optional
            计算上下文，可包含额外信息，默认为None
        
        Returns
        -------
        float
            计算出的指标值
        """
        pass
    
    def get_normalized_score(self, value: float) -> float:
        """
        将指标原始值转换为0-100的标准化分数
        
        Parameters
        ----------
        value : float
            原始指标值
        
        Returns
        -------
        float
            0-100的标准化分数
        """
        # 默认实现，子类可覆盖以提供更合适的标准化方法
        return max(0, min(100, 50))
    
    def get_risk_direction(self) -> str:
        """
        获取指标的风险方向
        
        Returns
        -------
        str
            'higher' 表示值越高风险越大
            'lower' 表示值越低风险越大
            'neutral' 表示值与风险无直接关系
        """
        # 默认实现，子类必须覆盖
        return "neutral"


class RiskReport:
    """
    风险评估报告类
    
    存储和处理风险评估结果。
    """
    
    def __init__(self, strategy_id: str = None):
        """
        初始化风险报告
        
        Parameters
        ----------
        strategy_id : str, optional
            策略ID，默认为None，将自动生成
        """
        self.strategy_id = strategy_id or str(uuid.uuid4())
        self.timestamp = datetime.now()
        self.risk_score = 0.0
        self.risk_level = "Unknown"
        self.metrics = {}
        self.recommendations = []
        self.historical_scores = []
    
    def update_score(self, score: float, level: str) -> None:
        """
        更新风险评分和等级
        
        Parameters
        ----------
        score : float
            风险评分 (0-100)
        level : str
            风险等级 (Low/Medium/High/Critical)
        """
        self.risk_score = score
        self.risk_level = level
        self.historical_scores.append({
            "date": datetime.now(),
            "score": score
        })
    
    def add_metric(self, name: str, value: float, normalized_score: float = None) -> None:
        """
        添加风险指标结果
        
        Parameters
        ----------
        name : str
            指标名称
        value : float
            原始指标值
        normalized_score : float, optional
            标准化分数 (0-100)，默认为None
        """
        self.metrics[name] = {
            "value": value,
            "normalized_score": normalized_score
        }
    
    def add_recommendation(self, recommendation: str) -> None:
        """
        添加风险缓解建议
        
        Parameters
        ----------
        recommendation : str
            建议内容
        """
        if recommendation not in self.recommendations:
            self.recommendations.append(recommendation)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将报告转换为字典格式
        
        Returns
        -------
        Dict[str, Any]
            报告的字典表示
        """
        return {
            "strategy_id": self.strategy_id,
            "timestamp": self.timestamp.isoformat(),
            "risk_score": self.risk_score,
            "risk_level": self.risk_level,
            "metrics": self.metrics,
            "recommendations": self.recommendations,
            "historical_scores": [
                {"date": score["date"].isoformat(), "score": score["score"]}
                for score in self.historical_scores
            ]
        }
    
    def to_json(self, indent: int = 2) -> str:
        """
        将报告转换为JSON字符串
        
        Parameters
        ----------
        indent : int, optional
            JSON缩进空格数，默认为2
        
        Returns
        -------
        str
            JSON格式的报告
        """
        return json.dumps(self.to_dict(), indent=indent)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RiskReport':
        """
        从字典创建报告对象
        
        Parameters
        ----------
        data : Dict[str, Any]
            报告的字典表示
        
        Returns
        -------
        RiskReport
            报告对象
        """
        report = cls(strategy_id=data.get("strategy_id"))
        
        # 设置时间戳
        if "timestamp" in data:
            report.timestamp = datetime.fromisoformat(data["timestamp"])
        
        # 设置评分和等级
        report.risk_score = data.get("risk_score", 0.0)
        report.risk_level = data.get("risk_level", "Unknown")
        
        # 设置指标
        report.metrics = data.get("metrics", {})
        
        # 设置建议
        report.recommendations = data.get("recommendations", [])
        
        # 设置历史评分
        historical_scores = []
        for score in data.get("historical_scores", []):
            historical_scores.append({
                "date": datetime.fromisoformat(score["date"]),
                "score": score["score"]
            })
        report.historical_scores = historical_scores
        
        return report
    
    @classmethod
    def from_json(cls, json_str: str) -> 'RiskReport':
        """
        从JSON字符串创建报告对象
        
        Parameters
        ----------
        json_str : str
            JSON格式的报告
        
        Returns
        -------
        RiskReport
            报告对象
        """
        return cls.from_dict(json.loads(json_str))


class RiskAssessor:
    """
    风险评估器
    
    用于评估策略的整体风险，生成风险报告。
    """
    
    # 风险等级阈值，可在子类或实例中覆盖
    RISK_LEVELS = {
        "Low": (0, 25),
        "Medium": (25, 50),
        "High": (50, 75),
        "Critical": (75, 100)
    }
    
    def __init__(self):
        """初始化风险评估器"""
        self.metrics: Dict[str, RiskMetric] = {}
        self.metric_weights: Dict[str, float] = {}
        self.recommendation_rules: List[Callable] = []
    
    def add_metric(self, metric: RiskMetric, weight: float = 1.0) -> None:
        """
        添加风险指标
        
        Parameters
        ----------
        metric : RiskMetric
            风险指标对象
        weight : float, optional
            指标权重，用于计算总体风险分数，默认为1.0
        
        Raises
        ------
        ValueError
            如果指标名称已存在
        """
        if metric.name in self.metrics:
            raise ValueError(f"指标'{metric.name}'已存在")
        
        self.metrics[metric.name] = metric
        self.metric_weights[metric.name] = weight
    
    def remove_metric(self, metric_name: str) -> None:
        """
        移除风险指标
        
        Parameters
        ----------
        metric_name : str
            指标名称
        
        Raises
        ------
        KeyError
            如果指标不存在
        """
        if metric_name not in self.metrics:
            raise KeyError(f"指标'{metric_name}'不存在")
        
        del self.metrics[metric_name]
        del self.metric_weights[metric_name]
    
    def add_recommendation_rule(self, rule: Callable[[Dict[str, Any], RiskReport], None]) -> None:
        """
        添加建议生成规则
        
        Parameters
        ----------
        rule : Callable[[Dict[str, Any], RiskReport], None]
            建议生成函数，接受指标结果和报告对象，无返回值
        """
        self.recommendation_rules.append(rule)
    
    def calculate_risk_level(self, score: float) -> str:
        """
        根据分数确定风险等级
        
        Parameters
        ----------
        score : float
            风险评分 (0-100)
        
        Returns
        -------
        str
            风险等级 (Low/Medium/High/Critical)
        """
        for level, (lower, upper) in self.RISK_LEVELS.items():
            if lower <= score < upper:
                return level
        
        # 如果超出范围，返回最高等级
        return "Critical"
    
    def assess(self, data: pd.DataFrame, context: Dict[str, Any] = None) -> RiskReport:
        """
        执行风险评估
        
        Parameters
        ----------
        data : pd.DataFrame
            市场数据或绩效数据
        context : Dict[str, Any], optional
            评估上下文，可包含额外信息，默认为None
        
        Returns
        -------
        RiskReport
            风险评估报告
        """
        if context is None:
            context = {}
        
        # 创建报告
        strategy_id = context.get("strategy_id", None)
        report = RiskReport(strategy_id)
        
        # 计算各项风险指标
        metric_results = {}
        normalized_scores = {}
        total_weight = sum(self.metric_weights.values())
        weighted_score_sum = 0
        
        for name, metric in self.metrics.items():
            # 计算原始指标值
            try:
                value = metric.calculate(data, context)
                metric_results[name] = value
                
                # 计算标准化分数
                normalized_score = metric.get_normalized_score(value)
                normalized_scores[name] = normalized_score
                
                # 添加到报告
                report.add_metric(name, value, normalized_score)
                
                # 计算加权分数
                weight = self.metric_weights[name]
                weighted_score_sum += normalized_score * (weight / total_weight)
            except Exception as e:
                # 记录计算失败
                context["errors"] = context.get("errors", {})
                context["errors"][name] = str(e)
        
        # 设置总体风险评分和等级
        overall_score = round(weighted_score_sum, 2)
        risk_level = self.calculate_risk_level(overall_score)
        report.update_score(overall_score, risk_level)
        
        # 生成建议
        for rule in self.recommendation_rules:
            try:
                rule(metric_results, report)
            except Exception as e:
                # 记录规则应用失败
                context["errors"] = context.get("errors", {})
                context["errors"][f"rule_{self.recommendation_rules.index(rule)}"] = str(e)
        
        return report