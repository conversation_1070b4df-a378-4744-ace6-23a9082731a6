"""
数据操作API

提供数据转换、合并、特征工程等操作的高级接口。
"""

import logging
from typing import List, Dict, Optional, Any, Union, Tuple, Callable

import pandas as pd
import numpy as np

from data.processing.cleaner import (
    fill_missing_values, remove_outliers, remove_duplicates, align_timestamps
)
from data.processing.transformer import (
    normalize_data, calculate_returns, extract_time_features
)
from data.processing.features import (
    calculate_moving_averages, calculate_rsi, calculate_macd, calculate_bollinger_bands
)
from data.utils import merge_dataframes, resample_ohlcv


class DataOperationsAPI:
    """
    数据操作API
    
    提供数据转换、合并、特征工程等操作的高级接口。
    支持数据清洗、转换、特征提取和数据合并等功能。
    """
    
    def __init__(self):
        """初始化数据操作API"""
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def clean_data(self, data: pd.DataFrame, 
                  fill_method: Optional[str] = "ffill",
                  remove_outliers_method: Optional[str] = None,
                  outlier_threshold: float = 3.0,
                  remove_duplicates_flag: bool = True,
                  align_timestamps_flag: bool = True) -> pd.DataFrame:
        """
        清洗数据
        
        Args:
            data: 包含OHLCV数据的DataFrame
            fill_method: 缺失值填充方法 (可选)
            remove_outliers_method: 异常值处理方法 (可选)
            outlier_threshold: 异常值阈值
            remove_duplicates_flag: 是否去除重复数据
            align_timestamps_flag: 是否对齐时间戳
            
        Returns:
            清洗后的DataFrame
        """
        if data.empty:
            return data
        
        result = data.copy()
        
        # 填充缺失值
        if fill_method:
            result = fill_missing_values(result, method=fill_method)
        
        # 处理异常值
        if remove_outliers_method:
            result = remove_outliers(result, method=remove_outliers_method, threshold=outlier_threshold)
        
        # 去除重复数据
        if remove_duplicates_flag:
            result = remove_duplicates(result)
        
        # 对齐时间戳
        if align_timestamps_flag:
            # 对齐时间戳需要一个频率参数
            # 尝试根据数据推断频率
            if isinstance(result.index, pd.DatetimeIndex) and len(result) > 1:
                # 计算相邻时间点的间隔
                time_diffs = result.index.to_series().diff().dropna()
                if not time_diffs.empty:
                    # 找出最常见的时间间隔
                    most_common_diff = time_diffs.value_counts().idxmax()
                    # 将timedelta转换为pandas频率字符串
                    freq = pd.tseries.frequencies.to_offset(most_common_diff)
                    if freq:
                        result = align_timestamps(result, freq=str(freq))
        
        return result
    
    def transform_data(self, data: pd.DataFrame,
                      normalize: bool = False,
                      norm_method: str = "minmax",
                      calculate_returns_flag: bool = False,
                      returns_type: str = "pct_change",
                      extract_time_features_flag: bool = False) -> pd.DataFrame:
        """
        转换数据
        
        Args:
            data: 包含OHLCV数据的DataFrame
            normalize: 是否标准化数据
            norm_method: 标准化方法 (minmax, zscore)
            calculate_returns_flag: 是否计算收益率
            returns_type: 收益率类型 (pct_change, log_return)
            extract_time_features_flag: 是否提取时间特征
            
        Returns:
            转换后的DataFrame
        """
        if data.empty:
            return data
        
        result = data.copy()
        
        # 计算收益率
        if calculate_returns_flag:
            returns = calculate_returns(result, method=returns_type)
            # 将收益率添加到结果中
            for col in returns.columns:
                result[f"{col}_return"] = returns[col]
        
        # 标准化数据
        if normalize:
            # 只标准化数值列，排除日期列和计算出的收益率
            numeric_cols = result.select_dtypes(include=[np.number]).columns.tolist()
            # 排除已经计算的收益率列
            numeric_cols = [col for col in numeric_cols if not col.endswith('_return')]
            
            if numeric_cols:
                normalized = normalize_data(result[numeric_cols], method=norm_method)
                # 将标准化后的数据添加到结果中
                for col in normalized.columns:
                    result[f"{col}_norm"] = normalized[col]
        
        # 提取时间特征
        if extract_time_features_flag:
            time_features = extract_time_features(result)
            # 将时间特征添加到结果中
            for col in time_features.columns:
                result[col] = time_features[col]
        
        return result
    
    def add_technical_indicators(self, data: pd.DataFrame,
                               add_ma: bool = False, ma_periods: List[int] = [5, 10, 20],
                               add_rsi: bool = False, rsi_period: int = 14,
                               add_macd: bool = False, 
                               add_bbands: bool = False, bbands_period: int = 20) -> pd.DataFrame:
        """
        添加技术指标
        
        Args:
            data: 包含OHLCV数据的DataFrame
            add_ma: 是否添加移动平均线
            ma_periods: 移动平均线周期列表
            add_rsi: 是否添加RSI指标
            rsi_period: RSI计算周期
            add_macd: 是否添加MACD指标
            add_bbands: 是否添加布林带指标
            bbands_period: 布林带计算周期
            
        Returns:
            添加技术指标后的DataFrame
        """
        if data.empty:
            return data
        
        result = data.copy()
        
        # 添加移动平均线
        if add_ma:
            ma_data = calculate_moving_averages(result, windows=ma_periods)
            # 将移动平均线数据添加到结果中
            for col in ma_data.columns:
                result[col] = ma_data[col]
        
        # 添加RSI指标
        if add_rsi:
            rsi_data = calculate_rsi(result, window=rsi_period)
            # 将RSI数据添加到结果中
            for col in rsi_data.columns:
                result[col] = rsi_data[col]
        
        # 添加MACD指标
        if add_macd:
            macd_data = calculate_macd(result)
            # 将MACD数据添加到结果中
            for col in macd_data.columns:
                result[col] = macd_data[col]
        
        # 添加布林带指标
        if add_bbands:
            bbands_data = calculate_bollinger_bands(result, window=bbands_period)
            # 将布林带数据添加到结果中
            for col in bbands_data.columns:
                result[col] = bbands_data[col]
        
        return result
    
    def merge_data(self, dataframes: List[pd.DataFrame], 
                  how: str = "outer") -> pd.DataFrame:
        """
        合并多个DataFrame
        
        Args:
            dataframes: DataFrame列表
            how: 合并方式 (outer, inner)
            
        Returns:
            合并后的DataFrame
        """
        if not dataframes:
            return pd.DataFrame()
        
        # 过滤掉空的DataFrame
        valid_dfs = [df for df in dataframes if not df.empty]
        
        if not valid_dfs:
            return pd.DataFrame()
        
        # 如果只有一个有效的DataFrame，直接返回
        if len(valid_dfs) == 1:
            return valid_dfs[0].copy()
        
        # 合并多个DataFrame
        # 由于utils.merge_dataframes不支持how参数，这里做一个适配
        merged_df = merge_dataframes(valid_dfs)
        
        # 如果需要inner join，手动处理
        if how == "inner" and len(valid_dfs) > 1:
            # 获取所有DataFrame索引的交集
            common_index = set(valid_dfs[0].index)
            for df in valid_dfs[1:]:
                common_index = common_index.intersection(set(df.index))
            
            # 只保留共同的索引
            merged_df = merged_df.loc[sorted(common_index)]
        
        return merged_df
    
    def apply_function(self, data: pd.DataFrame, 
                      func: Callable[[pd.DataFrame], pd.DataFrame],
                      columns: Optional[List[str]] = None) -> pd.DataFrame:
        """
        应用自定义函数处理数据
        
        Args:
            data: 包含OHLCV数据的DataFrame
            func: 处理函数，接受DataFrame并返回DataFrame
            columns: 需要处理的列 (可选)
            
        Returns:
            处理后的DataFrame
        """
        if data.empty:
            return data
        
        result = data.copy()
        
        try:
            if columns:
                # 只处理指定的列
                subset = result[columns].copy()
                processed = func(subset)
                
                # 将处理后的结果合并回原始数据
                for col in processed.columns:
                    if col not in columns:
                        # 如果是新列，直接添加
                        result[col] = processed[col]
                    else:
                        # 如果是原有列，替换
                        result[col] = processed[col]
            else:
                # 处理整个DataFrame
                result = func(result)
        except Exception as e:
            self.logger.error(f"应用函数处理数据失败: {e}")
            # 发生错误时返回原始数据
            return data
        
        return result
    
    def resample_data(self, data: pd.DataFrame, 
                     target_timeframe: str,
                     aggregation: Optional[Dict[str, str]] = None) -> pd.DataFrame:
        """
        重采样数据
        
        Args:
            data: 包含OHLCV数据的DataFrame
            target_timeframe: 目标时间周期
            aggregation: 自定义聚合方法 (可选)
            
        Returns:
            重采样后的DataFrame
        """
        if data.empty:
            return data
        
        # 使用默认的OHLCV重采样方法，添加聚合方法支持
        if aggregation:
            # 如果有自定义聚合方法，我们需要手动在pandas.resample中处理
            # 但由于utils.resample_ohlcv不直接支持这个功能，这里做一个适配
            
            # 确保DataFrame索引为时间类型
            if not isinstance(data.index, pd.DatetimeIndex):
                raise ValueError("DataFrame必须以时间为索引")
            
            # 获取pandas格式的时间周期
            import re
            match = re.match(r"^(\d+)([mhdwM])$", target_timeframe)
            if not match:
                raise ValueError(f"无效的时间周期格式: {target_timeframe}")
            
            value, unit = match.group(1), match.group(2)
            pandas_rule = f"{value}{unit}"
            
            # 替换月份单位'M'为'MS'(月初)
            if unit == "M":
                pandas_rule = f"{value}MS"
            
            # 将字符串聚合方法转换为实际的函数或字符串
            agg_dict = {}
            for col, agg_method in aggregation.items():
                if agg_method == 'mean':
                    agg_dict[col] = 'mean'
                elif agg_method == 'sum':
                    agg_dict[col] = 'sum'
                elif agg_method == 'min':
                    agg_dict[col] = 'min'
                elif agg_method == 'max':
                    agg_dict[col] = 'max'
                elif agg_method == 'first':
                    agg_dict[col] = 'first'
                elif agg_method == 'last':
                    agg_dict[col] = 'last'
                elif agg_method == 'count':
                    agg_dict[col] = 'count'
                elif agg_method == 'median':
                    agg_dict[col] = 'median'
                elif agg_method == 'std':
                    agg_dict[col] = 'std'
                elif agg_method == 'var':
                    agg_dict[col] = 'var'
                else:
                    # 默认使用mean
                    agg_dict[col] = 'mean'
            
            # 执行重采样
            resampled = data.resample(pandas_rule).agg(agg_dict)
            return resampled
        else:
            # 使用默认的OHLCV重采样方法
            return resample_ohlcv(data, target_timeframe)
    
    def filter_by_condition(self, data: pd.DataFrame, 
                          condition: Callable[[pd.DataFrame], pd.Series]) -> pd.DataFrame:
        """
        按条件筛选数据
        
        Args:
            data: 包含OHLCV数据的DataFrame
            condition: 筛选条件函数，接受DataFrame并返回布尔Series
            
        Returns:
            筛选后的DataFrame
        """
        if data.empty:
            return data
        
        try:
            mask = condition(data)
            return data[mask].copy()
        except Exception as e:
            self.logger.error(f"按条件筛选数据失败: {e}")
            return data 