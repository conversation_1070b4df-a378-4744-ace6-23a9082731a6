#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
回测报告生成模块

提供回测结果的报告生成功能，包括HTML报告、PDF报告、文本报告等。
"""

from typing import Dict, Any, Union, Optional, List, Tuple
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import io
import base64
from datetime import datetime
import os
import json
import tabulate
import warnings
from pathlib import Path

# 尝试导入PDF生成相关依赖
try:
    import weasyprint
    from jinja2 import Template
    HAS_PDF_SUPPORT = True
except (ImportError, OSError) as e:
    warnings.warn(
        f"WeasyPrint导入失败: {e}\n"
        "PDF报告功能将不可用。如果需要PDF功能，请按照文档安装必要的依赖: "
        "https://doc.courtbouillon.org/weasyprint/stable/first_steps.html#installation"
    )
    HAS_PDF_SUPPORT = False

from ..base import BacktestResults
from .metrics import calculate_metrics
from .visualization import (
    plot_performance,
    plot_equity_curve,
    plot_drawdowns,
    plot_monthly_returns_heatmap,
    plot_trades_distribution,
    plot_rolling_stats,
    plot_underwater
)


def generate_report(results: BacktestResults, benchmark: Optional[pd.Series] = None, 
                  output_format: str = 'html', output_path: Optional[str] = None,
                  strategy_name: str = 'Strategy', **kwargs) -> Union[str, None]:
    """
    生成回测报告
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    benchmark : pd.Series, optional
        基准指数，默认为None
    output_format : str, optional
        输出格式，可选值为'html'、'pdf'和'text'，默认为'html'
    output_path : str, optional
        输出路径，默认为None（返回报告内容）
    strategy_name : str, optional
        策略名称，默认为'Strategy'
    **kwargs : dict
        其他参数
        
    Returns
    -------
    str or None
        如果output_path为None，则返回报告内容，否则返回None
    """
    if output_format.lower() == 'html':
        report = generate_html_report(results, benchmark, strategy_name, **kwargs)
    elif output_format.lower() == 'pdf':
        try:
            generate_pdf_report(results, benchmark, strategy_name, output_path, **kwargs)
            return None  # PDF直接写入文件，不返回内容
        except ImportError as e:
            # 如果缺少weasyprint库，尝试退回到HTML格式
            print(f"Warning: {str(e)}. Falling back to HTML format.")
            report = generate_html_report(results, benchmark, strategy_name, **kwargs)
    elif output_format.lower() == 'text':
        report = generate_text_report(results, benchmark, strategy_name, **kwargs)
    else:
        raise ValueError(f"Unsupported output format: {output_format}, must be 'html', 'pdf' or 'text'")
    
    if output_path is not None:
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path) or '.', exist_ok=True)
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        return None
    else:
        return report


def generate_html_report(results: BacktestResults,
                       benchmark: Optional[pd.Series] = None,
                       strategy_name: str = 'Strategy',
                       **kwargs) -> str:
    """
    生成HTML格式的回测报告
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    benchmark : pd.Series, optional
        基准指数，默认为None
    strategy_name : str, optional
        策略名称，默认为'Strategy'
    **kwargs : dict
        其他参数
        
    Returns
    -------
    str
        HTML报告内容
    """
    # 计算指标
    metrics = calculate_metrics(results, benchmark=benchmark)
    
    # 生成图表
    # 使用内存中的缓冲区保存图片
    performance_img = _fig_to_base64(plot_performance(results, benchmark))
    rolling_stats_img = _fig_to_base64(plot_rolling_stats(results, **kwargs))
    
    # 分类指标
    returns_metrics = {
        'Total Return': f"{metrics['total_return']:.2%}",
        'Annualized Return': f"{metrics['annualized_return']:.2%}",
        'Sharpe Ratio': f"{metrics['daily_sharpe']:.2f}",
        'Sortino Ratio': f"{metrics['daily_sortino']:.2f}",
        'Calmar Ratio': f"{metrics['calmar_ratio']:.2f}",
        'Volatility': f"{metrics['volatility']:.2%}",
    }
    
    drawdown_metrics = {
        'Max Drawdown': f"{metrics['max_drawdown']:.2%}",
        'Average Drawdown': f"{metrics['avg_drawdown']:.2%}",
        'Max Drawdown Duration': f"{metrics['max_drawdown_duration']} days",
    }
    
    # 如果有交易记录，添加交易指标
    trade_metrics = {}
    if 'total_trades' in metrics:
        trade_metrics = {
            'Total Trades': f"{metrics['total_trades']}",
            'Win Rate': f"{metrics['win_rate']:.2%}",
            'Profit Factor': f"{metrics['profit_factor']:.2f}",
            'Average Profit per Trade': f"{metrics['avg_profit_per_trade']:.2f}",
            'Average Win': f"{metrics['avg_win']:.2f}",
            'Average Loss': f"{metrics['avg_loss']:.2f}",
            'Largest Win': f"{metrics['largest_win']:.2f}",
            'Largest Loss': f"{metrics['largest_loss']:.2f}",
            'Average Holding Period': f"{metrics['avg_holding_period']:.1f} days",
            'Expectancy': f"{metrics['expectancy']:.2f}",
        }
    
    # 如果有基准指标，添加基准比较指标
    benchmark_metrics = {}
    if 'alpha' in metrics:
        benchmark_metrics = {
            'Alpha': f"{metrics['alpha']:.2%}",
            'Beta': f"{metrics['beta']:.2f}",
            'R-Squared': f"{metrics['r_squared']:.2f}",
            'Tracking Error': f"{metrics['tracking_error']:.2%}",
            'Information Ratio': f"{metrics['information_ratio']:.2f}",
            'Excess Return': f"{metrics['excess_return']:.2%}",
        }
    
    # 统计性指标
    statistical_metrics = {
        'Skewness': f"{metrics['skew']:.2f}",
        'Kurtosis': f"{metrics['kurtosis']:.2f}",
        'Value at Risk (95%)': f"{metrics['var_95']:.2%}",
        'Conditional VaR (95%)': f"{metrics['cvar_95']:.2%}",
        'Omega Ratio': f"{metrics['omega_ratio']:.2f}",
    }
    
    # 创建HTML报告
    html_template = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{strategy_name} - Backtest Report</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }}
        .header {{
            background-color: #2c3e50;
            color: #fff;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 5px;
        }}
        .header h1 {{
            margin: 0;
            font-size: 24px;
        }}
        .header p {{
            margin: 5px 0 0 0;
            opacity: 0.8;
        }}
        .section {{
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }}
        .section h2 {{
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }}
        .metrics-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }}
        .metric-card {{
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 15px;
        }}
        .metric-card h3 {{
            margin-top: 0;
            font-size: 16px;
            color: #2c3e50;
        }}
        .metrics-table {{
            width: 100%;
            border-collapse: collapse;
        }}
        .metrics-table th {{
            text-align: left;
            padding: 8px;
            background-color: #f2f2f2;
            border-bottom: 1px solid #ddd;
        }}
        .metrics-table td {{
            padding: 8px;
            border-bottom: 1px solid #ddd;
        }}
        .chart-container {{
            margin-top: 20px;
            text-align: center;
        }}
        .chart-container img {{
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        @media (max-width: 768px) {{
            .metrics-grid {{
                grid-template-columns: 1fr;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{strategy_name} - Backtest Report</h1>
            <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>Period: {metrics['start_date'].strftime('%Y-%m-%d')} to {metrics['end_date'].strftime('%Y-%m-%d')} ({metrics['trading_days']} trading days)</p>
        </div>
        
        <div class="section">
            <h2>Performance Summary</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <h3>Returns</h3>
                    <table class="metrics-table">
                        <tbody>
                            {_dict_to_html_table_rows(returns_metrics)}
                        </tbody>
                    </table>
                </div>
                <div class="metric-card">
                    <h3>Drawdowns</h3>
                    <table class="metrics-table">
                        <tbody>
                            {_dict_to_html_table_rows(drawdown_metrics)}
                        </tbody>
                    </table>
                </div>
                {f'''
                <div class="metric-card">
                    <h3>Trading Statistics</h3>
                    <table class="metrics-table">
                        <tbody>
                            {_dict_to_html_table_rows(trade_metrics)}
                        </tbody>
                    </table>
                </div>
                ''' if trade_metrics else ''}
                {f'''
                <div class="metric-card">
                    <h3>Benchmark Comparison</h3>
                    <table class="metrics-table">
                        <tbody>
                            {_dict_to_html_table_rows(benchmark_metrics)}
                        </tbody>
                    </table>
                </div>
                ''' if benchmark_metrics else ''}
                <div class="metric-card">
                    <h3>Statistical Metrics</h3>
                    <table class="metrics-table">
                        <tbody>
                            {_dict_to_html_table_rows(statistical_metrics)}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>Performance Charts</h2>
            <div class="chart-container">
                <img src="data:image/png;base64,{performance_img}" alt="Performance Charts">
            </div>
        </div>
        
        <div class="section">
            <h2>Rolling Statistics</h2>
            <div class="chart-container">
                <img src="data:image/png;base64,{rolling_stats_img}" alt="Rolling Statistics">
            </div>
        </div>
        
        {f'''
        <div class="section">
            <h2>Monthly Returns</h2>
            <table class="metrics-table">
                <thead>
                    <tr>
                        <th>Year</th>
                        <th>Jan</th>
                        <th>Feb</th>
                        <th>Mar</th>
                        <th>Apr</th>
                        <th>May</th>
                        <th>Jun</th>
                        <th>Jul</th>
                        <th>Aug</th>
                        <th>Sep</th>
                        <th>Oct</th>
                        <th>Nov</th>
                        <th>Dec</th>
                        <th>YTD</th>
                    </tr>
                </thead>
                <tbody>
                    {_monthly_returns_to_html_table_rows(metrics.get('monthly_returns', None), metrics.get('yearly_returns', None))}
                </tbody>
            </table>
        </div>
        ''' if 'monthly_returns' in metrics and len(metrics['monthly_returns']) > 0 else ''}
        
        {f'''
        <div class="section">
            <h2>Trade Analysis</h2>
            <div class="chart-container">
                <img src="data:image/png;base64,{_fig_to_base64(plt.figure(figsize=(10, 6), constrained_layout=True), plot_trades_distribution(results, ax=plt.gca()))}" alt="Trade Distribution">
            </div>
            <h3>Trade Statistics</h3>
            <table class="metrics-table">
                <tbody>
                    {_dict_to_html_table_rows(trade_metrics)}
                </tbody>
            </table>
        </div>
        ''' if not results.trades.empty else ''}
        
        <div class="section">
            <h2>About</h2>
            <p>This report was generated using the VectorBT Backtest Engine of AriQuantification.</p>
            <p>© {datetime.now().year} AriQuantification. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
"""
    
    return html_template


def generate_text_report(results: BacktestResults,
                       benchmark: Optional[pd.Series] = None,
                       strategy_name: str = 'Strategy',
                       **kwargs) -> str:
    """
    生成文本格式的回测报告
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    benchmark : pd.Series, optional
        基准指数，默认为None
    strategy_name : str, optional
        策略名称，默认为'Strategy'
    **kwargs : dict
        其他参数
        
    Returns
    -------
    str
        文本报告内容
    """
    # 计算指标
    metrics = calculate_metrics(results, benchmark=benchmark)
    
    # 创建文本报告
    report = []
    
    # 标题和基本信息
    report.append(f"{strategy_name} - Backtest Report")
    report.append(f"Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append(f"Period: {metrics['start_date'].strftime('%Y-%m-%d')} to {metrics['end_date'].strftime('%Y-%m-%d')} ({metrics['trading_days']} trading days)")
    report.append("")
    
    # 性能概览
    report.append("Performance Summary")
    report.append("===================")
    report.append("")
    
    # 回报指标
    report.append("Returns:")
    report.append(f"  Total Return: {metrics['total_return']:.2%}")
    report.append(f"  Annualized Return: {metrics['annualized_return']:.2%}")
    report.append(f"  Sharpe Ratio: {metrics['daily_sharpe']:.2f}")
    report.append(f"  Sortino Ratio: {metrics['daily_sortino']:.2f}")
    report.append(f"  Calmar Ratio: {metrics['calmar_ratio']:.2f}")
    report.append(f"  Volatility: {metrics['volatility']:.2%}")
    report.append("")
    
    # 回撤指标
    report.append("Drawdowns:")
    report.append(f"  Max Drawdown: {metrics['max_drawdown']:.2%}")
    report.append(f"  Average Drawdown: {metrics['avg_drawdown']:.2%}")
    report.append(f"  Max Drawdown Duration: {metrics['max_drawdown_duration']} days")
    report.append("")
    
    # 如果有交易记录，添加交易指标
    if 'total_trades' in metrics:
        report.append("Trading Statistics:")
        report.append(f"  Total Trades: {metrics['total_trades']}")
        report.append(f"  Win Rate: {metrics['win_rate']:.2%}")
        report.append(f"  Profit Factor: {metrics['profit_factor']:.2f}")
        report.append(f"  Average Profit per Trade: {metrics['avg_profit_per_trade']:.2f}")
        report.append(f"  Average Win: {metrics['avg_win']:.2f}")
        report.append(f"  Average Loss: {metrics['avg_loss']:.2f}")
        report.append(f"  Largest Win: {metrics['largest_win']:.2f}")
        report.append(f"  Largest Loss: {metrics['largest_loss']:.2f}")
        report.append(f"  Average Holding Period: {metrics['avg_holding_period']:.1f} days")
        report.append(f"  Expectancy: {metrics['expectancy']:.2f}")
        report.append("")
    
    # 如果有基准指标，添加基准比较指标
    if 'alpha' in metrics:
        report.append("Benchmark Comparison:")
        report.append(f"  Alpha: {metrics['alpha']:.2%}")
        report.append(f"  Beta: {metrics['beta']:.2f}")
        report.append(f"  R-Squared: {metrics['r_squared']:.2f}")
        report.append(f"  Tracking Error: {metrics['tracking_error']:.2%}")
        report.append(f"  Information Ratio: {metrics['information_ratio']:.2f}")
        report.append(f"  Excess Return: {metrics['excess_return']:.2%}")
        report.append("")
    
    # 统计性指标
    report.append("Statistical Metrics:")
    report.append(f"  Skewness: {metrics['skew']:.2f}")
    report.append(f"  Kurtosis: {metrics['kurtosis']:.2f}")
    report.append(f"  Value at Risk (95%): {metrics['var_95']:.2%}")
    report.append(f"  Conditional VaR (95%): {metrics['cvar_95']:.2%}")
    report.append(f"  Omega Ratio: {metrics['omega_ratio']:.2f}")
    report.append("")
    
    # 月度收益
    if 'monthly_returns' in metrics and len(metrics['monthly_returns']) > 0:
        report.append("Monthly Returns:")
        monthly_table = _monthly_returns_to_text_table(
            metrics.get('monthly_returns', None),
            metrics.get('yearly_returns', None)
        )
        report.append(monthly_table)
        report.append("")
    
    # 关于
    report.append("About:")
    report.append("This report was generated using the VectorBT Backtest Engine of AriQuantification.")
    report.append(f"© {datetime.now().year} AriQuantification. All rights reserved.")
    
    return '\n'.join(report)


def _fig_to_base64(fig: plt.Figure, tight: bool = True) -> str:
    """将matplotlib图表转换为base64编码的字符串"""
    buf = io.BytesIO()
    if tight:
        fig.tight_layout()
    fig.savefig(buf, format='png', dpi=100)
    buf.seek(0)
    img_str = base64.b64encode(buf.read()).decode('utf-8')
    plt.close(fig)
    return img_str


def _dict_to_html_table_rows(data: Dict[str, Any]) -> str:
    """将字典转换为HTML表格行"""
    rows = []
    for key, value in data.items():
        rows.append(f"<tr><td>{key}</td><td>{value}</td></tr>")
    return '\n'.join(rows)


def _monthly_returns_to_html_table_rows(monthly_returns: Optional[pd.Series],
                                       yearly_returns: Optional[pd.Series]) -> str:
    """将月度收益转换为HTML表格行"""
    if monthly_returns is None or yearly_returns is None:
        return ""
    
    # 准备月度收益数据
    monthly_data = {}
    for date, value in monthly_returns.items():
        year = date.year
        month = date.month
        if year not in monthly_data:
            monthly_data[year] = {}
        monthly_data[year][month] = value
    
    # 生成HTML行
    rows = []
    for year in sorted(monthly_data.keys()):
        row = [f"<td>{year}</td>"]
        for month in range(1, 13):
            if month in monthly_data[year]:
                value = monthly_data[year][month]
                color = "green" if value > 0 else "red"
                row.append(f"<td style='color: {color};'>{value:.2%}</td>")
            else:
                row.append("<td>-</td>")
        
        # 添加年度收益
        if year in yearly_returns.index:
            ytd_value = yearly_returns[year]
            color = "green" if ytd_value > 0 else "red"
            row.append(f"<td style='color: {color};'>{ytd_value:.2%}</td>")
        else:
            row.append("<td>-</td>")
        
        rows.append("<tr>" + ''.join(row) + "</tr>")
    
    return '\n'.join(rows)


def _monthly_returns_to_text_table(monthly_returns: Optional[pd.Series],
                                  yearly_returns: Optional[pd.Series]) -> str:
    """将月度收益转换为文本表格"""
    if monthly_returns is None or yearly_returns is None:
        return ""
    
    # 准备月度收益数据
    monthly_data = {}
    for date, value in monthly_returns.items():
        year = date.year
        month = date.month
        if year not in monthly_data:
            monthly_data[year] = {}
        monthly_data[year][month] = value
    
    # 生成表格数据
    table_data = []
    headers = ["Year", "Jan", "Feb", "Mar", "Apr", "May", "Jun", 
              "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "YTD"]
    
    for year in sorted(monthly_data.keys()):
        row = [year]
        for month in range(1, 13):
            if month in monthly_data[year]:
                value = monthly_data[year][month]
                row.append(f"{value:.2%}")
            else:
                row.append("-")
        
        # 添加年度收益
        if year in yearly_returns.index:
            ytd_value = yearly_returns[year]
            row.append(f"{ytd_value:.2%}")
        else:
            row.append("-")
        
        table_data.append(row)
    
    # 使用tabulate生成表格
    return tabulate.tabulate(table_data, headers=headers)


def export_metrics_to_json(results: BacktestResults,
                         output_path: str,
                         benchmark: Optional[pd.Series] = None,
                         **kwargs) -> None:
    """
    将回测指标导出为JSON文件
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    output_path : str
        输出文件路径
    benchmark : pd.Series, optional
        基准指数，默认为None
    **kwargs : dict
        其他参数
    """
    # 计算指标
    metrics = calculate_metrics(results, benchmark=benchmark)
    
    # 将datetime对象转换为字符串
    serializable_metrics = {}
    for key, value in metrics.items():
        if isinstance(value, pd.Series):
            if isinstance(value.index[0], pd.Timestamp):
                # 转换为可序列化的字典
                serializable_metrics[key] = {
                    str(date): float(val) for date, val in value.items()
                }
            else:
                serializable_metrics[key] = {
                    str(idx): float(val) for idx, val in value.items()
                }
        elif isinstance(value, pd.Timestamp):
            serializable_metrics[key] = value.strftime('%Y-%m-%d')
        elif isinstance(value, (np.float64, np.float32)):
            serializable_metrics[key] = float(value)
        elif isinstance(value, (np.int64, np.int32)):
            serializable_metrics[key] = int(value)
        else:
            serializable_metrics[key] = value
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path) or '.', exist_ok=True)
    
    # 写入JSON文件
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(serializable_metrics, f, indent=2)


def export_trades_to_csv(results: BacktestResults, output_path: str) -> None:
    """
    将交易记录导出为CSV文件
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    output_path : str
        输出文件路径
    """
    if results.trades.empty:
        return
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path) or '.', exist_ok=True)
    
    # 导出交易记录
    results.trades.to_csv(output_path, index=False)


def export_results_to_csv(results: BacktestResults, output_path: str) -> None:
    """
    将回测结果导出为CSV文件
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    output_path : str
        输出文件路径
    """
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path) or '.', exist_ok=True)
    
    # 创建结果DataFrame
    result_df = pd.DataFrame({
        'equity': results.equity,
        'returns': results.get_returns(),
        'drawdowns': results.get_drawdowns(),
    })
    
    # 如果有持仓信息，添加到结果中
    if not results.positions.empty:
        result_df = pd.concat([
            result_df,
            results.positions.add_prefix('position_')
        ], axis=1)
    
    # 导出结果
    result_df.to_csv(output_path)


def generate_pdf_report(results: BacktestResults,
                      benchmark: Optional[pd.Series] = None,
                      strategy_name: str = 'Strategy',
                      output_path: Optional[str] = None,
                      **kwargs) -> None:
    """
    生成PDF格式的回测报告
    
    Parameters
    ----------
    results : BacktestResults
        回测结果对象
    benchmark : pd.Series, optional
        基准指数，默认为None
    strategy_name : str, optional
        策略名称，默认为'Strategy'
    output_path : str, optional
        输出路径，必须提供以保存PDF文件
    **kwargs : dict
        其他参数
        
    Returns
    -------
    None
    """
    if not HAS_PDF_SUPPORT:
        raise ImportError("PDF report generation requires weasyprint library. "
                         "Please install it with 'pip install weasyprint'")
        
    if output_path is None:
        raise ValueError("Output path must be provided for PDF report generation")
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path) or '.', exist_ok=True)
    
    # 生成HTML报告内容
    html_content = generate_html_report(results, benchmark, strategy_name, **kwargs)
    
    # 使用WeasyPrint将HTML转换为PDF
    pdf = weasyprint.HTML(string=html_content).write_pdf()
    
    # 写入PDF文件
    with open(output_path, 'wb') as f:
        f.write(pdf)
    
    return None 