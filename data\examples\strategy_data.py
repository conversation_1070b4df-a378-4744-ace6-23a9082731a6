"""
策略数据准备示例

展示如何使用数据API准备策略回测所需的数据。
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from data.api.market_data import MarketDataAPI
from data.api.operations import DataOperationsAPI
from data.storage.optimized_storage import OptimizedStorage
from data.sources.ccxt_source import CCXTDataSource, CCXTConfig


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def prepare_moving_average_crossover_data(operations_api: DataOperationsAPI, data: pd.DataFrame) -> pd.DataFrame:
    """
    准备移动平均线交叉策略所需的数据
    
    Args:
        operations_api: 数据操作API
        data: 原始OHLCV数据
        
    Returns:
        添加了技术指标的DataFrame
    """
    # 添加移动平均线
    data_with_indicators = operations_api.add_technical_indicators(
        data,
        add_ma=True,
        ma_periods=[5, 20, 50]
    )
    
    # 计算交叉信号
    data_with_indicators['ma_5_above_20'] = data_with_indicators['ma_5_close'] > data_with_indicators['ma_20_close']
    # 计算金叉和死叉
    data_with_indicators['golden_cross'] = (
        (data_with_indicators['ma_5_above_20'] != data_with_indicators['ma_5_above_20'].shift(1)) & 
        (data_with_indicators['ma_5_above_20'] == True)
    )
    data_with_indicators['death_cross'] = (
        (data_with_indicators['ma_5_above_20'] != data_with_indicators['ma_5_above_20'].shift(1)) & 
        (data_with_indicators['ma_5_above_20'] == False)
    )
    
    return data_with_indicators


def prepare_rsi_strategy_data(operations_api: DataOperationsAPI, data: pd.DataFrame) -> pd.DataFrame:
    """
    准备RSI策略所需的数据
    
    Args:
        operations_api: 数据操作API
        data: 原始OHLCV数据
        
    Returns:
        添加了技术指标的DataFrame
    """
    # 添加RSI指标
    data_with_indicators = operations_api.add_technical_indicators(
        data,
        add_rsi=True,
        rsi_period=14
    )
    
    # 计算RSI交易信号
    data_with_indicators['rsi_oversold'] = data_with_indicators['rsi_14_close'] < 30
    data_with_indicators['rsi_overbought'] = data_with_indicators['rsi_14_close'] > 70
    
    # 计算买入和卖出信号
    data_with_indicators['rsi_buy_signal'] = (
        (data_with_indicators['rsi_oversold'] != data_with_indicators['rsi_oversold'].shift(1)) & 
        (data_with_indicators['rsi_oversold'] == False) &
        (data_with_indicators['rsi_14_close'].shift(1) < 30)
    )
    data_with_indicators['rsi_sell_signal'] = (
        (data_with_indicators['rsi_overbought'] != data_with_indicators['rsi_overbought'].shift(1)) & 
        (data_with_indicators['rsi_overbought'] == False) &
        (data_with_indicators['rsi_14_close'].shift(1) > 70)
    )
    
    return data_with_indicators


def prepare_bollinger_band_strategy_data(operations_api: DataOperationsAPI, data: pd.DataFrame) -> pd.DataFrame:
    """
    准备布林带策略所需的数据
    
    Args:
        operations_api: 数据操作API
        data: 原始OHLCV数据
        
    Returns:
        添加了技术指标的DataFrame
    """
    # 添加布林带指标
    data_with_indicators = operations_api.add_technical_indicators(
        data,
        add_bbands=True,
        bbands_period=20
    )
    
    # 计算价格与布林带的关系
    data_with_indicators['price_below_lower'] = data_with_indicators['close'] < data_with_indicators['bbands_lower_close']
    data_with_indicators['price_above_upper'] = data_with_indicators['close'] > data_with_indicators['bbands_upper_close']
    
    # 计算买入和卖出信号
    data_with_indicators['bbands_buy_signal'] = (
        (data_with_indicators['price_below_lower'] != data_with_indicators['price_below_lower'].shift(1)) & 
        (data_with_indicators['price_below_lower'] == False) &
        (data_with_indicators['close'].shift(1) < data_with_indicators['bbands_lower_close'].shift(1))
    )
    data_with_indicators['bbands_sell_signal'] = (
        (data_with_indicators['price_above_upper'] != data_with_indicators['price_above_upper'].shift(1)) & 
        (data_with_indicators['price_above_upper'] == False) &
        (data_with_indicators['close'].shift(1) > data_with_indicators['bbands_upper_close'].shift(1))
    )
    
    return data_with_indicators


def visualize_strategy_signals(data: pd.DataFrame, symbol: str, strategy_name: str):
    """
    可视化策略信号
    
    Args:
        data: 包含策略信号的DataFrame
        symbol: 交易对
        strategy_name: 策略名称
    """
    plt.figure(figsize=(12, 8))
    
    if strategy_name == 'ma_crossover':
        # 绘制价格和移动平均线
        plt.subplot(2, 1, 1)
        plt.plot(data.index, data['close'], label='价格')
        plt.plot(data.index, data['ma_5_close'], label='MA5')
        plt.plot(data.index, data['ma_20_close'], label='MA20')
        
        # 标记金叉和死叉
        golden_cross_points = data[data['golden_cross']]
        death_cross_points = data[data['death_cross']]
        
        plt.scatter(golden_cross_points.index, golden_cross_points['close'], 
                   color='green', marker='^', s=100, label='金叉')
        plt.scatter(death_cross_points.index, death_cross_points['close'], 
                   color='red', marker='v', s=100, label='死叉')
        
        plt.title(f"{symbol} 移动平均线交叉策略")
        plt.legend()
        plt.grid(True)
        
    elif strategy_name == 'rsi':
        # 绘制价格
        plt.subplot(2, 1, 1)
        plt.plot(data.index, data['close'], label='价格')
        
        # 标记RSI买入和卖出信号
        buy_signal_points = data[data['rsi_buy_signal']]
        sell_signal_points = data[data['rsi_sell_signal']]
        
        plt.scatter(buy_signal_points.index, buy_signal_points['close'], 
                   color='green', marker='^', s=100, label='买入信号')
        plt.scatter(sell_signal_points.index, sell_signal_points['close'], 
                   color='red', marker='v', s=100, label='卖出信号')
        
        plt.title(f"{symbol} RSI策略")
        plt.legend()
        plt.grid(True)
        
        # 绘制RSI指标
        plt.subplot(2, 1, 2)
        plt.plot(data.index, data['rsi_14_close'], label='RSI(14)')
        plt.axhline(y=70, color='r', linestyle='--')
        plt.axhline(y=30, color='g', linestyle='--')
        plt.title("RSI指标")
        plt.legend()
        plt.grid(True)
        
    elif strategy_name == 'bbands':
        # 绘制价格和布林带
        plt.plot(data.index, data['close'], label='价格')
        plt.plot(data.index, data['bbands_upper_close'], label='上轨', color='red', linestyle='--')
        plt.plot(data.index, data['bbands_middle_close'], label='中轨', color='blue', linestyle='--')
        plt.plot(data.index, data['bbands_lower_close'], label='下轨', color='green', linestyle='--')
        
        # 标记布林带买入和卖出信号
        buy_signal_points = data[data['bbands_buy_signal']]
        sell_signal_points = data[data['bbands_sell_signal']]
        
        plt.scatter(buy_signal_points.index, buy_signal_points['close'], 
                   color='green', marker='^', s=100, label='买入信号')
        plt.scatter(sell_signal_points.index, sell_signal_points['close'], 
                   color='red', marker='v', s=100, label='卖出信号')
        
        plt.title(f"{symbol} 布林带策略")
        plt.legend()
        plt.grid(True)
    
    plt.tight_layout()
    output_path = os.path.join(os.path.dirname(__file__), f'{strategy_name}_signals.png')
    plt.savefig(output_path)
    print(f"策略信号图已保存到: {output_path}")


def export_strategy_data(data: pd.DataFrame, strategy_name: str):
    """
    导出策略数据
    
    Args:
        data: 包含策略信号的DataFrame
        strategy_name: 策略名称
    """
    # 导出CSV
    output_path = os.path.join(os.path.dirname(__file__), f'{strategy_name}_data.csv')
    data.to_csv(output_path)
    print(f"策略数据已导出到CSV: {output_path}")
    
    # 导出Excel
    output_path = os.path.join(os.path.dirname(__file__), f'{strategy_name}_data.xlsx')
    data.to_excel(output_path)
    print(f"策略数据已导出到Excel: {output_path}")


def main():
    """主函数"""
    setup_logging()
    
    # 创建存储对象
    storage_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../data/storage/data'))
    os.makedirs(storage_path, exist_ok=True)
    storage = OptimizedStorage(storage_path)
    
    # 创建数据源
    exchange_id = 'binance'
    ccxt_config = CCXTConfig(exchange_id=exchange_id)
    ccxt_source = CCXTDataSource(ccxt_config)
    
    # 创建API对象
    market_api = MarketDataAPI(storage)
    operations_api = DataOperationsAPI()
    
    # 注册数据源
    market_api.register_data_source('binance', ccxt_source, is_default=True)
    
    # 设置参数
    symbol = 'BTC/USDT'
    timeframe = '1d'
    end_time = datetime.now()
    start_time = end_time - timedelta(days=180)  # 获取半年数据
    
    print(f"获取 {symbol} {timeframe} 数据，从 {start_time} 到 {end_time}")
    data = market_api.get_data(symbol, timeframe, start_time, end_time)
    
    if data.empty:
        print("未获取到数据，无法准备策略数据")
        return
    
    print(f"获取到 {len(data)} 条数据")
    
    # 数据清洗
    data = operations_api.clean_data(data, fill_method="ffill")
    
    # 准备移动平均线交叉策略数据
    print("\n===== 准备移动平均线交叉策略数据 =====")
    ma_crossover_data = prepare_moving_average_crossover_data(operations_api, data)
    visualize_strategy_signals(ma_crossover_data, symbol, 'ma_crossover')
    export_strategy_data(ma_crossover_data, 'ma_crossover')
    
    # 准备RSI策略数据
    print("\n===== 准备RSI策略数据 =====")
    rsi_data = prepare_rsi_strategy_data(operations_api, data)
    visualize_strategy_signals(rsi_data, symbol, 'rsi')
    export_strategy_data(rsi_data, 'rsi')
    
    # 准备布林带策略数据
    print("\n===== 准备布林带策略数据 =====")
    bbands_data = prepare_bollinger_band_strategy_data(operations_api, data)
    visualize_strategy_signals(bbands_data, symbol, 'bbands')
    export_strategy_data(bbands_data, 'bbands')


if __name__ == "__main__":
    main() 