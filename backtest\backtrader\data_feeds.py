#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Backtrader数据源连接器

提供从数据模块到Backtrader数据源的转换和适配功能。
"""

import backtrader as bt
import pandas as pd
import numpy as np
from typing import Dict, Any, Union, Optional, List, Tuple
from datetime import datetime

from data.base import DataSource
from data.structures import OHLCV

class PandasDataFeed(bt.feeds.PandasData):
    """
    基于Pandas的Backtrader数据源
    
    扩展Backtrader的PandasData类，支持自定义列映射和数据处理。
    """
    
    # 设置OHLCV列的默认映射
    params = (
        ('datetime', None),  # 使用索引作为日期时间
        ('open', 'open'),
        ('high', 'high'),
        ('low', 'low'),
        ('close', 'close'),
        ('volume', 'volume'),
        ('openinterest', None),
    )
    
    @staticmethod
    def validate_dataframe(dataframe):
        """
        检查DataFrame是否符合要求
        
        Parameters
        ----------
        dataframe : pd.DataFrame
            要检查的数据
            
        Raises
        ------
        ValueError
            如果数据不符合要求
        """
        # 检查是否为DataFrame
        if not isinstance(dataframe, pd.DataFrame):
            raise ValueError("数据必须为pandas DataFrame格式")
        
        # 检查是否有索引
        if dataframe.index.empty:
            raise ValueError("DataFrame必须有索引")
        
        # 检查是否有日期索引
        if not isinstance(dataframe.index, pd.DatetimeIndex):
            raise ValueError("DataFrame必须有DatetimeIndex类型的索引")


class DataSourceFeed:
    """
    数据源适配器
    
    将项目的DataSource转换为Backtrader可用的数据源。
    """
    
    @staticmethod
    def from_data_source(data_source: DataSource, start_date: Optional[datetime] = None, 
                         end_date: Optional[datetime] = None, timeframe: str = '1d') -> bt.feeds.DataBase:
        """
        从DataSource创建Backtrader数据源
        
        Parameters
        ----------
        data_source : DataSource
            数据源对象
        start_date : datetime, optional
            开始日期，默认为None表示使用所有可用数据
        end_date : datetime, optional
            结束日期，默认为None表示使用所有可用数据
        timeframe : str, optional
            时间周期，默认为'1d'（日线）
            
        Returns
        -------
        bt.feeds.DataBase
            Backtrader数据源
        """
        # 提取OHLCV数据
        ohlcv_data = data_source.get_ohlcv(start_date=start_date, end_date=end_date, timeframe=timeframe)
        
        # 转换为DataFrame
        df = ohlcv_data.to_dataframe()
        
        # 验证数据
        PandasDataFeed.validate_dataframe(df)
        
        # 创建Backtrader数据源
        bt_data = PandasDataFeed(dataname=df)
        
        return bt_data
    
    @staticmethod
    def from_ohlcv(ohlcv: OHLCV) -> bt.feeds.DataBase:
        """
        从OHLCV对象创建Backtrader数据源
        
        Parameters
        ----------
        ohlcv : OHLCV
            OHLCV数据对象
            
        Returns
        -------
        bt.feeds.DataBase
            Backtrader数据源
        """
        # 转换为DataFrame
        df = ohlcv.to_dataframe()
        
        # 验证数据
        PandasDataFeed.validate_dataframe(df)
        
        # 创建Backtrader数据源
        bt_data = PandasDataFeed(dataname=df)
        
        return bt_data
    
    @staticmethod
    def from_dataframe(dataframe: pd.DataFrame, **kwargs) -> bt.feeds.DataBase:
        """
        从DataFrame创建Backtrader数据源
        
        Parameters
        ----------
        dataframe : pd.DataFrame
            包含OHLCV数据的DataFrame
        **kwargs : dict
            可选参数，包括列映射
            
        Returns
        -------
        bt.feeds.DataBase
            Backtrader数据源
        """
        # 验证数据
        PandasDataFeed.validate_dataframe(dataframe)
        
        # 创建Backtrader数据源
        params = {**kwargs, 'dataname': dataframe}
        bt_data = PandasDataFeed(**params)
        
        return bt_data
