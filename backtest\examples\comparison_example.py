#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
比较分析示例脚本

展示如何比较多个策略、不同参数和不同时间段的回测结果。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from backtest.base import BacktestResults
from backtest.analysis.comparison import (
    compare_strategies,
    compare_with_benchmark,
    plot_strategy_comparison,
    plot_benchmark_comparison,
    plot_parameter_comparison,
    plot_period_comparison,
    create_comparison_table,
    plot_correlation_matrix
)
from backtest.analysis.reporting import generate_report


def create_sample_strategy_results(name, start_date='2018-01-01', end_date='2022-12-31', 
                                 performance_factor=1.0, volatility_factor=1.0, seed=None):
    """创建示例策略回测结果"""
    # 设置随机种子
    if seed is not None:
        np.random.seed(seed)
    
    # 创建日期范围
    dates = pd.date_range(start=start_date, end=end_date, freq='B')
    n = len(dates)
    
    # 创建模拟净值曲线
    # 基础趋势
    trend = np.linspace(0, 0.5 * performance_factor, n)
    
    # 随机波动
    noise = 0.001 * volatility_factor * np.random.randn(n).cumsum()
    
    # 组合成收益率
    returns = pd.Series(0.0005 * performance_factor + 
                       0.002 * volatility_factor * np.random.randn(n) + 
                       np.diff(np.concatenate([[0], trend + noise])), 
                       index=dates)
    
    # 创建净值曲线
    equity = (1 + returns).cumprod()
    
    # 创建回撤序列
    drawdowns = equity / equity.cummax() - 1
    
    # 创建模拟交易记录
    trades = pd.DataFrame({
        'Entry Time': pd.date_range(start=start_date, end=end_date, freq='W')[:-1],
        'Exit Time': pd.date_range(start=start_date, end=end_date, freq='W')[1:],
        'Entry Price': np.random.uniform(90, 110, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'Exit Price': np.random.uniform(90, 110, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'Size': np.random.randint(1, 10, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'PnL': np.random.uniform(-5, 5, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1),
        'Return': np.random.uniform(-0.05, 0.05, len(pd.date_range(start=start_date, end=end_date, freq='W')) - 1)
    })
    
    # 创建模拟持仓记录
    positions = pd.DataFrame(index=dates)
    positions['position'] = np.random.randint(-2, 3, len(dates))
    
    # 创建回测结果对象
    class SampleBacktestResults(BacktestResults):
        @property
        def name(self):
            return name
        
        @property
        def trades(self):
            return trades
        
        @property
        def positions(self):
            return positions
        
        def get_returns(self):
            return returns
        
        def get_drawdowns(self):
            return drawdowns
        
        def equity(self):
            return equity
        
        @property
        def metrics(self):
            return {}
    
    return SampleBacktestResults()


def create_sample_parameter_results(param_values, start_date='2018-01-01', end_date='2022-12-31'):
    """创建不同参数的示例回测结果"""
    results_dict = {}
    
    for i, param in enumerate(param_values):
        # 参数影响 - 假设是移动平均线周期
        # 较小的值表现更好，但波动性更大
        performance_factor = 1.2 - 0.5 * (param / 100)  # 较小的值性能更好
        volatility_factor = 0.8 + 0.4 * (param / 100)   # 较小的值波动性更大
        
        # 创建结果对象
        results = create_sample_strategy_results(
            f"Param_{param}", 
            start_date, 
            end_date, 
            performance_factor, 
            volatility_factor,
            seed=42+i
        )
        
        results_dict[param] = results
    
    return results_dict


def main():
    """主函数"""
    # 设置输出目录
    output_dir = os.path.join(os.path.dirname(__file__), 'output', 'comparison')
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. 创建多个策略的回测结果
    print("1. 创建多个策略的回测结果...")
    strategy1 = create_sample_strategy_results("Moving Average", 
                                             performance_factor=1.0, 
                                             volatility_factor=1.0, 
                                             seed=42)
    
    strategy2 = create_sample_strategy_results("RSI", 
                                             performance_factor=1.2, 
                                             volatility_factor=1.3, 
                                             seed=43)
    
    strategy3 = create_sample_strategy_results("Breakout", 
                                             performance_factor=0.8, 
                                             volatility_factor=0.7, 
                                             seed=44)
    
    # 创建基准指数
    benchmark = create_sample_strategy_results("Benchmark", 
                                             performance_factor=0.7, 
                                             volatility_factor=1.1, 
                                             seed=45)
    benchmark_returns = benchmark.get_returns()
    
    # 2. 比较多个策略
    print("\n2. 比较多个策略...")
    strategies = {
        "Moving Average": strategy1,
        "RSI": strategy2,
        "Breakout": strategy3
    }
    
    # 执行策略比较
    comparison_results = compare_strategies(strategies, benchmark_returns)
    
    # 打印比较结果
    print("策略比较结果:")
    print(comparison_results[['total_return', 'annualized_return', 'sharpe_ratio', 
                             'max_drawdown', 'volatility', 'win_rate', 'profit_factor']])
    
    # 绘制策略比较图
    fig_dict = plot_strategy_comparison(strategies)
    # 保存返回字典中的第一个图表
    for name, fig in fig_dict.items():
        fig.savefig(os.path.join(output_dir, f'strategy_comparison_{name}.png'))
        plt.close(fig)
    
    # 3. 与基准对比
    print("\n3. 与基准对比...")
    benchmark_comparison = compare_with_benchmark(strategy1, benchmark_returns)
    
    # 打印基准对比结果
    print("与基准对比结果:")
    for key, value in benchmark_comparison.items():
        print(f"  {key}: {value}")
    
    # 绘制基准对比图
    fig2 = plot_benchmark_comparison(strategy1, benchmark)
    fig2.savefig(os.path.join(output_dir, 'benchmark_comparison.png'))
    
    # 4. 参数比较
    print("\n4. 参数比较...")
    # 创建不同参数的回测结果
    param_values = [20, 40, 60, 80, 100]
    param_results = create_sample_parameter_results(param_values)
    
    # 绘制参数比较图
    fig3 = plot_parameter_comparison(param_results, param_name='MA Period')
    fig3.savefig(os.path.join(output_dir, 'parameter_comparison.png'))
    
    # 5. 不同时间段比较
    print("\n5. 不同时间段比较...")
    # 定义时间段
    full_period = ('2018-01-01', '2022-12-31')
    bull_market = ('2019-01-01', '2019-12-31')
    bear_market = ('2020-01-01', '2020-06-30')
    recovery = ('2020-07-01', '2021-12-31')
    recent = ('2022-01-01', '2022-12-31')
    
    periods = {
        'Full Period': full_period,
        'Bull Market': bull_market,
        'Bear Market': bear_market,
        'Recovery': recovery,
        'Recent': recent
    }
    
    # 绘制不同时间段比较图
    fig4 = plot_period_comparison(strategy1, periods)
    fig4.savefig(os.path.join(output_dir, 'period_comparison.png'))
    
    # 6. 创建比较表格
    print("\n6. 创建比较表格...")
    comparison_table = create_comparison_table(strategies, benchmark_returns)
    
    # 将表格保存为CSV
    comparison_table.to_csv(os.path.join(output_dir, 'strategy_comparison.csv'))
    print(f"比较表格已保存至: {os.path.join(output_dir, 'strategy_comparison.csv')}")
    
    # 7. 绘制相关性矩阵
    print("\n7. 绘制相关性矩阵...")
    # 收集所有策略的收益率
    returns_dict = {
        "Moving Average": strategy1.get_returns(),
        "RSI": strategy2.get_returns(),
        "Breakout": strategy3.get_returns(),
        "Benchmark": benchmark_returns
    }
    
    fig5 = plot_correlation_matrix(returns_dict)
    fig5.savefig(os.path.join(output_dir, 'correlation_matrix.png'))
    
    # 8. 生成HTML报告
    print("\n8. 生成HTML报告...")
    report_path = os.path.join(output_dir, 'comparison_report.html')
    generate_report(strategy1, benchmark=benchmark_returns, output_format='html', 
                   output_path=report_path, strategy_name='策略比较分析')
    print(f"报告已保存至: {report_path}")
    
    print("\n分析完成，结果已保存至:", output_dir)


if __name__ == "__main__":
    main() 