 #!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于遗传算法的Backtrader策略参数优化器

通过遗传算法寻找策略参数的最优组合，适用于参数空间较大的情况。
"""

import backtrader as bt
import pandas as pd
import numpy as np
from typing import Dict, Any, Union, Optional, List, Tuple, Callable
import random
import time
import logging
from functools import partial
import multiprocessing as mp

from ..base import Strategy, BacktestResults
from . import core

logger = logging.getLogger(__name__)

class GeneticOptimizer:
    """
    基于遗传算法的参数优化器
    
    通过模拟自然选择和进化过程，寻找策略参数的最优组合。
    """
    
    def __init__(self, engine: core.BacktraderEngine, strategy_class, data: pd.DataFrame,
                 metric: str = 'sharpe_ratio', maximize: bool = True, **engine_kwargs):
        """
        初始化遗传算法优化器
        
        Parameters
        ----------
        engine : core.BacktraderEngine
            Backtrader引擎实例
        strategy_class : type
            策略类（非实例）
        data : pd.DataFrame
            回测数据
        metric : str, optional
            优化目标指标，默认为'sharpe_ratio'
        maximize : bool, optional
            是否最大化指标，默认为True
        **engine_kwargs : dict
            Backtrader引擎参数
        """
        self.engine = engine
        self.strategy_class = strategy_class
        self.data = data
        self.metric = metric
        self.maximize = maximize
        self.engine_kwargs = engine_kwargs
        self.results = None
        self.best_individual = None
        self.population_history = []
        self.fitness_history = []
        
    def optimize(self, param_ranges: Dict[str, Tuple], 
                population_size: int = 20, 
                generations: int = 10, 
                mutation_rate: float = 0.1,
                crossover_rate: float = 0.7,
                elite_size: int = 2,
                n_jobs: int = 1) -> pd.DataFrame:
        """
        运行遗传算法优化
        
        Parameters
        ----------
        param_ranges : dict
            参数范围，格式为 {'param_name': (min_value, max_value, type)}
            type可以是'int'或'float'
        population_size : int, optional
            种群大小，默认为20
        generations : int, optional
            迭代代数，默认为10
        mutation_rate : float, optional
            变异率，默认为0.1
        crossover_rate : float, optional
            交叉率，默认为0.7
        elite_size : int, optional
            精英数量，直接保留到下一代，默认为2
        n_jobs : int, optional
            并行任务数，默认为1
            
        Returns
        -------
        pd.DataFrame
            优化结果，包含参数和性能指标
        """
        logger.info(f"开始遗传算法优化，种群大小:{population_size}，代数:{generations}")
        
        # 初始化参数范围和类型
        self.param_ranges = param_ranges
        self.param_names = list(param_ranges.keys())
        
        # 初始化种群
        population = self._initialize_population(population_size)
        
        # 创建评估函数
        eval_func = partial(self._evaluate_individual)
        
        # 遗传算法主循环
        start_time = time.time()
        
        for gen in range(generations):
            gen_start_time = time.time()
            
            # 评估种群适应度
            if n_jobs > 1:
                # 并行评估
                with mp.Pool(n_jobs) as pool:
                    fitnesses = pool.map(eval_func, population)
            else:
                # 串行评估
                fitnesses = [eval_func(individual) for individual in population]
            
            # 记录种群和适应度历史
            self.population_history.append(population.copy())
            self.fitness_history.append(fitnesses.copy())
            
            # 找出最佳个体
            best_idx = max(range(len(fitnesses)), key=lambda i: fitnesses[i]) if self.maximize else min(range(len(fitnesses)), key=lambda i: fitnesses[i])
            best_fitness = fitnesses[best_idx]
            best_individual = population[best_idx]
            
            if self.best_individual is None or (self.maximize and best_fitness > self.best_fitness) or (not self.maximize and best_fitness < self.best_fitness):
                self.best_individual = best_individual.copy()
                self.best_fitness = best_fitness
            
            gen_time = time.time() - gen_start_time
            logger.info(f"第{gen+1}代完成，最佳适应度:{best_fitness:.4f}，耗时:{gen_time:.2f}秒")
            
            # 如果是最后一代，则不需要再进化
            if gen == generations - 1:
                break
                
            # 选择、交叉和变异，产生新一代
            population = self._evolve_population(population, fitnesses, elite_size, crossover_rate, mutation_rate)
        
        # 计算总耗时
        duration = time.time() - start_time
        logger.info(f"优化完成，共{generations}代，总耗时:{duration:.2f}秒")
        
        # 生成完整的优化结果
        results = self._generate_results()
        self.results = results
        
        return results
    
    def _initialize_population(self, population_size: int) -> List[Dict[str, Any]]:
        """初始化种群"""
        population = []
        
        for _ in range(population_size):
            # 随机生成个体
            individual = {}
            
            for param_name, (min_val, max_val, param_type) in self.param_ranges.items():
                if param_type == 'int':
                    individual[param_name] = random.randint(min_val, max_val)
                else:  # 'float'
                    individual[param_name] = min_val + random.random() * (max_val - min_val)
            
            population.append(individual)
            
        return population
    
    def _evaluate_individual(self, individual: Dict[str, Any]) -> float:
        """评估个体适应度"""
        try:
            # 创建策略实例
            strategy = self.strategy_class(**individual)
            
            # 创建新的Backtrader引擎实例
            bt_engine = core.BacktraderEngine(self.data, **self.engine_kwargs)
            
            # 运行回测
            results = bt_engine.run(strategy)
            
            # 获取性能指标
            fitness = results.metrics.get(self.metric, float('-inf') if self.maximize else float('inf'))
            
            return fitness
            
        except Exception as e:
            logger.error(f"评估个体 {individual} 时出错: {e}")
            return float('-inf') if self.maximize else float('inf')
    
    def _select_parent(self, population: List[Dict[str, Any]], fitnesses: List[float]) -> Dict[str, Any]:
        """使用锦标赛选择法选择父代"""
        # 随机选择几个个体进行比较
        tournament_size = 3
        tournament_indices = random.sample(range(len(population)), min(tournament_size, len(population)))
        
        # 选择最佳适应度的个体
        if self.maximize:
            best_idx = max(tournament_indices, key=lambda i: fitnesses[i])
        else:
            best_idx = min(tournament_indices, key=lambda i: fitnesses[i])
            
        return population[best_idx].copy()
    
    def _crossover(self, parent1: Dict[str, Any], parent2: Dict[str, Any]) -> Dict[str, Any]:
        """交叉操作，生成子代"""
        child = {}
        
        # 随机决定每个参数从哪个父代继承
        for param_name in self.param_names:
            if random.random() < 0.5:
                child[param_name] = parent1[param_name]
            else:
                child[param_name] = parent2[param_name]
                
        return child
    
    def _mutate(self, individual: Dict[str, Any], mutation_rate: float) -> Dict[str, Any]:
        """变异操作"""
        for param_name, (min_val, max_val, param_type) in self.param_ranges.items():
            if random.random() < mutation_rate:
                # 对该参数进行变异
                if param_type == 'int':
                    individual[param_name] = random.randint(min_val, max_val)
                else:  # 'float'
                    individual[param_name] = min_val + random.random() * (max_val - min_val)
                    
        return individual
    
    def _evolve_population(self, population: List[Dict[str, Any]], fitnesses: List[float],
                          elite_size: int, crossover_rate: float, mutation_rate: float) -> List[Dict[str, Any]]:
        """进化种群到下一代"""
        # 计算种群大小
        pop_size = len(population)
        
        # 保留精英
        elite_indices = sorted(range(pop_size), key=lambda i: fitnesses[i], reverse=self.maximize)[:elite_size]
        new_population = [population[i].copy() for i in elite_indices]
        
        # 生成剩余个体
        while len(new_population) < pop_size:
            # 选择父代
            parent1 = self._select_parent(population, fitnesses)
            parent2 = self._select_parent(population, fitnesses)
            
            # 交叉
            if random.random() < crossover_rate:
                child = self._crossover(parent1, parent2)
            else:
                child = parent1.copy()  # 直接继承父代1
                
            # 变异
            child = self._mutate(child, mutation_rate)
            
            # 添加到新种群
            new_population.append(child)
            
        return new_population
    
    def _generate_results(self) -> pd.DataFrame:
        """生成优化结果DataFrame"""
        # 评估所有历史中的最佳个体
        all_individuals = []
        for gen, (population, fitnesses) in enumerate(zip(self.population_history, self.fitness_history)):
            for i, (individual, fitness) in enumerate(zip(population, fitnesses)):
                result = {
                    'generation': gen + 1,
                    'individual': i + 1,
                    self.metric: fitness,
                    **individual
                }
                all_individuals.append(result)
                
        # 转换为DataFrame并排序
        results_df = pd.DataFrame(all_individuals)
        if self.maximize:
            results_df = results_df.sort_values(by=self.metric, ascending=False)
        else:
            results_df = results_df.sort_values(by=self.metric, ascending=True)
            
        return results_df
    
    def get_best_params(self) -> Dict[str, Any]:
        """获取最佳参数组合"""
        if self.best_individual is None:
            raise ValueError("请先运行优化")
            
        return self.best_individual.copy()
    
    def plot_evolution(self):
        """绘制进化过程图"""
        if not self.fitness_history:
            raise ValueError("请先运行优化")
            
        import matplotlib.pyplot as plt
        
        # 计算每代的最佳、平均和最差适应度
        generations = range(1, len(self.fitness_history) + 1)
        best_fitnesses = []
        avg_fitnesses = []
        worst_fitnesses = []
        
        for fitnesses in self.fitness_history:
            if self.maximize:
                best_fitnesses.append(max(fitnesses))
                worst_fitnesses.append(min(fitnesses))
            else:
                best_fitnesses.append(min(fitnesses))
                worst_fitnesses.append(max(fitnesses))
                
            avg_fitnesses.append(sum(fitnesses) / len(fitnesses))
        
        # 绘制适应度进化图
        plt.figure(figsize=(12, 6))
        plt.plot(generations, best_fitnesses, 'o-', label='Best Fitness')
        plt.plot(generations, avg_fitnesses, 's-', label='Average Fitness')
        plt.plot(generations, worst_fitnesses, '^-', label='Worst Fitness')
        plt.xlabel('Generation')
        plt.ylabel(self.metric)
        plt.title('Fitness Evolution')
        plt.legend()
        plt.grid(True)
        plt.show()
        
        # 绘制参数分布图
        param_values = {param: [] for param in self.param_names}
        for gen, population in enumerate(self.population_history):
            for individual in population:
                for param, value in individual.items():
                    param_values[param].append((gen + 1, value))
        
        # 绘制每个参数的散点图
        n_params = len(self.param_names)
        fig, axes = plt.subplots(n_params, 1, figsize=(12, 3 * n_params))
        
        for i, param in enumerate(self.param_names):
            ax = axes[i] if n_params > 1 else axes
            generations, values = zip(*param_values[param])
            ax.scatter(generations, values, alpha=0.6)
            ax.set_xlabel('Generation')
            ax.set_ylabel(param)
            ax.set_title(f'{param} Distribution')
            ax.grid(True)
            
        plt.tight_layout()
        plt.show()
        
        # 绘制最佳个体参数比较
        best_params = self.get_best_params()
        plt.figure(figsize=(10, 6))
        param_names = list(best_params.keys())
        normalized_values = []
        
        for param in param_names:
            min_val, max_val, _ = self.param_ranges[param]
            if max_val > min_val:
                norm_value = (best_params[param] - min_val) / (max_val - min_val)
            else:
                norm_value = 0.5
            normalized_values.append(norm_value)
            
        plt.bar(param_names, normalized_values)
        plt.title('Best Parameters (Normalized)')
        plt.ylabel('Normalized Value')
        plt.ylim(0, 1)
        plt.grid(True, axis='y')
        
        # 添加实际值标签
        for i, param in enumerate(param_names):
            plt.text(i, normalized_values[i] + 0.05, f"{best_params[param]:.4g}", ha='center')
            
        plt.show()