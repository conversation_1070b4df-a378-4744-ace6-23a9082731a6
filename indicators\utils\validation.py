#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
指标数据验证模块

提供验证和准备指标计算所需数据的功能。
"""

from typing import Dict, List, Union, Tuple, Optional
import pandas as pd
import numpy as np


def validate_data(data: pd.DataFrame, required_columns: List[str] = None) -> bool:
    """
    验证数据是否满足指标计算要求
    
    Parameters
    ----------
    data : pd.DataFrame
        输入数据
    required_columns : List[str], optional
        所需列名列表，默认为['close']
        
    Returns
    -------
    bool
        数据是否满足要求
    
    Raises
    ------
    ValueError
        如果数据不满足要求
    """
    if required_columns is None:
        required_columns = ['close']
    
    # 检查是否为DataFrame
    if not isinstance(data, pd.DataFrame):
        raise ValueError("输入数据必须是pandas DataFrame")
    
    # 检查是否为空
    if data.empty:
        raise ValueError("输入数据不能为空")
    
    # 检查必要的列
    missing_cols = [col for col in required_columns if col not in data.columns]
    if missing_cols:
        raise ValueError(f"输入数据缺少必要的列: {', '.join(missing_cols)}")
    
    # 检查数据类型
    for col in required_columns:
        if not pd.api.types.is_numeric_dtype(data[col]):
            raise ValueError(f"列 '{col}' 必须包含数值型数据")
    
    # 检查NaN值
    nan_cols = [col for col in required_columns if data[col].isna().any()]
    if nan_cols:
        raise ValueError(f"列 {', '.join(nan_cols)} 包含NaN值")
    
    return True


def prepare_ohlcv_data(
    data: pd.DataFrame, 
    standardize_names: bool = True,
    fill_missing: bool = False
) -> pd.DataFrame:
    """
    准备OHLCV数据用于指标计算
    
    Parameters
    ----------
    data : pd.DataFrame
        输入数据
    standardize_names : bool, optional
        是否标准化列名，默认为True
    fill_missing : bool, optional
        是否填充缺失值，默认为False
        
    Returns
    -------
    pd.DataFrame
        准备好的OHLCV数据
    """
    result = data.copy()
    
    # 标准化列名
    if standardize_names:
        column_mapping = {}
        
        # 寻找可能的OHLCV列名
        for col in result.columns:
            col_lower = col.lower()
            if any(name in col_lower for name in ['open', 'op']):
                column_mapping[col] = 'open'
            elif any(name in col_lower for name in ['high', 'hi', 'max']):
                column_mapping[col] = 'high'
            elif any(name in col_lower for name in ['low', 'lo', 'min']):
                column_mapping[col] = 'low'
            elif any(name in col_lower for name in ['close', 'cl', 'last']):
                column_mapping[col] = 'close'
            elif any(name in col_lower for name in ['volume', 'vol', 'qty']):
                column_mapping[col] = 'volume'
        
        # 重命名列
        if column_mapping:
            result = result.rename(columns=column_mapping)
    
    # 填充缺失值
    if fill_missing:
        # 如果只有close数据，尝试填充其他OHLCV数据
        if 'close' in result.columns:
            if 'open' not in result.columns:
                result['open'] = result['close']
            if 'high' not in result.columns:
                result['high'] = result['close']
            if 'low' not in result.columns:
                result['low'] = result['close']
            if 'volume' not in result.columns:
                result['volume'] = 0
        
        # 对于存在的列，填充NaN值
        ohlcv_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in [c for c in ohlcv_cols if c in result.columns]:
            if result[col].isna().any():
                if col == 'volume':
                    result[col].fillna(0, inplace=True)
                else:
                    result[col].fillna(method='ffill', inplace=True)
                    result[col].fillna(method='bfill', inplace=True)
    
    return result


def get_value_columns(
    data: pd.DataFrame, 
    exclude_ohlcv: bool = True,
    exclude_columns: List[str] = None
) -> List[str]:
    """
    获取数据中的值列（通常是计算出的指标列）
    
    Parameters
    ----------
    data : pd.DataFrame
        输入数据
    exclude_ohlcv : bool, optional
        是否排除OHLCV列，默认为True
    exclude_columns : List[str], optional
        要排除的其他列名列表
        
    Returns
    -------
    List[str]
        值列名列表
    """
    if exclude_columns is None:
        exclude_columns = []
    
    ohlcv_cols = ['open', 'high', 'low', 'close', 'volume']
    exclude_cols = exclude_columns.copy()
    
    if exclude_ohlcv:
        exclude_cols.extend([col for col in ohlcv_cols if col in data.columns])
    
    # 仅保留数值型列
    numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()
    
    # 排除指定列
    result_cols = [col for col in numeric_cols if col not in exclude_cols]
    
    return result_cols 