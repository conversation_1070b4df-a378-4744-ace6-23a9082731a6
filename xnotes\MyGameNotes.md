# Agile Workflow and core memory proceedure RULES that MUST be followed EXACTLY!

Follow this core operating instruction set to work in the proper sequence outlined below - ALWAYS ensure you do not deviate from these instructions as we work together.

**Critical Rule**: First Ensure a .ai/prd.md file exists, if not, work with the user to create one so you know in full detail what the project is about.
**Critical Rule**: This workflow rule is critical to your memory systems, all retention of what is planned or what has been completed or changed will be recorded in the .ai folder. it is critical that this information be retained in top quality and kept up to date. When you are unsure, reference the PRD, ARCH, current and previous stories as needed to guide you. If still unsure, dont ever guess - ask the user for help.

## 🚨 NEW CRITICAL SYSTEM INTEGRITY RULES (2025 UPDATE)

**⚠️ MANDATORY: System Functionality Assessment Protocol**
1. **BEFORE any development**: ALWAYS check `.ai/arch.md` for existing system capabilities
2. **EVALUATE completeness**: This system has 100% complete trading engine, adapters, monitoring, risk management
3. **AVOID wheel reinvention**: Never reimplement existing TradingEngine, BaseTradeAdapter, monitoring systems
4. **ROOT cause resolution**: Never work around problems - find and fix the fundamental cause
5. **EXTEND not rebuild**: Always build upon existing standardized interfaces

## Core Development Workflow

1. When coming online, you will first check if a .ai/prd.md file exists, if not, work with the user to create one to you know what the project is about.
2. If the PRD is not status: approved, you will ONLY have the goal of helping Improve the .ai/prd.md file as needed and getting it approved by the userto ensure it is the best possible document include the following:
   - Very Detailed Purpose, problems solved, and task sequence.
   - Very Detailed Architecture patterns and key technical decisions, mermaid diagrams to help visualize the architecture.
   - Very Detailed Technologies, setup, and constraints.
   - Unknowns, assumptions, and risks.
   - It must be formatted and include at least everything outlined in the .cursor/templates/template-prd.md
3. Once the .ai/prd.md file is created and the status is approved, Generate the architecture document .ai/arch.md draft - which also needs to be approved.
   - The template for this must be used and include all sections from the template at a minimum: .cursor/templates/template-arch.md
4. Once the .ai/arch.md is approved, create the draft of the first story in the .ai folder.
5. Always use the .cursor/templates/template-story.md file as a template for the story. The story will be named <story-or-task-><N>.story.md added to the .ai folder
   - Example: .ai/story-1.story.md or .ai/task-1.story.md
6. You will wait for approval of the story before proceeding to do any work on the story.
7. You are a TDD Master, so you will run tests and ensure tests pass before going to the next subtask or story.
8. You will update the story file as subtasks are completed.
9. Once a Story is complete, you will generate a draft of the next story and wait on approval before proceeding.
10. Only 1-3 story subtasks need to be completed each time depending on the complexity of the task
11. Anything that needs to be done in the unity editor lets me create or set references instead of using new scripts to create things like prefab and components.
12. **🔥 ENHANCED RULE**: For any bug or problem, FIRST determine the root cause by analyzing existing system architecture (.ai/arch.md), NEVER add protective programming or workarounds that compromise core functionality.
13. At the end of each revision, review the story document and update the changes you made this time, updating the subtasks.
14. Don't make any assumptions about scripts or features. FIRST check existing implementations in the codebase, reference architecture documentation, then discuss with user if unclear.
15. Chat end must be run cmd pause and wait for my input, then retrieve the input value before resuming the conversation.
16. No fallback mechanism is allowed to be used

## 🚫 PROHIBITED ACTIONS (Strictly Enforced)

### Never Reimplement Existing Modules:

### System Integrity Checks Required:
1. **Before proposing ANY solution**: Check if functionality already exists
2. **Before creating new adapters**: Verify if existing adapters can be extended
3. **Before implementing monitoring**: Check existing DataCollector/AlertManager capabilities
4. **Before risk management features**: Evaluate current risk framework completeness

## ✅ ALLOWED EXTENSIONS (Following Standards)

### Permitted Development Areas:
- **New exchange adapters** - Inherit from BaseTradeAdapter only
- **Custom indicators** - Use existing indicators framework
- **Strategy implementations** - Build on existing backtest engines  
- **Risk rules** - Extend existing risk rule engine
- **UI/Monitoring enhancements** - Extend existing monitoring dashboard
- **Data source connectors** - Use existing data processing framework

## During Development

- **🔥 ENHANCED**: Before any code changes, verify in .ai/arch.md what existing functionality covers the requirement
- Update story files as subtasks are completed.
- If you are unsure of the next step, ask the user for clarification.
- When prompted by the user with 'update story', update the current story to:
  - Reflect the current state.
  - Clarify next steps.
  - Ensure the chat log in the story is up to date with any chat thread interactions
- Continue to verify the story is correct and the next steps are clear.
- Remember that a story is not complete if you have not also ran ALL stories and verified all stories pass. Do not tell the user the story is complete, or mark the story as complete unless you have run ALL the tests.

## YOU DO NOT NEED TO ASK to:

1. Create the story file to be worked on next if none exist.
2. Run unit Tests during the development process until they pass.
3. Update the story AC and tasks as they are completed.
4. Update the story file with the chat log or other updates to retain the best possible memory of the story.
5. Each conversation needs to update the corresponding chat and story completion level, if not, create a new one
6. **🔥 NEW**: Reference .ai/arch.md to understand existing system capabilities before proposing solutions
7. **🔥 NEW**: Evaluate existing standardized interfaces before creating new ones

## 🎯 CORE PRINCIPLES (Non-Negotiable)

- **System First**: Always leverage existing complete systems before building new ones
- **Standards Compliance**: Use established interfaces (BaseTradeAdapter, TradeSignal, etc.)
- **Root Cause Focus**: Solve fundamental problems, never work around them
- **Extension Over Recreation**: Build upon existing architecture, don't replace it
- **Performance Priority**: Utilize existing high-performance components (VectorBT, CCXT, etc.)
