#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
成交量变化指标模块

实现成交量变化指标，用于分析成交量的变化趋势和特征，包括成交量比率、成交量变化率等。
"""

from typing import Dict, Any, Union, Optional, List
import pandas as pd
import numpy as np

from ..base import Indicator
from ..utils.validation import validate_data


class VolumeChange(Indicator):
    """
    成交量变化指标
    
    一系列基于成交量变化的指标，包括:
    - 成交量比率(Volume Ratio): 当前成交量与N期移动平均的比值
    - 成交量变化率(Volume Change): 当前成交量与前期成交量的变化百分比
    - 相对成交量(Relative Volume): 当前成交量与N日平均成交量的比值
    - 价量相关性(Price-Volume Correlation): 价格变化与成交量变化的相关性
    """
    
    def __init__(
        self,
        window: int = 20,
        short_window: int = 5,
        volume_col: str = 'volume',
        close_col: str = 'close',
        **kwargs
    ):
        """
        初始化成交量变化指标
        
        Parameters
        ----------
        window : int, optional
            长周期窗口大小，默认为20
        short_window : int, optional
            短周期窗口大小，默认为5
        volume_col : str, optional
            成交量列名，默认为'volume'
        close_col : str, optional
            收盘价列名，默认为'close'
        **kwargs : dict
            其他参数
        """
        name = kwargs.pop('name', 'VolumeChange')
        super().__init__(name, 'volume', **kwargs)
        self.window = window
        self.short_window = short_window
        self.volume_col = volume_col
        self.close_col = close_col
    
    def validate_params(self) -> bool:
        """验证参数是否有效"""
        if not isinstance(self.window, int) or self.window <= 0:
            raise ValueError(f"窗口大小必须为正整数，当前值: {self.window}")
        if not isinstance(self.short_window, int) or self.short_window <= 0:
            raise ValueError(f"短周期窗口大小必须为正整数，当前值: {self.short_window}")
        if self.short_window >= self.window:
            raise ValueError(f"短周期窗口必须小于长周期窗口，当前值: {self.short_window} >= {self.window}")
        return True
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算成交量变化指标
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据，要求包含成交量和收盘价列
            
        Returns
        -------
        pd.DataFrame
            包含各种成交量变化指标的DataFrame
        """
        # 验证数据
        validate_data(data, [self.volume_col, self.close_col])
        self.validate_params()
        
        # 计算成交量变化指标
        result = data.copy()
        
        # 提取数据
        volume = result[self.volume_col]
        close = result[self.close_col]
        
        # 1. 成交量比率 (Volume Ratio)
        # 当前成交量与N期移动平均的比值
        vol_ma = volume.rolling(window=self.window).mean()
        result[f'Volume_Ratio_{self.window}'] = volume / vol_ma
        
        # 2. 成交量变化率 (Volume Change)
        # 当前成交量与前期成交量的变化百分比
        result['Volume_Change_1'] = volume.pct_change() * 100
        
        # 3. 短周期成交量变化率
        # 当前成交量与N期前成交量的变化百分比
        result[f'Volume_Change_{self.short_window}'] = (
            volume / volume.shift(self.short_window) - 1) * 100
        
        # 4. 相对成交量 (Relative Volume)
        # 当前成交量与N日平均成交量的比值
        result[f'Relative_Volume_{self.window}'] = volume / vol_ma
        
        # 5. 成交量变化趋势
        # 短期成交量均值 / 长期成交量均值
        vol_short_ma = volume.rolling(window=self.short_window).mean()
        result[f'Volume_Trend_{self.short_window}_{self.window}'] = vol_short_ma / vol_ma
        
        # 6. 价量相关性 (Price-Volume Correlation)
        # 价格变化与成交量变化的相关性（使用移动窗口）
        price_change = close.pct_change()
        vol_change = volume.pct_change()
        
        def rolling_correlation(x, y, window):
            return np.corrcoef(x[-window:], y[-window:])[0, 1] if len(x) >= window else np.nan
        
        # 初始化相关性序列
        corr = pd.Series(np.nan, index=result.index)
        
        # 计算每个窗口的相关性（注意：这种实现对大数据集可能不太高效）
        for i in range(self.window, len(result)):
            price_window = price_change.iloc[i-self.window+1:i+1].values
            vol_window = vol_change.iloc[i-self.window+1:i+1].values
            valid_indices = ~(np.isnan(price_window) | np.isnan(vol_window))
            if sum(valid_indices) > 1:  # 至少需要两个有效值
                p_window = price_window[valid_indices]
                v_window = vol_window[valid_indices]
                if len(p_window) > 0 and len(v_window) > 0:
                    corr.iloc[i] = np.corrcoef(p_window, v_window)[0, 1]
        
        result[f'Price_Volume_Corr_{self.window}'] = corr
        
        self._result = result
        return result
    
    def plot(self, ax=None, **kwargs) -> Any:
        """
        绘制成交量变化指标图表
        
        Parameters
        ----------
        ax : matplotlib.axes.Axes, optional
            用于绘图的Axes对象
        **kwargs : dict
            传递给绘图函数的其他参数
            
        Returns
        -------
        matplotlib.axes.Axes
            绘图结果
        """
        if self._result is None:
            raise ValueError("没有计算结果可供绘制，请先调用calculate方法")
        
        import matplotlib.pyplot as plt
        
        # 创建子图
        if ax is None:
            fig, axs = plt.subplots(3, 1, figsize=kwargs.get('figsize', (10, 12)), sharex=True)
        else:
            axs = [ax] * 3  # 如果传入了ax，所有图都画在同一个ax上
        
        # 1. 价格和成交量图
        axs[0].plot(self._result.index, self._result[self.close_col], color='blue')
        axs[0].set_title('Price')
        axs[0].set_ylabel('Price')
        axs[0].grid(True)
        
        # 2. 成交量柱状图
        volume_ax = axs[1]
        volume = self._result[self.volume_col]
        
        # 根据价格变化给成交量柱状图上色
        colors = np.where(self._result[self.close_col].diff() > 0, 'green', 'red')
        
        for i, (idx, vol) in enumerate(zip(self._result.index, volume)):
            color = colors[i]
            volume_ax.bar(idx, vol, color=color, alpha=0.6)
        
        volume_ax.set_title('Volume')
        volume_ax.set_ylabel('Volume')
        volume_ax.grid(True)
        
        # 3. 成交量指标图
        indicator_ax = axs[2]
        
        # 绘制相对成交量
        rel_vol_col = f'Relative_Volume_{self.window}'
        indicator_ax.plot(self._result.index, self._result[rel_vol_col], 
                        label=f'Relative Volume ({self.window})', color='blue')
        
        # 绘制成交量趋势
        vol_trend_col = f'Volume_Trend_{self.short_window}_{self.window}'
        indicator_ax.plot(self._result.index, self._result[vol_trend_col], 
                        label=f'Volume Trend ({self.short_window}/{self.window})', color='red')
        
        # 绘制价量相关性
        corr_col = f'Price_Volume_Corr_{self.window}'
        indicator_ax.plot(self._result.index, self._result[corr_col], 
                        label=f'Price-Volume Corr ({self.window})', color='green')
        
        # 添加参考线
        indicator_ax.axhline(y=1.0, color='black', linestyle='--', alpha=0.3)
        indicator_ax.axhline(y=0.0, color='black', linestyle='--', alpha=0.3)
        
        indicator_ax.set_title('Volume Indicators')
        indicator_ax.set_ylabel('Value')
        indicator_ax.legend()
        indicator_ax.grid(True)
        
        plt.tight_layout()
        
        return axs 