"""
监控系统基础类

定义监控系统的核心接口和基类，包括监控器、事件和事件处理器。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, Callable, Set
from datetime import datetime
import uuid
from enum import Enum
import pandas as pd


class EventSeverity(Enum):
    """
    事件严重性枚举
    """
    INFO = "Info"
    WARNING = "Warning" 
    ERROR = "Error"
    CRITICAL = "Critical"


class MonitoringEvent:
    """
    监控事件类
    
    表示一个监控系统检测到的事件。
    """
    
    def __init__(self, event_type: str, severity: EventSeverity, message: str, 
                related_data: Dict[str, Any] = None):
        """
        初始化监控事件
        
        Parameters
        ----------
        event_type : str
            事件类型
        severity : EventSeverity
            事件严重性
        message : str
            事件描述消息
        related_data : Dict[str, Any], optional
            相关数据，默认为None
        """
        self.event_id = str(uuid.uuid4())
        self.timestamp = datetime.now()
        self.event_type = event_type
        self.severity = severity
        self.message = message
        self.related_data = related_data or {}
        self.acknowledged = False
    
    def acknowledge(self) -> None:
        """标记事件为已确认"""
        self.acknowledged = True
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将事件转换为字典格式
        
        Returns
        -------
        Dict[str, Any]
            事件的字典表示
        """
        return {
            "event_id": self.event_id,
            "timestamp": self.timestamp.isoformat(),
            "event_type": self.event_type,
            "severity": self.severity.value,
            "message": self.message,
            "related_data": self.related_data,
            "acknowledged": self.acknowledged
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MonitoringEvent':
        """
        从字典创建事件对象
        
        Parameters
        ----------
        data : Dict[str, Any]
            事件的字典表示
        
        Returns
        -------
        MonitoringEvent
            事件对象
        """
        event = cls(
            event_type=data["event_type"],
            severity=EventSeverity(data["severity"]),
            message=data["message"],
            related_data=data.get("related_data", {})
        )
        event.event_id = data["event_id"]
        event.timestamp = datetime.fromisoformat(data["timestamp"])
        event.acknowledged = data.get("acknowledged", False)
        return event


class EventHandler(ABC):
    """
    事件处理器抽象基类
    
    所有具体事件处理器必须继承此类并实现相应方法。
    """
    
    def __init__(self, name: str, description: str = "", enabled: bool = True):
        """
        初始化事件处理器
        
        Parameters
        ----------
        name : str
            处理器名称
        description : str, optional
            处理器描述，默认为空字符串
        enabled : bool, optional
            处理器是否启用，默认为True
        """
        self.name = name
        self.description = description
        self.enabled = enabled
    
    @abstractmethod
    def handle_event(self, event: MonitoringEvent) -> bool:
        """
        处理事件
        
        Parameters
        ----------
        event : MonitoringEvent
            要处理的事件
        
        Returns
        -------
        bool
            处理是否成功
        """
        pass
    
    def is_enabled(self) -> bool:
        """
        检查处理器是否启用
        
        Returns
        -------
        bool
            处理器是否启用
        """
        return self.enabled
    
    def enable(self) -> None:
        """启用处理器"""
        self.enabled = True
    
    def disable(self) -> None:
        """禁用处理器"""
        self.enabled = False


class MonitorBase(ABC):
    """
    监控器抽象基类
    
    所有具体监控器必须继承此类并实现相应方法。
    """
    
    def __init__(self, name: str, description: str = "", enabled: bool = True, 
                check_interval: int = 1):
        """
        初始化监控器
        
        Parameters
        ----------
        name : str
            监控器名称
        description : str, optional
            监控器描述，默认为空字符串
        enabled : bool, optional
            监控器是否启用，默认为True
        check_interval : int, optional
            检查间隔（以秒为单位），默认为1
        """
        self.name = name
        self.description = description
        self.enabled = enabled
        self.check_interval = check_interval
        self.last_check_time = None
        self.event_handlers: List[EventHandler] = []
    
    def add_event_handler(self, handler: EventHandler) -> None:
        """
        添加事件处理器
        
        Parameters
        ----------
        handler : EventHandler
            要添加的事件处理器
        """
        self.event_handlers.append(handler)
    
    def remove_event_handler(self, handler_name: str) -> None:
        """
        移除事件处理器
        
        Parameters
        ----------
        handler_name : str
            要移除的处理器名称
        
        Raises
        ------
        ValueError
            如果处理器不存在
        """
        for i, handler in enumerate(self.event_handlers):
            if handler.name == handler_name:
                self.event_handlers.pop(i)
                return
        raise ValueError(f"处理器'{handler_name}'不存在")
    
    def process_event(self, event: MonitoringEvent) -> None:
        """
        处理事件
        
        Parameters
        ----------
        event : MonitoringEvent
            要处理的事件
        """
        # 记录事件发生时间
        self.last_check_time = datetime.now()
        
        # 传递给所有启用的处理器
        for handler in self.event_handlers:
            if handler.is_enabled():
                handler.handle_event(event)
    
    @abstractmethod
    def check(self, context: Dict[str, Any] = None) -> List[MonitoringEvent]:
        """
        执行监控检查
        
        Parameters
        ----------
        context : Dict[str, Any], optional
            检查上下文，包含与检查相关的数据，默认为None
        
        Returns
        -------
        List[MonitoringEvent]
            检测到的事件列表
        """
        pass
    
    def is_enabled(self) -> bool:
        """
        检查监控器是否启用
        
        Returns
        -------
        bool
            监控器是否启用
        """
        return self.enabled
    
    def enable(self) -> None:
        """启用监控器"""
        self.enabled = True
    
    def disable(self) -> None:
        """禁用监控器"""
        self.enabled = False


class MonitoringSystem:
    """
    监控系统
    
    管理多个监控器和事件处理。
    """
    
    def __init__(self):
        """初始化监控系统"""
        self.monitors: Dict[str, MonitorBase] = {}
        self.recent_events: List[MonitoringEvent] = []
        self.max_events_history = 1000  # 最大事件历史记录数
    
    def add_monitor(self, monitor: MonitorBase) -> None:
        """
        添加监控器
        
        Parameters
        ----------
        monitor : MonitorBase
            要添加的监控器
        
        Raises
        ------
        ValueError
            如果已存在同名监控器
        """
        if monitor.name in self.monitors:
            raise ValueError(f"监控器'{monitor.name}'已存在")
        self.monitors[monitor.name] = monitor
    
    def remove_monitor(self, monitor_name: str) -> None:
        """
        移除监控器
        
        Parameters
        ----------
        monitor_name : str
            要移除的监控器名称
        
        Raises
        ------
        KeyError
            如果监控器不存在
        """
        if monitor_name not in self.monitors:
            raise KeyError(f"监控器'{monitor_name}'不存在")
        del self.monitors[monitor_name]
    
    def run_all_monitors(self, context: Dict[str, Any] = None) -> List[MonitoringEvent]:
        """
        运行所有监控器
        
        Parameters
        ----------
        context : Dict[str, Any], optional
            检查上下文，默认为None
        
        Returns
        -------
        List[MonitoringEvent]
            检测到的事件列表
        """
        if context is None:
            context = {}
        
        all_events = []
        
        # 运行每个监控器并收集事件
        for monitor in self.monitors.values():
            if monitor.is_enabled():
                events = monitor.check(context)
                all_events.extend(events)
                
                # 处理每个事件
                for event in events:
                    monitor.process_event(event)
        
        # 更新事件历史
        self.recent_events.extend(all_events)
        if len(self.recent_events) > self.max_events_history:
            self.recent_events = self.recent_events[-self.max_events_history:]
        
        return all_events
    
    def get_recent_events(self, limit: int = None, severity: EventSeverity = None, 
                         event_type: str = None) -> List[MonitoringEvent]:
        """
        获取最近事件
        
        Parameters
        ----------
        limit : int, optional
            要返回的事件数量限制，默认为None表示返回全部
        severity : EventSeverity, optional
            按严重性筛选，默认为None表示不筛选
        event_type : str, optional
            按事件类型筛选，默认为None表示不筛选
        
        Returns
        -------
        List[MonitoringEvent]
            事件列表
        """
        filtered_events = self.recent_events
        
        # 按严重性筛选
        if severity is not None:
            filtered_events = [e for e in filtered_events if e.severity == severity]
        
        # 按事件类型筛选
        if event_type is not None:
            filtered_events = [e for e in filtered_events if e.event_type == event_type]
        
        # 应用限制
        if limit is not None:
            filtered_events = filtered_events[-limit:]
        
        return filtered_events
    
    def acknowledge_event(self, event_id: str) -> bool:
        """
        确认事件
        
        Parameters
        ----------
        event_id : str
            事件ID
        
        Returns
        -------
        bool
            确认是否成功
        """
        for event in self.recent_events:
            if event.event_id == event_id:
                event.acknowledge()
                return True
        return False
    
    def get_unacknowledged_events(self, severity: EventSeverity = None) -> List[MonitoringEvent]:
        """
        获取未确认事件
        
        Parameters
        ----------
        severity : EventSeverity, optional
            按严重性筛选，默认为None表示不筛选
        
        Returns
        -------
        List[MonitoringEvent]
            未确认事件列表
        """
        events = [e for e in self.recent_events if not e.acknowledged]
        
        if severity is not None:
            events = [e for e in events if e.severity == severity]
        
        return events 