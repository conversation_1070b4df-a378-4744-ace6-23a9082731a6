"""
数据预处理流水线

提供可配置的数据处理流程，允许用户组合多个预处理步骤，
并保存、加载和重用预处理配置。
"""

import pandas as pd
import json
import pickle
from typing import Dict, List, Tuple, Optional, Union, Any, Callable, Literal
from dataclasses import dataclass, field
from abc import ABC, abstractmethod

from data.processing.cleaner import fill_missing_values, handle_outliers, remove_duplicates, align_timestamps
from data.processing.transformer import normalize_data, standardize_data, extract_time_features, calculate_returns
from data.processing.features import calculate_volatility, calculate_moving_averages, calculate_rsi, calculate_macd


class ProcessingStep(ABC):
    """处理步骤抽象基类"""
    
    @abstractmethod
    def process(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        执行处理步骤
        
        Args:
            df: 要处理的DataFrame
            
        Returns:
            处理后的DataFrame
        """
        pass
    
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """
        将处理步骤转换为字典表示
        
        Returns:
            步骤参数的字典表示
        """
        pass
    
    @classmethod
    @abstractmethod
    def from_dict(cls, config: Dict[str, Any]) -> 'ProcessingStep':
        """
        从字典创建处理步骤
        
        Args:
            config: 配置字典
            
        Returns:
            处理步骤实例
        """
        pass


@dataclass
class CleaningStep(ProcessingStep):
    """数据清洗步骤"""
    step_type: str = "cleaning"
    fill_missing: Optional[str] = None  # 填充缺失值方法
    fill_columns: Optional[List[str]] = None  # 填充哪些列
    outlier_method: Optional[str] = None  # 异常值检测方法
    outlier_threshold: float = 3.0  # 异常值阈值
    outlier_action: Optional[str] = None  # 异常值处理方法
    outlier_columns: Optional[List[str]] = None  # 处理哪些列的异常值
    remove_duplicates: bool = False  # 是否去除重复值
    duplicate_keep: Union[Literal['first', 'last'], Literal[False]] = 'last'  # 保留哪个重复项
    align_timestamps: bool = False  # 是否对齐时间戳
    align_freq: Optional[str] = None  # 对齐频率
    align_method: str = 'nearest'  # 对齐方法
    
    def process(self, df: pd.DataFrame) -> pd.DataFrame:
        """执行清洗步骤"""
        result = df.copy()
        
        # 填充缺失值
        if self.fill_missing is not None:
            result = fill_missing_values(
                result, 
                method=self.fill_missing, 
                columns=self.fill_columns
            )
        
        # 处理异常值
        if self.outlier_method is not None and self.outlier_action is not None:
            result = handle_outliers(
                result, 
                method=self.outlier_method, 
                threshold=self.outlier_threshold,
                action=self.outlier_action,
                columns=self.outlier_columns
            )
        
        # 去除重复值
        if self.remove_duplicates:
            result = remove_duplicates(result, keep=self.duplicate_keep)
        
        # 对齐时间戳
        if self.align_timestamps and self.align_freq is not None:
            result = align_timestamps(
                result, 
                freq=self.align_freq, 
                method=self.align_method
            )
        
        return result
    
    def to_dict(self) -> Dict[str, Any]:
        """将清洗步骤转换为字典"""
        return {
            'step_type': self.step_type,
            'fill_missing': self.fill_missing,
            'fill_columns': self.fill_columns,
            'outlier_method': self.outlier_method,
            'outlier_threshold': self.outlier_threshold,
            'outlier_action': self.outlier_action,
            'outlier_columns': self.outlier_columns,
            'remove_duplicates': self.remove_duplicates,
            'duplicate_keep': self.duplicate_keep,
            'align_timestamps': self.align_timestamps,
            'align_freq': self.align_freq,
            'align_method': self.align_method
        }
    
    @classmethod
    def from_dict(cls, config: Dict[str, Any]) -> 'CleaningStep':
        """从字典创建清洗步骤"""
        return cls(**config)


@dataclass
class TransformStep(ProcessingStep):
    """数据转换步骤"""
    step_type: str = "transform"
    normalize: bool = False  # 是否归一化
    norm_method: str = 'minmax'  # 归一化方法
    norm_columns: Optional[List[str]] = None  # 归一化哪些列
    standardize: bool = False  # 是否标准化
    std_columns: Optional[List[str]] = None  # 标准化哪些列
    extract_time: bool = False  # 是否提取时间特征
    calc_returns: bool = False  # 是否计算收益率
    return_method: str = 'pct_change'  # 收益率计算方法
    return_periods: int = 1  # 收益率计算周期
    return_column: Optional[str] = None  # 用于计算收益率的列
    
    def process(self, df: pd.DataFrame) -> pd.DataFrame:
        """执行转换步骤"""
        result = df.copy()
        
        # 归一化数据
        if self.normalize:
            result = normalize_data(
                result, 
                method=self.norm_method, 
                columns=self.norm_columns
            )
        
        # 标准化数据
        if self.standardize:
            result = standardize_data(
                result, 
                columns=self.std_columns
            )
        
        # 提取时间特征
        if self.extract_time:
            result = extract_time_features(result)
        
        # 计算收益率
        if self.calc_returns and self.return_column is not None:
            result = calculate_returns(
                result, 
                method=self.return_method,
                periods=self.return_periods,
                column=self.return_column
            )
        
        return result
    
    def to_dict(self) -> Dict[str, Any]:
        """将转换步骤转换为字典"""
        return {
            'step_type': self.step_type,
            'normalize': self.normalize,
            'norm_method': self.norm_method,
            'norm_columns': self.norm_columns,
            'standardize': self.standardize,
            'std_columns': self.std_columns,
            'extract_time': self.extract_time,
            'calc_returns': self.calc_returns,
            'return_method': self.return_method,
            'return_periods': self.return_periods,
            'return_column': self.return_column
        }
    
    @classmethod
    def from_dict(cls, config: Dict[str, Any]) -> 'TransformStep':
        """从字典创建转换步骤"""
        return cls(**config)


@dataclass
class FeatureEngineeringStep(ProcessingStep):
    """特征工程步骤"""
    step_type: str = "feature_engineering"
    calc_volatility: bool = False  # 是否计算波动率
    vol_window: int = 20  # 波动率窗口
    vol_column: Optional[str] = None  # 用于计算波动率的列
    vol_annualize: bool = True  # 是否年化波动率
    vol_scaling: float = 252.0  # 年化因子
    calc_ma: bool = False  # 是否计算移动平均
    ma_column: Optional[str] = None  # 用于计算移动平均的列
    ma_windows: List[int] = field(default_factory=lambda: [5, 10, 20, 50, 200])  # 移动平均周期
    calc_rsi: bool = False  # 是否计算RSI
    rsi_column: Optional[str] = None  # 用于计算RSI的列
    rsi_window: int = 14  # RSI周期
    calc_macd: bool = False  # 是否计算MACD
    macd_column: Optional[str] = None  # 用于计算MACD的列
    macd_fast: int = 12  # MACD快线周期
    macd_slow: int = 26  # MACD慢线周期
    macd_signal: int = 9  # MACD信号线周期
    
    def process(self, df: pd.DataFrame) -> pd.DataFrame:
        """执行特征工程步骤"""
        result = df.copy()
        
        # 计算波动率
        if self.calc_volatility and self.vol_column is not None:
            result = calculate_volatility(
                result, 
                window=self.vol_window,
                column=self.vol_column,
                annualize=self.vol_annualize,
                scaling=self.vol_scaling
            )
        
        # 计算移动平均
        if self.calc_ma and self.ma_column is not None:
            result = calculate_moving_averages(
                result, 
                column=self.ma_column,
                windows=self.ma_windows
            )
        
        # 计算RSI
        if self.calc_rsi and self.rsi_column is not None:
            result = calculate_rsi(
                result, 
                column=self.rsi_column,
                window=self.rsi_window
            )
        
        # 计算MACD
        if self.calc_macd and self.macd_column is not None:
            result = calculate_macd(
                result, 
                column=self.macd_column,
                fast_period=self.macd_fast,
                slow_period=self.macd_slow,
                signal_period=self.macd_signal
            )
        
        return result
    
    def to_dict(self) -> Dict[str, Any]:
        """将特征工程步骤转换为字典"""
        return {
            'step_type': self.step_type,
            'calc_volatility': self.calc_volatility,
            'vol_window': self.vol_window,
            'vol_column': self.vol_column,
            'vol_annualize': self.vol_annualize,
            'vol_scaling': self.vol_scaling,
            'calc_ma': self.calc_ma,
            'ma_column': self.ma_column,
            'ma_windows': self.ma_windows,
            'calc_rsi': self.calc_rsi,
            'rsi_column': self.rsi_column,
            'rsi_window': self.rsi_window,
            'calc_macd': self.calc_macd,
            'macd_column': self.macd_column,
            'macd_fast': self.macd_fast,
            'macd_slow': self.macd_slow,
            'macd_signal': self.macd_signal
        }
    
    @classmethod
    def from_dict(cls, config: Dict[str, Any]) -> 'FeatureEngineeringStep':
        """从字典创建特征工程步骤"""
        return cls(**config)


class ProcessingPipeline:
    """数据预处理流水线"""
    
    def __init__(self, name: str = "默认流水线"):
        """
        初始化流水线
        
        Args:
            name: 流水线名称
        """
        self.name = name
        self.steps: List[ProcessingStep] = []
    
    def add_step(self, step: ProcessingStep) -> None:
        """
        添加处理步骤
        
        Args:
            step: 要添加的处理步骤
        """
        self.steps.append(step)
    
    def process(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        执行整个流水线的处理
        
        Args:
            df: 要处理的DataFrame
            
        Returns:
            处理后的DataFrame
        """
        result = df.copy()
        
        for step in self.steps:
            result = step.process(result)
        
        return result
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将流水线转换为字典表示
        
        Returns:
            流水线配置的字典表示
        """
        return {
            'name': self.name,
            'steps': [step.to_dict() for step in self.steps]
        }
    
    def to_json(self, indent: int = 2) -> str:
        """
        将流水线转换为JSON字符串
        
        Args:
            indent: JSON缩进
            
        Returns:
            JSON字符串
        """
        return json.dumps(self.to_dict(), indent=indent)
    
    def save_to_file(self, filepath: str) -> None:
        """
        将流水线保存到文件
        
        Args:
            filepath: 文件路径
        """
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(self.to_json())
    
    def save_to_pickle(self, filepath: str) -> None:
        """
        将流水线保存为pickle文件
        
        Args:
            filepath: 文件路径
        """
        with open(filepath, 'wb') as f:
            pickle.dump(self, f)
    
    @classmethod
    def from_dict(cls, config: Dict[str, Any]) -> 'ProcessingPipeline':
        """
        从字典创建流水线
        
        Args:
            config: 配置字典
            
        Returns:
            流水线实例
        """
        pipeline = cls(name=config.get('name', '默认流水线'))
        
        # 添加步骤
        for step_config in config.get('steps', []):
            step_type = step_config.get('step_type')
            
            if step_type == "cleaning":
                pipeline.add_step(CleaningStep.from_dict(step_config))
            elif step_type == "transform":
                pipeline.add_step(TransformStep.from_dict(step_config))
            elif step_type == "feature_engineering":
                pipeline.add_step(FeatureEngineeringStep.from_dict(step_config))
        
        return pipeline
    
    @classmethod
    def from_json(cls, json_str: str) -> 'ProcessingPipeline':
        """
        从JSON字符串创建流水线
        
        Args:
            json_str: JSON字符串
            
        Returns:
            流水线实例
        """
        config = json.loads(json_str)
        return cls.from_dict(config)
    
    @classmethod
    def load_from_file(cls, filepath: str) -> 'ProcessingPipeline':
        """
        从文件加载流水线
        
        Args:
            filepath: 文件路径
            
        Returns:
            流水线实例
        """
        with open(filepath, 'r', encoding='utf-8') as f:
            return cls.from_json(f.read())
    
    @classmethod
    def load_from_pickle(cls, filepath: str) -> 'ProcessingPipeline':
        """
        从pickle文件加载流水线
        
        Args:
            filepath: 文件路径
            
        Returns:
            流水线实例
        """
        with open(filepath, 'rb') as f:
            return pickle.load(f) 