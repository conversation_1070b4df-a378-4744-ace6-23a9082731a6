"""
Freqtrade API客户端

🔥 根本性重构：严格遵循freqtrade官方API规范，零容错设计
基于官方文档: https://www.freqtrade.io/en/stable/rest-api/
"""

import json
import logging
import requests
import os
from typing import Dict, Any, List, Optional, Union

from .models import TradeSignal, TradeResult, OrderStatus, AccountBalance, BotStatus, OrderType


logger = logging.getLogger(__name__)


class FreqtradeError(Exception):
    """Freqtrade API错误"""
    pass


class FreqtradeClient:
    """
    Freqtrade API客户端 - 🔥 根据官方文档完全重构
    严格遵循 https://www.freqtrade.io/en/stable/rest-api/ 规范
    """
    
    def __init__(self, 
                server_url: str,
                username: str = "",
                password: str = "",
                config: Optional[Dict[str, Any]] = None):
        """初始化客户端 - 按官方文档规范"""
        self.server_url = server_url.rstrip('/')
        
        # 创建session
        self.session = requests.Session()
        
        # 设置HTTP基础认证 - 官方文档标准方式
        if username and password:
            self.session.auth = (username, password)
        
        # 根本性修复：添加代理配置支持
        self._configure_proxy(config)
    
    def _configure_proxy(self, config: Optional[Dict[str, Any]] = None) -> None:
        """
        配置代理设置 - 🔥 根本性修复：智能代理判断
        只对外部网络调用使用代理，本地API调用直接连接
        """
        # 检查是否为本地服务器
        if self._is_local_server():
            logger.info("✅ 检测到本地服务器，跳过代理配置")
            return
        
        # 只有远程服务器才配置代理
        proxy_url = None
        
        # 优先级1: 配置参数中的代理
        if config and 'proxy' in config:
            proxy_url = config['proxy']
            logger.debug(f"使用配置代理: {proxy_url}")
        
        # 优先级2: 环境变量代理  
        elif os.getenv('HTTP_PROXY') or os.getenv('HTTPS_PROXY'):
            proxy_url = os.getenv('HTTPS_PROXY') or os.getenv('HTTP_PROXY')
            logger.debug(f"使用环境变量代理: {proxy_url}")
        
        # 配置session代理（仅远程服务器）
        if proxy_url:
            self.session.proxies.update({
                'http': proxy_url,
                'https': proxy_url
            })
            logger.info(f"FreqtradeClient已配置代理: {proxy_url}")
    
    def _is_local_server(self) -> bool:
        """
        检查是否为本地服务器
        
        Returns
        -------
        bool
            如果是本地服务器返回True
        """
        local_indicators = [
            '127.0.0.1',
            'localhost',
            '0.0.0.0',
            '::1'
        ]
        
        return any(indicator in self.server_url for indicator in local_indicators)
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """发起API请求 - 严格按官方文档规范"""
        url = f"{self.server_url}/api/v1/{endpoint.lstrip('/')}"
        
        # 官方推荐的超时设置
        timeout = 10
        logger.debug(f"🚀 API调用: {method} {endpoint}")
        
        try:
            response = self.session.request(method, url, timeout=timeout, **kwargs)
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ API调用失败: {endpoint} - {e}")
            raise FreqtradeError(f"API调用失败: {e}")
    
    def get(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """GET请求"""
        return self._make_request('GET', endpoint, params=params).json()
    
    def post(self, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """POST请求"""
        response = self._make_request('POST', endpoint, json=data)
        return response.json()
        
    def ping(self) -> bool:
        """测试连接 - 官方文档: /api/v1/ping 返回 {"status":"pong"}"""
        try:
            result = self.get("ping")
            return result.get("status") == "pong"
        except Exception:
            return False
    
    def get_balance(self) -> Dict[str, Any]:
        """获取账户余额 - 官方端点: /api/v1/balance"""
        return self.get("balance")
    
    def get_open_trades(self) -> List[Dict[str, Any]]:
        """获取开放交易列表 - 官方端点: /api/v1/status"""
        try:
            trades_list = self.get('status')
            
            if isinstance(trades_list, list):
                logger.debug(f"✅ 获取到{len(trades_list)}笔开放交易")
                return trades_list
            else:
                logger.warning(f"⚠️ API返回非列表数据: {type(trades_list)}")
                return []
                
        except Exception as e:
            logger.error(f"❌ 获取开放交易失败: {e}")
            raise
    
    def force_enter_trade(self, signal: TradeSignal) -> TradeResult:
        """
        强制开始新交易 - 官方端点: /api/v1/forceenter
        
        官方文档参数:
        - pair: 交易对 (必需)
        - side: 'long' 或 'short' (必需)  
        - price: 价格 (可选)
        - amount: 数量 (可选)
        - enter_tag: 进入标签 (可选)
        """
        # 🔥 严格按照官方文档构建请求
        request_data = {
            'pair': signal.pair,
            'side': signal.side,
        }
        
        # 可选参数 - 仅在提供时添加
        if signal.amount is not None:
            request_data['amount'] = signal.amount
        if signal.rate is not None:
            request_data['price'] = signal.rate  # 注意：官方文档使用'price'而不是'rate'
        if signal.enter_tag is not None:
            request_data['enter_tag'] = signal.enter_tag
        
        logger.info(f"🔄 forceenter: {signal.pair} {signal.side}")
        
        try:
            response = self.post('forceenter', data=request_data)
            
            # 🔥 根据官方文档，成功响应应包含trade信息
            if isinstance(response, dict):
                # 检查明确的错误
                if 'error' in response:
                    error_msg = response.get('error')
                    logger.error(f"❌ forceenter错误: {error_msg}")
                    return TradeResult(success=False, error=error_msg)
                
                # 检查trade ID - 官方文档中的成功标志
                if 'tradeid' in response:
                    tradeid = response['tradeid']
                    logger.info(f"✅ 交易创建成功: ID={tradeid}")
                    return TradeResult(success=True, tradeid=tradeid, details=response)
                
                # 其他成功情况
                elif response.get('success') is True:
                    logger.info(f"✅ forceenter执行成功")
                    return TradeResult(success=True, details=response)
                
                # 默认失败
                else:
                    error_msg = f"未知响应格式: {response}"
                    logger.error(f"❌ {error_msg}")
                    return TradeResult(success=False, error=error_msg)
            
            else:
                error_msg = f"无效响应类型: {type(response)}"
                logger.error(f"❌ {error_msg}")
                return TradeResult(success=False, error=error_msg)
                
        except Exception as e:
            error_msg = f"forceenter调用失败: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return TradeResult(success=False, error=error_msg)
    
    def force_exit_trade(self, tradeid: Union[int, str], amount: Optional[float] = None) -> TradeResult:
        """
        强制退出交易 - 官方端点: /api/v1/forceexit
        
        官方文档参数:
        - tradeid: 交易ID (必需)
        - amount: 退出数量 (可选)
        """
        request_data = {
            'tradeid': str(tradeid)
        }
        
        if amount is not None:
            request_data['amount'] = amount
        
        logger.info(f"🔄 forceexit: tradeid={tradeid}")
        
        try:
            response = self.post('forceexit', data=request_data)
            
            if isinstance(response, dict):
                # 检查错误
                if 'error' in response:
                    error_msg = response.get('error')
                    logger.error(f"❌ forceexit错误: {error_msg}")
                    return TradeResult(success=False, error=error_msg)
                
                # 成功
                logger.info(f"✅ 强制退出成功: tradeid={tradeid}")
                return TradeResult(success=True, details=response)
            
            else:
                error_msg = f"无效响应类型: {type(response)}"
                logger.error(f"❌ {error_msg}")
                return TradeResult(success=False, error=error_msg)
                
        except Exception as e:
            error_msg = f"forceexit调用失败: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return TradeResult(success=False, error=error_msg)
    
    def get_bot_status(self) -> BotStatus:
        """获取机器人状态 - 使用 /api/v1/status 端点"""
        try:
            data = self.get('status')
            trade_count = len(data) if isinstance(data, list) else 0
            
            return BotStatus(
                status='running',  # API可访问即为运行状态
                running_since=None,
                trade_count=trade_count,
                performance=[]
            )
        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            return BotStatus(status='error', running_since=None, trade_count=0, performance=[])
    
    def get_trade_history(self, limit: int = 50) -> Dict[str, Any]:
        """获取交易历史 - 官方端点: /api/v1/trades"""
        return self.get('trades', params={'limit': limit})
    
    def start_bot(self) -> bool:
        """启动机器人 - 官方端点: /api/v1/start"""
        try:
            self.post('start')
            return True
        except Exception as e:
            logger.error(f"启动失败: {e}")
            return False
    
    def stop_bot(self) -> bool:
        """停止机器人 - 官方端点: /api/v1/stop"""
        try:
            self.post('stop')
            return True
        except Exception as e:
            logger.error(f"停止失败: {e}")
            return False
    
    def pause_bot(self) -> bool:
        """暂停机器人 - 官方端点: /api/v1/stopbuy"""
        try:
            self.post('stopbuy')
            return True
        except Exception as e:
            logger.error(f"暂停失败: {e}")
            return False
    
    def get_whitelist(self) -> List[str]:
        """获取交易对白名单 - 官方端点: /api/v1/whitelist"""
        try:
            response = self.get('whitelist')
            return response.get('whitelist', [])
        except Exception as e:
            logger.error(f"获取白名单失败: {e}")
            return []
    
    def reload_config(self) -> bool:
        """
        重新加载机器人配置
        
        Returns
        -------
        bool
            如果操作成功则返回True
        """
        try:
            self.post('reload_config')
            return True
        except FreqtradeError as e:
            logger.error(f"重新加载配置失败: {str(e)}")
            return False
    
    def get_strategy_list(self) -> List[str]:
        """
        获取可用策略列表
        
        Returns
        -------
        List[str]
            策略名称列表
        """
        try:
            response = self.get('strategies')
            return response.get('strategies', [])
        except FreqtradeError as e:
            logger.error(f"获取策略列表失败: {str(e)}")
            return []
    
    def get_profit_summary(self) -> Dict[str, Any]:
        """
        获取利润摘要
        
        Returns
        -------
        Dict[str, Any]
            利润摘要数据
        """
        return self.get('profit')
    
    def get_daily_stats(self, days: int = 7) -> Dict[str, Any]:
        """
        获取每日统计数据
        
        Parameters
        ----------
        days : int, optional
            要返回的天数, 默认 7
            
        Returns
        -------
        Dict[str, Any]
            每日统计数据
        """
        return self.get('daily', params={'timescale': days})
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取Freqtrade配置信息
        
        Returns
        -------
        Dict[str, Any]
            配置信息
        """
        return self.get('show_config') 