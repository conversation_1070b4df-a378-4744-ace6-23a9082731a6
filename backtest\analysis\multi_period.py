#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
多周期表现分析模块

提供策略在不同时间周期下的表现分析，包括期间分解、周期性模式检测和滚动窗口分析。
"""

from typing import Dict, Any, Union, Optional, List, Tuple, Callable
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings

from ..base import BacktestResults
from .metrics import calculate_metrics


def analyze_by_periods(returns: pd.Series,
                    periods: Dict[str, Tuple[datetime, datetime]] = None,
                    risk_free_rate: float = 0.0) -> pd.DataFrame:
    """
    按指定时间段分析收益
    
    Parameters
    ----------
    returns : pd.Series
        策略收益率序列
    periods : Dict[str, Tuple[datetime, datetime]], optional
        时间段字典，键为时间段名称，值为(开始日期, 结束日期)的元组
    risk_free_rate : float, optional
        无风险利率，默认为0.0
        
    Returns
    -------
    pd.DataFrame
        包含不同时间段性能指标的DataFrame
    """
    # 如果未指定时间段，创建默认时间段
    if periods is None:
        # 获取数据的时间范围
        start_date = returns.index[0]
        end_date = returns.index[-1]
        
        # 将时间范围分为4个相等的时间段
        date_range = pd.date_range(start=start_date, end=end_date, periods=5)
        
        periods = {
            f'Period {i+1}': (date_range[i], date_range[i+1])
            for i in range(4)
        }
        
        # 添加全期间
        periods['Full Period'] = (start_date, end_date)
    
    # 创建一个简单的BacktestResults类
    class PeriodResult(BacktestResults):
        def __init__(self, period_returns):
            self._returns = period_returns
            self._equity = (1 + period_returns).cumprod()
            self._drawdowns = self._equity / self._equity.cummax() - 1
            
        def get_returns(self):
            return self._returns
            
        def get_drawdowns(self):
            return self._drawdowns
            
        def equity(self):
            return self._equity
            
        @property
        def trades(self):
            return pd.DataFrame()
    
    # 计算每个时间段的指标
    period_metrics = {}
    
    for name, (start, end) in periods.items():
        # 获取时间段内的收益率
        mask = (returns.index >= start) & (returns.index <= end)
        period_returns = returns[mask]
        
        # 如果时间段内有收益率数据
        if len(period_returns) > 0:
            # 创建时间段回测结果对象
            period_result = PeriodResult(period_returns)
            
            # 计算指标
            try:
                metrics = calculate_metrics(period_result, risk_free_rate=risk_free_rate)
                
                # 提取主要指标
                period_metrics[name] = {
                    'start_date': start,
                    'end_date': end,
                    'trading_days': len(period_returns),
                    'total_return': metrics['total_return'],
                    'annualized_return': metrics['annualized_return'],
                    'volatility': metrics['volatility'],
                    'sharpe_ratio': metrics['daily_sharpe'],
                    'sortino_ratio': metrics['daily_sortino'],
                    'max_drawdown': metrics['max_drawdown'],
                    'calmar_ratio': metrics['calmar_ratio']
                }
            except Exception as e:
                warnings.warn(f"Error calculating metrics for {name}: {str(e)}")
                period_metrics[name] = {
                    'start_date': start,
                    'end_date': end,
                    'trading_days': len(period_returns),
                    'error': str(e)
                }
    
    # 创建汇总DataFrame
    return pd.DataFrame(period_metrics).T


def analyze_by_timeframes(returns: pd.Series,
                        equity: Optional[pd.Series] = None,
                        risk_free_rate: float = 0.0,
                        custom_timeframes: Optional[List[str]] = None) -> Dict[str, pd.DataFrame]:
    """
    按不同时间框架分析收益
    
    分析策略在日、周、月、季、年等不同时间框架下的表现。
    
    Parameters
    ----------
    returns : pd.Series
        策略收益率序列
    equity : pd.Series, optional
        策略净值序列，默认为None（从收益率计算）
    risk_free_rate : float, optional
        无风险利率，默认为0.0
    custom_timeframes : List[str], optional
        自定义时间框架列表，默认为None（使用预定义的时间框架）
        
    Returns
    -------
    Dict[str, pd.DataFrame]
        包含不同时间框架分析结果的字典
    """
    if equity is None:
        equity = (1 + returns).cumprod()
    
    # 确保索引是DatetimeIndex
    if not isinstance(returns.index, pd.DatetimeIndex):
        raise ValueError("Returns series must have DatetimeIndex")
    
    # 定义时间框架映射
    timeframe_mapping = {
        'D': 'Daily',
        'W': 'Weekly',
        'M': 'Monthly',
        'Q': 'Quarterly',
        'Y': 'Yearly'
    }
    
    # 使用默认时间框架或自定义时间框架
    if custom_timeframes is None:
        # 默认使用所有时间框架
        timeframes_to_use = {
            'Daily': 'D',
            'Weekly': 'W',
            'Monthly': 'M',
            'Quarterly': 'Q',
            'Yearly': 'Y'
        }
    else:
        # 使用自定义时间框架
        timeframes_to_use = {}
        for tf in custom_timeframes:
            if tf in timeframe_mapping:
                timeframes_to_use[timeframe_mapping[tf]] = tf
            else:
                timeframes_to_use[f'Custom_{tf}'] = tf
    
    results = {}
    
    for name, rule in timeframes_to_use.items():
        # 重采样收益率
        if rule == 'D':
            # 日收益率不需要重采样
            period_returns = returns
        else:
            # 重采样到指定的时间频率
            period_returns = returns.resample(rule).apply(
                lambda x: (1 + x).prod() - 1
            )
        
        # 计算每个时间周期的指标
        if len(period_returns) > 0:
            # 创建统计表
            stats = pd.DataFrame({
                'Return': period_returns,
                'Cumulative': (1 + period_returns).cumprod(),
            })
            
            # 计算移动平均
            if len(period_returns) > 3:
                stats['MA3'] = period_returns.rolling(3).mean()
            if len(period_returns) > 6:
                stats['MA6'] = period_returns.rolling(6).mean()
            if len(period_returns) > 12:
                stats['MA12'] = period_returns.rolling(12).mean()
            
            # 计算胜率
            stats['Win'] = period_returns > 0
            stats['Win Rate'] = stats['Win'].rolling(12, min_periods=3).mean()
            
            results[name] = stats
    
    # 添加汇总统计
    summary = {}
    
    for name, stats in results.items():
        if not stats.empty:
            period_returns = stats['Return']
            
            # 计算每个时间框架的汇总指标
            summary[name] = {
                'total_return': (1 + period_returns).prod() - 1,
                'cagr': ((1 + period_returns).prod()) ** (252 / len(period_returns)) - 1 if name != 'Daily' else None,
                'win_rate': (period_returns > 0).mean(),
                'avg_return': period_returns.mean(),
                'std_dev': period_returns.std(),
                'skew': period_returns.skew(),
                'kurtosis': period_returns.kurtosis(),
                'max_return': period_returns.max(),
                'min_return': period_returns.min(),
                'max_consecutive_wins': max_consecutive(period_returns > 0),
                'max_consecutive_losses': max_consecutive(period_returns < 0)
            }
    
    results['Summary'] = pd.DataFrame(summary).T
    
    return results


def analyze_by_calendar(returns: pd.Series) -> Dict[str, Any]:
    """
    按日历周期分析收益
    
    分析策略在不同日历周期（日内、日间、周内、月内、季内、年内等）的表现模式。
    
    Parameters
    ----------
    returns : pd.Series
        策略收益率序列
        
    Returns
    -------
    Dict[str, Any]
        包含不同日历周期分析结果的字典
    """
    # 确保索引是DatetimeIndex
    if not isinstance(returns.index, pd.DatetimeIndex):
        raise ValueError("Returns series must have DatetimeIndex")
    
    results = {}
    
    # 日内模式（如果有时间信息）
    # 检查第一个日期索引是否有非零的时间部分
    first_time = returns.index[0].time()
    has_time_info = (first_time.hour != 0 or first_time.minute != 0 or first_time.second != 0)
    
    if has_time_info:
        # 按小时分组
        hour_stats = pd.DataFrame()
        hour_stats['mean'] = returns.groupby(returns.index.hour).mean()
        hour_stats['std'] = returns.groupby(returns.index.hour).std()
        hour_stats['count'] = returns.groupby(returns.index.hour).count()
        hour_stats['win_rate'] = returns.groupby(returns.index.hour).apply(lambda x: (x > 0).mean())
        results['HourOfDay'] = hour_stats
    
    # 日间模式
    day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    day_stats = pd.DataFrame(index=day_names)
    
    # 计算每天的统计数据
    day_groups = returns.groupby(returns.index.dayofweek)
    for day_num, day_name in enumerate(day_names):
        if day_num in day_groups.groups:
            day_data = day_groups.get_group(day_num)
            day_stats.loc[day_name, 'mean'] = day_data.mean()
            day_stats.loc[day_name, 'std'] = day_data.std()
            day_stats.loc[day_name, 'count'] = len(day_data)
            day_stats.loc[day_name, 'win_rate'] = (day_data > 0).mean()
    
    results['DayOfWeek'] = day_stats
    
    # 月内模式
    day_of_month_stats = pd.DataFrame()
    day_of_month_stats['mean'] = returns.groupby(returns.index.day).mean()
    day_of_month_stats['std'] = returns.groupby(returns.index.day).std()
    day_of_month_stats['count'] = returns.groupby(returns.index.day).count()
    day_of_month_stats['win_rate'] = returns.groupby(returns.index.day).apply(lambda x: (x > 0).mean())
    results['DayOfMonth'] = day_of_month_stats
    
    # 月间模式
    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    month_stats = pd.DataFrame(index=month_names)
    
    # 计算每月的统计数据
    month_groups = returns.groupby(returns.index.month)
    for month_num, month_name in enumerate(month_names, 1):  # 月份从1开始
        if month_num in month_groups.groups:
            month_data = month_groups.get_group(month_num)
            month_stats.loc[month_name, 'mean'] = month_data.mean()
            month_stats.loc[month_name, 'std'] = month_data.std()
            month_stats.loc[month_name, 'count'] = len(month_data)
            month_stats.loc[month_name, 'win_rate'] = (month_data > 0).mean()
    
    results['Month'] = month_stats
    
    # 季度模式
    quarter_stats = pd.DataFrame()
    quarter_stats['mean'] = returns.groupby(returns.index.quarter).mean()
    quarter_stats['std'] = returns.groupby(returns.index.quarter).std()
    quarter_stats['count'] = returns.groupby(returns.index.quarter).count()
    quarter_stats['win_rate'] = returns.groupby(returns.index.quarter).apply(lambda x: (x > 0).mean())
    results['Quarter'] = quarter_stats
    
    # 年度模式
    if len(returns.index.year.unique()) > 1:
        year_stats = pd.DataFrame()
        year_stats['mean'] = returns.groupby(returns.index.year).mean()
        year_stats['std'] = returns.groupby(returns.index.year).std()
        year_stats['count'] = returns.groupby(returns.index.year).count()
        year_stats['win_rate'] = returns.groupby(returns.index.year).apply(lambda x: (x > 0).mean())
        results['Year'] = year_stats
    
    # 创建月度效应热图数据
    if len(returns.index.year.unique()) > 1:
        # 准备月度效应矩阵
        years = sorted(returns.index.year.unique())
        monthly_matrix = pd.DataFrame(index=years, columns=month_names)
        
        # 填充月度收益率
        for year in years:
            for month_num, month_name in enumerate(month_names, 1):
                mask = (returns.index.year == year) & (returns.index.month == month_num)
                if mask.any():
                    monthly_matrix.loc[year, month_name] = returns[mask].mean()
        
        results['MonthlyMatrix'] = monthly_matrix
    
    return results


def analyze_rolling_windows(returns: pd.Series,
                          windows: List[int] = None,
                          risk_free_rate: float = 0.0) -> Dict[str, pd.DataFrame]:
    """
    滚动窗口分析
    
    使用滚动窗口计算关键指标，分析策略表现的稳定性和演变。
    
    Parameters
    ----------
    returns : pd.Series
        策略收益率序列
    windows : List[int], optional
        窗口大小列表，默认为[21, 63, 126, 252]
    risk_free_rate : float, optional
        无风险利率，默认为0.0
        
    Returns
    -------
    Dict[str, pd.DataFrame]
        包含不同窗口大小分析结果的字典
    """
    if windows is None:
        windows = [21, 63, 126, 252]  # 约1个月、3个月、6个月、1年
    
    results = {}
    
    for window in windows:
        if len(returns) < window:
            warnings.warn(f"Not enough data for {window} day window analysis")
            continue
            
        # 创建滚动指标DataFrame
        rolling_stats = pd.DataFrame(index=returns.index[window-1:])
        
        # 计算滚动回报
        rolling_stats['return'] = returns.rolling(window=window).apply(
            lambda x: (1 + x).prod() - 1)
        
        # 计算滚动波动率
        rolling_stats['volatility'] = returns.rolling(window=window).std() * np.sqrt(252)
        
        # 计算滚动Sharpe比率
        excess_returns = returns - risk_free_rate / 252
        rolling_stats['sharpe'] = (
            excess_returns.rolling(window=window).mean() / 
            excess_returns.rolling(window=window).std() * np.sqrt(252)
        )
        
        # 计算滚动最大回撤
        def rolling_max_drawdown(window_returns):
            # 计算窗口内的净值
            equity = (1 + window_returns).cumprod()
            # 计算窗口内的最大回撤
            roll_max = equity.cummax()
            drawdowns = equity / roll_max - 1
            return drawdowns.min()
        
        rolling_stats['max_drawdown'] = returns.rolling(
            window=window).apply(rolling_max_drawdown)
        
        # 计算滚动Calmar比率
        rolling_stats['calmar'] = (
            rolling_stats['return'] / 
            rolling_stats['max_drawdown'].abs()
        )
        
        # 计算滚动胜率
        rolling_stats['win_rate'] = returns.rolling(window=window).apply(
            lambda x: (x > 0).mean())
        
        results[f"{window}d"] = rolling_stats
    
    return results


def plot_calendar_heatmap(calendar_analysis: Dict[str, Any],
                        analysis_type: str = 'DayOfWeek',
                        metric: str = 'mean',
                        figsize: tuple = (12, 8),
                        cmap: str = 'RdYlGn',
                        **kwargs) -> plt.Figure:
    """
    绘制日历分析热图
    
    Parameters
    ----------
    calendar_analysis : Dict[str, Any]
        由analyze_by_calendar函数生成的分析结果
    analysis_type : str, optional
        要绘制的日历类型，默认为'DayOfWeek'
    metric : str, optional
        要绘制的指标，默认为'mean'
    figsize : tuple, optional
        图表大小，默认为(12, 8)
    cmap : str, optional
        颜色映射，默认为'RdYlGn'
    **kwargs : dict
        其他绘图参数
        
    Returns
    -------
    plt.Figure
        图表对象
    """
    if analysis_type not in calendar_analysis:
        raise ValueError(f"Analysis type '{analysis_type}' not found in calendar analysis")
    
    if analysis_type == 'MonthlyMatrix':
        # 月度矩阵热图
        data = calendar_analysis[analysis_type]
    else:
        # 其他日历分析
        if metric not in calendar_analysis[analysis_type].columns:
            raise ValueError(f"Metric '{metric}' not found in {analysis_type} analysis")
        
        data = calendar_analysis[analysis_type][metric]
    
    # 创建图表
    fig, ax = plt.subplots(figsize=figsize)
    
    if analysis_type == 'MonthlyMatrix':
        # 月度矩阵热图
        sns.heatmap(data, cmap=cmap, annot=True, fmt='.2%', 
                   linewidths=0.5, ax=ax, center=0)
        title = 'Monthly Returns by Year'
    else:
        # 转换为DataFrame用于热图
        df = data.to_frame().T
        
        # 绘制热图
        sns.heatmap(df, cmap=cmap, annot=True, fmt='.4f' if metric == 'mean' else '.2f', 
                   linewidths=0.5, ax=ax)
        title = f'{metric.capitalize()} Returns by {analysis_type}'
    
    # 设置标题
    ax.set_title(title, fontsize=14)
    
    # 调整布局
    plt.tight_layout()
    
    return fig


def plot_rolling_windows(rolling_analysis: Dict[str, pd.DataFrame],
                       metrics: List[str] = None,
                       figsize: tuple = (15, 10),
                       **kwargs) -> plt.Figure:
    """
    绘制滚动窗口分析图表
    
    Parameters
    ----------
    rolling_analysis : Dict[str, pd.DataFrame]
        由analyze_rolling_windows函数生成的分析结果
    metrics : List[str], optional
        要绘制的指标列表，默认为['return', 'volatility', 'sharpe', 'max_drawdown']
    figsize : tuple, optional
        图表大小，默认为(15, 10)
    **kwargs : dict
        其他绘图参数
        
    Returns
    -------
    plt.Figure
        图表对象
    """
    if not rolling_analysis:
        raise ValueError("No rolling window analysis results provided")
    
    # 默认指标
    if metrics is None:
        metrics = ['return', 'volatility', 'sharpe', 'max_drawdown']
    
    # 过滤掉不存在的指标
    available_metrics = set()
    for window_data in rolling_analysis.values():
        available_metrics.update(window_data.columns)
    
    valid_metrics = [m for m in metrics if m in available_metrics]
    
    if not valid_metrics:
        raise ValueError("No valid metrics found for plotting")
        
    # 创建图表
    n_metrics = len(valid_metrics)
    fig, axes = plt.subplots(n_metrics, 1, figsize=figsize, sharex=True)
    
    # 获取所有窗口大小
    windows = list(rolling_analysis.keys())
    
    # 确保axes是一个列表
    if n_metrics == 1:
        axes = [axes]
    
    # 为每个指标创建一个子图
    for i, metric in enumerate(valid_metrics):
        ax = axes[i]
        
        # 为每个窗口大小绘制一条线
        for window, df in rolling_analysis.items():
            if metric in df.columns:
                ax.plot(df.index, df[metric], label=window, linewidth=2)
        
        # 设置标题和标签
        ax.set_title(f'Rolling {metric.capitalize()}', fontsize=12)
        ax.set_ylabel(metric)
        ax.grid(True, alpha=0.3)
        
        # 仅在第一个子图上添加图例
        if i == 0:
            ax.legend(loc='upper left')
    
    # 设置x轴标签
    axes[-1].set_xlabel('Date')
    
    # 调整布局
    plt.tight_layout()
    
    return fig


def max_consecutive(series: pd.Series) -> int:
    """计算序列中最大连续True值的数量"""
    # 将布尔序列转换为0和1
    int_series = series.astype(int)
    
    # 计算元素变化的位置
    changes = int_series.diff().fillna(int_series.iloc[0]).ne(0)
    
    # 为每个连续段分配一个组标识
    groups = changes.cumsum()
    
    # 计算每个组的大小
    counts = int_series.groupby(groups).sum()
    
    # 返回最大的连续True值数量
    return counts.max() if len(counts) > 0 else 0


def plot_comparison_by_periods(period_analysis: pd.DataFrame,
                             metrics: List[str] = None,
                             figsize: tuple = (15, 10),
                             **kwargs) -> plt.Figure:
    """
    绘制时间段比较图表
    
    Parameters
    ----------
    period_analysis : pd.DataFrame
        由analyze_by_periods函数生成的分析结果
    metrics : List[str], optional
        要绘制的指标列表，默认为['total_return', 'annualized_return', 'sharpe_ratio', 'max_drawdown']
    figsize : tuple, optional
        图表大小，默认为(15, 10)
    **kwargs : dict
        其他绘图参数
        
    Returns
    -------
    plt.Figure
        图表对象
    """
    # 默认指标
    if metrics is None:
        metrics = ['total_return', 'annualized_return', 'sharpe_ratio', 'max_drawdown']
    
    # 过滤出有效的列
    valid_metrics = [m for m in metrics if m in period_analysis.columns]
    
    if not valid_metrics:
        raise ValueError("No valid metrics found for plotting")
    
    # 创建图表
    n_metrics = len(valid_metrics)
    fig, axes = plt.subplots(1, n_metrics, figsize=figsize)
    
    # 确保axes是一个列表
    if n_metrics == 1:
        axes = [axes]
    
    # 绘制每个指标的条形图
    for i, metric in enumerate(valid_metrics):
        ax = axes[i]
        
        # 绘制条形图
        period_analysis[metric].plot(kind='bar', ax=ax)
        
        # 设置标题和标签
        ax.set_title(f'{metric.replace("_", " ").title()}', fontsize=12)
        ax.set_ylabel(metric)
        ax.grid(True, alpha=0.3)
        
        # 将轴标签旋转，以便更好地显示
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    # 调整布局
    plt.tight_layout()
    
    return fig


def analyze_by_period(returns: Union[pd.Series, BacktestResults],
                    period_type: str = 'year',
                    risk_free_rate: float = 0.0) -> pd.DataFrame:
    """
    按时间周期分析收益（兼容性函数）
    
    Parameters
    ----------
    returns : pd.Series or BacktestResults
        策略收益率序列或BacktestResults对象
    period_type : str, optional
        周期类型，可选值: 'year', 'quarter', 'month'，默认为'year'
    risk_free_rate : float, optional
        无风险利率，默认为0.0
        
    Returns
    -------
    pd.DataFrame
        包含不同时间段性能指标的DataFrame
    """
    # 提取收益率序列
    if isinstance(returns, BacktestResults):
        returns_series = returns.get_returns() if callable(returns.get_returns) else returns.get_returns
    else:
        returns_series = returns
    
    # 确保索引是DatetimeIndex
    if not isinstance(returns_series.index, pd.DatetimeIndex):
        raise ValueError("Returns series must have DatetimeIndex")
    
    # 根据period_type划分时间段
    periods = {}
    
    if period_type == 'year':
        # 按年度划分
        years = returns_series.index.year.unique()
        for year in years:
            start = pd.Timestamp(year=year, month=1, day=1)
            end = pd.Timestamp(year=year, month=12, day=31)
            periods[f'Year {year}'] = (start, end)
    
    elif period_type == 'quarter':
        # 按季度划分
        for year in returns_series.index.year.unique():
            for quarter in range(1, 5):
                start_month = (quarter - 1) * 3 + 1
                end_month = quarter * 3
                start = pd.Timestamp(year=year, month=start_month, day=1)
                # 计算季度末
                if end_month == 12:
                    end = pd.Timestamp(year=year, month=end_month, day=31)
                else:
                    end = pd.Timestamp(year=year, month=end_month + 1, day=1) - pd.Timedelta(days=1)
                periods[f'Q{quarter} {year}'] = (start, end)
    
    elif period_type == 'month':
        # 按月份划分
        for year in returns_series.index.year.unique():
            for month in range(1, 13):
                start = pd.Timestamp(year=year, month=month, day=1)
                # 计算月末
                if month == 12:
                    end = pd.Timestamp(year=year, month=month, day=31)
                else:
                    end = pd.Timestamp(year=year, month=month + 1, day=1) - pd.Timedelta(days=1)
                periods[f'{year}-{month:02d}'] = (start, end)
    
    else:
        raise ValueError(f"Invalid period_type: {period_type}. Choose from 'year', 'quarter', 'month'")
    
    # 使用analyze_by_periods函数
    return analyze_by_periods(returns_series, periods, risk_free_rate)


def analyze_calendar_effects(returns: Union[pd.Series, BacktestResults]) -> Dict[str, Any]:
    """
    分析日历效应（兼容性函数）
    
    Parameters
    ----------
    returns : pd.Series or BacktestResults
        策略收益率序列或BacktestResults对象
        
    Returns
    -------
    Dict[str, Any]
        包含不同日历周期分析结果的字典
    """
    # 提取收益率序列
    if isinstance(returns, BacktestResults):
        returns_series = returns.get_returns() if callable(returns.get_returns) else returns.get_returns
    else:
        returns_series = returns
    
    # 调用analyze_by_calendar函数
    return analyze_by_calendar(returns_series)


def plot_period_performance(returns: Union[pd.Series, BacktestResults],
                          period_type: str = 'year',
                          metrics: List[str] = None,
                          figsize: tuple = (15, 10),
                          **kwargs) -> plt.Figure:
    """
    绘制期间表现对比图（兼容性函数）
    
    Parameters
    ----------
    returns : pd.Series or BacktestResults
        策略收益率序列或BacktestResults对象
    period_type : str, optional
        周期类型，可选值: 'year', 'quarter', 'month'，默认为'year'
    metrics : List[str], optional
        要绘制的指标列表，默认为['total_return', 'annualized_return', 'sharpe_ratio', 'max_drawdown']
    figsize : tuple, optional
        图表大小，默认为(15, 10)
    **kwargs : dict
        其他绘图参数
        
    Returns
    -------
    plt.Figure
        图表对象
    """
    # 先通过analyze_by_period获取期间分析结果
    period_analysis = analyze_by_period(returns, period_type)
    
    # 然后使用plot_comparison_by_periods函数绘制图表
    return plot_comparison_by_periods(period_analysis, metrics, figsize, **kwargs)


def analyze_by_timeframe(returns: Union[pd.Series, BacktestResults], 
                        timeframes: List[str] = None,
                        risk_free_rate: float = 0.0) -> Dict[str, pd.DataFrame]:
    """
    按不同时间框架分析收益（兼容性函数）
    
    分析策略在日、周、月等不同时间框架下的表现。
    
    Parameters
    ----------
    returns : pd.Series or BacktestResults
        策略收益率序列或BacktestResults对象
    timeframes : List[str], optional
        要分析的时间框架列表，默认为['D', 'W', 'M']
    risk_free_rate : float, optional
        无风险利率，默认为0.0
        
    Returns
    -------
    Dict[str, pd.DataFrame]
        包含不同时间框架分析结果的字典
    """
    # 提取收益率序列
    if isinstance(returns, BacktestResults):
        returns_series = returns.get_returns()
        if callable(returns_series):  # 如果get_returns是方法而不是属性
            returns_series = returns_series()
        equity_series = returns.equity()
        if callable(equity_series):  # 如果equity是方法而不是属性
            equity_series = equity_series()
    else:
        returns_series = returns
        equity_series = None
    
    # 定义时间框架映射
    if timeframes is None:
        timeframes = ['D', 'W', 'M']
    
    # 创建自定义的analyze_by_timeframes函数调用
    return analyze_by_timeframes(returns_series, equity_series, risk_free_rate, custom_timeframes=timeframes)


def plot_regime_analysis(returns: Union[pd.Series, BacktestResults],
                        market_data: pd.Series,
                        n_regimes: int = 3,
                        figsize: tuple = (15, 10),
                        **kwargs) -> plt.Figure:
    """
    绘制市场状态(Regime)分析图
    
    根据市场数据将市场划分为不同状态(如牛市、熊市、震荡市),
    并分析策略在各种市场状态下的表现。
    
    Parameters
    ----------
    returns : pd.Series or BacktestResults
        策略收益率序列或BacktestResults对象
    market_data : pd.Series
        市场数据，用于状态划分(如市场指数、波动率等)
    n_regimes : int, optional
        市场状态数量，默认为3
    figsize : tuple, optional
        图表大小，默认为(15, 10)
    **kwargs : dict
        其他绘图参数
        
    Returns
    -------
    plt.Figure
        图表对象
    """
    # 提取收益率序列
    if isinstance(returns, BacktestResults):
        returns_series = returns.get_returns() if callable(returns.get_returns) else returns.get_returns
    else:
        returns_series = returns
    
    # 确保市场数据和收益率有相同的索引
    common_index = market_data.index.intersection(returns_series.index)
    if len(common_index) == 0:
        raise ValueError("Market data and returns series have no common dates")
    
    market_data = market_data.loc[common_index]
    returns_series = returns_series.loc[common_index]
    
    # 根据市场数据将市场划分为n_regimes个状态
    # 这里使用分位数划分，也可以使用聚类等其他方法
    labels = ['Regime ' + str(i+1) for i in range(n_regimes)]
    regime = pd.qcut(market_data, n_regimes, labels=labels)
    
    # 计算每个状态下的策略表现
    regime_performance = {}
    for r in labels:
        regime_returns = returns_series[regime == r]
        if len(regime_returns) > 0:
            regime_performance[r] = {
                'mean_return': regime_returns.mean(),
                'cum_return': (1 + regime_returns).prod() - 1,
                'volatility': regime_returns.std() * np.sqrt(252),
                'win_rate': (regime_returns > 0).mean(),
                'count': len(regime_returns),
                'percent_total': len(regime_returns) / len(returns_series) * 100
            }
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=figsize)
    axes = axes.flatten()
    
    # 绘制市场状态序列
    ax0 = axes[0]
    regime_series = pd.Series(regime, index=common_index)
    
    # 将regime序列转换为数值以用于绘图
    regime_numeric = regime_series.cat.codes
    
    # 绘制市场数据和状态
    ax0.plot(market_data, color='blue', alpha=0.6, label='Market Data')
    ax0.set_ylabel('Market Data', color='blue')
    ax0.tick_params(axis='y', colors='blue')
    
    # 创建第二个Y轴
    ax0_2 = ax0.twinx()
    ax0_2.fill_between(regime_numeric.index, regime_numeric, alpha=0.3, color='red')
    ax0_2.set_ylabel('Market Regime', color='red')
    ax0_2.tick_params(axis='y', colors='red')
    ax0_2.set_yticks(range(n_regimes))
    ax0_2.set_yticklabels(labels)
    
    ax0.set_title('Market Regimes Over Time')
    ax0.grid(True, alpha=0.3)
    
    # 绘制每个状态的累计收益
    regime_cum_returns = {}
    for r in labels:
        r_returns = returns_series[regime == r]
        if len(r_returns) > 0:
            regime_cum_returns[r] = (1 + r_returns).cumprod()
    
    ax1 = axes[1]
    for r, cum_ret in regime_cum_returns.items():
        ax1.plot(cum_ret, label=r)
    
    ax1.set_title('Cumulative Returns by Regime')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 绘制状态表现对比条形图
    ax2 = axes[2]
    
    # 准备条形图数据
    metrics = ['mean_return', 'cum_return', 'volatility', 'win_rate']
    metric_labels = ['Mean Daily Return', 'Cumulative Return', 'Volatility', 'Win Rate']
    
    for i, (metric, metric_label) in enumerate(zip(metrics, metric_labels)):
        values = [regime_performance[r][metric] for r in labels]
        positions = np.arange(len(labels)) + i * 0.2
        ax2.bar(positions, values, width=0.2, label=metric_label)
    
    ax2.set_title('Performance Metrics by Regime')
    ax2.set_xticks(np.arange(len(labels)) + 0.3)
    ax2.set_xticklabels(labels)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 绘制状态分布饼图
    ax3 = axes[3]
    counts = [regime_performance[r]['count'] for r in labels]
    ax3.pie(counts, labels=labels, autopct='%1.1f%%', startangle=90)
    ax3.set_title('Distribution of Market Regimes')
    
    # 调整布局
    plt.tight_layout()
    
    return fig


def plot_drawdown_recovery_analysis(returns: Union[pd.Series, BacktestResults],
                                 min_drawdown: float = -0.05,
                                 figsize: tuple = (15, 10),
                                 **kwargs) -> plt.Figure:
    """
    绘制回撤恢复分析图
    
    分析策略的回撤特征和恢复模式，包括回撤深度、持续时间和恢复时间。
    
    Parameters
    ----------
    returns : pd.Series or BacktestResults
        策略收益率序列或BacktestResults对象
    min_drawdown : float, optional
        要考虑的最小回撤阈值，默认为-0.05(5%)
    figsize : tuple, optional
        图表大小，默认为(15, 10)
    **kwargs : dict
        其他绘图参数
        
    Returns
    -------
    plt.Figure
        图表对象
    """
    # 提取收益率和净值序列
    if isinstance(returns, BacktestResults):
        returns_series = returns.get_returns() if callable(returns.get_returns) else returns.get_returns
        equity_series = returns.equity() if callable(returns.equity) else returns.equity
        drawdowns_series = returns.get_drawdowns() if callable(returns.get_drawdowns) else returns.get_drawdowns
    else:
        returns_series = returns
        equity_series = (1 + returns_series).cumprod()
        drawdowns_series = equity_series / equity_series.cummax() - 1
    
    # 识别回撤周期
    drawdown_periods = []
    in_drawdown = False
    start_idx = 0
    
    for i, drawdown in enumerate(drawdowns_series):
        if not in_drawdown and drawdown <= min_drawdown:
            # 开始新的回撤
            in_drawdown = True
            start_idx = i
        elif in_drawdown and drawdown == 0:
            # 回撤恢复
            in_drawdown = False
            drawdown_periods.append({
                'start_idx': start_idx,
                'bottom_idx': drawdowns_series.iloc[start_idx:i+1].idxmin(),
                'end_idx': i,
                'max_drawdown': drawdowns_series.iloc[start_idx:i+1].min(),
                'recovery_time': (drawdowns_series.index[i] - drawdowns_series.index[start_idx]).days
            })
    
    # 如果最后仍在回撤中
    if in_drawdown:
        i = len(drawdowns_series) - 1
        drawdown_periods.append({
            'start_idx': start_idx,
            'bottom_idx': drawdowns_series.iloc[start_idx:i+1].idxmin(),
            'end_idx': None,  # 尚未恢复
            'max_drawdown': drawdowns_series.iloc[start_idx:i+1].min(),
            'recovery_time': None  # 尚未恢复
        })
    
    # 过滤符合最小回撤阈值的周期
    drawdown_periods = [p for p in drawdown_periods if p['max_drawdown'] <= min_drawdown]
    
    # 创建图表
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=figsize)
    
    # 绘制净值曲线和回撤标记
    ax1.plot(equity_series, label='Equity')
    
    # 标记重要回撤区域
    for period in drawdown_periods:
        start = drawdowns_series.index[period['start_idx']]
        bottom = period['bottom_idx']
        end = drawdowns_series.index[period['end_idx']] if period['end_idx'] is not None else drawdowns_series.index[-1]
        
        # 标记回撤开始
        ax1.axvline(start, color='red', alpha=0.5, linestyle='--')
        
        # 标记回撤最低点
        ax1.axvline(bottom, color='darkred', alpha=0.5, linestyle='-.')
        
        # 标记回撤结束
        if period['end_idx'] is not None:
            ax1.axvline(end, color='green', alpha=0.5, linestyle='--')
    
    ax1.set_title('Equity Curve with Drawdown Periods')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 绘制回撤曲线
    ax2.fill_between(drawdowns_series.index, drawdowns_series.values, 0,
                    where=(drawdowns_series < 0), color='red', alpha=0.3)
    ax2.plot(drawdowns_series, color='red', label='Drawdowns')
    ax2.axhline(min_drawdown, color='black', linestyle='--',
               label=f'Threshold ({min_drawdown:.1%})')
    
    ax2.set_title('Drawdowns')
    ax2.set_ylim(min(drawdowns_series.min() * 1.1, -0.1), 0.05)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 绘制回撤分析
    if drawdown_periods:
        # 准备数据
        max_drawdowns = [p['max_drawdown'] for p in drawdown_periods]
        recovery_times = [p['recovery_time'] for p in drawdown_periods if p['recovery_time'] is not None]
        drawdown_durations = [(drawdowns_series.index[p['end_idx'] if p['end_idx'] is not None else -1] -
                              drawdowns_series.index[p['start_idx']]).days
                             for p in drawdown_periods]
        
        # 回撤深度与恢复时间散点图
        if recovery_times:
            ax3.scatter([p['max_drawdown'] for p in drawdown_periods if p['recovery_time'] is not None], 
                       recovery_times,
                       s=50, alpha=0.7)
            
            # 添加趋势线
            try:
                z = np.polyfit([p['max_drawdown'] for p in drawdown_periods if p['recovery_time'] is not None], 
                              recovery_times, 1)
                p = np.poly1d(z)
                x_trend = np.linspace(min_drawdown, 0, 10)
                ax3.plot(x_trend, p(x_trend), "r--", alpha=0.8)
            except:
                pass
            
            ax3.set_title('Drawdown Depth vs Recovery Time')
            ax3.set_xlabel('Drawdown Depth')
            ax3.set_ylabel('Recovery Time (Days)')
            ax3.grid(True, alpha=0.3)
        else:
            ax3.text(0.5, 0.5, 'No completed recovery periods to analyze', 
                    ha='center', va='center', transform=ax3.transAxes)
    else:
        ax3.text(0.5, 0.5, 'No drawdown periods meeting the threshold criteria', 
                ha='center', va='center', transform=ax3.transAxes)
    
    # 调整布局
    plt.tight_layout()
    
    return fig


__all__ = [
    'analyze_by_periods',
    'analyze_by_period',
    'analyze_by_timeframes',
    'analyze_by_timeframe',
    'analyze_by_calendar',
    'analyze_calendar_effects',
    'analyze_rolling_windows',
    'plot_calendar_heatmap',
    'plot_rolling_windows',
    'plot_comparison_by_periods',
    'plot_period_performance',
    'plot_regime_analysis',
    'plot_drawdown_recovery_analysis'
] 