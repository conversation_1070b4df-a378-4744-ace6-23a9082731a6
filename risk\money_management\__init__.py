"""
资金管理系统模块

提供资金管理策略和交易规模调整功能，帮助控制风险和优化资金使用效率。
"""

from risk.money_management.base import (
    MoneyManager, 
    PositionSizer
)
from risk.money_management.position_sizing import (
    FixedAmountSizer,
    FixedPercentSizer,
    KellyCriterionSizer,
    OptimalFSizer,
    VolatilityAdjustedSizer,
    MaxDrawdownSizer
)
from risk.money_management.capital_allocation import (
    EqualAllocationStrategy,
    VolatilityAdjustedAllocation,
    PerformanceBasedAllocation
)

__all__ = [
    # 核心基类
    'MoneyManager',
    'PositionSizer',
    
    # 仓位规模策略
    'FixedAmountSizer',
    'FixedPercentSizer',
    'KellyCriterionSizer',
    'OptimalFSizer',
    'VolatilityAdjustedSizer',
    'MaxDrawdownSizer',
    
    # 资金分配策略
    'EqualAllocationStrategy',
    'VolatilityAdjustedAllocation',
    'PerformanceBasedAllocation',
]

# 版本信息
__version__ = '0.1.0' 