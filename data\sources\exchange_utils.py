"""
交易所工具函数

提供与加密货币交易所交互的常用工具函数。
"""

import re
import time
import logging
from typing import Dict, List, Tuple, Optional, Any, Union, Callable

import ccxt

# 配置日志
logger = logging.getLogger(__name__)


def normalize_symbol(symbol: str, exchange_id: Optional[str] = None) -> str:
    """
    标准化交易对符号，统一格式
    
    Args:
        symbol: 交易对符号，如"BTC/USDT", "btcusdt"
        exchange_id: 交易所ID (可选)
        
    Returns:
        标准化的交易对符号，如"BTC/USDT"
    """
    # 去除空格和转换为大写
    symbol = symbol.strip().upper()
    
    # 如果已经是标准格式（包含"/"），则返回
    if "/" in symbol:
        return symbol
    
    # 尝试使用正则表达式提取基础货币和报价货币
    # 常见模式如 BTCUSDT, BTC-USDT, BTC_USDT
    patterns = [
        r"^([A-Z0-9]{2,10})[-_]?([A-Z]{2,10})$",  # 如BTC-USDT, BTC_USDT
        r"^([A-Z0-9]{2,10})([A-Z]{2,10})$",       # 如BTCUSDT
    ]
    
    for pattern in patterns:
        match = re.match(pattern, symbol)
        if match:
            base, quote = match.groups()
            return f"{base}/{quote}"
    
    # 如果没有匹配成功且提供了交易所ID，尝试使用交易所的市场信息
    if exchange_id:
        try:
            exchange_class = getattr(ccxt, exchange_id)
            exchange = exchange_class()
            
            # 加载市场
            exchange.load_markets()
            
            # 尝试查找匹配的市场
            for market_symbol in exchange.markets:
                normalized = exchange.markets[market_symbol]['id'].upper()
                if normalized == symbol:
                    return market_symbol
        except Exception:
            pass  # 忽略交易所错误，继续使用默认方法
    
    # 如果无法确定格式，返回原始符号
    return symbol


def rate_limit_handler(exchange: ccxt.Exchange, 
                      cooldown: float = 1.0, 
                      max_retries: int = 3) -> Callable:
    """
    创建请求处理器，处理速率限制异常
    
    Args:
        exchange: CCXT交易所实例
        cooldown: 请求被限制时的冷却时间（秒）
        max_retries: 最大重试次数
        
    Returns:
        处理请求的函数，自动处理速率限制
    """
    def handler(method, *args, **kwargs):
        """
        调用交易所API方法，处理速率限制异常
        
        Args:
            method: 要调用的方法名
            args: 位置参数
            kwargs: 关键字参数
            
        Returns:
            API调用的结果
        """
        retries = 0
        while retries < max_retries:
            try:
                # 调用方法
                exchange_method = getattr(exchange, method)
                result = exchange_method(*args, **kwargs)
                return result
            except ccxt.RateLimitExceeded:
                retries += 1
                if retries >= max_retries:
                    raise  # 重试次数用完，抛出异常
                
                # 指数退避
                sleep_time = cooldown * (2 ** (retries - 1))
                time.sleep(sleep_time)
            except Exception:
                # 其他异常直接抛出
                raise
                
    return handler


def get_timeframe_milliseconds(timeframe: str) -> int:
    """
    将时间周期转换为毫秒
    
    Args:
        timeframe: 时间周期字符串，如"1m", "1h", "1d"
        
    Returns:
        时间周期的毫秒数
    """
    # 解析时间周期
    match = re.match(r"^(\d+)([a-zA-Z])$", timeframe)
    if not match:
        raise ValueError(f"无效的时间周期格式: {timeframe}")
    
    value, unit = int(match.group(1)), match.group(2).lower()
    
    # 转换为毫秒
    if unit == "m":
        return value * 60 * 1000
    elif unit == "h":
        return value * 60 * 60 * 1000
    elif unit == "d":
        return value * 24 * 60 * 60 * 1000
    elif unit == "w":
        return value * 7 * 24 * 60 * 60 * 1000
    elif unit == "M":
        return value * 30 * 24 * 60 * 60 * 1000  # 近似
    else:
        raise ValueError(f"不支持的时间单位: {unit}")


def split_time_range(start_timestamp: int, end_timestamp: int, 
                    max_range_milliseconds: int) -> List[Tuple[int, int]]:
    """
    将时间范围分割为多个小范围
    
    Args:
        start_timestamp: 开始时间戳（毫秒）
        end_timestamp: 结束时间戳（毫秒）
        max_range_milliseconds: 每个范围的最大长度（毫秒）
        
    Returns:
        时间范围列表，每个元素为(开始时间戳, 结束时间戳)
    """
    ranges = []
    current_start = start_timestamp
    
    while current_start < end_timestamp:
        current_end = min(current_start + max_range_milliseconds, end_timestamp)
        ranges.append((current_start, current_end))
        current_start = current_end
    
    return ranges


def get_exchange_instance(exchange_id: str, config: Optional[Dict[str, Any]] = None) -> ccxt.Exchange:
    """
    创建交易所实例
    
    Args:
        exchange_id: 交易所ID
        config: 交易所配置参数 (可选)
        
    Returns:
        CCXT交易所实例
    """
    if exchange_id not in ccxt.exchanges:
        raise ValueError(f"不支持的交易所: {exchange_id}")
    
    exchange_class = getattr(ccxt, exchange_id)
    
    if config:
        exchange = exchange_class(config)
    else:
        exchange = exchange_class()
    
    # 加载市场
    exchange.load_markets()
    
    return exchange 