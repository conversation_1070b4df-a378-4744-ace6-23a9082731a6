#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
V2Ray协议转换器

功能：
- 自动配置trojan → HTTP/SOCKS5转换
- 管理V2Ray进程生命周期
- 提供转换器状态检测
"""

import json
import os
import subprocess
import time
import logging
import requests
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class TrojanConfig:
    """Trojan节点配置"""
    server: str
    port: int
    password: str
    sni: Optional[str] = None
    allowinsecure: bool = False


@dataclass
class V2RayPorts:
    """V2Ray端口配置"""
    socks5: int = 10808
    http: int = 10809


class V2RayConverter:
    """
    V2Ray协议转换器
    
    将trojan协议转换为标准HTTP/SOCKS5代理，供FreqTrade使用
    """
    
    def __init__(self, 
                 config_dir: str = "config/v2ray",
                 binary_path: Optional[str] = None):
        """
        初始化V2Ray转换器
        
        Parameters
        ----------
        config_dir : str
            配置文件目录
        binary_path : Optional[str]
            V2Ray可执行文件路径，None则自动查找
        """
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        self.config_file = self.config_dir / "config.json"
        self.binary_path = binary_path or self._find_v2ray_binary()
        self.process = None
        self.ports = V2RayPorts()
        
        logger.info(f"V2Ray转换器初始化: 配置目录={self.config_dir}")
    
    def _find_v2ray_binary(self) -> Optional[str]:
        """查找V2Ray可执行文件"""
        common_paths = [
            "v2ray/v2ray.exe",
            "v2ray/v2ray",
            "xray/xray.exe", 
            "xray/xray",
            "C:/Program Files/v2ray/v2ray.exe",
            "/usr/local/bin/v2ray",
            "/usr/bin/v2ray"
        ]
        
        for path in common_paths:
            if os.path.exists(path) and os.access(path, os.X_OK):
                logger.info(f"找到V2Ray: {path}")
                return path
        
        # 检查PATH环境变量
        for binary in ["v2ray", "xray", "v2ray.exe", "xray.exe"]:
            try:
                result = subprocess.run(['which', binary], 
                                      capture_output=True, 
                                      text=True, 
                                      timeout=5)
                if result.returncode == 0 and result.stdout.strip():
                    path = result.stdout.strip()
                    logger.info(f"在PATH中找到V2Ray: {path}")
                    return path
            except:
                continue
        
        logger.warning("未找到V2Ray可执行文件")
        return None
    
    def generate_config(self, trojan_config: TrojanConfig) -> Dict[str, Any]:
        """
        生成V2Ray配置文件
        
        Parameters
        ----------
        trojan_config : TrojanConfig
            Trojan节点配置
            
        Returns
        -------
        Dict[str, Any]
            V2Ray配置字典
        """
        config = {
            "log": {
                "loglevel": "warning"
            },
            "inbounds": [
                {
                    "tag": "socks-in",
                    "port": self.ports.socks5,
                    "protocol": "socks",
                    "settings": {
                        "udp": True
                    },
                    "listen": "127.0.0.1"
                },
                {
                    "tag": "http-in", 
                    "port": self.ports.http,
                    "protocol": "http",
                    "settings": {},
                    "listen": "127.0.0.1"
                }
            ],
            "outbounds": [
                {
                    "tag": "trojan-out",
                    "protocol": "trojan",
                    "settings": {
                        "servers": [
                            {
                                "address": trojan_config.server,
                                "port": trojan_config.port,
                                "password": trojan_config.password
                            }
                        ]
                    },
                    "streamSettings": {
                        "network": "tcp",
                        "security": "tls",
                        "tlsSettings": {
                            "serverName": trojan_config.sni or trojan_config.server,
                            "allowInsecure": trojan_config.allowinsecure
                        }
                    }
                },
                {
                    "tag": "direct",
                    "protocol": "freedom"
                }
            ],
            "routing": {
                "rules": [
                    {
                        "type": "field",
                        "ip": ["*********/8", "10.0.0.0/8", "***********/16"],
                        "outboundTag": "direct"
                    },
                    {
                        "type": "field",
                        "inboundTag": ["socks-in", "http-in"],
                        "outboundTag": "trojan-out"
                    }
                ]
            }
        }
        
        return config
    
    def create_config_file(self, trojan_config: TrojanConfig) -> bool:
        """
        创建V2Ray配置文件
        
        Parameters
        ----------
        trojan_config : TrojanConfig
            Trojan节点配置
            
        Returns
        -------
        bool
            创建是否成功
        """
        try:
            config = self.generate_config(trojan_config)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"V2Ray配置文件已创建: {self.config_file}")
            return True
            
        except Exception as e:
            logger.error(f"创建V2Ray配置失败: {e}")
            return False
    
    def start(self) -> bool:
        """
        启动V2Ray转换器
        
        Returns
        -------
        bool
            启动是否成功
        """
        if not self.binary_path:
            logger.error("V2Ray可执行文件未找到")
            return False
        
        if not self.config_file.exists():
            logger.error(f"V2Ray配置文件不存在: {self.config_file}")
            return False
        
        if self.is_running():
            logger.warning("V2Ray转换器已在运行")
            return True
        
        try:
            # 启动V2Ray进程
            self.process = subprocess.Popen([
                self.binary_path,
                "-config", str(self.config_file)
            ], 
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            # 等待启动
            time.sleep(2)
            
            # 检查进程状态
            if self.process.poll() is None:
                # 测试转换器连接
                if self._test_converter():
                    logger.info(f"V2Ray转换器启动成功 PID={self.process.pid}")
                    return True
                else:
                    logger.error("V2Ray转换器启动失败：连接测试失败")
                    self.stop()
                    return False
            else:
                logger.error(f"V2Ray进程启动失败，退出码: {self.process.returncode}")
                return False
                
        except Exception as e:
            logger.error(f"启动V2Ray转换器失败: {e}")
            return False
    
    def stop(self) -> bool:
        """
        停止V2Ray转换器
        
        Returns
        -------
        bool
            停止是否成功
        """
        if not self.process:
            return True
        
        try:
            self.process.terminate()
            
            # 等待进程结束
            try:
                self.process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                # 强制终止
                self.process.kill()
                self.process.wait()
            
            self.process = None
            logger.info("V2Ray转换器已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止V2Ray转换器失败: {e}")
            return False
    
    def is_running(self) -> bool:
        """
        检查V2Ray转换器是否运行
        
        Returns
        -------
        bool
            是否正在运行
        """
        if self.process and self.process.poll() is None:
            return True
        return False
    
    def _test_converter(self) -> bool:
        """测试转换器连接"""
        test_urls = [
            'https://httpbin.org/ip',
            'https://api.binance.com/api/v3/ping'
        ]
        
        # 测试HTTP代理
        try:
            session = requests.Session()
            session.proxies.update({
                'http': f'http://127.0.0.1:{self.ports.http}',
                'https': f'http://127.0.0.1:{self.ports.http}'
            })
            
            for url in test_urls:
                try:
                    response = session.get(url, timeout=5)
                    if response.status_code == 200:
                        logger.debug(f"V2Ray HTTP转换器测试成功: {url}")
                        return True
                except:
                    continue
        except:
            pass
        
        return False
    
    def get_proxy_urls(self) -> Tuple[str, str]:
        """
        获取代理URL
        
        Returns
        -------
        Tuple[str, str]
            (HTTP代理URL, SOCKS5代理URL)
        """
        http_url = f'http://127.0.0.1:{self.ports.http}'
        socks5_url = f'socks5://127.0.0.1:{self.ports.socks5}'
        return http_url, socks5_url
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取转换器状态
        
        Returns
        -------
        Dict[str, Any]
            状态信息
        """
        http_url, socks5_url = self.get_proxy_urls()
        
        return {
            "running": self.is_running(),
            "pid": self.process.pid if self.process else None,
            "config_file": str(self.config_file),
            "binary_path": self.binary_path,
            "http_proxy": http_url,
            "socks5_proxy": socks5_url,
            "ports": {
                "http": self.ports.http,
                "socks5": self.ports.socks5
            }
        }


def create_trojan_converter(trojan_server: str, 
                          trojan_port: int, 
                          trojan_password: str,
                          sni: Optional[str] = None) -> V2RayConverter:
    """
    便捷函数：创建trojan转换器
    
    Parameters
    ----------
    trojan_server : str
        Trojan服务器地址
    trojan_port : int  
        Trojan端口
    trojan_password : str
        Trojan密码
    sni : Optional[str]
        SNI域名
        
    Returns
    -------
    V2RayConverter
        V2Ray转换器实例
    """
    converter = V2RayConverter()
    
    trojan_config = TrojanConfig(
        server=trojan_server,
        port=trojan_port, 
        password=trojan_password,
        sni=sni
    )
    
    # 创建配置文件
    if converter.create_config_file(trojan_config):
        logger.info("Trojan转换器配置创建成功")
    else:
        logger.error("Trojan转换器配置创建失败")
    
    return converter


def auto_setup_converter() -> Optional[V2RayConverter]:
    """
    自动设置转换器（如果检测到trojan配置）
    
    Returns
    -------
    Optional[V2RayConverter]
        转换器实例，如果设置失败则返回None
    """
    # 这里可以添加自动检测trojan配置的逻辑
    # 例如从环境变量、配置文件等读取
    
    # 示例：从环境变量读取trojan配置
    trojan_server = os.getenv('TROJAN_SERVER')
    trojan_port = os.getenv('TROJAN_PORT')
    trojan_password = os.getenv('TROJAN_PASSWORD')
    trojan_sni = os.getenv('TROJAN_SNI')
    
    if trojan_server and trojan_port and trojan_password:
        try:
            converter = create_trojan_converter(
                trojan_server=trojan_server,
                trojan_port=int(trojan_port),
                trojan_password=trojan_password,
                sni=trojan_sni
            )
            
            if converter.start():
                logger.info("Trojan转换器自动设置成功")
                return converter
            else:
                logger.error("Trojan转换器启动失败")
                
        except Exception as e:
            logger.error(f"Trojan转换器自动设置失败: {e}")
    
    return None 