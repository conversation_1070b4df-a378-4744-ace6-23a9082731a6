"""
Freqtrade接口模型模块

定义与Freqtrade交易机器人接口相关的数据模型。
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from enum import Enum
import datetime


class OrderType(str, Enum):
    """订单类型枚举"""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    STOP_LOSS_LIMIT = "stop_loss_limit"


@dataclass
class TradeSignal:
    """
    交易信号模型，用于从策略发送到Freqtrade实例
    """
    pair: str  # 交易对，例如 "BTC/USDT"
    side: str  # 交易方向: "long" 或 "short"
    order_type: OrderType = OrderType.MARKET  # 订单类型
    amount: Optional[float] = None  # 交易数量
    rate: Optional[float] = None  # 限价单的价格
    enter_tag: Optional[str] = None  # 进场标签
    leverage: Optional[float] = None  # 杠杆倍数
    stop_loss: Optional[float] = None  # 止损价格
    take_profit: Optional[float] = None  # 止盈价格
    timeframe: Optional[str] = None  # 信号来源的时间框架
    timestamp: datetime.datetime = field(default_factory=datetime.datetime.now)  # 信号生成时间
    metadata: Dict[str, Any] = field(default_factory=dict)  # 附加元数据


@dataclass
class TradeResult:
    """
    交易执行结果模型
    """
    success: bool  # 是否成功
    tradeid: Optional[int] = None  # 交易ID (如果成功)
    error: Optional[str] = None  # 错误消息 (如果失败)
    error_type: Optional[str] = None  # 错误类型: position_conflict, business_error, network_error等
    details: Dict[str, Any] = field(default_factory=dict)  # 详细信息


@dataclass
class OrderStatus:
    """
    订单状态模型
    """
    order_id: str  # 订单ID
    pair: str  # 交易对
    status: str  # 状态: open, closed, canceled
    order_type: str  # 订单类型
    side: str  # 买/卖
    price: float  # 价格
    amount: float  # 数量
    filled: float  # 已成交数量
    remaining: float  # 剩余数量
    cost: Optional[float] = None  # 成交金额
    timestamp: Optional[datetime.datetime] = None  # 订单时间


@dataclass
class AccountBalance:
    """
    账户余额模型
    """
    currency: str  # 货币
    free: float  # 可用余额
    used: float  # 已使用余额
    total: float  # 总余额


@dataclass
class BotStatus:
    """
    机器人状态模型
    """
    status: str  # 状态: running, stopped, paused
    running_since: Optional[datetime.datetime] = None  # 运行开始时间
    trade_count: int = 0  # 当前交易数量
    performance: Dict[str, Any] = field(default_factory=dict)  # 性能统计 