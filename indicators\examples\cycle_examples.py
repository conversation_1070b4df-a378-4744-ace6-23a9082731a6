#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
周期指标使用示例

演示如何使用周期指标进行市场周期分析和交易决策。
"""

import os
import sys
# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec

# 尝试导入yfinance，如果不成功则不使用
try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    YFINANCE_AVAILABLE = False
    print("yfinance模块未安装，将使用模拟数据。如需获取实际数据，请安装yfinance: pip install yfinance")

from indicators.cycles import CycleFinder, FourierTransform, SeasonalDecomposition


def fetch_sample_data(ticker="SPY", period="2y", interval="1d", simulate_cycles=True):
    """
    获取示例数据，可选地添加人工周期
    
    Parameters
    ----------
    ticker : str, optional
        股票代码，默认为"SPY"
    period : str, optional
        获取数据的时间范围，默认为"2y"
    interval : str, optional
        数据间隔，默认为"1d"
    simulate_cycles : bool, optional
        是否在模拟数据中添加人工周期，默认为True
        
    Returns
    -------
    pd.DataFrame
        股票价格数据
    """
    # 创建模拟数据
    dates = pd.date_range(start='2022-01-01', periods=500, freq='D')
    np.random.seed(42)
    
    # 基础趋势和噪声
    trend = np.linspace(100, 140, len(dates))
    noise = np.random.normal(0, 2, len(dates))
    
    # 添加人工周期组件
    if simulate_cycles:
        # 添加3个不同周期的正弦波
        cycle1 = 8 * np.sin(2 * np.pi * np.arange(len(dates)) / 21)  # ~21天周期
        cycle2 = 5 * np.sin(2 * np.pi * np.arange(len(dates)) / 63)  # ~63天周期
        cycle3 = 10 * np.sin(2 * np.pi * np.arange(len(dates)) / 252)  # ~252天周期（年度）
        close = trend + cycle1 + cycle2 + cycle3 + noise
    else:
        close = trend + noise
    
    # 创建其他价格数据
    high = close + np.random.uniform(0, 3, len(dates))
    low = close - np.random.uniform(0, 3, len(dates))
    open_prices = close + np.random.normal(0, 1, len(dates))
    volume = np.random.randint(1000, 10000, len(dates))
    
    data = pd.DataFrame({
        'open': open_prices,
        'high': high,
        'low': low,
        'close': close,
        'volume': volume
    }, index=dates)
    
    # 如果yfinance可用且用户希望尝试获取真实数据，则尝试获取
    if YFINANCE_AVAILABLE:
        try:
            print(f"try to get data from Yahoo Finance for {ticker}...")
            real_data = yf.download(ticker, period=period, interval=interval, progress=False)
            if not real_data.empty:
                # 确保列名转换为小写
                column_map = {}
                for col in real_data.columns:
                    column_map[col] = col.lower()
                
                data = real_data.rename(columns=column_map)
                print("success to get real data.")
            else:
                print("failed to get real data, use simulated data instead.")
        except Exception as e:
            print(f"failed to get real data: {e}, use simulated data instead.")
    
    # 确保数据包含必需的列
    required_columns = ['open', 'high', 'low', 'close', 'volume']
    for col in required_columns:
        if col not in data.columns:
            print(f"warning: data missing '{col}' column, please check data source.")
    
    print(f"数据列: {list(data.columns)}")
    return data


def cycle_finder_example():
    """
    cycle finder indicator example
    """
    print("=== cycle finder example ===")
    
    # 获取数据
    data = fetch_sample_data(simulate_cycles=True)
    
    # 创建周期查找器指标
    cf = CycleFinder(max_lag=100, min_periods=5, detrend=True)
    
    # 计算周期
    result = cf.calculate(data)
    
    # 获取主要周期
    dominant_cycles = cf.get_dominant_cycles(n=5)
    print(f"\nfound dominant cycles:")
    print(dominant_cycles)
    
    # 绘制结果
    cf.plot(figsize=(12, 8))
    
    plt.tight_layout()
    plt.show()
    
    print("cycle finder trading ideas:")
    print("1. identified cycles can be used to predict potential turning points")
    print("2. short-term cycles can be used to select entry/exit points for intraday trading and short-term trading")
    print("3. medium-term cycles help determine market fluctuations on a larger scale")
    print("4. the superposition of multiple cycles may mark important support or resistance levels")


def fourier_transform_example():
    """
    fourier transform indicator example
    """
    print("\n=== fourier transform example ===")
    
    # 获取数据
    data = fetch_sample_data(simulate_cycles=True)
    
    # 创建傅立叶变换指标
    ft = FourierTransform(n_harmonics=5, detrend=True, padding=True, min_freq=0.01)
    
    # 计算傅立叶变换
    result = ft.calculate(data)
    
    # 获取主要周期
    dominant_cycles = ft.get_dominant_cycles()
    print(f"\nfound dominant frequencies and cycles:")
    print(dominant_cycles)
    
    # 绘制结果
    ft.plot(figsize=(12, 12))
    
    plt.tight_layout()
    plt.show()
    
    print("傅立叶变换交易思路:")
    print("1. 使用主要周期进行周期性时间预测(如周期顶底)")
    print("2. 结合多个周期的影响评估当前市场位置")
    print("3. 利用周期重建信号预测未来可能的价格路径")
    print("4. 周期强度变化可能暗示市场结构变化")


def seasonal_decomposition_example():
    """
    季节性分解示例
    """
    print("\n=== 季节性分解示例 ===")
    
    # 获取数据
    data = fetch_sample_data(simulate_cycles=True)
    
    # 创建季节性分解指标
    sd = SeasonalDecomposition(period=21, model='additive', extrapolate_trend=3)
    
    # 计算季节性分解
    result = sd.calculate(data)
    
    # 获取季节性模式
    seasonal_pattern = sd.get_seasonal_pattern()
    print(f"\n季节性模式 (周期={sd.params['period']}):")
    print(seasonal_pattern.head())
    
    # 绘制结果
    sd.plot(figsize=(12, 12))
    
    plt.tight_layout()
    plt.show()
    
    print("季节性分解交易思路:")
    print("1. 通过季节性模式识别重复出现的市场特征（如月度或季度模式）")
    print("2. 残差部分可用于识别异常价格变动")
    print("3. 趋势成分提供长期市场方向")
    print("4. 结合季节性成分和趋势进行交易时机选择")


def cycles_comparison_example():
    """
    不同周期指标比较示例
    """
    print("\n=== 周期指标比较 ===")
    
    # 获取数据
    data = fetch_sample_data(simulate_cycles=True)
    
    # 使用较短的数据样本，便于比较
    sample_data = data.iloc[-200:].copy()
    
    # 创建各种周期指标
    cf = CycleFinder(max_lag=50)
    ft = FourierTransform(n_harmonics=3)
    sd = SeasonalDecomposition(period=21)
    
    # 计算指标
    cf_result = cf.calculate(sample_data)
    ft_result = ft.calculate(sample_data)
    sd_result = sd.calculate(sample_data)
    
    # 获取主要周期
    cf_cycles = cf.get_dominant_cycles(n=3)
    ft_cycles = ft.get_dominant_cycles()
    
    print("\n各方法找到的主要周期:")
    print("周期查找器:")
    print(cf_cycles)
    print("\n傅立叶变换:")
    print(ft_cycles)
    
    # 创建比较图
    fig = plt.figure(figsize=(14, 12))
    gs = GridSpec(4, 1, figure=fig)
    
    # 价格子图，包含所有重建
    ax1 = fig.add_subplot(gs[0, 0])
    ax1.plot(sample_data.index, sample_data['close'], label='original price', color='black')
    
    # 添加傅立叶重建
    if 'fourier_reconstructed' in ft_result.columns:
        ax1.plot(ft_result.index, ft_result['fourier_reconstructed'], 
               label='fourier reconstruction', color='red', linestyle='--', alpha=0.7)
    
    # 添加季节性分解的趋势+季节性成分
    if 'seasonal_trend' in sd_result.columns and 'seasonal_seasonal' in sd_result.columns:
        trend_seasonal = sd_result['seasonal_trend'] + sd_result['seasonal_seasonal']
        ax1.plot(sd_result.index, trend_seasonal, 
               label='seasonal decomposition', color='green', linestyle='-.', alpha=0.7)
    
    ax1.set_title('price and cycle reconstruction comparison')
    ax1.grid(True)
    ax1.legend()
    
    # 自相关子图
    if hasattr(cf, '_autocorr'):
        ax2 = fig.add_subplot(gs[1, 0])
        lags = range(1, len(cf._autocorr) + 1)
        ax2.bar(lags, cf._autocorr, alpha=0.7)
        ax2.set_title("autocorrelation analysis")
        ax2.set_xlabel('lag periods')
        ax2.set_ylabel('autocorrelation coefficient')
        ax2.grid(True)
    
    # 傅立叶频谱子图
    if hasattr(ft, '_freq') and hasattr(ft, '_magnitude'):
        ax3 = fig.add_subplot(gs[2, 0])
        ax3.plot(ft._freq, ft._magnitude, color='blue')
        ax3.set_title("spectrum analysis")
        ax3.set_xlabel('frequency')
        ax3.set_ylabel('magnitude')
        ax3.grid(True)
    
    # 季节性成分子图
    if 'seasonal_seasonal' in sd_result.columns:
        ax4 = fig.add_subplot(gs[3, 0])
        ax4.plot(sd_result.index, sd_result['seasonal_seasonal'], color='green')
        ax4.set_title("seasonal component")
        ax4.grid(True)
    
    plt.tight_layout()
    plt.show()
    
    print("周期分析方法比较:")
    print("1. 自相关法(周期查找器)适合识别短期的重复模式")
    print("2. 傅立叶变换适合分析复杂波动中的主要周期成分")
    print("3. 季节性分解适合分离趋势和固定长度的周期成分")
    print("4. 结合多种方法可以提高周期识别的稳健性")


if __name__ == "__main__":
    # 运行示例
    cycle_finder_example()
    fourier_transform_example()
    seasonal_decomposition_example()
    cycles_comparison_example() 