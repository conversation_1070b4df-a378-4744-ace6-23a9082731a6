#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
标准差指标模块

实现标准差指标，用于测量价格分布的波动性。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Union, List

from ..base import Indicator
from ..utils import validate_data


class StandardDeviation(Indicator):
    """
    标准差指标
    
    标准差是衡量价格波动性的统计指标，表示价格与其平均值的偏离程度。
    标准差越大，表示价格波动性越大；标准差越小，表示价格波动性越小。
    """
    
    def __init__(self, period: int = 20, column: str = 'close', **kwargs):
        """
        初始化标准差指标
        
        Parameters
        ----------
        period : int, optional
            计算周期，默认为20
        column : str, optional
            用于计算的列名，默认为'close'
        **kwargs : dict
            其他参数
        """
        if period <= 0:
            raise ValueError("周期必须大于0")
        
        super().__init__(
            name="SD", 
            category="volatility", 
            period=period, 
            column=column,
            **kwargs
        )
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算标准差指标
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据，需包含用于计算的列
            
        Returns
        -------
        pd.DataFrame
            包含标准差指标值的DataFrame
        """
        column = self.params['column']
        
        # 确保列名为小写
        df = data.copy()
        df.columns = [col.lower() for col in df.columns]
        
        # 使用小写列名进行验证
        validate_data(df, [column])
        
        period = self.params['period']
        
        # 计算移动标准差
        sd = df[column].rolling(window=period).std()
        
        # 添加指标到结果
        result = df.copy()
        result['sd'] = sd
        
        # 可以添加其他有意义的信息，如标准差百分比等
        if column == 'close':
            # 计算标准差占收盘价的百分比
            result['sd_pct'] = result['sd'] / result[column] * 100
        
        # 存储计算结果
        self._result = result
        
        return result

    def plot(self, ax=None, **kwargs):
        """
        绘制标准差指标
        
        Parameters
        ----------
        ax : matplotlib.axes.Axes, optional
            用于绘图的Axes对象
        **kwargs : dict
            传递给绘图函数的其他参数
            
        Returns
        -------
        matplotlib.axes.Axes
            绘图结果
        """
        if self._result is None:
            raise ValueError("没有计算结果可供绘制，请先调用calculate方法")
        
        import matplotlib.pyplot as plt
        
        if ax is None:
            fig, ax = plt.subplots(figsize=kwargs.get('figsize', (10, 6)))
        
        # 绘制标准差
        ax.plot(self._result.index, self._result['sd'], label='Standard Deviation', color='purple', linewidth=1.5)
        
        # 如果有标准差百分比，添加第二个Y轴
        if 'sd_pct' in self._result.columns:
            ax2 = ax.twinx()
            ax2.plot(self._result.index, self._result['sd_pct'], label='SD %', color='orange', linestyle='--', linewidth=1.2)
            ax2.set_ylabel('标准差百分比 (%)')
            ax2.legend(loc='upper right')
        
        # 设置图表属性
        column = self.params['column']
        period = self.params['period']
        ax.set_title(f"标准差 ({period}周期, {column})")
        ax.set_ylabel('标准差值')
        ax.grid(True)
        ax.legend(loc='upper left')
        
        return ax 