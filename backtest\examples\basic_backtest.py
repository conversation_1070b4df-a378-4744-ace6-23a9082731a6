#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基础回测示例脚本

展示如何使用VectorBT回测引擎进行基本的策略回测。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 导入项目模块
from backtest.vectorbt import VectorBTEngine
from backtest.strategies import MovingAverageCrossover
from backtest.analysis import generate_report, plot_performance

# 控制是否显示图表
PLOT_RESULTS = True

# 显示常用指标的函数，处理可能是Series的情况
def print_metric(name: str, key: str, format_str: str = ".2%"):
    """
    打印指标
    
    Parameters
    ----------
    name : str
        指标名称
    key : str
        指标键名
    format_str : str
        格式化字符串，默认为百分比
    """
    try:
        value = metrics.get(key)
        
        # 如果键不存在，尝试计算
        if value is None and key == 'profit_factor':
            # 如果缺少盈亏比，尝试计算
            winning_trades = metrics.get('winning_trades', 0)
            losing_trades = metrics.get('losing_trades', 0)
            
            if winning_trades is not None and losing_trades is not None and losing_trades != 0:
                value = abs(winning_trades / losing_trades) if losing_trades != 0 else float('inf')
            else:
                value = float('nan')
        
        if value is None:
            print(f"{name}: 数据不可用")
            return
            
        # 如果是Series，取第一个值或平均值
        if hasattr(value, 'iloc'):
            if len(value) > 0:
                value = value.iloc[0]
            else:
                value = 0
                
        if isinstance(value, float):
            if format_str.endswith('%'):
                print(f"{name}: {value:.2%}")
            else:
                print(f"{name}: {value:.2f}")
        else:
            print(f"{name}: {value}")
    except Exception as e:
        print(f"{name}: 计算出错 ({e})")

# 创建示例数据
def create_sample_data(start_date='2020-01-01', end_date='2021-01-01', freq='D'):
    """
    创建示例数据
    
    Parameters
    ----------
    start_date : str
        起始日期
    end_date : str
        结束日期
    freq : str
        频率，默认为'D'（每日）
        
    Returns
    -------
    pd.DataFrame
        示例数据
    """
    # 创建日期范围
    date_range = pd.date_range(start=start_date, end=end_date, freq=freq)
    
    # 设置随机种子以便结果可复现
    np.random.seed(42)
    
    # 生成价格序列
    price = 100
    prices = [price]
    
    for _ in range(1, len(date_range)):
        change_percent = np.random.normal(0, 0.01)
        price *= (1 + change_percent)
        prices.append(price)
    
    # 创建OHLCV数据
    data = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
        'close': prices,
        'volume': [np.random.randint(100000, 1000000) for _ in prices]
    }, index=date_range)
    
    return data


def main():
    """主函数"""
    print("VectorBT回测示例")
    print("--------------------------------------------------")
    
    # 创建模拟数据
    print("创建示例数据...")
    data = create_sample_data()
    print(f"数据周期: {data.index[0].date()} 到 {data.index[-1].date()}")
    print(f"数据点数: {len(data)}")
    print()
    
    # 创建策略
    print("创建策略...")
    strategy = MovingAverageCrossover(short_window=10, long_window=30)
    print(f"策略: {strategy}")
    print()
    
    # 运行回测
    engine = VectorBTEngine(data, initial_capital=10000)
    results = engine.run(strategy)
    
    # 获取回测指标
    metrics = results.metrics
    
    # 简单分析回测结果
    print("\n回测结果摘要:")
    
    # 显示常用指标
    def print_metrics(metrics):
        """打印常用指标"""
        try:
            print(f"总回报: {metrics.get('total_return', 0):.2%}")
            print(f"年化收益率: {metrics.get('annual_return', 0):.2%}")
            print(f"夏普比率: {metrics.get('sharpe_ratio', 0):.2f}")
            print(f"最大回撤: {metrics.get('max_drawdown', 0):.2%}")
            print(f"胜率: {metrics.get('win_rate', 0):.2%}")
            
            # 尝试获取交易次数，兼容不同的键名
            trade_count = metrics.get('num_trades')
            if trade_count is None:
                trade_count = metrics.get('total_trades')
            if trade_count is None:
                trade_count = metrics.get('trades_count', '数据不可用')
                
            print(f"交易次数: {trade_count}")
            
            # 尝试计算盈亏比
            winning_trades = metrics.get('winning_trades', 0)
            losing_trades = metrics.get('losing_trades', 0)
            
            if losing_trades != 0:
                profit_factor = abs(winning_trades / losing_trades)
                print(f"盈亏比: {profit_factor:.2f}")
            else:
                print("盈亏比: ∞ (无亏损交易)")
                
        except Exception as e:
            print(f"打印指标时出错: {e}")
    
    # 打印指标
    print_metrics(metrics)
    
    # 绘制结果
    if PLOT_RESULTS:
        try:
            # 解决equity可能是函数的问题
            if hasattr(engine, 'portfolio') and engine.portfolio is not None:
                equity = engine.portfolio.value
                if callable(equity):
                    equity = equity()
                print(f"\n最终权益: {equity[-1]:.2f}")
            
            # 使用引擎的plot方法
            engine.plot()
        except Exception as e:
            print(f"绘图时出错: {e}")
    
    return results


if __name__ == '__main__':
    main() 