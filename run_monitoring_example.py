#!/usr/bin/env python
"""
监控和干预系统示例运行脚本

该脚本运行交易引擎监控和紧急干预系统的示例，用于展示系统功能。
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from data.api.trading_engine.examples.monitoring_example import main as run_monitoring_example


def print_header():
    """打印程序头部信息"""
    print("\n" + "=" * 80)
    print("       交易引擎监控和紧急干预系统示例")
    print("=" * 80)
    print("""
该示例演示了以下功能:
1. 交易执行数据收集
2. 异常检测和告警
3. 实时监控仪表盘
4. 自动和手动干预操作
5. 审计日志记录

监控系统会启动一个本地Web服务，您可以通过浏览器访问监控仪表盘。
    """)
    print("=" * 80 + "\n")


def print_footer():
    """打印程序尾部信息"""
    print("\n" + "=" * 80)
    print("       监控示例运行完成")
    print("=" * 80)
    print("""
如需更多信息，请参考:
- data/api/trading_engine/monitoring/ : 监控系统模块
- data/api/trading_engine/intervention/ : 干预系统模块
- tests/data/trading_engine_monitoring_test.py : 单元测试

您可以通过修改 data/api/trading_engine/examples/monitoring_example.py 来定制示例行为。
    """)
    print("=" * 80 + "\n")


if __name__ == "__main__":
    try:
        # 打印头部信息
        print_header()
        
        # 运行监控示例
        run_monitoring_example()
        
    except KeyboardInterrupt:
        print("\n用户中断，示例已停止。")
    except Exception as e:
        print(f"\n运行示例时发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 打印尾部信息
        print_footer() 