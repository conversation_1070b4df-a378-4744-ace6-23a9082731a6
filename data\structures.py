"""
数据结构定义

定义了系统使用的标准数据结构，包括OHLCV数据结构和扩展的市场数据结构。
"""

from enum import Enum
from typing import Dict, List, Optional, Union, Any, Tuple
import pandas as pd
import numpy as np
from datetime import datetime


class TimeFrame(Enum):
    """时间周期枚举"""
    MINUTE_1 = "1m"
    MINUTE_5 = "5m"
    MINUTE_15 = "15m"
    MINUTE_30 = "30m"
    HOUR_1 = "1h"
    HOUR_2 = "2h"
    HOUR_4 = "4h"
    HOUR_6 = "6h"
    HOUR_8 = "8h"
    HOUR_12 = "12h"
    DAY_1 = "1d"
    DAY_3 = "3d"
    WEEK_1 = "1w"
    MONTH_1 = "1M"
    
    @classmethod
    def from_string(cls, timeframe: str) -> 'TimeFrame':
        """从字符串获取TimeFrame枚举值"""
        for tf in cls:
            if tf.value == timeframe:
                return tf
        raise ValueError(f"不支持的时间周期: {timeframe}")


class OHLCV:
    """
    OHLCV数据结构

    包含开盘价、最高价、最低价、收盘价、成交量和日期数据。
    """

    def __init__(self, 
                open: Union[List[float], np.ndarray], 
                high: Union[List[float], np.ndarray], 
                low: Union[List[float], np.ndarray], 
                close: Union[List[float], np.ndarray], 
                volume: Union[List[float], np.ndarray], 
                dates: Union[List[datetime], np.ndarray, pd.DatetimeIndex]):
        """
        初始化OHLCV数据

        Args:
            open: 开盘价数组
            high: 最高价数组
            low: 最低价数组
            close: 收盘价数组
            volume: 成交量数组
            dates: 日期数组
        """
        self.open = np.array(open)
        self.high = np.array(high)
        self.low = np.array(low)
        self.close = np.array(close)
        self.volume = np.array(volume)
        
        # 确保日期格式正确
        if isinstance(dates, pd.DatetimeIndex):
            self.dates = dates.to_pydatetime()
        else:
            self.dates = np.array(dates)

    def to_dataframe(self) -> pd.DataFrame:
        """
        转换为DataFrame

        Returns:
            包含OHLCV数据的DataFrame
        """
        df = pd.DataFrame({
            OHLCVColumns.OPEN: self.open,
            OHLCVColumns.HIGH: self.high,
            OHLCVColumns.LOW: self.low,
            OHLCVColumns.CLOSE: self.close,
            OHLCVColumns.VOLUME: self.volume
        }, index=pd.DatetimeIndex(self.dates))

        return df

    def __len__(self) -> int:
        """返回数据长度"""
        return len(self.close)


class OHLCVColumns:
    """OHLCV列名常量"""
    TIMESTAMP = 'timestamp'
    OPEN = 'open'
    HIGH = 'high'
    LOW = 'low'
    CLOSE = 'close'
    VOLUME = 'volume'
    
    # 扩展列
    VWAP = 'vwap'  # 成交量加权平均价
    TRADES = 'trades'  # 交易笔数
    BID = 'bid'  # 买一价
    ASK = 'ask'  # 卖一价
    
    # 常用组合
    BASIC_COLUMNS = [TIMESTAMP, OPEN, HIGH, LOW, CLOSE, VOLUME]
    EXTENDED_COLUMNS = [TIMESTAMP, OPEN, HIGH, LOW, CLOSE, VOLUME, VWAP, TRADES, BID, ASK]


def create_empty_ohlcv_dataframe() -> pd.DataFrame:
    """
    创建空的OHLCV DataFrame
    
    Returns:
        带有OHLCV列结构的空DataFrame
    """
    df = pd.DataFrame(columns=OHLCVColumns.BASIC_COLUMNS)
    
    # 设置数据类型
    dtypes = {
        OHLCVColumns.TIMESTAMP: np.dtype('datetime64[ns]'),
        OHLCVColumns.OPEN: np.dtype('float64'),
        OHLCVColumns.HIGH: np.dtype('float64'),
        OHLCVColumns.LOW: np.dtype('float64'),
        OHLCVColumns.CLOSE: np.dtype('float64'),
        OHLCVColumns.VOLUME: np.dtype('float64')
    }
    
    for col, dtype in dtypes.items():
        df[col] = df[col].astype(dtype)
    
    # 设置索引
    df.set_index(OHLCVColumns.TIMESTAMP, inplace=True)
    
    return df


def validate_ohlcv_dataframe(df: pd.DataFrame, require_index: bool = True) -> bool:
    """
    验证DataFrame是否符合OHLCV数据结构
    
    Args:
        df: 要验证的DataFrame
        require_index: 是否要求timestamp作为索引
        
    Returns:
        如果符合OHLCV结构返回True，否则返回False
    """
    required_columns = set(OHLCVColumns.BASIC_COLUMNS)
    
    # 如果timestamp作为索引，则从required_columns中移除
    if require_index and df.index.name == OHLCVColumns.TIMESTAMP:
        required_columns.remove(OHLCVColumns.TIMESTAMP)
    
    # 检查是否包含所有必需的列
    df_columns = set(df.columns)
    if not required_columns.issubset(df_columns):
        missing = required_columns - df_columns
        if require_index and OHLCVColumns.TIMESTAMP in missing:
            missing.remove(OHLCVColumns.TIMESTAMP)
        if missing:
            return False
    
    return True


def clean_ohlcv_dataframe(df: pd.DataFrame) -> pd.DataFrame:
    """
    清洗OHLCV DataFrame，处理缺失值、异常值等
    
    Args:
        df: 要清洗的DataFrame
        
    Returns:
        清洗后的DataFrame
    """
    # 复制DataFrame以避免修改原始数据
    df_cleaned = df.copy()
    
    # 确保索引是datetime类型
    if df_cleaned.index.name == OHLCVColumns.TIMESTAMP:
        df_cleaned.index = pd.to_datetime(df_cleaned.index)
    elif OHLCVColumns.TIMESTAMP in df_cleaned.columns:
        df_cleaned[OHLCVColumns.TIMESTAMP] = pd.to_datetime(df_cleaned[OHLCVColumns.TIMESTAMP])
        df_cleaned.set_index(OHLCVColumns.TIMESTAMP, inplace=True)
    
    # 按时间排序
    df_cleaned = df_cleaned.sort_index()
    
    # 处理缺失值
    # 对于OHLC使用前向填充，对于成交量使用0填充
    df_cleaned[[OHLCVColumns.OPEN, OHLCVColumns.HIGH, OHLCVColumns.LOW, OHLCVColumns.CLOSE]] = \
        df_cleaned[[OHLCVColumns.OPEN, OHLCVColumns.HIGH, OHLCVColumns.LOW, OHLCVColumns.CLOSE]].ffill()
    
    df_cleaned[OHLCVColumns.VOLUME] = df_cleaned[OHLCVColumns.VOLUME].fillna(0)
    
    # 处理异常值
    # 确保OHLC逻辑正确
    df_cleaned[OHLCVColumns.HIGH] = df_cleaned[[OHLCVColumns.HIGH, OHLCVColumns.OPEN, OHLCVColumns.CLOSE]].max(axis=1)
    df_cleaned[OHLCVColumns.LOW] = df_cleaned[[OHLCVColumns.LOW, OHLCVColumns.OPEN, OHLCVColumns.CLOSE]].min(axis=1)
    
    # 确保成交量为非负数
    df_cleaned[OHLCVColumns.VOLUME] = df_cleaned[OHLCVColumns.VOLUME].clip(lower=0)
    
    return df_cleaned 