#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Backtrader回测结果实现

实现Backtrader引擎的回测结果类，继承自BacktestResults抽象基类。
"""

from typing import Dict, Any, Optional
import pandas as pd
import numpy as np

from ..base import BacktestResults


class BacktraderResults(BacktestResults):
    """
    Backtrader回测结果类
    
    实现BacktestResults抽象基类的具体版本，用于存储和处理Backtrader回测引擎的结果。
    """
    
    def __init__(
        self, 
        equity: pd.Series, 
        returns: pd.Series, 
        positions: pd.DataFrame, 
        trades: pd.DataFrame,
        metrics: Dict[str, Any], 
        strategy: str = None,
        params: Dict[str, Any] = None
    ):
        """
        初始化Backtrader回测结果
        
        Parameters
        ----------
        equity : pd.Series
            净值曲线
        returns : pd.Series
            收益率序列
        positions : pd.DataFrame
            持仓记录
        trades : pd.DataFrame
            交易记录
        metrics : Dict[str, Any]
            性能指标
        strategy : str, optional
            策略名称
        params : Dict[str, Any], optional
            策略参数
        """
        self._equity = equity
        self._returns = returns
        self._positions = positions
        self._trades = trades
        self._metrics = metrics or {}
        self._strategy = strategy
        self._params = params or {}
        
        # 计算回撤序列
        if not equity.empty:
            self._drawdowns = equity / equity.cummax() - 1
        else:
            self._drawdowns = pd.Series(dtype=float)
    
    @property
    def trades(self) -> pd.DataFrame:
        """
        获取交易记录
        
        Returns
        -------
        pd.DataFrame
            包含交易记录的DataFrame
        """
        return self._trades
    
    def equity(self) -> pd.Series:
        """
        获取净值曲线
        
        Returns
        -------
        pd.Series
            净值曲线
        """
        return self._equity
    
    def get_returns(self) -> pd.Series:
        """
        获取收益率序列
        
        Returns
        -------
        pd.Series
            收益率序列
        """
        return self._returns
    
    def get_drawdowns(self) -> pd.Series:
        """
        获取回撤序列
        
        Returns
        -------
        pd.Series
            回撤序列
        """
        return self._drawdowns
    
    @property
    def metrics(self) -> Dict[str, Any]:
        """
        获取性能指标
        
        Returns
        -------
        Dict[str, Any]
            性能指标
        """
        return self._metrics
    
    @property
    def positions(self) -> pd.DataFrame:
        """
        获取持仓记录
        
        Returns
        -------
        pd.DataFrame
            持仓记录
        """
        return self._positions
    
    @property
    def strategy(self) -> str:
        """
        获取策略名称
        
        Returns
        -------
        str
            策略名称
        """
        return self._strategy
    
    @property
    def params(self) -> Dict[str, Any]:
        """
        获取策略参数
        
        Returns
        -------
        Dict[str, Any]
            策略参数
        """
        return self._params 