#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
历史波动率指标模块

实现历史波动率指标，用于测量历史价格变动的波动程度，常用于期权定价和风险管理。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Union

from ..base import Indicator
from ..utils import validate_data


class HistoricalVolatility(Indicator):
    """
    历史波动率指标
    
    历史波动率是衡量资产价格变化幅度的指标，通常表示为标准差的年化值。
    它基于对数收益率的标准差计算，是期权定价和风险管理中的重要参数。
    """
    
    def __init__(
        self, 
        period: int = 20, 
        column: str = 'close', 
        trading_periods: int = 252, 
        method: str = 'log', 
        scaling: bool = True,
        **kwargs
    ):
        """
        初始化历史波动率指标
        
        Parameters
        ----------
        period : int, optional
            计算周期，默认为20
        column : str, optional
            用于计算的列名，默认为'close'
        trading_periods : int, optional
            年化周期数，默认为252（一年交易日数量）
        method : str, optional
            计算方法 ('log' 或 'pct')，默认为'log'
        scaling : bool, optional
            是否进行年化，默认为True
        **kwargs : dict
            其他参数
        """
        if period <= 0:
            raise ValueError("周期必须大于0")
        
        if trading_periods <= 0:
            raise ValueError("年化周期数必须大于0")
        
        valid_methods = ['log', 'pct']
        if method not in valid_methods:
            raise ValueError(f"计算方法必须是以下之一: {', '.join(valid_methods)}")
        
        super().__init__(
            name="HV", 
            category="volatility", 
            period=period, 
            column=column,
            trading_periods=trading_periods,
            method=method,
            scaling=scaling,
            **kwargs
        )
    
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算历史波动率指标
        
        Parameters
        ----------
        data : pd.DataFrame
            输入数据，需包含用于计算的列
            
        Returns
        -------
        pd.DataFrame
            包含历史波动率指标值的DataFrame
        """
        column = self.params['column']
        validate_data(data, [column])
        
        df = data.copy()
        period = self.params['period']
        trading_periods = self.params['trading_periods']
        method = self.params['method']
        scaling = self.params['scaling']
        
        # 计算收益率
        if method == 'log':
            # 使用对数收益率
            returns = np.log(df[column] / df[column].shift(1))
        else:
            # 使用百分比收益率
            returns = df[column].pct_change()
        
        # 计算收益率的标准差
        volatility = returns.rolling(window=period).std()
        
        # 年化波动率（如果需要）
        if scaling:
            volatility = volatility * np.sqrt(trading_periods)
        
        # 添加指标到结果
        result = df.copy()
        result['returns'] = returns
        result['hv'] = volatility
        
        # 添加百分比表示
        result['hv_pct'] = volatility * 100
        
        # 存储计算结果
        self._result = result
        
        return result

    def plot(self, ax=None, **kwargs):
        """
        绘制历史波动率指标
        
        Parameters
        ----------
        ax : matplotlib.axes.Axes, optional
            用于绘图的Axes对象
        **kwargs : dict
            传递给绘图函数的其他参数
            
        Returns
        -------
        matplotlib.axes.Axes
            绘图结果
        """
        if self._result is None:
            raise ValueError("没有计算结果可供绘制，请先调用calculate方法")
        
        import matplotlib.pyplot as plt
        
        if ax is None:
            fig, ax = plt.subplots(figsize=kwargs.get('figsize', (10, 6)))
        
        # 绘制历史波动率（百分比形式）
        ax.plot(self._result.index, self._result['hv_pct'], label='Historical Volatility %', color='red', linewidth=1.5)
        
        # 设置图表属性
        period = self.params['period']
        trading_periods = self.params['trading_periods']
        scaling = self.params['scaling']
        
        title = f"历史波动率 ({period}周期)"
        if scaling:
            title += f", 年化({trading_periods}交易日)"
        
        ax.set_title(title)
        ax.set_ylabel('波动率 (%)')
        ax.grid(True)
        ax.legend()
        
        return ax 