#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SMC FreqTrade 立即亏损问题修复方案

根据分析结果，提供具体的配置修复和代码优化方案
主要问题：
1. 市价单 + price_side='other' 导致不利成交价格
2. 交易费用0.08%直接影响初始盈亏
3. 1分钟高频交易容易受市场噪音影响
"""

import json
import os
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_optimized_freqtrade_config():
    """创建优化后的FreqTrade配置"""
    logger.info("🔧 创建优化的FreqTrade配置")
    
    # 读取当前配置
    with open("freqtrade-bot/config.json", 'r') as f:
        config = json.load(f)
    
    # 关键优化1: 修复入场价格配置
    config['entry_pricing'] = {
        "price_side": "same",  # 🔧 改为同侧价格，避免不利成交
        "use_order_book": True,
        "order_book_top": 1,
        "price_last_balance": 0.0,
        "check_depth_of_market": {
            "enabled": True,
            "bids_to_ask_delta": 0.03  # 🔧 提高到3%，确保流动性充足
        }
    }
    
    # 关键优化2: 修复出场价格配置
    config['exit_pricing'] = {
        "price_side": "same",  # 🔧 同样改为同侧价格
        "use_order_book": True,
        "order_book_top": 1
    }
    
    # 关键优化3: 减少处理延迟
    config['internals']['process_throttle_secs'] = 1  # 🔧 从5秒减少到1秒
    
    # 关键优化4: 减少同时开仓数，集中资金
    config['max_open_trades'] = 8  # 🔧 从15减少到8
    
    # 关键优化5: 增加订单超时保护
    config['unfilledtimeout'] = {
        "entry": 300,  # 🔧 从600秒减少到300秒
        "exit": 180,   # 🔧 从300秒减少到180秒
        "unit": "seconds"
    }
    
    # 保存优化后的配置
    backup_path = f"freqtrade-bot/config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(backup_path, 'w') as f:
        json.dump(config, f, indent=2)
    logger.info(f"✅ 原配置已备份到: {backup_path}")
    
    with open("freqtrade-bot/config_optimized.json", 'w') as f:
        json.dump(config, f, indent=2)
    logger.info("✅ 优化配置已保存到: freqtrade-bot/config_optimized.json")
    
    return config

def create_optimized_smc_strategy():
    """创建优化后的SMC策略配置"""
    logger.info("🔧 创建优化的SMC策略配置")
    
    strategy_optimizations = """
# SMC策略优化配置 - 解决立即亏损问题

class OptimizedSMCStrategy(SMCStrategy):
    '''优化版SMC策略 - 减少立即亏损'''
    
    # 🔧 关键优化1: 改用限价单减少滑点
    order_types = {
        'entry': 'limit',      # 改为限价单
        'exit': 'limit',       # 改为限价单  
        'stoploss': 'market',  # 止损保持市价单
        'stoploss_on_exchange': True,  # 启用交易所止损
        'stoploss_on_exchange_interval': 60,
    }
    
    # 🔧 关键优化2: 调整时间框架减少噪音
    timeframe = '5m'  # 从1分钟改为5分钟
    
    # 🔧 关键优化3: 调整止损避免过早触发
    stoploss = -0.025  # 从-2%调整到-2.5%
    
    # 🔧 关键优化4: 优化ROI设置
    minimal_roi = {
        "0": 0.025,   # 初始目标2.5%
        "30": 0.02,   # 30分钟后2%
        "60": 0.015,  # 1小时后1.5%
        "120": 0.01   # 2小时后1%
    }
    
    # 🔧 关键优化5: 增加入场确认
    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, 
                           rate: float, time_in_force: str, current_time, 
                           entry_tag: Optional[str], side: str, **kwargs) -> bool:
        '''增强的入场确认 - 避免不利价格成交'''
        
        # 获取当前市场价格
        try:
            ticker = self.dp.ticker(pair)
            if ticker:
                bid = ticker.get('bid', 0)
                ask = ticker.get('ask', 0)
                
                if side == 'long':
                    # 做多：确保入场价格不高于ask价格的0.05%
                    if rate > ask * 1.0005:
                        logger.warning(f"⚠️ 做多入场价格过高 - {pair}: {rate} > {ask * 1.0005}")
                        return False
                        
                elif side == 'short':
                    # 做空：确保入场价格不低于bid价格的0.05%
                    if rate < bid * 0.9995:
                        logger.warning(f"⚠️ 做空入场价格过低 - {pair}: {rate} < {bid * 0.9995}")
                        return False
                
                logger.info(f"✅ 入场价格确认通过 - {pair} {side}: {rate}")
                
        except Exception as e:
            logger.debug(f"入场确认检查失败: {e}")
        
        return True
    
    # 🔧 关键优化6: 智能限价单价格计算
    def custom_entry_price(self, pair: str, current_time, proposed_rate: float,
                          entry_tag: Optional[str], side: str, **kwargs) -> float:
        '''智能限价单价格计算'''
        
        try:
            ticker = self.dp.ticker(pair)
            if not ticker:
                return proposed_rate
            
            bid = ticker.get('bid', 0)
            ask = ticker.get('ask', 0)
            spread = (ask - bid) / bid if bid > 0 else 0
            
            if side == 'long':
                # 做多：使用略高于bid的价格，但不超过mid价格
                mid_price = (bid + ask) / 2
                optimal_price = min(bid * 1.0002, mid_price)  # bid + 0.02%
                logger.info(f"📊 做多限价优化 - {pair}: {proposed_rate:.6f} -> {optimal_price:.6f}")
                return optimal_price
                
            elif side == 'short':
                # 做空：使用略低于ask的价格，但不低于mid价格  
                mid_price = (bid + ask) / 2
                optimal_price = max(ask * 0.9998, mid_price)  # ask - 0.02%
                logger.info(f"📊 做空限价优化 - {pair}: {proposed_rate:.6f} -> {optimal_price:.6f}")
                return optimal_price
                
        except Exception as e:
            logger.debug(f"限价单价格优化失败: {e}")
        
        return proposed_rate
"""
    
    # 保存策略优化代码
    with open("backtest/strategies/optimized_smc_strategy.py", 'w', encoding='utf-8') as f:
        f.write(f'''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化版SMC策略 - 解决立即亏损问题
基于原SMC策略，针对立即亏损问题进行优化

主要优化：
1. 改用限价单减少滑点
2. 调整时间框架减少噪音  
3. 智能入场价格确认
4. 优化止损和ROI设置
"""

from backtest.strategies.smc_strategy import SMCStrategy
from typing import Optional
import logging

logger = logging.getLogger(__name__)

{strategy_optimizations}
''')
    
    logger.info("✅ 优化策略已保存到: backtest/strategies/optimized_smc_strategy.py")

def create_deployment_guide():
    """创建部署指南"""
    guide = """
# SMC FreqTrade 立即亏损问题修复部署指南

## 🚨 问题根因分析
经过详细分析，发现立即显示-0.10%亏损的主要原因：

1. **交易费用影响 (0.08%)**：
   - 使用市价单：入场0.04% + 出场0.04% = 0.08%
   - 这是立即亏损的主要原因

2. **不利成交价格**：
   - `price_side: "other"` 导致以对手价成交
   - 市价单在流动性不足时滑点较大

3. **高频交易噪音**：
   - 1分钟时间框架容易受市场噪音影响
   - 信号质量不稳定

## 🔧 修复方案

### 1. 立即修复 (减少80%的立即亏损)
```bash
# 1. 备份当前配置
cp freqtrade-bot/config.json freqtrade-bot/config_backup.json

# 2. 使用优化配置
cp freqtrade-bot/config_optimized.json freqtrade-bot/config.json

# 3. 重启FreqTrade
# 停止当前FreqTrade进程，然后重新启动
```

### 2. 策略优化 (进一步减少亏损)
```bash
# 1. 使用优化策略
# 修改config.json中的strategy设置：
"strategy": "OptimizedSMCStrategy"

# 2. 重启FreqTrade应用新策略
```

### 3. 关键配置变更

#### FreqTrade配置优化：
- `entry_pricing.price_side`: "other" → "same"
- `process_throttle_secs`: 5 → 1
- `max_open_trades`: 15 → 8
- `unfilledtimeout.entry`: 600 → 300

#### 策略配置优化：
- `order_types.entry`: "market" → "limit"
- `order_types.exit`: "market" → "limit"  
- `timeframe`: "1m" → "5m"
- `stoploss`: -0.02 → -0.025

## 📊 预期效果

### 修复前：
- 立即亏损：-0.10% (主要是费用0.08% + 滑点0.02%)
- 成交价格：经常不利
- 信号噪音：较多

### 修复后：
- 立即亏损：-0.02% (仅限价单费用0.02%)
- 成交价格：更优
- 信号质量：更稳定

### 预期改善：
- **减少80%的立即亏损** (从-0.10%到-0.02%)
- **提高成交价格质量**
- **减少无效交易**

## ⚠️ 注意事项

1. **限价单风险**：
   - 可能出现未成交情况
   - 需要监控成交率

2. **时间框架变更**：
   - 从1分钟改为5分钟会减少交易频率
   - 但提高信号质量

3. **监控指标**：
   - 成交率 (应保持>90%)
   - 平均滑点 (应<0.02%)
   - 立即亏损比例 (应<0.03%)

## 🚀 部署步骤

1. **停止当前FreqTrade**
2. **备份配置文件**
3. **应用优化配置**
4. **重启FreqTrade**
5. **监控效果**

预计修复后，立即亏损问题将显著改善！
"""
    
    with open("immediate_loss_fix_guide.md", 'w', encoding='utf-8') as f:
        f.write(guide)
    
    logger.info("✅ 部署指南已保存到: immediate_loss_fix_guide.md")

def main():
    """主函数"""
    logger.info("🚀 开始创建SMC FreqTrade立即亏损修复方案")
    
    # 1. 创建优化配置
    create_optimized_freqtrade_config()
    
    # 2. 创建优化策略
    create_optimized_smc_strategy()
    
    # 3. 创建部署指南
    create_deployment_guide()
    
    logger.info("="*80)
    logger.info("🎉 修复方案创建完成")
    logger.info("="*80)
    logger.info("📁 生成的文件：")
    logger.info("  • freqtrade-bot/config_optimized.json - 优化的FreqTrade配置")
    logger.info("  • backtest/strategies/optimized_smc_strategy.py - 优化的策略")
    logger.info("  • immediate_loss_fix_guide.md - 部署指南")
    logger.info("")
    logger.info("🎯 关键修复点：")
    logger.info("  • 改用限价单：减少费用从0.08%到0.02%")
    logger.info("  • 优化成交价格：避免不利价格成交")
    logger.info("  • 调整时间框架：减少市场噪音影响")
    logger.info("  • 智能入场确认：避免极端价格成交")
    logger.info("")
    logger.info("📈 预期效果：")
    logger.info("  • 立即亏损从-0.10%减少到-0.02%")
    logger.info("  • 改善约80%的立即亏损问题")
    logger.info("  • 提高整体交易质量")
    logger.info("="*80)
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
