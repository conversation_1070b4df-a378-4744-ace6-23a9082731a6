#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据模块API - CCXT标准化版本

提供统一的数据访问接口，管理数据源和存储。
采用CCXT标准化接口，提供向后兼容的封装。
"""

import logging
from datetime import datetime
from typing import List, Dict, Optional, Any, Union, Tuple, cast, Callable

import pandas as pd
import numpy as np

from data.base import DataSource, DataStorage
from data.structures import OHLCVColumns, clean_ohlcv_dataframe
from data.utils import resample_ohlcv, merge_dataframes
from data.storage.optimized_storage import OptimizedStorage
# 导入子模块API类
from data.api.market_data import MarketDataAPI
from data.api.operations import DataOperationsAPI
from data.api.management import DataManagementAPI

# 明确导出DataAPI类
__all__ = ['DataAPI']


class DataAPI:
    """
    数据API - CCXT标准化版本
    
    作为数据模块的统一访问点，集成市场数据、数据操作和数据管理功能。
    提供CCXT标准化接口和向后兼容的封装。
    """
    
    def __init__(self, storage: DataStorage):
        """
        初始化数据API
        
        Args:
            storage: 数据存储对象
        """
        self.storage = storage
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 创建子API
        self.market = MarketDataAPI(storage)
        self.operations = DataOperationsAPI()
        self.management = DataManagementAPI(storage)
    
    def register_data_source(self, name: str, data_source: DataSource, 
                           is_default: bool = False) -> None:
        """
        注册数据源
        
        Args:
            name: 数据源名称
            data_source: 数据源对象
            is_default: 是否设为默认数据源
        """
        self.market.register_data_source(name, data_source, is_default)
        self.management.register_data_source(name, data_source, is_default)
    
    # ========== CCXT标准化接口 ==========
    
    def get_ohlcv(self, symbol: str, timeframe: str, 
                  since: Optional[int] = None, limit: Optional[int] = None,
                  data_source: Optional[str] = None) -> pd.DataFrame:
        """
        获取OHLCV数据 - CCXT标准接口
        
        Args:
            symbol: 交易对符号 (如 'BTC/USDT')
            timeframe: 时间周期 (如 '1m', '5m', '1h', '1d')
            since: 开始时间戳(毫秒) (可选)
            limit: 获取的最大数据点数量 (可选，默认100)
            data_source: 数据源名称 (可选)
            
        Returns:
            包含OHLCV数据的DataFrame，索引为时间戳
        """
        return self.market.get_ohlcv(symbol, timeframe, since, limit, data_source)
    
    def fetch_ohlcv(self, symbol: str, timeframe: str, 
                    since: Optional[int] = None, limit: Optional[int] = None,
                    data_source: Optional[str] = None) -> List[List]:
        """
        获取OHLCV数据 - CCXT原生格式
        
        Args:
            symbol: 交易对符号
            timeframe: 时间周期
            since: 开始时间戳(毫秒) (可选)
            limit: 获取的最大数据点数量 (可选)
            data_source: 数据源名称 (可选)
            
        Returns:
            CCXT标准格式的OHLCV数据: [[timestamp, open, high, low, close, volume], ...]
        """
        return self.market.fetch_ohlcv(symbol, timeframe, since, limit, data_source)
    
    # ========== 向后兼容接口 ==========
    
    def get_data(self, symbol: str, timeframe: str, 
                start_time: datetime, end_time: datetime,
                data_source: Optional[str] = None, 
                use_cache: bool = True, update_cache: bool = True) -> pd.DataFrame:
        """
        获取市场数据 - 向后兼容接口
        
        内部转换为CCXT标准接口调用
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            start_time: 开始时间
            end_time: 结束时间
            data_source: 数据源名称 (可选)
            use_cache: 是否使用缓存数据 (兼容参数，暂不使用)
            update_cache: 是否更新缓存数据 (兼容参数，暂不使用)
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        # 转换为CCXT标准参数
        since = int(start_time.timestamp() * 1000)
        
        # 估算需要的数据点数量
        timeframe_to_minutes = {
            "1m": 1, "5m": 5, "15m": 15, "30m": 30,
            "1h": 60, "2h": 120, "4h": 240, "6h": 360, "8h": 480, "12h": 720,
            "1d": 1440, "3d": 4320, "1w": 10080, "1M": 43200
        }
        
        minutes = timeframe_to_minutes.get(timeframe, 60)
        duration_minutes = int((end_time - start_time).total_seconds() / 60)
        limit = max(100, duration_minutes // minutes + 10)  # 添加缓冲
        
        return self.market.get_ohlcv(symbol, timeframe, since, limit, data_source)
    
    def get_latest_data(self, symbol: str, timeframe: str, 
                       n_periods: int = 1, 
                       data_source: Optional[str] = None) -> pd.DataFrame:
        """
        获取最新的市场数据 - 向后兼容接口
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            n_periods: 获取的周期数量
            data_source: 数据源名称 (可选)
            
        Returns:
            包含最新OHLCV数据的DataFrame
        """
        return self.market.get_ohlcv(symbol, timeframe, limit=n_periods, data_source=data_source)
    
    # ========== 其他接口 ==========
    
    def get_symbols(self, data_source: Optional[str] = None) -> List[str]:
        """
        获取支持的交易对列表
        
        Args:
            data_source: 数据源名称 (可选)
            
        Returns:
            交易对代码列表
        """
        return self.market.get_symbols(data_source)
    
    def get_timeframes(self, data_source: Optional[str] = None) -> List[str]:
        """
        获取支持的时间周期列表
        
        Args:
            data_source: 数据源名称 (可选)
            
        Returns:
            时间周期列表
        """
        return self.market.get_timeframes(data_source)
    
    def resample_data(self, data: pd.DataFrame, target_timeframe: str) -> pd.DataFrame:
        """
        重采样数据到指定的时间周期
        
        Args:
            data: 包含OHLCV数据的DataFrame
            target_timeframe: 目标时间周期
            
        Returns:
            重采样后的DataFrame
        """
        return self.market.resample_data(data, target_timeframe)
    
    def filter_data(self, data: pd.DataFrame, 
                   start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None,
                   columns: Optional[List[str]] = None) -> pd.DataFrame:
        """
        筛选数据
        
        Args:
            data: 包含OHLCV数据的DataFrame
            start_time: 开始时间 (可选)
            end_time: 结束时间 (可选)
            columns: 需要的列 (可选)
            
        Returns:
            筛选后的DataFrame
        """
        return self.market.filter_data(data, start_time, end_time, columns)
    
    def clean_data(self, data: pd.DataFrame, 
                  fill_method: str = "ffill",
                  remove_outliers_method: Optional[str] = None,
                  outlier_threshold: float = 3.0,
                  custom_cleaning_func: Optional[Callable] = None) -> pd.DataFrame:
        """
        清洗数据
        
        Args:
            data: 包含OHLCV数据的DataFrame
            fill_method: 缺失值填充方法
            remove_outliers_method: 异常值处理方法
            outlier_threshold: 异常值阈值
            custom_cleaning_func: 自定义清洗函数
            
        Returns:
            清洗后的DataFrame
        """
        return self.operations.clean_data(
            data, fill_method, remove_outliers_method, 
            outlier_threshold, custom_cleaning_func
        )
    
    def transform_data(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        转换数据
        
        Args:
            data: 包含OHLCV数据的DataFrame
            **kwargs: 转换参数
            
        Returns:
            转换后的DataFrame
        """
        return self.operations.transform_data(data, **kwargs)
    
    def sync_data(self, symbol: str, timeframe: str, 
                start_time: Optional[datetime] = None,
                end_time: Optional[datetime] = None,
                data_source: Optional[str] = None,
                force_update: bool = False) -> bool:
        """
        同步数据
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            start_time: 开始时间 (可选)
            end_time: 结束时间 (可选)
            data_source: 数据源名称 (可选)
            force_update: 是否强制更新已有数据
            
        Returns:
            是否成功同步
        """
        return self.management.sync_data(
            symbol, 
            timeframe, 
            start_time=start_time,
            end_time=end_time,
            data_source=data_source,
            force_update=force_update
        )
    
    def get_storage_info(self) -> Dict[str, Any]:
        """
        获取存储信息
        
        Returns:
            存储信息字典
        """
        return self.management.get_storage_info()
    
    def get_available_symbols(self) -> List[str]:
        """
        获取存储中可用的交易对列表
        
        Returns:
            交易对代码列表
        """
        return self.management.get_available_symbols()
    
    def get_available_timeframes(self, symbol: Optional[str] = None) -> List[str]:
        """
        获取存储中可用的时间周期列表
        
        Args:
            symbol: 交易对或资产代码 (可选)
            
        Returns:
            时间周期列表
        """
        return self.management.get_available_timeframes(symbol)
    
    def delete_data(self, symbol: str, timeframe: str) -> bool:
        """
        删除数据
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            
        Returns:
            是否成功删除
        """
        return self.management.delete_data(symbol, timeframe)
    
    def prepare_strategy_data(self, symbol: str, timeframe: str,
                             start_time: datetime, end_time: datetime,
                             indicators: Optional[Dict[str, Dict[str, Any]]] = None,
                             clean_data: bool = True,
                             transform_data: bool = False,
                             transform_params: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        准备策略数据
        
        一站式方法，获取、清洗、转换数据并添加技术指标，用于策略开发和回测。
        
        Args:
            symbol: 交易对或资产代码
            timeframe: 时间周期
            start_time: 开始时间
            end_time: 结束时间
            indicators: 需要添加的技术指标及参数
            clean_data: 是否清洗数据
            transform_data: 是否转换数据
            transform_params: 数据转换参数
            
        Returns:
            准备好的策略数据
        """
        # 获取原始数据
        data = self.get_data(symbol, timeframe, start_time, end_time)
        
        if data.empty:
            return data
        
        # 清洗数据
        if clean_data:
            data = self.clean_data(data)
        
        # 转换数据
        if transform_data:
            transform_params = transform_params or {}
            data = self.transform_data(data, **transform_params)
        
        # 添加技术指标
        if indicators:
            data = self.operations.add_indicators(data, indicators)
        
        return data 