#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修复后的SMC策略信号生成
验证FreqTrade信号是否正确生成和执行
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data(length=1000):
    """创建测试数据"""
    dates = pd.date_range(start='2024-01-01', periods=length, freq='1min')
    
    # 创建模拟价格数据
    np.random.seed(42)
    base_price = 50000
    price_changes = np.random.normal(0, 0.001, length)
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    # 创建OHLCV数据
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        high = price * (1 + abs(np.random.normal(0, 0.0005)))
        low = price * (1 - abs(np.random.normal(0, 0.0005)))
        volume = np.random.uniform(100, 1000)
        
        data.append({
            'date': date,
            'open': price,
            'high': high,
            'low': low,
            'close': price,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('date', inplace=True)
    return df

def test_smc_strategy():
    """测试SMC策略信号生成"""
    logger.info("🔧 开始测试修复后的SMC策略信号生成")
    
    try:
        # 导入策略
        from backtest.strategies.smc_strategy import SMCStrategy
        
        # 创建策略实例
        strategy = SMCStrategy()
        logger.info("✅ 策略实例创建成功")
        
        # 创建测试数据
        test_data = create_test_data(500)
        logger.info(f"✅ 测试数据创建成功: {len(test_data)} 行")
        
        # 测试指标计算
        metadata = {'pair': 'BTC/USDT:USDT'}
        
        logger.info("🔧 开始指标计算...")
        dataframe = strategy.populate_indicators(test_data.copy(), metadata)
        logger.info(f"✅ 指标计算完成，列数: {len(dataframe.columns)}")
        
        # 检查必需的指标
        required_indicators = ['EMA_20', 'EMA_50', 'RSI', 'ATR']
        missing_indicators = [ind for ind in required_indicators if ind not in dataframe.columns]
        if missing_indicators:
            logger.error(f"❌ 缺少必需指标: {missing_indicators}")
            return False
        
        logger.info("✅ 所有必需指标都已计算")
        
        # 检查信号列初始化
        signal_columns = ['enter_long', 'enter_short', 'exit_long', 'exit_short', 'enter_tag', 'exit_tag']
        missing_signals = [col for col in signal_columns if col not in dataframe.columns]
        if missing_signals:
            logger.error(f"❌ 缺少信号列: {missing_signals}")
            return False
        
        logger.info("✅ 所有信号列都已初始化")
        
        # 测试入场信号生成
        logger.info("🔧 开始入场信号生成...")
        dataframe = strategy.populate_entry_trend(dataframe, metadata)
        
        # 统计入场信号
        long_signals = dataframe['enter_long'].sum()
        short_signals = dataframe['enter_short'].sum()
        total_entry_signals = long_signals + short_signals
        
        logger.info(f"📊 入场信号统计:")
        logger.info(f"  多头信号: {long_signals}")
        logger.info(f"  空头信号: {short_signals}")
        logger.info(f"  总入场信号: {total_entry_signals}")
        
        # 测试出场信号生成
        logger.info("🔧 开始出场信号生成...")
        dataframe = strategy.populate_exit_trend(dataframe, metadata)
        
        # 统计出场信号
        exit_long_signals = dataframe['exit_long'].sum()
        exit_short_signals = dataframe['exit_short'].sum()
        total_exit_signals = exit_long_signals + exit_short_signals
        
        logger.info(f"📊 出场信号统计:")
        logger.info(f"  多头出场信号: {exit_long_signals}")
        logger.info(f"  空头出场信号: {exit_short_signals}")
        logger.info(f"  总出场信号: {total_exit_signals}")
        
        # 验证信号质量
        if total_entry_signals == 0:
            logger.warning("⚠️ 没有生成任何入场信号！")
            return False
        
        if total_exit_signals == 0:
            logger.warning("⚠️ 没有生成任何出场信号！")
            return False
        
        # 检查信号格式
        entry_signal_rows = dataframe[(dataframe['enter_long'] == 1) | (dataframe['enter_short'] == 1)]
        if len(entry_signal_rows) > 0:
            logger.info("✅ 入场信号格式正确")
            # 显示前几个信号的详情
            for i, (idx, row) in enumerate(entry_signal_rows.head(3).iterrows()):
                signal_type = "多头" if row['enter_long'] == 1 else "空头"
                logger.info(f"  信号{i+1}: {signal_type} @ {idx}, 标签: {row['enter_tag']}")
        
        exit_signal_rows = dataframe[(dataframe['exit_long'] == 1) | (dataframe['exit_short'] == 1)]
        if len(exit_signal_rows) > 0:
            logger.info("✅ 出场信号格式正确")
            # 显示前几个信号的详情
            for i, (idx, row) in enumerate(exit_signal_rows.head(3).iterrows()):
                signal_type = "多头出场" if row['exit_long'] == 1 else "空头出场"
                logger.info(f"  信号{i+1}: {signal_type} @ {idx}, 标签: {row['exit_tag']}")
        
        # 检查策略配置
        logger.info("🔧 检查策略配置...")
        logger.info(f"  时间框架: {strategy.timeframe}")
        logger.info(f"  最小ROI: {strategy.minimal_roi}")
        logger.info(f"  止损: {strategy.stoploss}")
        logger.info(f"  可做空: {strategy.can_short}")
        logger.info(f"  启动蜡烛数: {strategy.startup_candle_count}")
        
        logger.info("🎉 SMC策略信号生成测试完成！")
        logger.info("✅ 策略修复成功，应该能够正常生成和执行信号")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_smc_strategy()
    if success:
        print("\n🎉 测试成功！策略修复完成，可以重新启动FreqTrade测试。")
        print("\n📋 修复要点:")
        print("1. ✅ 简化了信号生成逻辑，移除复杂的信号控制系统")
        print("2. ✅ 在populate_indicators中正确初始化所有信号列")
        print("3. ✅ 使用FreqTrade标准信号格式，直接设置dataframe列")
        print("4. ✅ 简化了RSI和趋势条件，确保信号可用性")
        print("5. ✅ 移除了可能阻止信号执行的过滤逻辑")
        print("\n🚀 建议重新启动FreqTrade，应该能看到交易执行！")
    else:
        print("\n❌ 测试失败！需要进一步调试。")
