#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试Backtrader回测引擎
"""

import sys
sys.path.insert(0, '.')
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 创建测试数据
print("Creating test data...")
dates = pd.date_range(start='2024-01-01', end='2024-01-10', freq='1min')
np.random.seed(42)
prices = 50000 + np.cumsum(np.random.randn(len(dates)) * 10)

test_data = pd.DataFrame({
    'open': prices,
    'high': prices + np.random.rand(len(dates)) * 20,
    'low': prices - np.random.rand(len(dates)) * 20,
    'close': prices + np.random.randn(len(dates)) * 5,
    'volume': np.random.randint(100, 1000, len(dates))
}, index=dates)

print(f'Test data created: {len(test_data)} rows')
print('Testing Backtrader engine...')

try:
    from backtest.examples.smc_strategy_backtest import BacktraderFreqTradeEngine
    from backtest.strategies.smc_strategy import SMCStrategy
    from config.smc_strategy_config import SMCConfigManager

    # 创建策略
    config_manager = SMCConfigManager()
    strategy_params = config_manager.get_strategy_params()
    config = {'timeframe': '1m'}
    strategy_params['config'] = config
    strategy = SMCStrategy(**strategy_params)

    # 创建回测引擎
    engine = BacktraderFreqTradeEngine(test_data, initial_capital=10000, commission=0.001)

    print('Running backtest...')
    results = engine.run(strategy, pair_name='BTC_USDT', debug_mode=False)

    print('Backtest completed!')
    print(f'Initial Capital: {results.metrics["initial_capital"]}')
    print(f'Final Capital: {results.metrics["final_capital"]}')
    print(f'Total Return: {results.metrics["total_return"]:.2%}')
    print(f'Total Trades: {results.metrics["total_trades"]}')
    print(f'Win Rate: {results.metrics["win_rate"]:.2%}')
    print(f'Sharpe Ratio: {results.metrics["sharpe_ratio"]:.2f}')
    
    print("✅ Backtrader engine test successful!")

except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
