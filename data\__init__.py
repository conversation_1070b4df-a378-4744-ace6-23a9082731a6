"""
AriQuantification数据模块

提供数据获取、处理和存储功能。
"""

# 基础类和结构
from data.base import DataSource, DataStorage
from data.structures import TimeFrame, OHLCVColumns

# 避免循环导入和不存在的模块导入
# 不要在这里导入可能导致循环导入或不存在的模块

__all__ = [
    # 基础类和结构
    'DataSource', 'DataStorage',
    'TimeFrame', 'OHLCVColumns',
]

"""Data package initializer.

Exposes high-level API helpers such as download_data.
"""

from importlib import import_module as _imp

try:
    download_data = _imp('data.api').download_data  # type: ignore
except Exception:  # pragma: no cover
    # 在某些环境下载数据可能依赖外部库，若导入失败保持静默
    pass 