"""
风险价值(VaR)模型

实现多种风险价值(VaR)计算方法，包括历史模拟法、参数法、蒙特卡洛模拟法等。
"""

from typing import Dict, Any, List, Optional, Union, Callable, Tuple
import pandas as pd
import numpy as np
from scipy import stats
from abc import ABC, abstractmethod


class VaRModel(ABC):
    """
    风险价值(VaR)模型抽象基类
    
    所有具体VaR模型必须继承此类并实现相应方法。
    """
    
    def __init__(self, confidence_level: float = 0.95, time_horizon: int = 1):
        """
        初始化VaR模型
        
        Parameters
        ----------
        confidence_level : float, optional
            置信水平，默认为0.95 (95%)
        time_horizon : int, optional
            时间范围，默认为1，表示1日VaR
        """
        if not 0 < confidence_level < 1:
            raise ValueError("置信水平必须在0到1之间")
        
        if time_horizon <= 0:
            raise ValueError("时间范围必须为正整数")
        
        self.confidence_level = confidence_level
        self.time_horizon = time_horizon
    
    @abstractmethod
    def calculate(self, returns: pd.Series, portfolio_value: float = 1.0) -> float:
        """
        计算VaR
        
        Parameters
        ----------
        returns : pd.Series
            历史收益率序列
        portfolio_value : float, optional
            投资组合价值，默认为1.0
        
        Returns
        -------
        float
            风险价值(VaR)，表示为金额损失
        """
        pass
    
    def calculate_percentage(self, returns: pd.Series) -> float:
        """
        计算VaR百分比
        
        Parameters
        ----------
        returns : pd.Series
            历史收益率序列
        
        Returns
        -------
        float
            风险价值(VaR)，表示为收益率损失
        """
        return self.calculate(returns, 1.0)
    
    def calculate_conditional_var(self, returns: pd.Series, portfolio_value: float = 1.0) -> float:
        """
        计算条件风险价值(CVaR)，也称为期望亏损(Expected Shortfall)
        
        Parameters
        ----------
        returns : pd.Series
            历史收益率序列
        portfolio_value : float, optional
            投资组合价值，默认为1.0
        
        Returns
        -------
        float
            条件风险价值(CVaR)，表示为金额损失
        """
        var = self.calculate_percentage(returns)
        cvar_returns = returns[returns < -var].mean()
        
        if np.isnan(cvar_returns):  # 如果没有超过VaR的损失
            return var * portfolio_value
        
        return abs(cvar_returns) * portfolio_value


class HistoricalVaRModel(VaRModel):
    """
    历史模拟法VaR模型
    
    基于历史收益率分布计算VaR，不假设特定分布形式。
    """
    
    def calculate(self, returns: pd.Series, portfolio_value: float = 1.0) -> float:
        """
        使用历史模拟法计算VaR
        
        Parameters
        ----------
        returns : pd.Series
            历史收益率序列
        portfolio_value : float, optional
            投资组合价值，默认为1.0
        
        Returns
        -------
        float
            风险价值(VaR)，表示为金额损失
        """
        if returns.empty:
            return 0.0
        
        # 计算特定分位数的损失
        var_percentile = np.percentile(returns, 100 * (1 - self.confidence_level))
        
        # 缩放到多日VaR（如果需要）
        if self.time_horizon > 1:
            var_percentile = var_percentile * np.sqrt(self.time_horizon)
        
        # 转换为正值，表示损失
        var_amount = abs(var_percentile) * portfolio_value
        
        return var_amount
    
    def calculate_conditional_var(self, returns: pd.Series, portfolio_value: float = 1.0) -> float:
        """
        使用历史模拟法计算条件风险价值(CVaR)
        
        Parameters
        ----------
        returns : pd.Series
            历史收益率序列
        portfolio_value : float, optional
            投资组合价值，默认为1.0
        
        Returns
        -------
        float
            条件风险价值(CVaR)，表示为金额损失
        """
        if returns.empty:
            return 0.0
        
        # 计算VaR的分位数值
        var_percentile = np.percentile(returns, 100 * (1 - self.confidence_level))
        
        # 计算超过VaR损失的平均值
        cvar = returns[returns <= var_percentile].mean()
        
        # 缩放到多日CVaR（如果需要）
        if self.time_horizon > 1:
            cvar = cvar * np.sqrt(self.time_horizon)
        
        # 转换为正值，表示损失
        cvar_amount = abs(cvar) * portfolio_value
        
        return cvar_amount


class ParametricVaRModel(VaRModel):
    """
    参数法VaR模型
    
    假设收益率服从正态分布，基于均值和标准差计算VaR。
    """
    
    def calculate(self, returns: pd.Series, portfolio_value: float = 1.0) -> float:
        """
        使用参数法计算VaR
        
        Parameters
        ----------
        returns : pd.Series
            历史收益率序列
        portfolio_value : float, optional
            投资组合价值，默认为1.0
        
        Returns
        -------
        float
            风险价值(VaR)，表示为金额损失
        """
        if returns.empty:
            return 0.0
        
        # 计算收益率的均值和标准差
        mean = returns.mean()
        std = returns.std()
        
        # 计算正态分布下的VaR
        z_score = stats.norm.ppf(1 - self.confidence_level)
        var_percentage = -(mean + z_score * std)
        
        # 缩放到多日VaR（如果需要）
        if self.time_horizon > 1:
            var_percentage = var_percentage * np.sqrt(self.time_horizon)
        
        # 转换为正值，表示损失
        var_amount = max(0, var_percentage) * portfolio_value
        
        return var_amount


class CornishFisherVaRModel(VaRModel):
    """
    Cornish-Fisher展开VaR模型
    
    考虑收益率分布的偏度和峰度，对正态分布假设进行修正。
    """
    
    def calculate(self, returns: pd.Series, portfolio_value: float = 1.0) -> float:
        """
        使用Cornish-Fisher展开计算VaR
        
        Parameters
        ----------
        returns : pd.Series
            历史收益率序列
        portfolio_value : float, optional
            投资组合价值，默认为1.0
        
        Returns
        -------
        float
            风险价值(VaR)，表示为金额损失
        """
        if returns.empty:
            return 0.0
        
        # 计算收益率的统计矩
        mean = returns.mean()
        std = returns.std()
        skew = stats.skew(returns)
        kurt = stats.kurtosis(returns)  # 超额峰度
        
        # 计算正态分布下的z值
        z = stats.norm.ppf(1 - self.confidence_level)
        
        # Cornish-Fisher展开，修正z值
        z_cf = z + (z**2 - 1) * skew / 6 + (z**3 - 3*z) * kurt / 24 - (2*z**3 - 5*z) * skew**2 / 36
        
        # 计算调整后的VaR
        var_percentage = -(mean + z_cf * std)
        
        # 缩放到多日VaR（如果需要）
        if self.time_horizon > 1:
            var_percentage = var_percentage * np.sqrt(self.time_horizon)
        
        # 转换为正值，表示损失
        var_amount = max(0, var_percentage) * portfolio_value
        
        return var_amount


class MonteCarloVaRModel(VaRModel):
    """
    蒙特卡洛模拟VaR模型
    
    通过模拟大量可能的市场情景计算VaR。
    """
    
    def __init__(self, confidence_level: float = 0.95, time_horizon: int = 1, 
                 num_simulations: int = 10000, random_state: int = None):
        """
        初始化蒙特卡洛VaR模型
        
        Parameters
        ----------
        confidence_level : float, optional
            置信水平，默认为0.95 (95%)
        time_horizon : int, optional
            时间范围，默认为1，表示1日VaR
        num_simulations : int, optional
            模拟次数，默认为10000
        random_state : int, optional
            随机种子，默认为None
        """
        super().__init__(confidence_level, time_horizon)
        self.num_simulations = num_simulations
        self.random_state = random_state
    
    def calculate(self, returns: pd.Series, portfolio_value: float = 1.0) -> float:
        """
        使用蒙特卡洛模拟计算VaR
        
        Parameters
        ----------
        returns : pd.Series
            历史收益率序列
        portfolio_value : float, optional
            投资组合价值，默认为1.0
        
        Returns
        -------
        float
            风险价值(VaR)，表示为金额损失
        """
        if returns.empty:
            return 0.0
        
        # 设置随机种子
        if self.random_state is not None:
            np.random.seed(self.random_state)
        
        # 计算收益率的均值和标准差
        mean = returns.mean()
        std = returns.std()
        
        # 模拟收益率序列
        simulated_returns = np.random.normal(
            loc=mean * self.time_horizon,
            scale=std * np.sqrt(self.time_horizon),
            size=self.num_simulations
        )
        
        # 计算VaR
        var_percentile = np.percentile(simulated_returns, 100 * (1 - self.confidence_level))
        
        # 转换为正值，表示损失
        var_amount = max(0, -var_percentile) * portfolio_value
        
        return var_amount


class CopulaVaRModel(VaRModel):
    """
    Copula VaR模型
    
    用于计算多资产组合的VaR，考虑资产间的依赖结构。
    注意: 此实现需要额外的依赖: scipy.stats.multivariate_normal
    """
    
    def __init__(self, confidence_level: float = 0.95, time_horizon: int = 1, 
                 num_simulations: int = 10000, copula_type: str = 'gaussian', random_state: int = None):
        """
        初始化Copula VaR模型
        
        Parameters
        ----------
        confidence_level : float, optional
            置信水平，默认为0.95 (95%)
        time_horizon : int, optional
            时间范围，默认为1，表示1日VaR
        num_simulations : int, optional
            模拟次数，默认为10000
        copula_type : str, optional
            Copula类型，可选'gaussian'或't'，默认为'gaussian'
        random_state : int, optional
            随机种子，默认为None
        """
        super().__init__(confidence_level, time_horizon)
        self.num_simulations = num_simulations
        self.copula_type = copula_type
        self.random_state = random_state
        
        if copula_type not in ['gaussian', 't']:
            raise ValueError("copula_type必须是'gaussian'或't'")
    
    def calculate(self, returns: pd.Series, portfolio_value: float = 1.0) -> float:
        """
        此方法对于单资产组合，等同于参数法或蒙特卡洛模拟
        
        Parameters
        ----------
        returns : pd.Series
            历史收益率序列
        portfolio_value : float, optional
            投资组合价值，默认为1.0
        
        Returns
        -------
        float
            风险价值(VaR)，表示为金额损失
        """
        # 对于单资产，使用蒙特卡洛模拟
        model = MonteCarloVaRModel(
            confidence_level=self.confidence_level,
            time_horizon=self.time_horizon,
            num_simulations=self.num_simulations,
            random_state=self.random_state
        )
        
        return model.calculate(returns, portfolio_value)
    
    def calculate_portfolio_var(self, returns_df: pd.DataFrame, weights: np.ndarray, 
                                portfolio_value: float = 1.0) -> float:
        """
        使用Copula模型计算投资组合VaR
        
        Parameters
        ----------
        returns_df : pd.DataFrame
            各资产的历史收益率，每一列对应一个资产
        weights : np.ndarray
            各资产权重数组
        portfolio_value : float, optional
            投资组合价值，默认为1.0
        
        Returns
        -------
        float
            投资组合风险价值(VaR)，表示为金额损失
        
        Notes
        -----
        此方法使用Copula来模拟资产间的依赖结构，适用于多资产组合。
        """
        from scipy.stats import multivariate_normal, t
        
        if returns_df.empty or len(weights) != returns_df.shape[1]:
            raise ValueError("收益率数据为空或权重维度不匹配")
        
        # 设置随机种子
        if self.random_state is not None:
            np.random.seed(self.random_state)
        
        # 计算相关性矩阵
        correlation = returns_df.corr().values
        
        # 计算各资产的均值和标准差
        means = returns_df.mean().values * self.time_horizon
        stds = returns_df.std().values * np.sqrt(self.time_horizon)
        
        # 生成多元正态分布的随机数
        if self.copula_type == 'gaussian':
            simulated_normal = multivariate_normal.rvs(
                mean=np.zeros(returns_df.shape[1]),
                cov=correlation,
                size=self.num_simulations
            )
        else:  # t-copula
            dof = 5  # t分布的自由度，可调整
            simulated_normal = t.rvs(
                df=dof,
                loc=np.zeros(returns_df.shape[1]),
                scale=correlation,
                size=self.num_simulations
            )
        
        # 转换为均匀分布（即Copula的输出）
        simulated_uniform = stats.norm.cdf(simulated_normal)
        
        # 反变换到原始边缘分布
        simulated_returns = np.zeros_like(simulated_uniform)
        for i in range(returns_df.shape[1]):
            simulated_returns[:, i] = stats.norm.ppf(
                simulated_uniform[:, i],
                loc=means[i],
                scale=stds[i]
            )
        
        # 计算投资组合收益率
        portfolio_returns = np.dot(simulated_returns, weights)
        
        # 计算VaR
        var_percentile = np.percentile(portfolio_returns, 100 * (1 - self.confidence_level))
        
        # 转换为正值，表示损失
        var_amount = max(0, -var_percentile) * portfolio_value
        
        return var_amount


class BootstrapVaRModel(VaRModel):
    """
    Bootstrap重采样VaR模型
    
    通过重采样历史收益率计算VaR，保留原始数据的分布特性。
    """
    
    def __init__(self, confidence_level: float = 0.95, time_horizon: int = 1, 
                 num_samples: int = 10000, block_size: int = 1, random_state: int = None):
        """
        初始化Bootstrap VaR模型
        
        Parameters
        ----------
        confidence_level : float, optional
            置信水平，默认为0.95 (95%)
        time_horizon : int, optional
            时间范围，默认为1，表示1日VaR
        num_samples : int, optional
            重采样次数，默认为10000
        block_size : int, optional
            块重采样的块大小，默认为1（标准Bootstrap）
        random_state : int, optional
            随机种子，默认为None
        """
        super().__init__(confidence_level, time_horizon)
        self.num_samples = num_samples
        self.block_size = block_size
        self.random_state = random_state
    
    def calculate(self, returns: pd.Series, portfolio_value: float = 1.0) -> float:
        """
        使用Bootstrap重采样计算VaR
        
        Parameters
        ----------
        returns : pd.Series
            历史收益率序列
        portfolio_value : float, optional
            投资组合价值，默认为1.0
        
        Returns
        -------
        float
            风险价值(VaR)，表示为金额损失
        """
        if returns.empty:
            return 0.0
        
        # 设置随机种子
        if self.random_state is not None:
            np.random.seed(self.random_state)
        
        returns_array = returns.values
        n = len(returns_array)
        
        # 标准Bootstrap或块Bootstrap
        if self.block_size == 1:
            # 标准Bootstrap
            indices = np.random.randint(0, n, size=(self.num_samples, self.time_horizon))
            bootstrap_returns = returns_array[indices].sum(axis=1)
        else:
            # 块Bootstrap
            max_start = n - self.block_size + 1
            if max_start <= 0:
                raise ValueError("数据长度不足以支持块大小")
            
            bootstrap_returns = np.zeros(self.num_samples)
            blocks_needed = (self.time_horizon + self.block_size - 1) // self.block_size
            
            for i in range(self.num_samples):
                # 抽取随机块
                starts = np.random.randint(0, max_start, size=blocks_needed)
                block_returns = np.zeros(blocks_needed * self.block_size)
                
                for j, start in enumerate(starts):
                    end = min(start + self.block_size, n)
                    block_idx = j * self.block_size
                    block_returns[block_idx:block_idx + (end - start)] = returns_array[start:end]
                
                # 取所需的时间长度
                bootstrap_returns[i] = block_returns[:self.time_horizon].sum()
        
        # 计算VaR
        var_percentile = np.percentile(bootstrap_returns, 100 * (1 - self.confidence_level))
        
        # 转换为正值，表示损失
        var_amount = max(0, -var_percentile) * portfolio_value
        
        return var_amount


class EWMAVaRModel(VaRModel):
    """
    指数加权移动平均(EWMA)VaR模型
    
    使用EWMA方法计算波动率，适用于波动率随时间变化的情况。
    """
    
    def __init__(self, confidence_level: float = 0.95, time_horizon: int = 1, 
                 lambda_decay: float = 0.94):
        """
        初始化EWMA VaR模型
        
        Parameters
        ----------
        confidence_level : float, optional
            置信水平，默认为0.95 (95%)
        time_horizon : int, optional
            时间范围，默认为1，表示1日VaR
        lambda_decay : float, optional
            衰减因子，默认为0.94（RiskMetrics标准）
        """
        super().__init__(confidence_level, time_horizon)
        
        if not 0 < lambda_decay < 1:
            raise ValueError("lambda_decay必须在0到1之间")
        
        self.lambda_decay = lambda_decay
    
    def calculate_ewma_variance(self, returns: pd.Series) -> float:
        """
        计算EWMA方差
        
        Parameters
        ----------
        returns : pd.Series
            历史收益率序列
        
        Returns
        -------
        float
            EWMA方差
        """
        # 计算平方收益率
        squared_returns = returns ** 2
        
        # 计算EWMA方差
        weights = np.array([(1 - self.lambda_decay) * self.lambda_decay ** i for i in range(len(squared_returns))])
        weights = weights[::-1]  # 反转权重，使得最近的观测权重最大
        weights = weights / np.sum(weights)  # 归一化权重
        
        ewma_var = np.sum(weights * squared_returns.values)
        
        return ewma_var
    
    def calculate(self, returns: pd.Series, portfolio_value: float = 1.0) -> float:
        """
        使用EWMA方法计算VaR
        
        Parameters
        ----------
        returns : pd.Series
            历史收益率序列
        portfolio_value : float, optional
            投资组合价值，默认为1.0
        
        Returns
        -------
        float
            风险价值(VaR)，表示为金额损失
        """
        if returns.empty:
            return 0.0
        
        # 计算EWMA方差
        ewma_var = self.calculate_ewma_variance(returns)
        ewma_std = np.sqrt(ewma_var)
        
        # 计算正态分布下的VaR
        z_score = stats.norm.ppf(1 - self.confidence_level)
        var_percentage = -(returns.mean() + z_score * ewma_std)
        
        # 缩放到多日VaR（如果需要）
        if self.time_horizon > 1:
            var_percentage = var_percentage * np.sqrt(self.time_horizon)
        
        # 转换为正值，表示损失
        var_amount = max(0, var_percentage) * portfolio_value
        
        return var_amount


# 便捷的直接调用函数
def calculate_var(returns, confidence_level=0.95, method='historical', portfolio_value=1.0):
    """
    计算风险价值(VaR)
    
    Parameters
    ----------
    returns : pd.Series
        收益率序列
    confidence_level : float, optional
        置信水平，默认为0.95 (95%)
    method : str, optional
        计算方法，可选 'historical', 'parametric', 'monte_carlo'，默认为'historical'
    portfolio_value : float, optional
        投资组合价值，默认为1.0
        
    Returns
    -------
    float
        风险价值(VaR)，表示为金额损失
    """
    if method == 'historical':
        model = HistoricalVaRModel(confidence_level=confidence_level)
    elif method == 'parametric':
        model = ParametricVaRModel(confidence_level=confidence_level)
    elif method == 'monte_carlo':
        model = MonteCarloVaRModel(confidence_level=confidence_level)
    else:
        raise ValueError(f"不支持的方法: {method}，可用选项: 'historical', 'parametric', 'monte_carlo'")
    
    return model.calculate(returns, portfolio_value)