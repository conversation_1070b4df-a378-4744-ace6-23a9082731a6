#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
回测批量处理模块

提供多个回测结果的批量分析和报告生成功能。
"""

from typing import Dict, Any, Union, Optional, List, Tuple, Callable
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime
import json
import warnings
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing

from ..base import BacktestResults
from .metrics import calculate_metrics
from .reporting import generate_report
from .comparison import StrategyComparison


class BatchProcessor:
    """
    批量处理器
    
    用于处理和分析多个回测结果，并生成批量或汇总报告。
    """
    
    def __init__(self, parallel: bool = False, max_workers: Optional[int] = None):
        """
        初始化批量处理器
        
        Parameters
        ----------
        parallel : bool, optional
            是否使用并行处理，默认为False
        max_workers : int, optional
            最大工作线程数，默认为None（使用CPU核心数）
        """
        self.results_collection = {}
        self.metadata = {}
        self.comparison_results = {}
        self.parallel = parallel
        self.max_workers = max_workers or (multiprocessing.cpu_count() - 1 or 1)
    
    def add_result(self, 
                 name: str, 
                 result: BacktestResults,
                 metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        添加回测结果
        
        Parameters
        ----------
        name : str
            结果名称
        result : BacktestResults
            回测结果对象
        metadata : dict, optional
            结果元数据，默认为None
        """
        self.results_collection[name] = result
        self.metadata[name] = metadata or {}
    
    def add_results(self, 
                  results: Dict[str, BacktestResults],
                  metadata: Optional[Dict[str, Dict[str, Any]]] = None) -> None:
        """
        批量添加回测结果
        
        Parameters
        ----------
        results : Dict[str, BacktestResults]
            名称和回测结果的字典
        metadata : Dict[str, Dict[str, Any]], optional
            名称和元数据的字典，默认为None
        """
        for name, result in results.items():
            meta = metadata.get(name, {}) if metadata else {}
            self.add_result(name, result, meta)
    
    def generate_summary_metrics(self, 
                               risk_free_rate: float = 0.0,
                               benchmark: Optional[pd.Series] = None,
                               metrics: Optional[List[str]] = None) -> pd.DataFrame:
        """
        生成汇总性能指标
        
        Parameters
        ----------
        risk_free_rate : float, optional
            无风险利率，默认为0.0
        benchmark : pd.Series, optional
            基准指数，默认为None
        metrics : List[str], optional
            要计算的指标列表，默认为None（计算所有核心指标）
            
        Returns
        -------
        pd.DataFrame
            汇总指标表格
        """
        if not self.results_collection:
            raise ValueError("No results to analyze")
            
        # 默认比较指标
        default_metrics = [
            'total_return', 'annualized_return', 'daily_sharpe', 'daily_sortino',
            'max_drawdown', 'volatility', 'calmar_ratio', 'win_rate'
        ]
        
        metrics_to_compute = metrics or default_metrics
        
        # 计算所有结果的指标
        summary_data = {}
        
        if self.parallel and len(self.results_collection) > 1:
            # 并行计算指标
            with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
                # 使用串行方式处理指标计算，不使用并行处理
                for name, result in self.results_collection.items():
                    try:
                        metrics_dict = calculate_metrics(
                            result, 
                            risk_free_rate=risk_free_rate, 
                            benchmark=benchmark
                        )
                        
                        summary_data[name] = {
                            k: metrics_dict.get(k) 
                            for k in metrics_to_compute 
                            if k in metrics_dict
                        }
                    except Exception as e:
                        warnings.warn(f"Error calculating metrics for {name}: {str(e)}")
                        summary_data[name] = {k: None for k in metrics_to_compute}
        else:
            # 串行计算指标
            for name, result in self.results_collection.items():
                try:
                    metrics_dict = calculate_metrics(
                        result, 
                        risk_free_rate=risk_free_rate, 
                        benchmark=benchmark
                    )
                    
                    summary_data[name] = {
                        k: metrics_dict.get(k) 
                        for k in metrics_to_compute 
                        if k in metrics_dict
                    }
                except Exception as e:
                    warnings.warn(f"Error calculating metrics for {name}: {str(e)}")
                    summary_data[name] = {k: None for k in metrics_to_compute}
        
        # 创建汇总表格
        summary_df = pd.DataFrame(summary_data).T
        
        # 添加元数据列
        if self.metadata:
            # 找出所有元数据字段
            all_meta_fields = set()
            for meta in self.metadata.values():
                all_meta_fields.update(meta.keys())
                
            # 为每个元数据字段创建列
            for field in sorted(all_meta_fields):
                summary_df[f'meta_{field}'] = summary_df.index.map(
                    lambda name: self.metadata.get(name, {}).get(field, None)
                )
        
        return summary_df
    
    def generate_correlation_matrix(self, method: str = 'returns') -> pd.DataFrame:
        """
        生成策略相关性矩阵
        
        Parameters
        ----------
        method : str, optional
            相关性计算方法，可选值为'returns'、'equity'，默认为'returns'
            
        Returns
        -------
        pd.DataFrame
            相关性矩阵
        """
        if not self.results_collection:
            raise ValueError("No results to analyze")
            
        if method not in ['returns', 'equity']:
            raise ValueError(f"Unsupported method: {method}, must be 'returns' or 'equity'")
            
        # 收集所有结果的收益率或净值序列
        series_dict = {}
        for name, result in self.results_collection.items():
            if method == 'returns':
                # 获取收益率序列
                get_returns = result.get_returns
                series = get_returns() if callable(get_returns) else get_returns
            else:
                # 获取净值曲线
                equity = result.equity() if callable(result.equity) else result.equity
                series = equity
                
            series_dict[name] = series
        
        # 创建包含所有结果的DataFrame
        df = pd.DataFrame(series_dict)
        
        # 计算相关性矩阵
        corr_matrix = df.corr(method='pearson')
        
        return corr_matrix
    
    def plot_correlation_heatmap(self, 
                               method: str = 'returns',
                               figsize: tuple = (10, 8),
                               cmap: str = 'coolwarm',
                               **kwargs) -> plt.Figure:
        """
        绘制相关性热力图
        
        Parameters
        ----------
        method : str, optional
            相关性计算方法，可选值为'returns'、'equity'，默认为'returns'
        figsize : tuple, optional
            图表大小，默认为(10, 8)
        cmap : str, optional
            颜色映射，默认为'coolwarm'
        **kwargs : dict
            其他绘图参数
            
        Returns
        -------
        plt.Figure
            热力图对象
        """
        # 计算相关性矩阵
        corr_matrix = self.generate_correlation_matrix(method=method)
        
        # 创建热力图
        fig, ax = plt.subplots(figsize=figsize)
        
        im = ax.imshow(corr_matrix, cmap=cmap, **kwargs)
        
        # 添加色标
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Correlation')
        
        # 设置刻度和标签
        ax.set_xticks(np.arange(len(corr_matrix.columns)))
        ax.set_yticks(np.arange(len(corr_matrix.index)))
        ax.set_xticklabels(corr_matrix.columns)
        ax.set_yticklabels(corr_matrix.index)
        
        # 旋转x轴标签
        plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")
        
        # 在每个单元格中显示相关系数
        for i in range(len(corr_matrix.index)):
            for j in range(len(corr_matrix.columns)):
                ax.text(j, i, f"{corr_matrix.iloc[i, j]:.2f}", 
                       ha="center", va="center", color="black" if abs(corr_matrix.iloc[i, j]) < 0.7 else "white")
        
        # 设置标题
        ax.set_title(f"Strategy Correlation Matrix ({method.capitalize()})")
        
        plt.tight_layout()
        return fig
    
    def generate_batch_reports(self, 
                             output_dir: str,
                             output_format: str = 'html',
                             benchmark: Optional[pd.Series] = None,
                             risk_free_rate: float = 0.0,
                             **kwargs) -> None:
        """
        生成批量回测报告
        
        Parameters
        ----------
        output_dir : str
            输出目录
        output_format : str, optional
            输出格式，可选值为'html'、'pdf'、'text'，默认为'html'
        benchmark : pd.Series, optional
            基准指数，默认为None
        risk_free_rate : float, optional
            无风险利率，默认为0.0
        **kwargs : dict
            其他报告参数
        """
        if not self.results_collection:
            raise ValueError("No results to generate reports")
            
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 如果结果数量大于1且启用并行，使用并行处理
        if self.parallel and len(self.results_collection) > 1:
            # 使用串行方式处理报告生成
            for name, result in self.results_collection.items():
                try:
                    # 构建输出路径
                    output_path = os.path.join(output_dir, f"{name}.{output_format}")
                    
                    # 生成报告
                    generate_report(
                        result,
                        benchmark=benchmark,
                        output_format=output_format,
                        output_path=output_path,
                        strategy_name=name,
                        **kwargs
                    )
                except Exception as e:
                    warnings.warn(f"Error generating report: {str(e)}")
        else:
            # 串行生成报告
            for name, result in self.results_collection.items():
                try:
                    # 构建输出路径
                    output_path = os.path.join(output_dir, f"{name}.{output_format}")
                    
                    # 生成报告
                    generate_report(
                        result,
                        benchmark=benchmark,
                        output_format=output_format,
                        output_path=output_path,
                        strategy_name=name,
                        **kwargs
                    )
                except Exception as e:
                    warnings.warn(f"Error generating report for {name}: {str(e)}")
    
    def generate_summary_report(self,
                              output_path: str,
                              benchmark: Optional[pd.Series] = None,
                              risk_free_rate: float = 0.0,
                              include_correlation: bool = True,
                              include_returns_chart: bool = True,
                              include_drawdowns_chart: bool = True,
                              **kwargs) -> None:
        """
        生成汇总报告
        
        Parameters
        ----------
        output_path : str
            输出路径
        benchmark : pd.Series, optional
            基准指数，默认为None
        risk_free_rate : float, optional
            无风险利率，默认为0.0
        include_correlation : bool, optional
            是否包含相关性热力图，默认为True
        include_returns_chart : bool, optional
            是否包含收益曲线图，默认为True
        include_drawdowns_chart : bool, optional
            是否包含回撤曲线图，默认为True
        **kwargs : dict
            其他报告参数
        """
        if not self.results_collection:
            raise ValueError("No results to generate summary report")
            
        # 创建输出目录
        output_dir = os.path.dirname(output_path)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            
        # 计算汇总指标
        summary_metrics = self.generate_summary_metrics(
            risk_free_rate=risk_free_rate,
            benchmark=benchmark
        )
        
        # 创建策略比较器
        comparison = StrategyComparison(self.results_collection, benchmark, risk_free_rate)
        
        # 生成汇总HTML报告
        output_format = os.path.splitext(output_path)[1][1:].lower()
        if not output_format or output_format not in ['html', 'pdf', 'txt']:
            output_format = 'html'
        
        if output_format == 'html':
            self._generate_html_summary_report(
                output_path=output_path,
                summary_metrics=summary_metrics,
                comparison=comparison,
                benchmark=benchmark,
                include_correlation=include_correlation,
                include_returns_chart=include_returns_chart,
                include_drawdowns_chart=include_drawdowns_chart,
                **kwargs
            )
        elif output_format == 'pdf':
            # PDF报告生成需要额外的依赖，如reportlab或weasyprint
            self._generate_pdf_summary_report(
                output_path=output_path,
                summary_metrics=summary_metrics,
                comparison=comparison,
                **kwargs
            )
        else:  # txt
            self._generate_text_summary_report(
                output_path=output_path,
                summary_metrics=summary_metrics,
                **kwargs
            )
            
    def _generate_html_summary_report(self,
                                    output_path: str,
                                    summary_metrics: pd.DataFrame,
                                    comparison: StrategyComparison,
                                    benchmark: Optional[pd.Series] = None,
                                    include_correlation: bool = True,
                                    include_returns_chart: bool = True,
                                    include_drawdowns_chart: bool = True,
                                    **kwargs) -> None:
        """
        生成HTML格式的汇总报告
        
        Parameters
        ----------
        output_path : str
            输出路径
        summary_metrics : pd.DataFrame
            汇总指标表格
        comparison : StrategyComparison
            策略比较器对象
        benchmark : pd.Series, optional
            基准指数，默认为None
        include_correlation : bool, optional
            是否包含相关性热力图，默认为True
        include_returns_chart : bool, optional
            是否包含收益曲线图，默认为True
        include_drawdowns_chart : bool, optional
            是否包含回撤曲线图，默认为True
        **kwargs : dict
            其他报告参数
        """
        # 导入必要的模块
        import matplotlib
        matplotlib.use('Agg')
        import io
        import base64
        
        # 定义辅助函数，将图表转换为base64编码
        def fig_to_base64(fig):
            buf = io.BytesIO()
            fig.savefig(buf, format='png', dpi=100)
            buf.seek(0)
            img_str = base64.b64encode(buf.read()).decode('utf-8')
            plt.close(fig)
            return img_str
        
        # 准备报告内容
        strategy_count = len(self.results_collection)
        
        # 生成图表
        img_data = {}
        
        # 生成收益曲线比较图
        if include_returns_chart:
            try:
                returns_fig = comparison.compare_returns(
                    normalize=True,
                    figsize=(12, 6),
                    title='Strategy Returns Comparison'
                )
                img_data['returns_chart'] = fig_to_base64(returns_fig)
            except Exception as e:
                warnings.warn(f"Error generating returns chart: {str(e)}")
                img_data['returns_chart'] = None
        
        # 生成回撤曲线比较图
        if include_drawdowns_chart:
            try:
                drawdowns_fig = comparison.compare_drawdowns(
                    figsize=(12, 6),
                    title='Strategy Drawdowns Comparison'
                )
                img_data['drawdowns_chart'] = fig_to_base64(drawdowns_fig)
            except Exception as e:
                warnings.warn(f"Error generating drawdowns chart: {str(e)}")
                img_data['drawdowns_chart'] = None
        
        # 生成相关性热力图
        if include_correlation and strategy_count > 1:
            try:
                corr_fig = self.plot_correlation_heatmap(
                    method='returns',
                    figsize=(8, 6),
                    cmap='coolwarm'
                )
                img_data['correlation_chart'] = fig_to_base64(corr_fig)
            except Exception as e:
                warnings.warn(f"Error generating correlation chart: {str(e)}")
                img_data['correlation_chart'] = None
        
        # 创建HTML报告
        html_template = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backtest Summary Report</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }}
        .header {{
            background-color: #2c3e50;
            color: #fff;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 5px;
        }}
        .header h1 {{
            margin: 0;
            font-size: 24px;
        }}
        .header p {{
            margin: 5px 0 0 0;
            opacity: 0.8;
        }}
        .section {{
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }}
        .section h2 {{
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }}
        table th, table td {{
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        table th {{
            background-color: #f2f2f2;
        }}
        .chart-container {{
            margin-bottom: 20px;
            text-align: center;
        }}
        .chart-container img {{
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .meta-label {{
            font-weight: bold;
            color: #666;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Backtest Summary Report</h1>
            <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>Total Strategies: {strategy_count}</p>
        </div>
        
        <div class="section">
            <h2>Performance Summary</h2>
            <table>
                <thead>
                    <tr>
                        <th>Strategy</th>
                        {''.join(f'<th>{col}</th>' for col in summary_metrics.columns if not col.startswith('meta_'))}
                    </tr>
                </thead>
                <tbody>
                    {''.join(f'<tr><td>{idx}</td>{"".join(f"<td>{row[col]}</td>" for col in summary_metrics.columns if not col.startswith("meta_"))}</tr>' for idx, row in summary_metrics.iterrows())}
                </tbody>
            </table>
        </div>
        
        {'<div class="section"><h2>Strategy Metadata</h2><table><thead><tr><th>Strategy</th>' + ''.join(f'<th>{col[5:]}</th>' for col in summary_metrics.columns if col.startswith('meta_')) + '</tr></thead><tbody>' + ''.join(f'<tr><td>{idx}</td>{"".join(f"<td>{row[col]}</td>" for col in summary_metrics.columns if col.startswith("meta_"))}</tr>' for idx, row in summary_metrics.iterrows()) + '</tbody></table></div>' if any(col.startswith('meta_') for col in summary_metrics.columns) else ''}
        
        {f'''
        <div class="section">
            <h2>Returns Comparison</h2>
            <div class="chart-container">
                <img src="data:image/png;base64,{img_data['returns_chart']}" alt="Returns Comparison">
            </div>
        </div>
        ''' if include_returns_chart and img_data.get('returns_chart') else ''}
        
        {f'''
        <div class="section">
            <h2>Drawdowns Comparison</h2>
            <div class="chart-container">
                <img src="data:image/png;base64,{img_data['drawdowns_chart']}" alt="Drawdowns Comparison">
            </div>
        </div>
        ''' if include_drawdowns_chart and img_data.get('drawdowns_chart') else ''}
        
        {f'''
        <div class="section">
            <h2>Strategy Correlation</h2>
            <div class="chart-container">
                <img src="data:image/png;base64,{img_data['correlation_chart']}" alt="Strategy Correlation">
            </div>
        </div>
        ''' if include_correlation and strategy_count > 1 and img_data.get('correlation_chart') else ''}
        
        <div class="section">
            <h2>About</h2>
            <p>This report was generated using the Batch Processor of AriQuantification.</p>
            <p>© {datetime.now().year} AriQuantification. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
"""
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_template)
            
    def _generate_pdf_summary_report(self,
                                  output_path: str,
                                  summary_metrics: pd.DataFrame,
                                  comparison: StrategyComparison,
                                  **kwargs) -> None:
        """
        生成PDF格式的汇总报告
        
        Parameters
        ----------
        output_path : str
            输出路径
        summary_metrics : pd.DataFrame
            汇总指标表格
        comparison : StrategyComparison
            策略比较器对象
        **kwargs : dict
            其他报告参数
        """
        try:
            # 检查是否安装了必要的依赖
            import weasyprint
        except ImportError:
            raise ImportError("WeasyPrint is required for PDF report generation. "
                           "Please install it using 'pip install weasyprint'")
        
        # 首先生成HTML报告
        html_path = output_path.replace('.pdf', '.html')
        self._generate_html_summary_report(
            output_path=html_path,
            summary_metrics=summary_metrics,
            comparison=comparison,
            **kwargs
        )
        
        # 将HTML转换为PDF
        import weasyprint
        html = weasyprint.HTML(filename=html_path)
        html.write_pdf(output_path)
        
        # 删除临时HTML文件
        if os.path.exists(html_path):
            os.remove(html_path)
            
    def _generate_text_summary_report(self,
                                   output_path: str,
                                   summary_metrics: pd.DataFrame,
                                   **kwargs) -> None:
        """
        生成文本格式的汇总报告
        
        Parameters
        ----------
        output_path : str
            输出路径
        summary_metrics : pd.DataFrame
            汇总指标表格
        **kwargs : dict
            其他报告参数
        """
        # 导入必要的模块
        from tabulate import tabulate
        
        # 准备报告内容
        strategy_count = len(self.results_collection)
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 分离元数据列和指标列
        metrics_cols = [col for col in summary_metrics.columns if not col.startswith('meta_')]
        meta_cols = [col for col in summary_metrics.columns if col.startswith('meta_')]
        
        # 创建文本报告
        with open(output_path, 'w', encoding='utf-8') as f:
            # 标题和基本信息
            f.write("======================================\n")
            f.write("       BACKTEST SUMMARY REPORT        \n")
            f.write("======================================\n\n")
            f.write(f"Generated on: {current_time}\n")
            f.write(f"Total Strategies: {strategy_count}\n\n")
            
            # 指标表格
            f.write("PERFORMANCE SUMMARY\n")
            f.write("------------------\n\n")
            f.write(tabulate(
                summary_metrics[metrics_cols], 
                headers='keys', 
                tablefmt='pipe', 
                showindex=True,
                numalign='right',
                stralign='left'
            ))
            f.write("\n\n")
            
            # 如果有元数据，添加元数据表格
            if meta_cols:
                f.write("STRATEGY METADATA\n")
                f.write("----------------\n\n")
                # 重命名元数据列（去除'meta_'前缀）
                meta_df = summary_metrics[meta_cols].copy()
                meta_df.columns = [col[5:] for col in meta_cols]
                f.write(tabulate(
                    meta_df, 
                    headers='keys', 
                    tablefmt='pipe', 
                    showindex=True,
                    numalign='right',
                    stralign='left'
                ))
                f.write("\n\n")
            
            # 相关性矩阵（如果策略数量大于1）
            if strategy_count > 1:
                try:
                    corr_matrix = self.generate_correlation_matrix(method='returns')
                    
                    f.write("STRATEGY CORRELATION (RETURNS)\n")
                    f.write("-----------------------------\n\n")
                    f.write(tabulate(
                        corr_matrix, 
                        headers='keys', 
                        tablefmt='pipe', 
                        showindex=True,
                        numalign='right',
                        floatfmt='.2f'
                    ))
                    f.write("\n\n")
                except Exception as e:
                    f.write(f"Error generating correlation matrix: {str(e)}\n\n")
            
            # 页脚
            f.write("======================================\n")
            f.write("© AriQuantification. All rights reserved.\n")
    
    def export_summary_to_csv(self, output_path: str, **kwargs) -> None:
        """
        导出汇总指标到CSV文件
        
        Parameters
        ----------
        output_path : str
            输出路径
        **kwargs : dict
            传递给generate_summary_metrics的参数
        """
        # 计算汇总指标
        summary_metrics = self.generate_summary_metrics(**kwargs)
        
        # 导出到CSV
        summary_metrics.to_csv(output_path)
    
    def export_summary_to_excel(self, output_path: str, **kwargs) -> None:
        """
        导出汇总指标到Excel文件
        
        Parameters
        ----------
        output_path : str
            输出路径
        **kwargs : dict
            传递给generate_summary_metrics的参数
        """
        try:
            # 检查是否安装了必要的依赖
            import openpyxl
            import xlsxwriter
            
            # 计算汇总指标
            summary_metrics = self.generate_summary_metrics(**kwargs)
            
            # 导出到Excel
            with pd.ExcelWriter(output_path, engine='xlsxwriter') as writer:
                summary_metrics.to_excel(writer, sheet_name='Summary Metrics')
                
                # 如果策略数量大于1，添加相关性矩阵
                if len(self.results_collection) > 1:
                    try:
                        corr_matrix = self.generate_correlation_matrix(method='returns')
                        corr_matrix.to_excel(writer, sheet_name='Correlation Matrix')
                    except Exception as e:
                        warnings.warn(f"Error exporting correlation matrix: {str(e)}")
        except ImportError:
            # 如果缺少依赖库，尝试导出为CSV格式
            warnings.warn("openpyxl或xlsxwriter未安装，将导出为CSV格式")
            csv_path = output_path.replace('.xlsx', '.csv')
            self.export_summary_to_csv(csv_path, **kwargs)
        except Exception as e:
            warnings.warn(f"导出Excel失败: {str(e)}，尝试导出为CSV格式")
            try:
                csv_path = output_path.replace('.xlsx', '.csv')
                self.export_summary_to_csv(csv_path, **kwargs)
            except Exception as e2:
                warnings.warn(f"导出CSV也失败了: {str(e2)}")
    
    def analyze_by_groups(self, 
                        group_by: str,
                        risk_free_rate: float = 0.0,
                        benchmark: Optional[pd.Series] = None) -> Dict[str, pd.DataFrame]:
        """
        按组进行分析
        
        Parameters
        ----------
        group_by : str
            分组依据（元数据字段名称，需要包含'meta_'前缀）
        risk_free_rate : float, optional
            无风险利率，默认为0.0
        benchmark : pd.Series, optional
            基准指数，默认为None
            
        Returns
        -------
        Dict[str, pd.DataFrame]
            各组的汇总指标
        """
        if not self.results_collection:
            raise ValueError("No results to analyze")
            
        if not self.metadata:
            raise ValueError("No metadata available for grouping")
            
        # 确保group_by是有效的元数据字段
        field_name = group_by[5:] if group_by.startswith('meta_') else group_by
        
        # 检查是否所有结果都有该元数据字段
        missing_field = [
            name for name, meta in self.metadata.items()
            if field_name not in meta
        ]
        
        if missing_field:
            warnings.warn(f"{len(missing_field)} results do not have metadata field '{field_name}'")
        
        # 按组分类结果
        groups = {}
        for name, result in self.results_collection.items():
            group_value = self.metadata.get(name, {}).get(field_name, 'Unknown')
            if group_value not in groups:
                groups[group_value] = {}
            groups[group_value][name] = result
        
        # 对每个组计算汇总指标
        group_summaries = {}
        for group_name, group_results in groups.items():
            # 创建临时批处理器
            temp_processor = BatchProcessor(parallel=self.parallel, max_workers=self.max_workers)
            temp_processor.add_results(group_results)
            
            # 计算汇总指标
            group_summary = temp_processor.generate_summary_metrics(
                risk_free_rate=risk_free_rate,
                benchmark=benchmark
            )
            
            group_summaries[group_name] = group_summary
        
        return group_summaries
    
    def clean_cache(self) -> None:
        """清除缓存的计算结果"""
        self.comparison_results = {}


def batch_analyze_results(results: Dict[str, BacktestResults],
                        output_dir: str,
                        benchmark: Optional[pd.Series] = None,
                        risk_free_rate: float = 0.0,
                        generate_individual_reports: bool = True,
                        generate_summary: bool = True,
                        parallel: bool = False,
                        **kwargs) -> None:
    """
    批量分析回测结果并生成报告
    
    Parameters
    ----------
    results : Dict[str, BacktestResults]
        名称和回测结果的字典
    output_dir : str
        输出目录
    benchmark : pd.Series, optional
        基准指数，默认为None
    risk_free_rate : float, optional
        无风险利率，默认为0.0
    generate_individual_reports : bool, optional
        是否生成个别报告，默认为True
    generate_summary : bool, optional
        是否生成汇总报告，默认为True
    parallel : bool, optional
        是否使用并行处理，默认为False
    **kwargs : dict
        其他报告参数
    """
    # 创建批处理器
    processor = BatchProcessor(parallel=parallel)
    
    # 添加结果
    processor.add_results(results)
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成个别报告
    if generate_individual_reports:
        individual_dir = os.path.join(output_dir, 'individual_reports')
        processor.generate_batch_reports(
            output_dir=individual_dir,
            benchmark=benchmark,
            risk_free_rate=risk_free_rate,
            **kwargs
        )
    
    # 生成汇总报告
    if generate_summary:
        summary_path = os.path.join(output_dir, 'summary_report.html')
        processor.generate_summary_report(
            output_path=summary_path,
            benchmark=benchmark,
            risk_free_rate=risk_free_rate,
            **kwargs
        )
        
        # 导出汇总指标
        csv_path = os.path.join(output_dir, 'summary_metrics.csv')
        processor.export_summary_to_csv(
            output_path=csv_path,
            risk_free_rate=risk_free_rate,
            benchmark=benchmark
        )
        
        try:
            excel_path = os.path.join(output_dir, 'summary_metrics.xlsx')
            processor.export_summary_to_excel(
                output_path=excel_path,
                risk_free_rate=risk_free_rate,
                benchmark=benchmark
            )
        except ImportError:
            pass  # 如果缺少Excel导出依赖，跳过Excel导出 